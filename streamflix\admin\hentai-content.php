<?php
session_start();
require_once '../config/database.php';

// Simple admin check
$is_admin = false;
if (isset($_SESSION['user_id'])) {
    if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
        $is_admin = true;
    } elseif (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
        $is_admin = true;
    }
}

if (!$is_admin) {
    // Try to check database directly
    try {
        $db = new Database();
        $conn = $db->connect();

        if (isset($_SESSION['user_id'])) {
            $stmt = $conn->prepare("SELECT role FROM users WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($user && $user['role'] === 'admin') {
                $is_admin = true;
                $_SESSION['role'] = 'admin';
            }
        }
    } catch (Exception $e) {
        // Database check failed
    }
}

if (!$is_admin) {
    header('Location: ../login.php');
    exit;
}

// Database connection (reuse if already created)
if (!isset($conn)) {
    $db = new Database();
    $conn = $db->connect();
}

$message = '';
$error = '';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        if ($action === 'convert_to_hentai') {
            $selected_ids = $_POST['selected_items'] ?? [];
            if (!empty($selected_ids)) {
                $success_count = 0;
                foreach ($selected_ids as $id) {
                    $stmt = $conn->prepare("UPDATE tv_shows SET content_type = 'hentai' WHERE id = ?");
                    $stmt->execute([$id]);
                    $success_count++;
                }
                $message = "Successfully converted {$success_count} items to hentai content.";
            }
        }
        
        if ($action === 'revert_to_tv') {
            $selected_ids = $_POST['selected_items'] ?? [];
            if (!empty($selected_ids)) {
                $success_count = 0;
                foreach ($selected_ids as $id) {
                    $stmt = $conn->prepare("UPDATE tv_shows SET content_type = 'tv_show' WHERE id = ?");
                    $stmt->execute([$id]);
                    $success_count++;
                }
                $message = "Successfully reverted {$success_count} items to TV shows.";
            }
        }
        
        if ($action === 'delete_content') {
            $selected_ids = $_POST['selected_items'] ?? [];
            if (!empty($selected_ids)) {
                $success_count = 0;
                foreach ($selected_ids as $id) {
                    // Delete related data first
                    $stmt = $conn->prepare("DELETE FROM tv_show_genres WHERE tv_show_id = ?");
                    $stmt->execute([$id]);
                    
                    // Delete the show
                    $stmt = $conn->prepare("DELETE FROM tv_shows WHERE id = ?");
                    $stmt->execute([$id]);
                    $success_count++;
                }
                $message = "Successfully deleted {$success_count} items.";
            }
        }
        
    } catch (Exception $e) {
        $error = 'Error: ' . $e->getMessage();
    }
}

// Get filter parameters
$filter = $_GET['filter'] ?? 'hentai';
$search = $_GET['search'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build query based on filter
$where_conditions = [];
$params = [];

if ($filter === 'hentai') {
    $where_conditions[] = "content_type = 'hentai'";
} elseif ($filter === 'potential') {
    $where_conditions[] = "(
        LOWER(name) LIKE '%hentai%' OR 
        LOWER(name) LIKE '%ecchi%' OR 
        LOWER(name) LIKE '%adult%' OR 
        LOWER(overview) LIKE '%hentai%' OR 
        LOWER(overview) LIKE '%adult%'
    ) AND (content_type != 'hentai' OR content_type IS NULL)";
} elseif ($filter === 'all') {
    // No additional filter
}

if (!empty($search)) {
    $where_conditions[] = "(name LIKE ? OR overview LIKE ?)";
    $params[] = "%{$search}%";
    $params[] = "%{$search}%";
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total count
$count_sql = "SELECT COUNT(*) FROM tv_shows {$where_clause}";
$stmt = $conn->prepare($count_sql);
$stmt->execute($params);
$total_items = $stmt->fetchColumn();
$total_pages = ceil($total_items / $per_page);

// Get content items
$sql = "
    SELECT id, tmdb_id, name, overview, content_type, vote_average, first_air_date, poster_path, number_of_seasons, number_of_episodes
    FROM tv_shows 
    {$where_clause}
    ORDER BY name ASC 
    LIMIT {$per_page} OFFSET {$offset}
";
$stmt = $conn->prepare($sql);
$stmt->execute($params);
$content_items = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get statistics
$stats = [];
$stmt = $conn->query("SELECT COUNT(*) FROM tv_shows WHERE content_type = 'hentai'");
$stats['hentai'] = $stmt->fetchColumn();

$stmt = $conn->query("SELECT COUNT(*) FROM tv_shows WHERE content_type = 'tv_show' OR content_type IS NULL");
$stats['tv_shows'] = $stmt->fetchColumn();

$stmt = $conn->query("SELECT COUNT(*) FROM embed_servers WHERE hentai_url IS NOT NULL AND hentai_url != ''");
$stats['hentai_servers'] = $stmt->fetchColumn();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hentai Content Management - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .page-header {
            background: linear-gradient(135deg, #ff1744, #e91e63);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: var(--secondary-color);
            border: 2px solid #ff1744;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #ff1744;
            margin-bottom: 5px;
        }
        
        .filters {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            border: 1px solid var(--border-color);
        }
        
        .filter-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .content-card {
            background: var(--secondary-color);
            border-radius: 12px;
            overflow: hidden;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .content-card:hover {
            border-color: #ff1744;
            transform: translateY(-2px);
        }
        
        .content-card.hentai {
            border-color: #ff1744;
        }
        
        .content-poster {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .content-info {
            padding: 15px;
        }
        
        .content-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-primary);
        }
        
        .content-meta {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-bottom: 10px;
        }
        
        .content-type {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
            margin-bottom: 10px;
        }
        
        .type-hentai {
            background: #ff1744;
            color: white;
        }
        
        .type-tv_show {
            background: #17a2b8;
            color: white;
        }
        
        .type-null {
            background: #6c757d;
            color: white;
        }
        
        .content-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 0.8rem;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }
        
        .bulk-actions {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            border: 1px solid var(--border-color);
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }
        
        .pagination a, .pagination span {
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            border: 1px solid var(--border-color);
        }
        
        .pagination .current {
            background: #ff1744;
            color: white;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 1px solid #28a745;
        }
        
        .alert-error {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid #dc3545;
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo defined('SITE_NAME') ? SITE_NAME : 'StreamFlix'; ?></a>
            <div class="nav-links">
                <a href="index.php">📊 Dashboard</a>
                <a href="hentai-content.php" class="active">🔞 Hentai Content</a>
                <a href="anime-content.php">🎌 Anime Content</a>
                <a href="hentai-servers.php">🖥️ Hentai Servers</a>
            </div>
            <div class="user-menu">
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="page-header">
            <h1><i class="fas fa-heart"></i> Hentai Content Management</h1>
            <p>Manage adult anime content, convert between categories, and organize hentai series</p>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['hentai']); ?></div>
                <div class="stat-label">Hentai Content</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['tv_shows']); ?></div>
                <div class="stat-label">TV Shows</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['hentai_servers']); ?></div>
                <div class="stat-label">Hentai Servers</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($total_items); ?></div>
                <div class="stat-label">Current Results</div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters">
            <form method="GET" class="filter-row">
                <div>
                    <label for="filter">Filter:</label>
                    <select name="filter" id="filter" onchange="this.form.submit()">
                        <option value="hentai" <?= $filter === 'hentai' ? 'selected' : '' ?>>Hentai Content</option>
                        <option value="potential" <?= $filter === 'potential' ? 'selected' : '' ?>>Potential Hentai</option>
                        <option value="all" <?= $filter === 'all' ? 'selected' : '' ?>>All Content</option>
                    </select>
                </div>
                <div>
                    <label for="search">Search:</label>
                    <input type="text" name="search" id="search" value="<?= htmlspecialchars($search) ?>" placeholder="Search by name or description">
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Search
                </button>
                <a href="?" class="btn btn-secondary">
                    <i class="fas fa-refresh"></i> Reset
                </a>
            </form>
        </div>

        <!-- Bulk Actions -->
        <form method="POST" id="bulkForm">
            <div class="bulk-actions">
                <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px;">
                    <div>
                        <button type="button" onclick="selectAll()" class="btn btn-secondary">
                            <i class="fas fa-check-square"></i> Select All
                        </button>
                        <button type="button" onclick="selectNone()" class="btn btn-secondary">
                            <i class="fas fa-square"></i> Select None
                        </button>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button type="submit" name="action" value="convert_to_hentai" class="btn btn-danger" onclick="return confirm('Convert selected items to hentai?')">
                            <i class="fas fa-heart"></i> Convert to Hentai
                        </button>
                        <button type="submit" name="action" value="revert_to_tv" class="btn btn-info" onclick="return confirm('Revert selected items to TV shows?')">
                            <i class="fas fa-tv"></i> Revert to TV
                        </button>
                        <button type="submit" name="action" value="delete_content" class="btn btn-danger" onclick="return confirm('Delete selected items permanently?')">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            </div>

            <!-- Content Grid -->
            <?php if (empty($content_items)): ?>
                <div style="text-align: center; padding: 60px; color: var(--text-secondary);">
                    <i class="fas fa-search" style="font-size: 4rem; margin-bottom: 20px; opacity: 0.3;"></i>
                    <h3>No hentai content found</h3>
                    <p>Current filter: <strong><?= htmlspecialchars($filter) ?></strong></p>
                    <p>Total items in query: <strong><?= $total_items ?></strong></p>

                    <?php if ($filter === 'hentai'): ?>
                        <div style="margin: 20px 0;">
                            <p>No content is currently marked as hentai. Try these options:</p>
                            <a href="?filter=potential" class="btn btn-primary">🔍 Find Potential Hentai Content</a>
                            <a href="?filter=all" class="btn btn-secondary">📺 View All TV Shows</a>
                            <a href="quick-hentai-anime-converter.php" class="btn btn-danger">🔄 Auto-Convert Content</a>
                        </div>
                    <?php elseif ($filter === 'potential'): ?>
                        <div style="margin: 20px 0;">
                            <p>No potential hentai content detected. Try:</p>
                            <a href="?filter=all" class="btn btn-secondary">📺 View All Content</a>
                            <a href="?search=hentai" class="btn btn-primary">🔍 Search "hentai"</a>
                        </div>
                    <?php else: ?>
                        <div style="margin: 20px 0;">
                            <p>No content matches your search criteria.</p>
                            <a href="?" class="btn btn-secondary">🔄 Reset Filters</a>
                        </div>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <div class="content-grid">
                    <?php foreach ($content_items as $item): ?>
                        <div class="content-card <?= $item['content_type'] === 'hentai' ? 'hentai' : '' ?>">
                            <img src="https://image.tmdb.org/t/p/w300<?= htmlspecialchars($item['poster_path'] ?? '') ?>"
                                 alt="<?= htmlspecialchars($item['name']) ?>"
                                 class="content-poster"
                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMjAgODBIMTgwVjkwSDEyMFY4MFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHA+PC9wYXRoPgo8L3N2Zz4K'">

                            <div class="content-info">
                                <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 10px;">
                                    <input type="checkbox" name="selected_items[]" value="<?= $item['id'] ?>" class="item-checkbox">
                                    <span class="content-type type-<?= $item['content_type'] ?? 'null' ?>">
                                        <?= $item['content_type'] ?? 'Uncategorized' ?>
                                    </span>
                                </div>

                                <h4 class="content-title"><?= htmlspecialchars($item['name']) ?></h4>

                                <div class="content-meta">
                                    <div><strong>TMDB ID:</strong> <?= $item['tmdb_id'] ?></div>
                                    <div><strong>Rating:</strong> ⭐ <?= number_format($item['vote_average'], 1) ?>/10</div>
                                    <div><strong>Year:</strong> <?= $item['first_air_date'] ? date('Y', strtotime($item['first_air_date'])) : 'N/A' ?></div>
                                    <?php if ($item['number_of_seasons']): ?>
                                    <div><strong>Seasons:</strong> <?= $item['number_of_seasons'] ?> | <strong>Episodes:</strong> <?= $item['number_of_episodes'] ?? 'N/A' ?></div>
                                    <?php endif; ?>
                                </div>

                                <?php if (!empty($item['overview'])): ?>
                                <div style="margin: 10px 0; font-size: 0.85rem; color: var(--text-secondary);">
                                    <?= htmlspecialchars(strlen($item['overview']) > 100 ? substr($item['overview'], 0, 100) . '...' : $item['overview']) ?>
                                </div>
                                <?php endif; ?>

                                <div class="content-actions">
                                    <a href="../player.php?id=<?= $item['tmdb_id'] ?>&type=tv_show"
                                       target="_blank"
                                       class="btn-small btn-primary"
                                       title="Play">
                                        <i class="fas fa-play"></i> Play
                                    </a>

                                    <?php if ($item['content_type'] !== 'hentai'): ?>
                                    <button type="button"
                                            onclick="convertSingle(<?= $item['id'] ?>, '<?= htmlspecialchars($item['name']) ?>')"
                                            class="btn-small btn-danger"
                                            title="Convert to Hentai">
                                        <i class="fas fa-heart"></i> To Hentai
                                    </button>
                                    <?php else: ?>
                                    <button type="button"
                                            onclick="revertSingle(<?= $item['id'] ?>, '<?= htmlspecialchars($item['name']) ?>')"
                                            class="btn-small btn-info"
                                            title="Revert to TV Show">
                                        <i class="fas fa-tv"></i> To TV
                                    </button>
                                    <?php endif; ?>

                                    <button type="button"
                                            onclick="deleteSingle(<?= $item['id'] ?>, '<?= htmlspecialchars($item['name']) ?>')"
                                            class="btn-small btn-danger"
                                            title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </form>

        <!-- Debug Info -->
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; font-size: 0.9rem; color: #666;">
            <h4>🔍 Debug Information:</h4>
            <ul>
                <li><strong>Filter:</strong> <?= htmlspecialchars($filter) ?></li>
                <li><strong>Search:</strong> <?= htmlspecialchars($search) ?></li>
                <li><strong>Total Items:</strong> <?= $total_items ?></li>
                <li><strong>Current Page:</strong> <?= $page ?> of <?= $total_pages ?></li>
                <li><strong>Content Items Count:</strong> <?= count($content_items) ?></li>
            </ul>
        </div>

        <!-- Quick Links -->
        <div style="margin-top: 40px; text-align: center;">
            <a href="index.php" class="btn btn-secondary">← Back to Dashboard</a>
            <a href="anime-content.php" class="btn btn-secondary">🎌 Anime Content</a>
            <a href="hentai-servers.php" class="btn btn-secondary">🖥️ Hentai Servers</a>
            <a href="../test_hentai_servers.php" target="_blank" class="btn btn-primary">🧪 Test Servers</a>
        </div>
    </div>

    <script>
        function selectAll() {
            document.querySelectorAll('.item-checkbox').forEach(cb => cb.checked = true);
        }

        function selectNone() {
            document.querySelectorAll('.item-checkbox').forEach(cb => cb.checked = false);
        }

        function convertSingle(id, name) {
            if (confirm(`Convert "${name}" to hentai content?`)) {
                submitSingleAction('convert_to_hentai', id);
            }
        }

        function revertSingle(id, name) {
            if (confirm(`Revert "${name}" back to TV show?`)) {
                submitSingleAction('revert_to_tv', id);
            }
        }

        function deleteSingle(id, name) {
            if (confirm(`Delete "${name}" permanently?`)) {
                submitSingleAction('delete_content', id);
            }
        }

        function submitSingleAction(action, id) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="${action}">
                <input type="hidden" name="selected_items[]" value="${id}">
            `;
            document.body.appendChild(form);
            form.submit();
        }

        // Auto-submit search form on Enter
        document.getElementById('search').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                this.form.submit();
            }
        });

        // Debug: Log current filter and results
        console.log('Current filter:', '<?= $filter ?>');
        console.log('Total items:', <?= $total_items ?>);
        console.log('Content items count:', <?= count($content_items) ?>);
    </script>
</body>
</html>
