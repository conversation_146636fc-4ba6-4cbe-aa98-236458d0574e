<?php
session_start();
require_once '../config/database.php';

// Simple admin check
$is_admin = false;
if (isset($_SESSION['user_id'])) {
    if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
        $is_admin = true;
    } elseif (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
        $is_admin = true;
    }
}

if (!$is_admin) {
    try {
        $db = new Database();
        $conn = $db->connect();
        
        if (isset($_SESSION['user_id'])) {
            $stmt = $conn->prepare("SELECT role FROM users WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && $user['role'] === 'admin') {
                $is_admin = true;
                $_SESSION['role'] = 'admin';
            }
        }
    } catch (Exception $e) {
        // Database check failed
    }
}

if (!$is_admin) {
    header('Location: ../login.php');
    exit;
}

// Database connection
if (!isset($conn)) {
    $db = new Database();
    $conn = $db->connect();
}

$message = '';
$error = '';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        if ($action === 'convert_to_hentai') {
            $selected_ids = $_POST['selected_items'] ?? [];
            $update_genres = isset($_POST['update_genres']);
            
            if (!empty($selected_ids)) {
                $success_count = 0;
                foreach ($selected_ids as $id) {
                    // Update content type
                    $stmt = $conn->prepare("UPDATE tv_shows SET content_type = 'hentai' WHERE id = ?");
                    $stmt->execute([$id]);
                    
                    // Add hentai genre if requested
                    if ($update_genres) {
                        // Get or create hentai genre
                        $stmt = $conn->prepare("SELECT id FROM genres WHERE LOWER(name) = 'hentai'");
                        $stmt->execute();
                        $hentai_genre = $stmt->fetch(PDO::FETCH_ASSOC);
                        
                        if (!$hentai_genre) {
                            $stmt = $conn->prepare("INSERT INTO genres (tmdb_id, name) VALUES (99999, 'Hentai')");
                            $stmt->execute();
                            $hentai_genre_id = $conn->lastInsertId();
                        } else {
                            $hentai_genre_id = $hentai_genre['id'];
                        }
                        
                        // Add genre relation if not exists
                        $stmt = $conn->prepare("INSERT IGNORE INTO tv_show_genres (tv_show_id, genre_id) VALUES (?, ?)");
                        $stmt->execute([$id, $hentai_genre_id]);
                    }
                    
                    $success_count++;
                }
                $message = "Successfully converted {$success_count} items to hentai content.";
            }
        }
        
        if ($action === 'convert_to_anime') {
            $selected_ids = $_POST['selected_items'] ?? [];
            $update_genres = isset($_POST['update_genres']);
            
            if (!empty($selected_ids)) {
                $success_count = 0;
                foreach ($selected_ids as $id) {
                    // Check if it's a movie or TV show
                    $stmt = $conn->prepare("SELECT 'tv_show' as type FROM tv_shows WHERE id = ?");
                    $stmt->execute([$id]);
                    $item_type = $stmt->fetchColumn();
                    
                    if ($item_type) {
                        $stmt = $conn->prepare("UPDATE tv_shows SET content_type = 'anime' WHERE id = ?");
                        $stmt->execute([$id]);
                    } else {
                        // Try movies table
                        $stmt = $conn->prepare("UPDATE movies SET content_type = 'anime' WHERE id = ?");
                        $stmt->execute([$id]);
                    }
                    
                    // Add anime genre if requested
                    if ($update_genres) {
                        // Get or create anime genre
                        $stmt = $conn->prepare("SELECT id FROM genres WHERE LOWER(name) = 'anime'");
                        $stmt->execute();
                        $anime_genre = $stmt->fetch(PDO::FETCH_ASSOC);
                        
                        if (!$anime_genre) {
                            $stmt = $conn->prepare("INSERT INTO genres (tmdb_id, name) VALUES (99998, 'Anime')");
                            $stmt->execute();
                            $anime_genre_id = $conn->lastInsertId();
                        } else {
                            $anime_genre_id = $anime_genre['id'];
                        }
                        
                        // Add genre relation
                        if ($item_type) {
                            $stmt = $conn->prepare("INSERT IGNORE INTO tv_show_genres (tv_show_id, genre_id) VALUES (?, ?)");
                            $stmt->execute([$id, $anime_genre_id]);
                        } else {
                            $stmt = $conn->prepare("INSERT IGNORE INTO movie_genres (movie_id, genre_id) VALUES (?, ?)");
                            $stmt->execute([$id, $anime_genre_id]);
                        }
                    }
                    
                    $success_count++;
                }
                $message = "Successfully converted {$success_count} items to anime content.";
            }
        }
        
        if ($action === 'revert_content') {
            $selected_ids = $_POST['selected_items'] ?? [];
            
            if (!empty($selected_ids)) {
                $success_count = 0;
                foreach ($selected_ids as $id) {
                    // Try both tables
                    $stmt = $conn->prepare("UPDATE tv_shows SET content_type = 'tv_show' WHERE id = ?");
                    $stmt->execute([$id]);
                    
                    $stmt = $conn->prepare("UPDATE movies SET content_type = 'movie' WHERE id = ?");
                    $stmt->execute([$id]);
                    
                    $success_count++;
                }
                $message = "Successfully reverted {$success_count} items to original content type.";
            }
        }
        
        if ($action === 'delete_content') {
            $selected_ids = $_POST['selected_items'] ?? [];
            
            if (!empty($selected_ids)) {
                $success_count = 0;
                foreach ($selected_ids as $id) {
                    // Delete from both tables
                    $stmt = $conn->prepare("DELETE FROM tv_show_genres WHERE tv_show_id = ?");
                    $stmt->execute([$id]);
                    $stmt = $conn->prepare("DELETE FROM tv_shows WHERE id = ?");
                    $stmt->execute([$id]);
                    
                    $stmt = $conn->prepare("DELETE FROM movie_genres WHERE movie_id = ?");
                    $stmt->execute([$id]);
                    $stmt = $conn->prepare("DELETE FROM movies WHERE id = ?");
                    $stmt->execute([$id]);
                    
                    $success_count++;
                }
                $message = "Successfully deleted {$success_count} items.";
            }
        }
        
    } catch (Exception $e) {
        $error = 'Error: ' . $e->getMessage();
    }
}

// Get filter parameters
$category = $_GET['category'] ?? 'all'; // hentai, anime, potential_hentai, potential_anime, all
$content_type = $_GET['content_type'] ?? 'all'; // movies, tv_shows, all
$search = $_GET['search'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 24;
$offset = ($page - 1) * $per_page;

// Build unified query for both movies and TV shows
$union_queries = [];
$params = [];

// Movies query
if ($content_type === 'all' || $content_type === 'movies') {
    $movie_where = [];
    
    if ($category === 'hentai') {
        $movie_where[] = "content_type = 'hentai'";
    } elseif ($category === 'anime') {
        $movie_where[] = "content_type = 'anime'";
    } elseif ($category === 'potential_hentai') {
        $movie_where[] = "(
            LOWER(title) LIKE '%hentai%' OR 
            LOWER(title) LIKE '%ecchi%' OR 
            LOWER(title) LIKE '%adult%' OR 
            LOWER(overview) LIKE '%hentai%' OR 
            LOWER(overview) LIKE '%adult%'
        ) AND (content_type != 'hentai' OR content_type IS NULL)";
    } elseif ($category === 'potential_anime') {
        $movie_where[] = "(
            LOWER(title) LIKE '%anime%' OR 
            LOWER(title) LIKE '%manga%' OR 
            LOWER(overview) LIKE '%anime%' OR 
            LOWER(overview) LIKE '%manga%'
        ) AND (content_type != 'anime' OR content_type IS NULL)";
    }
    
    if (!empty($search)) {
        $movie_where[] = "(title LIKE ? OR overview LIKE ?)";
        $params[] = "%{$search}%";
        $params[] = "%{$search}%";
    }
    
    $movie_where_clause = !empty($movie_where) ? 'WHERE ' . implode(' AND ', $movie_where) : '';
    
    $union_queries[] = "
        SELECT id, tmdb_id, title as name, overview, content_type, vote_average, release_date as air_date, poster_path, runtime as duration, 'movie' as item_type
        FROM movies 
        {$movie_where_clause}
    ";
}

// TV Shows query
if ($content_type === 'all' || $content_type === 'tv_shows') {
    $tv_where = [];
    
    if ($category === 'hentai') {
        $tv_where[] = "content_type = 'hentai'";
    } elseif ($category === 'anime') {
        $tv_where[] = "content_type = 'anime'";
    } elseif ($category === 'potential_hentai') {
        $tv_where[] = "(
            LOWER(name) LIKE '%hentai%' OR 
            LOWER(name) LIKE '%ecchi%' OR 
            LOWER(name) LIKE '%adult%' OR 
            LOWER(overview) LIKE '%hentai%' OR 
            LOWER(overview) LIKE '%adult%'
        ) AND (content_type != 'hentai' OR content_type IS NULL)";
    } elseif ($category === 'potential_anime') {
        $tv_where[] = "(
            LOWER(name) LIKE '%anime%' OR 
            LOWER(name) LIKE '%manga%' OR 
            LOWER(overview) LIKE '%anime%' OR 
            LOWER(overview) LIKE '%manga%'
        ) AND (content_type != 'anime' OR content_type IS NULL)";
    }
    
    if (!empty($search)) {
        $tv_where[] = "(name LIKE ? OR overview LIKE ?)";
        $params[] = "%{$search}%";
        $params[] = "%{$search}%";
    }
    
    $tv_where_clause = !empty($tv_where) ? 'WHERE ' . implode(' AND ', $tv_where) : '';
    
    $union_queries[] = "
        SELECT id, tmdb_id, name, overview, content_type, vote_average, first_air_date as air_date, poster_path, number_of_episodes as duration, 'tv_show' as item_type
        FROM tv_shows 
        {$tv_where_clause}
    ";
}

$full_query = implode(' UNION ALL ', $union_queries) . " ORDER BY name ASC LIMIT {$per_page} OFFSET {$offset}";

// Get total count (simplified for performance)
$total_items = 0;
foreach ($union_queries as $query) {
    $count_query = preg_replace('/SELECT.*?FROM/', 'SELECT COUNT(*) FROM', $query);
    $count_query = preg_replace('/ORDER BY.*/', '', $count_query);
    
    $stmt = $conn->prepare($count_query);
    $stmt->execute(array_slice($params, 0, 2)); // Each query uses max 2 params for search
    $total_items += $stmt->fetchColumn();
}

$total_pages = ceil($total_items / $per_page);

// Get content items
$stmt = $conn->prepare($full_query);
$stmt->execute($params);
$content_items = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get statistics
$stats = [];
$stmt = $conn->query("SELECT COUNT(*) FROM tv_shows WHERE content_type = 'hentai'");
$stats['hentai_tv'] = $stmt->fetchColumn();

$stmt = $conn->query("SELECT COUNT(*) FROM movies WHERE content_type = 'hentai'");
$stats['hentai_movies'] = $stmt->fetchColumn();

$stmt = $conn->query("SELECT COUNT(*) FROM tv_shows WHERE content_type = 'anime'");
$stats['anime_tv'] = $stmt->fetchColumn();

$stmt = $conn->query("SELECT COUNT(*) FROM movies WHERE content_type = 'anime'");
$stats['anime_movies'] = $stmt->fetchColumn();

$stats['total_hentai'] = $stats['hentai_tv'] + $stats['hentai_movies'];
$stats['total_anime'] = $stats['anime_tv'] + $stats['anime_movies'];

$stmt = $conn->query("SELECT COUNT(*) FROM embed_servers WHERE hentai_url IS NOT NULL AND hentai_url != ''");
$stats['hentai_servers'] = $stmt->fetchColumn();

$stmt = $conn->query("SELECT COUNT(*) FROM embed_servers WHERE anime_url IS NOT NULL AND anime_url != ''");
$stats['anime_servers'] = $stmt->fetchColumn();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hentai & Anime Management - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .page-header {
            background: linear-gradient(135deg, #ff1744, #ff6b35);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: var(--secondary-color);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .stat-card.hentai {
            border-color: #ff1744;
        }
        
        .stat-card.anime {
            border-color: #ff6b35;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-number.hentai {
            color: #ff1744;
        }
        
        .stat-number.anime {
            color: #ff6b35;
        }
        
        .filters {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            border: 1px solid var(--border-color);
        }
        
        .filter-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .content-card {
            background: var(--secondary-color);
            border-radius: 12px;
            overflow: hidden;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .content-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .content-card.hentai {
            border-color: #ff1744;
        }
        
        .content-card.anime {
            border-color: #ff6b35;
        }
        
        .content-poster {
            width: 100%;
            height: 180px;
            object-fit: cover;
        }
        
        .content-info {
            padding: 15px;
        }
        
        .content-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-primary);
            line-height: 1.3;
        }
        
        .content-meta {
            font-size: 0.85rem;
            color: var(--text-secondary);
            margin-bottom: 10px;
        }
        
        .content-type {
            display: inline-block;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: bold;
            text-transform: uppercase;
            margin-bottom: 8px;
        }
        
        .type-hentai {
            background: #ff1744;
            color: white;
        }
        
        .type-anime {
            background: #ff6b35;
            color: white;
        }
        
        .type-movie {
            background: #28a745;
            color: white;
        }
        
        .type-tv_show {
            background: #17a2b8;
            color: white;
        }
        
        .type-null {
            background: #6c757d;
            color: white;
        }
        
        .item-type-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .content-actions {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }
        
        .btn-small {
            padding: 5px 10px;
            font-size: 0.75rem;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            transition: all 0.3s ease;
        }
        
        .btn-small:hover {
            transform: translateY(-1px);
        }
        
        .bulk-actions {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            border: 1px solid var(--border-color);
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }
        
        .pagination a, .pagination span {
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            border: 1px solid var(--border-color);
        }
        
        .pagination .current {
            background: linear-gradient(135deg, #ff1744, #ff6b35);
            color: white;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 1px solid #28a745;
        }
        
        .alert-error {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid #dc3545;
        }
        
        @media (max-width: 768px) {
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .content-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            }
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo defined('SITE_NAME') ? SITE_NAME : 'StreamFlix'; ?></a>
            <div class="nav-links">
                <a href="index.php">📊 Dashboard</a>
                <a href="hentai-anime-management.php" class="active">🔞🎌 Hentai & Anime</a>
                <a href="hentai-servers.php">🖥️ Servers</a>
                <a href="import.php">📥 Import</a>
            </div>
            <div class="user-menu">
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="page-header">
            <h1><i class="fas fa-heart"></i> <i class="fas fa-torii-gate"></i> Hentai & Anime Management</h1>
            <p>Unified management system for adult anime content and regular anime content</p>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card hentai">
                <div class="stat-number hentai"><?php echo number_format($stats['total_hentai']); ?></div>
                <div class="stat-label">Total Hentai</div>
            </div>
            <div class="stat-card hentai">
                <div class="stat-number hentai"><?php echo number_format($stats['hentai_tv']); ?></div>
                <div class="stat-label">Hentai Series</div>
            </div>
            <div class="stat-card hentai">
                <div class="stat-number hentai"><?php echo number_format($stats['hentai_movies']); ?></div>
                <div class="stat-label">Hentai Movies</div>
            </div>
            <div class="stat-card anime">
                <div class="stat-number anime"><?php echo number_format($stats['total_anime']); ?></div>
                <div class="stat-label">Total Anime</div>
            </div>
            <div class="stat-card anime">
                <div class="stat-number anime"><?php echo number_format($stats['anime_tv']); ?></div>
                <div class="stat-label">Anime Series</div>
            </div>
            <div class="stat-card anime">
                <div class="stat-number anime"><?php echo number_format($stats['anime_movies']); ?></div>
                <div class="stat-label">Anime Movies</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #28a745;"><?php echo number_format($stats['hentai_servers']); ?></div>
                <div class="stat-label">Hentai Servers</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #17a2b8;"><?php echo number_format($stats['anime_servers']); ?></div>
                <div class="stat-label">Anime Servers</div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters">
            <form method="GET" class="filter-row">
                <div>
                    <label for="category">Category:</label>
                    <select name="category" id="category" onchange="this.form.submit()">
                        <option value="all" <?= $category === 'all' ? 'selected' : '' ?>>All Content</option>
                        <option value="hentai" <?= $category === 'hentai' ? 'selected' : '' ?>>🔞 Hentai Content</option>
                        <option value="anime" <?= $category === 'anime' ? 'selected' : '' ?>>🎌 Anime Content</option>
                        <option value="potential_hentai" <?= $category === 'potential_hentai' ? 'selected' : '' ?>>🔍 Potential Hentai</option>
                        <option value="potential_anime" <?= $category === 'potential_anime' ? 'selected' : '' ?>>🔍 Potential Anime</option>
                    </select>
                </div>
                <div>
                    <label for="content_type">Type:</label>
                    <select name="content_type" id="content_type" onchange="this.form.submit()">
                        <option value="all" <?= $content_type === 'all' ? 'selected' : '' ?>>Movies & TV Shows</option>
                        <option value="movies" <?= $content_type === 'movies' ? 'selected' : '' ?>>Movies Only</option>
                        <option value="tv_shows" <?= $content_type === 'tv_shows' ? 'selected' : '' ?>>TV Shows Only</option>
                    </select>
                </div>
                <div>
                    <label for="search">Search:</label>
                    <input type="text" name="search" id="search" value="<?= htmlspecialchars($search) ?>" placeholder="Search by name or description">
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Search
                </button>
                <a href="?" class="btn btn-secondary">
                    <i class="fas fa-refresh"></i> Reset
                </a>
            </form>
        </div>

        <!-- Bulk Actions -->
        <form method="POST" id="bulkForm">
            <div class="bulk-actions">
                <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px;">
                    <div>
                        <button type="button" onclick="selectAll()" class="btn btn-secondary">
                            <i class="fas fa-check-square"></i> Select All
                        </button>
                        <button type="button" onclick="selectNone()" class="btn btn-secondary">
                            <i class="fas fa-square"></i> Select None
                        </button>
                        <label style="margin-left: 15px;">
                            <input type="checkbox" name="update_genres" value="1" checked>
                            Update Genres
                        </label>
                    </div>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <button type="submit" name="action" value="convert_to_hentai" class="btn btn-danger" onclick="return confirm('Convert selected items to hentai?')">
                            <i class="fas fa-heart"></i> To Hentai
                        </button>
                        <button type="submit" name="action" value="convert_to_anime" class="btn" style="background: #ff6b35;" onclick="return confirm('Convert selected items to anime?')">
                            <i class="fas fa-torii-gate"></i> To Anime
                        </button>
                        <button type="submit" name="action" value="revert_content" class="btn btn-info" onclick="return confirm('Revert selected items to original type?')">
                            <i class="fas fa-undo"></i> Revert
                        </button>
                        <button type="submit" name="action" value="delete_content" class="btn btn-danger" onclick="return confirm('Delete selected items permanently?')">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            </div>
