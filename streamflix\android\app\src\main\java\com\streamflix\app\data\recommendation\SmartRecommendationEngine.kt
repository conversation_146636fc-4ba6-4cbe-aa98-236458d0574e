package com.streamflix.app.data.recommendation

import com.streamflix.app.data.local.UserPreferences
import com.streamflix.app.data.model.*
import com.streamflix.app.data.repository.StreamFlixRepository
import com.streamflix.app.utils.Resource
import kotlinx.coroutines.flow.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.*

@Singleton
class SmartRecommendationEngine @Inject constructor(
    private val repository: StreamFlixRepository,
    private val userPreferences: UserPreferences
) {

    companion object {
        private const val MIN_RATING_THRESHOLD = 6.0
        private const val POPULARITY_WEIGHT = 0.3
        private const val RATING_WEIGHT = 0.4
        private const val GENRE_WEIGHT = 0.3
        private const val RECENCY_WEIGHT = 0.2
        private const val WATCH_HISTORY_WEIGHT = 0.5
    }

    // ==================== Public API ====================

    suspend fun getPersonalizedRecommendations(limit: Int = 20): Flow<Resource<List<RecommendationItem>>> = flow {
        emit(Resource.Loading())
        
        try {
            val userProfile = buildUserProfile()
            val allContent = getAllContent()
            
            val recommendations = generateRecommendations(userProfile, allContent, limit)
            
            emit(Resource.Success(recommendations))
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Failed to generate recommendations"))
        }
    }

    suspend fun getSimilarContent(
        contentId: Int,
        contentType: ContentType,
        limit: Int = 10
    ): Flow<Resource<List<RecommendationItem>>> = flow {
        emit(Resource.Loading())
        
        try {
            val baseContent = when (contentType) {
                ContentType.MOVIE -> getMovieDetails(contentId)
                ContentType.TV_SHOW -> getTvShowDetails(contentId)
            }
            
            if (baseContent != null) {
                val similarContent = findSimilarContent(baseContent, limit)
                emit(Resource.Success(similarContent))
            } else {
                emit(Resource.Error("Content not found"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Failed to find similar content"))
        }
    }

    suspend fun getTrendingRecommendations(limit: Int = 15): Flow<Resource<List<RecommendationItem>>> = flow {
        emit(Resource.Loading())
        
        try {
            val trendingMovies = repository.getTrendingMovies(limit / 2).first()
            val trendingTvShows = repository.getTrendingTvShows(limit / 2).first()
            
            val recommendations = mutableListOf<RecommendationItem>()
            
            if (trendingMovies is Resource.Success) {
                recommendations.addAll(
                    trendingMovies.data?.map { movie ->
                        RecommendationItem(
                            content = movie,
                            score = calculateTrendingScore(movie.popularity, movie.voteAverage),
                            reason = "Trending now",
                            type = RecommendationType.TRENDING
                        )
                    } ?: emptyList()
                )
            }
            
            if (trendingTvShows is Resource.Success) {
                recommendations.addAll(
                    trendingTvShows.data?.map { tvShow ->
                        RecommendationItem(
                            content = tvShow,
                            score = calculateTrendingScore(tvShow.popularity, tvShow.voteAverage),
                            reason = "Trending now",
                            type = RecommendationType.TRENDING
                        )
                    } ?: emptyList()
                )
            }
            
            val sortedRecommendations = recommendations
                .sortedByDescending { it.score }
                .take(limit)
            
            emit(Resource.Success(sortedRecommendations))
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Failed to get trending recommendations"))
        }
    }

    suspend fun getGenreBasedRecommendations(
        genreId: Int,
        limit: Int = 20
    ): Flow<Resource<List<RecommendationItem>>> = flow {
        emit(Resource.Loading())
        
        try {
            val moviesByGenre = repository.getMoviesByGenre(genreId, 1).first()
            val tvShowsByGenre = repository.getTvShowsByGenre(genreId, 1).first()
            
            val recommendations = mutableListOf<RecommendationItem>()
            
            if (moviesByGenre is Resource.Success) {
                recommendations.addAll(
                    moviesByGenre.data?.movies?.map { movie ->
                        RecommendationItem(
                            content = movie,
                            score = calculateContentScore(movie.voteAverage, movie.popularity),
                            reason = "Based on your genre preferences",
                            type = RecommendationType.GENRE_BASED
                        )
                    } ?: emptyList()
                )
            }
            
            if (tvShowsByGenre is Resource.Success) {
                recommendations.addAll(
                    tvShowsByGenre.data?.tv_shows?.map { tvShow ->
                        RecommendationItem(
                            content = tvShow,
                            score = calculateContentScore(tvShow.voteAverage, tvShow.popularity),
                            reason = "Based on your genre preferences",
                            type = RecommendationType.GENRE_BASED
                        )
                    } ?: emptyList()
                )
            }
            
            val sortedRecommendations = recommendations
                .filter { it.score >= MIN_RATING_THRESHOLD }
                .sortedByDescending { it.score }
                .take(limit)
            
            emit(Resource.Success(sortedRecommendations))
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Failed to get genre-based recommendations"))
        }
    }

    suspend fun getContinueWatchingRecommendations(): Flow<Resource<List<RecommendationItem>>> = flow {
        emit(Resource.Loading())
        
        try {
            val continueWatching = repository.getContinueWatching().first()
            
            if (continueWatching is Resource.Success) {
                val recommendations = continueWatching.data?.map { item ->
                    RecommendationItem(
                        content = item,
                        score = calculateContinueWatchingScore(item.progress.toDouble()),
                        reason = "Continue watching",
                        type = RecommendationType.CONTINUE_WATCHING
                    )
                } ?: emptyList()
                
                emit(Resource.Success(recommendations.sortedByDescending { it.score }))
            } else {
                emit(Resource.Error("Failed to get continue watching data"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Failed to get continue watching recommendations"))
        }
    }

    // ==================== Private Methods ====================

    private suspend fun buildUserProfile(): UserProfile {
        val watchHistory = getWatchHistory()
        val favoriteGenres = extractFavoriteGenres(watchHistory)
        val averageRating = calculateAverageRating(watchHistory)
        val preferredContentTypes = extractPreferredContentTypes(watchHistory)
        
        return UserProfile(
            favoriteGenres = favoriteGenres,
            averageRating = averageRating,
            preferredContentTypes = preferredContentTypes,
            watchHistory = watchHistory
        )
    }

    private suspend fun getWatchHistory(): List<WatchHistoryItem> {
        return try {
            val history = repository.getWatchHistory().first()
            if (history is Resource.Success) {
                history.data ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }

    private fun extractFavoriteGenres(watchHistory: List<WatchHistoryItem>): List<Int> {
        // Extract genres from watch history and rank by frequency
        val genreFrequency = mutableMapOf<Int, Int>()

        watchHistory.forEach { item ->
            item.genreIds.forEach { genreId ->
                genreFrequency[genreId] = genreFrequency.getOrDefault(genreId, 0) + 1
            }
        }

        return genreFrequency.entries
            .sortedByDescending { it.value }
            .take(5)
            .map { it.key }
    }

    private fun calculateAverageRating(watchHistory: List<WatchHistoryItem>): Double {
        if (watchHistory.isEmpty()) return 7.0 // Default preference

        return watchHistory.mapNotNull { it.rating?.toDouble() }.takeIf { it.isNotEmpty() }?.average() ?: 7.0
    }

    private fun extractPreferredContentTypes(watchHistory: List<WatchHistoryItem>): List<String> {
        val typeFrequency = watchHistory.groupingBy { it.contentType }.eachCount()

        return typeFrequency.entries
            .sortedByDescending { it.value }
            .map { it.key }
    }

    private suspend fun getAllContent(): List<Any> {
        val allContent = mutableListOf<Any>()
        
        try {
            // Get popular movies
            val popularMovies = repository.getPopularMovies(50).first()
            if (popularMovies is Resource.Success) {
                allContent.addAll(popularMovies.data ?: emptyList())
            }
            
            // Get popular TV shows
            val popularTvShows = repository.getPopularTvShows(50).first()
            if (popularTvShows is Resource.Success) {
                allContent.addAll(popularTvShows.data ?: emptyList())
            }
            
            // Get latest content
            val latestMovies = repository.getLatestMovies(30).first()
            if (latestMovies is Resource.Success) {
                allContent.addAll(latestMovies.data ?: emptyList())
            }
            
        } catch (e: Exception) {
            // Handle error silently
        }
        
        return allContent
    }

    private fun generateRecommendations(
        userProfile: UserProfile,
        allContent: List<Any>,
        limit: Int
    ): List<RecommendationItem> {
        val recommendations = mutableListOf<RecommendationItem>()
        
        allContent.forEach { content ->
            val score = calculatePersonalizedScore(content, userProfile)
            val reason = generateRecommendationReason(content, userProfile)
            
            if (score >= MIN_RATING_THRESHOLD) {
                recommendations.add(
                    RecommendationItem(
                        content = content,
                        score = score,
                        reason = reason,
                        type = RecommendationType.PERSONALIZED
                    )
                )
            }
        }
        
        return recommendations
            .sortedByDescending { it.score }
            .take(limit)
    }

    private fun calculatePersonalizedScore(content: Any, userProfile: UserProfile): Double {
        var score = 0.0
        
        when (content) {
            is Movie -> {
                // Rating component
                score += content.voteAverage * RATING_WEIGHT
                
                // Popularity component
                score += normalizePopularity(content.popularity) * POPULARITY_WEIGHT
                
                // Genre matching
                val genreMatch = calculateGenreMatch(content.genres, userProfile.favoriteGenres)
                score += genreMatch * GENRE_WEIGHT
                
                // Recency bonus
                val recencyBonus = calculateRecencyBonus(content.releaseDate)
                score += recencyBonus * RECENCY_WEIGHT
            }
            
            is TvShow -> {
                // Similar calculation for TV shows
                score += content.voteAverage * RATING_WEIGHT
                score += normalizePopularity(content.popularity) * POPULARITY_WEIGHT
                
                val genreMatch = calculateGenreMatch(content.genres, userProfile.favoriteGenres)
                score += genreMatch * GENRE_WEIGHT
                
                val recencyBonus = calculateRecencyBonus(content.firstAirDate)
                score += recencyBonus * RECENCY_WEIGHT
            }
        }
        
        return score
    }

    private fun calculateGenreMatch(contentGenres: List<Genre>?, favoriteGenres: List<Int>): Double {
        if (contentGenres.isNullOrEmpty() || favoriteGenres.isEmpty()) return 0.0
        
        val matchingGenres = contentGenres.count { it.id in favoriteGenres }
        return matchingGenres.toDouble() / favoriteGenres.size * 10.0
    }

    private fun normalizePopularity(popularity: Double): Double {
        // Normalize popularity to 0-10 scale
        return min(10.0, popularity / 100.0)
    }

    private fun calculateRecencyBonus(dateString: String?): Double {
        if (dateString.isNullOrEmpty()) return 0.0
        
        try {
            val year = dateString.take(4).toInt()
            val currentYear = java.util.Calendar.getInstance().get(java.util.Calendar.YEAR)
            val yearsDiff = currentYear - year
            
            return when {
                yearsDiff <= 1 -> 3.0  // Very recent
                yearsDiff <= 3 -> 2.0  // Recent
                yearsDiff <= 5 -> 1.0  // Somewhat recent
                else -> 0.0            // Older content
            }
        } catch (e: Exception) {
            return 0.0
        }
    }

    private fun generateRecommendationReason(content: Any, userProfile: UserProfile): String {
        val reasons = mutableListOf<String>()
        
        when (content) {
            is Movie -> {
                if (content.voteAverage >= 8.0) {
                    reasons.add("Highly rated")
                }
                
                content.genres?.forEach { genre ->
                    if (genre.id in userProfile.favoriteGenres) {
                        reasons.add("You like ${genre.name}")
                    }
                }
                
                if (content.popularity > 50) {
                    reasons.add("Popular now")
                }
            }
            
            is TvShow -> {
                if (content.voteAverage >= 8.0) {
                    reasons.add("Highly rated")
                }
                
                content.genres?.forEach { genre ->
                    if (genre.id in userProfile.favoriteGenres) {
                        reasons.add("You like ${genre.name}")
                    }
                }
                
                if (content.popularity > 50) {
                    reasons.add("Popular now")
                }
            }
        }
        
        return reasons.firstOrNull() ?: "Recommended for you"
    }

    private suspend fun findSimilarContent(baseContent: Any, limit: Int): List<RecommendationItem> {
        val allContent = getAllContent()
        val similarities = mutableListOf<RecommendationItem>()
        
        allContent.forEach { content ->
            if (content != baseContent) {
                val similarity = calculateContentSimilarity(baseContent, content)
                if (similarity > 0.3) { // Minimum similarity threshold
                    similarities.add(
                        RecommendationItem(
                            content = content,
                            score = similarity * 10, // Convert to 0-10 scale
                            reason = "Similar to what you're watching",
                            type = RecommendationType.SIMILAR
                        )
                    )
                }
            }
        }
        
        return similarities
            .sortedByDescending { it.score }
            .take(limit)
    }

    private fun calculateContentSimilarity(content1: Any, content2: Any): Double {
        var similarity = 0.0
        
        // Genre similarity
        val genres1 = when (content1) {
            is Movie -> content1.genres
            is TvShow -> content1.genres
            else -> null
        }
        
        val genres2 = when (content2) {
            is Movie -> content2.genres
            is TvShow -> content2.genres
            else -> null
        }
        
        if (genres1 != null && genres2 != null) {
            val commonGenres = genres1.intersect(genres2.toSet()).size
            val totalGenres = (genres1.size + genres2.size) / 2.0
            similarity += if (totalGenres > 0) commonGenres / totalGenres * 0.6 else 0.0
        }
        
        // Rating similarity
        val rating1 = when (content1) {
            is Movie -> content1.voteAverage
            is TvShow -> content1.voteAverage
            else -> 0.0
        }
        
        val rating2 = when (content2) {
            is Movie -> content2.voteAverage
            is TvShow -> content2.voteAverage
            else -> 0.0
        }
        
        val ratingDiff = abs(rating1 - rating2)
        similarity += (1.0 - ratingDiff / 10.0) * 0.4
        
        return similarity
    }

    private suspend fun getMovieDetails(movieId: Int): Movie? {
        return try {
            val result = repository.getMovieDetails(movieId).first()
            if (result is Resource.Success) result.data else null
        } catch (e: Exception) {
            null
        }
    }

    private suspend fun getTvShowDetails(tvShowId: Int): TvShow? {
        return try {
            val result = repository.getTvShowDetails(tvShowId).first()
            if (result is Resource.Success) result.data else null
        } catch (e: Exception) {
            null
        }
    }

    private fun calculateTrendingScore(popularity: Double, rating: Double): Double {
        return (popularity * 0.6 + rating * 0.4)
    }

    private fun calculateContentScore(rating: Double, popularity: Double): Double {
        return (rating * 0.7 + normalizePopularity(popularity) * 0.3)
    }

    private fun calculateContinueWatchingScore(progress: Double): Double {
        // Higher score for content that's partially watched but not completed
        return when {
            progress in 10.0..90.0 -> 10.0 - (abs(progress - 50.0) / 50.0) * 3.0
            progress < 10.0 -> 7.0
            else -> 5.0
        }
    }
}

// ==================== Data Classes ====================

data class UserProfile(
    val favoriteGenres: List<Int>,
    val averageRating: Double,
    val preferredContentTypes: List<String>,
    val watchHistory: List<WatchHistoryItem>
)

data class RecommendationItem(
    val content: Any, // Movie, TvShow, or WatchHistoryItem
    val score: Double,
    val reason: String,
    val type: RecommendationType
)

enum class RecommendationType {
    PERSONALIZED,
    TRENDING,
    SIMILAR,
    GENRE_BASED,
    CONTINUE_WATCHING
}

enum class ContentType {
    MOVIE,
    TV_SHOW
}
