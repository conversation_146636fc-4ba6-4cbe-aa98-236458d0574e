<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/config/database.php')) {
    header('Location: install.php');
    exit('Please run the installer first.');
}

require_once 'includes/functions.php';

// Age verification check
if (!isset($_SESSION['age_verified']) || $_SESSION['age_verified'] !== true) {
    // Show age verification modal
    if (isset($_POST['verify_age'])) {
        if ($_POST['age_confirm'] === 'yes') {
            $_SESSION['age_verified'] = true;
        } else {
            header('Location: index.php');
            exit();
        }
    } else {
        // Show age verification form
        ?>
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Age Verification - <?php echo SITE_NAME; ?></title>
            <style>
                body {
                    font-family: 'Inter', sans-serif;
                    background: #0a0a0a;
                    color: #ffffff;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    min-height: 100vh;
                    margin: 0;
                }
                .verification-modal {
                    background: #1f1f1f;
                    padding: 3rem;
                    border-radius: 16px;
                    text-align: center;
                    max-width: 500px;
                    border: 1px solid rgba(255, 255, 255, 0.1);
                }
                .verification-modal h2 {
                    color: #e50914;
                    margin-bottom: 1rem;
                    font-size: 2rem;
                }
                .verification-modal p {
                    margin-bottom: 2rem;
                    color: #b3b3b3;
                    line-height: 1.6;
                }
                .btn {
                    padding: 1rem 2rem;
                    margin: 0.5rem;
                    border: none;
                    border-radius: 8px;
                    font-weight: 600;
                    cursor: pointer;
                    text-decoration: none;
                    display: inline-block;
                    transition: all 0.3s ease;
                }
                .btn-primary {
                    background: #e50914;
                    color: white;
                }
                .btn-secondary {
                    background: rgba(255, 255, 255, 0.1);
                    color: white;
                }
                .btn:hover {
                    transform: translateY(-2px);
                }
            </style>
        </head>
        <body>
            <div class="verification-modal">
                <h2>🔞 Age Verification Required</h2>
                <p>This section contains adult content. You must be 18 years or older to access this content.</p>
                <p>Are you 18 years of age or older?</p>
                <form method="POST">
                    <button type="submit" name="verify_age" value="yes" class="btn btn-primary">
                        Yes, I am 18 or older
                    </button>
                    <a href="index.php" class="btn btn-secondary">
                        No, take me back
                    </a>
                    <input type="hidden" name="age_confirm" value="yes">
                </form>
            </div>
        </body>
        </html>
        <?php
        exit();
    }
}

// Get parameters
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$sort = isset($_GET['sort']) ? sanitizeInput($_GET['sort']) : 'popularity';
$filter = isset($_GET['filter']) ? sanitizeInput($_GET['filter']) : '';
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = 24;
$offset = ($page - 1) * $limit;

// Initialize variables
$content = [];
$total_count = 0;

try {
    $db = new Database();
    $conn = $db->connect();

    // Create hentai genre if it doesn't exist
    $stmt = $conn->prepare("INSERT IGNORE INTO genres (name, tmdb_id) VALUES (?, ?)");
    $stmt->execute(['Hentai', 99999]);

    // Get hentai genre ID
    $stmt = $conn->prepare("SELECT id FROM genres WHERE name = 'Hentai'");
    $stmt->execute();
    $hentai_genre = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($hentai_genre) {
        // Build query conditions for hentai content
        $where_conditions = ["1=1"];
        $params = [];
        
        if ($search) {
            $where_conditions[] = "(title LIKE :search OR name LIKE :search OR overview LIKE :search)";
            $params[':search'] = "%{$search}%";
        }
        
        if ($filter === 'featured') {
            $where_conditions[] = "is_featured = 1";
        } elseif ($filter === 'trending') {
            $where_conditions[] = "is_trending = 1";
        }
        
        $where_clause = "WHERE " . implode(' AND ', $where_conditions);
        
        $order_clause = "ORDER BY ";
        switch ($sort) {
            case 'title':
                $order_clause .= "name ASC";
                break;
            case 'year':
                $order_clause .= "air_date DESC";
                break;
            case 'rating':
                $order_clause .= "vote_average DESC";
                break;
            default:
                $order_clause .= "popularity DESC";
        }

        // Simple approach - get hentai content directly
        $hentai_params = [$hentai_genre['id']];

        // Add search parameters
        if ($search) {
            $hentai_params[] = "%{$search}%";
            $hentai_params[] = "%{$search}%";
        }

        // Add filter parameters
        $filter_where = "";
        if ($filter === 'featured') {
            $filter_where = " AND is_featured = 1";
        } elseif ($filter === 'trending') {
            $filter_where = " AND is_trending = 1";
        }

        $search_where = "";
        if ($search) {
            $search_where = " AND (name LIKE ? OR overview LIKE ?)";
        }

        // Get hentai movies
        $movie_query = "
            SELECT m.id, m.tmdb_id, m.title as name, m.poster_path, m.backdrop_path,
                   m.release_date as air_date, m.vote_average, m.vote_count, m.popularity,
                   m.overview, m.is_featured, m.is_trending, 'movie' as content_type
            FROM movies m
            INNER JOIN movie_genres mg ON m.id = mg.movie_id
            WHERE mg.genre_id = ? {$search_where} {$filter_where}
            {$order_clause}
        ";

        $stmt = $conn->prepare($movie_query);
        $stmt->execute($hentai_params);
        $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Get hentai TV shows
        $tv_query = "
            SELECT t.id, t.tmdb_id, t.name, t.poster_path, t.backdrop_path,
                   t.first_air_date as air_date, t.vote_average, t.vote_count, t.popularity,
                   t.overview, t.is_featured, t.is_trending, 'tv_show' as content_type
            FROM tv_shows t
            INNER JOIN tv_show_genres tg ON t.id = tg.tv_show_id
            WHERE tg.genre_id = ? {$search_where} {$filter_where}
            {$order_clause}
        ";

        $stmt = $conn->prepare($tv_query);
        $stmt->execute($hentai_params);
        $tv_shows = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Combine results
        $content = array_merge($movies, $tv_shows);

        // Sort combined results
        if (!empty($content)) {
            usort($content, function($a, $b) use ($sort) {
                switch ($sort) {
                    case 'title':
                        return strcmp($a['name'], $b['name']);
                    case 'year':
                        return strtotime($b['air_date']) - strtotime($a['air_date']);
                    case 'rating':
                        return $b['vote_average'] - $a['vote_average'];
                    default:
                        return $b['popularity'] - $a['popularity'];
                }
            });
        }

        // Calculate total count
        $total_count = count($content);

        // Apply pagination
        $content = array_slice($content, $offset, $limit);
    }

} catch (Exception $e) {
    error_log("Hentai page error: " . $e->getMessage());
}

// Calculate pagination
$total_pages = ceil($total_count / $limit);
$page_title = 'Hentai';

if ($search) {
    $page_title .= " - Search: " . htmlspecialchars($search);
} elseif ($filter === 'featured') {
    $page_title = 'Featured Hentai';
} elseif ($filter === 'trending') {
    $page_title = 'Trending Hentai';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <meta name="description" content="Browse adult anime content on <?php echo SITE_NAME; ?>">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        /* Modern Hentai Page Design */
        :root {
            --primary-color: #e50914;
            --primary-dark: #b20710;
            --secondary-color: #1a1a1a;
            --dark-bg: #0a0a0a;
            --card-bg: #1f1f1f;
            --text-primary: #ffffff;
            --text-secondary: #b3b3b3;
            --text-muted: #737373;
            --border-color: #333333;
            --gradient-primary: linear-gradient(135deg, #e50914 0%, #b20710 100%);
            --gradient-dark: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            --shadow-light: 0 4px 20px rgba(229, 9, 20, 0.1);
            --shadow-medium: 0 8px 30px rgba(0, 0, 0, 0.3);
            --shadow-heavy: 0 20px 60px rgba(0, 0, 0, 0.5);
            --hentai-accent: #ff1744;
            --hentai-gradient: linear-gradient(135deg, #ff1744 0%, #d50000 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--dark-bg);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(10, 10, 10, 0.98);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 4%;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 800;
            color: var(--primary-color);
            text-decoration: none;
            letter-spacing: -0.5px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }

        .nav-menu a {
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
            position: relative;
        }

        .nav-menu a:hover,
        .nav-menu a.active {
            color: var(--hentai-accent);
        }

        .nav-menu a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--hentai-accent);
            transition: width 0.3s ease;
        }

        .nav-menu a:hover::after,
        .nav-menu a.active::after {
            width: 100%;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* Search Container */
        .search-container {
            position: relative;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .search-input {
            padding: 0.6rem 0.8rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            font-size: 0.85rem;
            width: 200px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--hentai-accent);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(255, 23, 68, 0.1);
        }

        .search-input::placeholder {
            color: var(--text-muted);
        }

        .search-btn {
            background: var(--hentai-gradient);
            border: none;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .search-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(255, 23, 68, 0.3);
        }

        .btn {
            padding: 0.7rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-light);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(229, 9, 20, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        /* Main Content */
        .main-content {
            margin-top: 80px;
            padding: 2rem 4%;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem 0;
        }

        .page-title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: var(--hentai-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .page-subtitle {
            font-size: 1.2rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        .age-warning {
            background: rgba(255, 23, 68, 0.1);
            border: 1px solid var(--hentai-accent);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 2rem;
            text-align: center;
            color: var(--hentai-accent);
            font-weight: 600;
        }

        /* Filters */
        .filters {
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
            align-items: center;
            justify-content: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: var(--card-bg);
            border-radius: 16px;
            border: 1px solid rgba(255, 23, 68, 0.2);
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 0.8rem;
        }

        .filter-group label {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.9rem;
        }

        .filter-select {
            padding: 0.7rem 1rem;
            border: 1px solid rgba(255, 23, 68, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            font-size: 0.9rem;
            min-width: 150px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--hentai-accent);
            box-shadow: 0 0 0 3px rgba(255, 23, 68, 0.1);
        }

        .filter-select option {
            background: var(--card-bg);
            color: var(--text-primary);
        }

        /* Content Grid */
        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .content-card {
            background: var(--card-bg);
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            cursor: pointer;
            position: relative;
            aspect-ratio: 2/3;
            box-shadow: var(--shadow-medium);
            border: 1px solid rgba(255, 23, 68, 0.1);
        }

        .content-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-heavy);
            border-color: var(--hentai-accent);
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, transparent 0%, rgba(255, 23, 68, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .content-card:hover::before {
            opacity: 1;
        }

        .content-card img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .content-card:hover img {
            transform: scale(1.05);
        }

        .card-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
            padding: 1.5rem;
            transform: translateY(100%);
            transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            z-index: 2;
        }

        .content-card:hover .card-overlay {
            transform: translateY(0);
        }

        .card-title {
            font-size: 1rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .card-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.85rem;
            color: var(--text-secondary);
        }

        .card-rating {
            display: flex;
            align-items: center;
            gap: 0.3rem;
            color: #ffd700;
            font-weight: 600;
        }

        .card-type {
            background: var(--hentai-gradient);
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .adult-badge {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: var(--hentai-gradient);
            color: white;
            padding: 0.3rem 0.6rem;
            border-radius: 6px;
            font-size: 0.7rem;
            font-weight: 700;
            z-index: 3;
        }
    </style>
</head>

<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <a href="index.php" class="logo"><?php echo SITE_NAME; ?></a>

            <ul class="nav-menu">
                <li><a href="index.php">Home</a></li>
                <li><a href="browse.php?type=movie">Movies</a></li>
                <li><a href="browse.php?type=tv">TV Shows</a></li>
                <li><a href="anime.php">🎌 Anime</a></li>
                <li><a href="hentai.php" class="active">🔞 Hentai</a></li>
                <li><a href="browse.php">Browse</a></li>
            </ul>

            <div class="user-menu">
                <!-- Search Box -->
                <div class="search-container">
                    <input type="text" id="searchInput" class="search-input"
                           placeholder="Search hentai..."
                           value="<?php echo htmlspecialchars($search); ?>">
                    <button class="search-btn" onclick="performSearch()">🔍</button>
                </div>

                <?php if (isLoggedIn()): ?>
                    <span style="color: var(--text-secondary);">Welcome, <?php echo $_SESSION['username']; ?></span>
                    <?php if (isAdmin()): ?>
                        <a href="admin/index.php" class="btn btn-secondary">Admin</a>
                    <?php endif; ?>
                    <a href="logout.php" class="btn btn-primary">Logout</a>
                <?php else: ?>
                    <a href="login.php" class="btn btn-secondary">Login</a>
                    <a href="register.php" class="btn btn-primary">Sign Up</a>
                <?php endif; ?>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">🔞 <?php echo $page_title; ?></h1>
            <p class="page-subtitle">
                <?php if ($search): ?>
                    Search results for "<?php echo htmlspecialchars($search); ?>"
                <?php elseif ($filter === 'featured'): ?>
                    Our handpicked selection of premium adult anime
                <?php elseif ($filter === 'trending'): ?>
                    The hottest adult anime everyone's watching
                <?php else: ?>
                    Premium adult anime content for mature audiences
                <?php endif; ?>
            </p>
            <div class="age-warning">
                ⚠️ This section contains adult content (18+). Viewer discretion is advised.
            </div>
        </div>

        <!-- Filters -->
        <div class="filters">
            <div class="filter-group">
                <label for="sortFilter">Sort by:</label>
                <select id="sortFilter" class="filter-select" onchange="updateFilters()">
                    <option value="popularity" <?php echo $sort === 'popularity' ? 'selected' : ''; ?>>Popularity</option>
                    <option value="title" <?php echo $sort === 'title' ? 'selected' : ''; ?>>Title</option>
                    <option value="year" <?php echo $sort === 'year' ? 'selected' : ''; ?>>Year</option>
                    <option value="rating" <?php echo $sort === 'rating' ? 'selected' : ''; ?>>Rating</option>
                </select>
            </div>

            <div class="filter-group">
                <label for="filterType">Filter:</label>
                <select id="filterType" class="filter-select" onchange="updateFilters()">
                    <option value="">All Content</option>
                    <option value="featured" <?php echo $filter === 'featured' ? 'selected' : ''; ?>>Featured</option>
                    <option value="trending" <?php echo $filter === 'trending' ? 'selected' : ''; ?>>Trending</option>
                </select>
            </div>

            <?php if ($search || $filter): ?>
                <div class="filter-group">
                    <a href="hentai.php" class="btn btn-secondary">Clear Filters</a>
                </div>
            <?php endif; ?>
        </div>

        <!-- Results Info -->
        <?php if (!empty($content)): ?>
            <div class="results-info">
                <div class="results-count">
                    Showing <strong><?php echo count($content); ?></strong> of <strong><?php echo number_format($total_count); ?></strong> titles
                </div>
                <div class="page-info">
                    Page <strong><?php echo $page; ?></strong> of <strong><?php echo $total_pages; ?></strong>
                </div>
            </div>
        <?php endif; ?>

        <!-- Content Grid -->
        <?php if (!empty($content)): ?>
            <div class="content-grid">
                <?php foreach ($content as $item): ?>
                    <a href="player.php?id=<?php echo $item['tmdb_id']; ?>&type=<?php echo $item['content_type']; ?>" class="content-card">
                        <div class="adult-badge">18+</div>
                        <img src="<?php echo getImageUrl($item['poster_path'], 'w500'); ?>"
                             alt="<?php echo htmlspecialchars($item['name']); ?>"
                             loading="lazy">
                        <div class="card-overlay">
                            <div class="card-title"><?php echo htmlspecialchars($item['name']); ?></div>
                            <div class="card-meta">
                                <span><?php echo date('Y', strtotime($item['air_date'])); ?></span>
                                <div class="card-rating">
                                    <span>⭐</span>
                                    <span><?php echo number_format($item['vote_average'], 1); ?></span>
                                </div>
                            </div>
                            <div class="card-type"><?php echo $item['content_type'] === 'movie' ? 'Movie' : 'Series'; ?></div>
                        </div>
                    </a>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <!-- No Results -->
            <div class="no-results">
                <h3>No Content Found</h3>
                <?php if ($search): ?>
                    <p>Sorry, we couldn't find any content matching "<?php echo htmlspecialchars($search); ?>"</p>
                    <p>Try adjusting your search terms or browse our collection.</p>
                <?php else: ?>
                    <p>No content available with the selected filters.</p>
                    <p>Try changing your filters or browse all content.</p>
                <?php endif; ?>
                <a href="hentai.php" class="btn btn-primary">Browse All Content</a>
            </div>
        <?php endif; ?>
    </main>

    <!-- JavaScript -->
    <script>
        function updateFilters() {
            const sort = document.getElementById('sortFilter').value;
            const filter = document.getElementById('filterType').value;

            let url = 'hentai.php?';
            const params = [];

            if (sort && sort !== 'popularity') params.push(`sort=${sort}`);
            if (filter) params.push(`filter=${filter}`);

            const search = document.getElementById('searchInput').value.trim();
            if (search) params.push(`search=${encodeURIComponent(search)}`);

            if (params.length > 0) {
                url += params.join('&');
            }

            window.location.href = url;
        }

        function performSearch() {
            const search = document.getElementById('searchInput').value.trim();
            if (search) {
                window.location.href = `hentai.php?search=${encodeURIComponent(search)}`;
            }
        }

        // Handle Enter key in search
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                performSearch();
            }
        });

        // Auto-submit search after typing stops
        let searchTimeout;
        document.getElementById('searchInput').addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length >= 3) {
                searchTimeout = setTimeout(() => {
                    performSearch();
                }, 1000);
            }
        });

        console.log('🔞 Hentai page loaded successfully!');
    </script>
</body>
</html>
