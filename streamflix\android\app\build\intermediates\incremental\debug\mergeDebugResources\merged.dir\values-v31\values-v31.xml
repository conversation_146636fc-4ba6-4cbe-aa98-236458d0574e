<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="m3_ref_palette_dynamic_neutral0">@android:color/system_neutral1_1000</color>
    <color name="m3_ref_palette_dynamic_neutral10">@android:color/system_neutral1_900</color>
    <color name="m3_ref_palette_dynamic_neutral100">@android:color/system_neutral1_0</color>
    <color name="m3_ref_palette_dynamic_neutral20">@android:color/system_neutral1_800</color>
    <color name="m3_ref_palette_dynamic_neutral30">@android:color/system_neutral1_700</color>
    <color name="m3_ref_palette_dynamic_neutral40">@android:color/system_neutral1_600</color>
    <color name="m3_ref_palette_dynamic_neutral50">@android:color/system_neutral1_500</color>
    <color name="m3_ref_palette_dynamic_neutral60">@android:color/system_neutral1_400</color>
    <color name="m3_ref_palette_dynamic_neutral70">@android:color/system_neutral1_300</color>
    <color name="m3_ref_palette_dynamic_neutral80">@android:color/system_neutral1_200</color>
    <color name="m3_ref_palette_dynamic_neutral90">@android:color/system_neutral1_100</color>
    <color name="m3_ref_palette_dynamic_neutral95">@android:color/system_neutral1_50</color>
    <color name="m3_ref_palette_dynamic_neutral99">@android:color/system_neutral1_10</color>
    <color name="m3_ref_palette_dynamic_neutral_variant0">@android:color/system_neutral2_1000</color>
    <color name="m3_ref_palette_dynamic_neutral_variant10">@android:color/system_neutral2_900</color>
    <color name="m3_ref_palette_dynamic_neutral_variant100">@android:color/system_neutral2_0</color>
    <color name="m3_ref_palette_dynamic_neutral_variant20">@android:color/system_neutral2_800</color>
    <color name="m3_ref_palette_dynamic_neutral_variant30">@android:color/system_neutral2_700</color>
    <color name="m3_ref_palette_dynamic_neutral_variant40">@android:color/system_neutral2_600</color>
    <color name="m3_ref_palette_dynamic_neutral_variant50">@android:color/system_neutral2_500</color>
    <color name="m3_ref_palette_dynamic_neutral_variant60">@android:color/system_neutral2_400</color>
    <color name="m3_ref_palette_dynamic_neutral_variant70">@android:color/system_neutral2_300</color>
    <color name="m3_ref_palette_dynamic_neutral_variant80">@android:color/system_neutral2_200</color>
    <color name="m3_ref_palette_dynamic_neutral_variant90">@android:color/system_neutral2_100</color>
    <color name="m3_ref_palette_dynamic_neutral_variant95">@android:color/system_neutral2_50</color>
    <color name="m3_ref_palette_dynamic_neutral_variant99">@android:color/system_neutral2_10</color>
    <color name="m3_ref_palette_dynamic_primary0">@android:color/system_accent1_1000</color>
    <color name="m3_ref_palette_dynamic_primary10">@android:color/system_accent1_900</color>
    <color name="m3_ref_palette_dynamic_primary100">@android:color/system_accent1_0</color>
    <color name="m3_ref_palette_dynamic_primary20">@android:color/system_accent1_800</color>
    <color name="m3_ref_palette_dynamic_primary30">@android:color/system_accent1_700</color>
    <color name="m3_ref_palette_dynamic_primary40">@android:color/system_accent1_600</color>
    <color name="m3_ref_palette_dynamic_primary50">@android:color/system_accent1_500</color>
    <color name="m3_ref_palette_dynamic_primary60">@android:color/system_accent1_400</color>
    <color name="m3_ref_palette_dynamic_primary70">@android:color/system_accent1_300</color>
    <color name="m3_ref_palette_dynamic_primary80">@android:color/system_accent1_200</color>
    <color name="m3_ref_palette_dynamic_primary90">@android:color/system_accent1_100</color>
    <color name="m3_ref_palette_dynamic_primary95">@android:color/system_accent1_50</color>
    <color name="m3_ref_palette_dynamic_primary99">@android:color/system_accent1_10</color>
    <color name="m3_ref_palette_dynamic_secondary0">@android:color/system_accent2_1000</color>
    <color name="m3_ref_palette_dynamic_secondary10">@android:color/system_accent2_900</color>
    <color name="m3_ref_palette_dynamic_secondary100">@android:color/system_accent2_0</color>
    <color name="m3_ref_palette_dynamic_secondary20">@android:color/system_accent2_800</color>
    <color name="m3_ref_palette_dynamic_secondary30">@android:color/system_accent2_700</color>
    <color name="m3_ref_palette_dynamic_secondary40">@android:color/system_accent2_600</color>
    <color name="m3_ref_palette_dynamic_secondary50">@android:color/system_accent2_500</color>
    <color name="m3_ref_palette_dynamic_secondary60">@android:color/system_accent2_400</color>
    <color name="m3_ref_palette_dynamic_secondary70">@android:color/system_accent2_300</color>
    <color name="m3_ref_palette_dynamic_secondary80">@android:color/system_accent2_200</color>
    <color name="m3_ref_palette_dynamic_secondary90">@android:color/system_accent2_100</color>
    <color name="m3_ref_palette_dynamic_secondary95">@android:color/system_accent2_50</color>
    <color name="m3_ref_palette_dynamic_secondary99">@android:color/system_accent2_10</color>
    <color name="m3_ref_palette_dynamic_tertiary0">@android:color/system_accent3_1000</color>
    <color name="m3_ref_palette_dynamic_tertiary10">@android:color/system_accent3_900</color>
    <color name="m3_ref_palette_dynamic_tertiary100">@android:color/system_accent3_0</color>
    <color name="m3_ref_palette_dynamic_tertiary20">@android:color/system_accent3_800</color>
    <color name="m3_ref_palette_dynamic_tertiary30">@android:color/system_accent3_700</color>
    <color name="m3_ref_palette_dynamic_tertiary40">@android:color/system_accent3_600</color>
    <color name="m3_ref_palette_dynamic_tertiary50">@android:color/system_accent3_500</color>
    <color name="m3_ref_palette_dynamic_tertiary60">@android:color/system_accent3_400</color>
    <color name="m3_ref_palette_dynamic_tertiary70">@android:color/system_accent3_300</color>
    <color name="m3_ref_palette_dynamic_tertiary80">@android:color/system_accent3_200</color>
    <color name="m3_ref_palette_dynamic_tertiary90">@android:color/system_accent3_100</color>
    <color name="m3_ref_palette_dynamic_tertiary95">@android:color/system_accent3_50</color>
    <color name="m3_ref_palette_dynamic_tertiary99">@android:color/system_accent3_10</color>
    <color name="m3_sys_color_dynamic_dark_background">@color/m3_ref_palette_dynamic_neutral_variant6</color>
    <color name="m3_sys_color_dynamic_dark_inverse_on_surface">@color/m3_ref_palette_dynamic_neutral20</color>
    <color name="m3_sys_color_dynamic_dark_inverse_primary">@color/m3_ref_palette_dynamic_primary40</color>
    <color name="m3_sys_color_dynamic_dark_inverse_surface">@color/m3_ref_palette_dynamic_neutral90</color>
    <color name="m3_sys_color_dynamic_dark_on_background">@color/m3_ref_palette_dynamic_neutral90</color>
    <color name="m3_sys_color_dynamic_dark_on_primary">@color/m3_ref_palette_dynamic_primary20</color>
    <color name="m3_sys_color_dynamic_dark_on_primary_container">@color/m3_ref_palette_dynamic_primary90</color>
    <color name="m3_sys_color_dynamic_dark_on_secondary">@color/m3_ref_palette_dynamic_secondary20</color>
    <color name="m3_sys_color_dynamic_dark_on_secondary_container">@color/m3_ref_palette_dynamic_secondary90</color>
    <color name="m3_sys_color_dynamic_dark_on_surface">@color/m3_ref_palette_dynamic_neutral90</color>
    <color name="m3_sys_color_dynamic_dark_on_surface_variant">@color/m3_ref_palette_dynamic_neutral_variant80</color>
    <color name="m3_sys_color_dynamic_dark_on_tertiary">@color/m3_ref_palette_dynamic_tertiary20</color>
    <color name="m3_sys_color_dynamic_dark_on_tertiary_container">@color/m3_ref_palette_dynamic_tertiary90</color>
    <color name="m3_sys_color_dynamic_dark_outline">@color/m3_ref_palette_dynamic_neutral_variant60</color>
    <color name="m3_sys_color_dynamic_dark_outline_variant">@color/m3_ref_palette_dynamic_neutral_variant30</color>
    <color name="m3_sys_color_dynamic_dark_primary">@color/m3_ref_palette_dynamic_primary80</color>
    <color name="m3_sys_color_dynamic_dark_primary_container">@color/m3_ref_palette_dynamic_primary30</color>
    <color name="m3_sys_color_dynamic_dark_secondary">@color/m3_ref_palette_dynamic_secondary80</color>
    <color name="m3_sys_color_dynamic_dark_secondary_container">@color/m3_ref_palette_dynamic_secondary30</color>
    <color name="m3_sys_color_dynamic_dark_surface">@color/m3_ref_palette_dynamic_neutral_variant6</color>
    <color name="m3_sys_color_dynamic_dark_surface_bright">@color/m3_ref_palette_dynamic_neutral_variant24</color>
    <color name="m3_sys_color_dynamic_dark_surface_container">@color/m3_ref_palette_dynamic_neutral_variant12</color>
    <color name="m3_sys_color_dynamic_dark_surface_container_high">@color/m3_ref_palette_dynamic_neutral_variant17</color>
    <color name="m3_sys_color_dynamic_dark_surface_container_highest">@color/m3_ref_palette_dynamic_neutral_variant22</color>
    <color name="m3_sys_color_dynamic_dark_surface_container_low">@color/m3_ref_palette_dynamic_neutral_variant10</color>
    <color name="m3_sys_color_dynamic_dark_surface_container_lowest">@color/m3_ref_palette_dynamic_neutral_variant4</color>
    <color name="m3_sys_color_dynamic_dark_surface_dim">@color/m3_ref_palette_dynamic_neutral_variant6</color>
    <color name="m3_sys_color_dynamic_dark_surface_variant">@color/m3_ref_palette_dynamic_neutral_variant30</color>
    <color name="m3_sys_color_dynamic_dark_tertiary">@color/m3_ref_palette_dynamic_tertiary80</color>
    <color name="m3_sys_color_dynamic_dark_tertiary_container">@color/m3_ref_palette_dynamic_tertiary30</color>
    <color name="m3_sys_color_dynamic_light_background">@color/m3_ref_palette_dynamic_neutral_variant98</color>
    <color name="m3_sys_color_dynamic_light_inverse_on_surface">@color/m3_ref_palette_dynamic_neutral95</color>
    <color name="m3_sys_color_dynamic_light_inverse_primary">@color/m3_ref_palette_dynamic_primary80</color>
    <color name="m3_sys_color_dynamic_light_inverse_surface">@color/m3_ref_palette_dynamic_neutral20</color>
    <color name="m3_sys_color_dynamic_light_on_background">@color/m3_ref_palette_dynamic_neutral10</color>
    <color name="m3_sys_color_dynamic_light_on_primary">@color/m3_ref_palette_dynamic_primary100</color>
    <color name="m3_sys_color_dynamic_light_on_primary_container">@color/m3_ref_palette_dynamic_primary10</color>
    <color name="m3_sys_color_dynamic_light_on_secondary">@color/m3_ref_palette_dynamic_secondary100</color>
    <color name="m3_sys_color_dynamic_light_on_secondary_container">@color/m3_ref_palette_dynamic_secondary10</color>
    <color name="m3_sys_color_dynamic_light_on_surface">@color/m3_ref_palette_dynamic_neutral10</color>
    <color name="m3_sys_color_dynamic_light_on_surface_variant">@color/m3_ref_palette_dynamic_neutral_variant30</color>
    <color name="m3_sys_color_dynamic_light_on_tertiary">@color/m3_ref_palette_dynamic_tertiary100</color>
    <color name="m3_sys_color_dynamic_light_on_tertiary_container">@color/m3_ref_palette_dynamic_tertiary10</color>
    <color name="m3_sys_color_dynamic_light_outline">@color/m3_ref_palette_dynamic_neutral_variant50</color>
    <color name="m3_sys_color_dynamic_light_outline_variant">@color/m3_ref_palette_dynamic_neutral_variant80</color>
    <color name="m3_sys_color_dynamic_light_primary">@color/m3_ref_palette_dynamic_primary40</color>
    <color name="m3_sys_color_dynamic_light_primary_container">@color/m3_ref_palette_dynamic_primary90</color>
    <color name="m3_sys_color_dynamic_light_secondary">@color/m3_ref_palette_dynamic_secondary40</color>
    <color name="m3_sys_color_dynamic_light_secondary_container">@color/m3_ref_palette_dynamic_secondary90</color>
    <color name="m3_sys_color_dynamic_light_surface">@color/m3_ref_palette_dynamic_neutral_variant98</color>
    <color name="m3_sys_color_dynamic_light_surface_bright">@color/m3_ref_palette_dynamic_neutral_variant98</color>
    <color name="m3_sys_color_dynamic_light_surface_container">@color/m3_ref_palette_dynamic_neutral_variant94</color>
    <color name="m3_sys_color_dynamic_light_surface_container_high">@color/m3_ref_palette_dynamic_neutral_variant92</color>
    <color name="m3_sys_color_dynamic_light_surface_container_highest">@color/m3_ref_palette_dynamic_neutral_variant90</color>
    <color name="m3_sys_color_dynamic_light_surface_container_low">@color/m3_ref_palette_dynamic_neutral_variant96</color>
    <color name="m3_sys_color_dynamic_light_surface_container_lowest">@color/m3_ref_palette_dynamic_neutral_variant100</color>
    <color name="m3_sys_color_dynamic_light_surface_dim">@color/m3_ref_palette_dynamic_neutral_variant87</color>
    <color name="m3_sys_color_dynamic_light_surface_variant">@color/m3_ref_palette_dynamic_neutral_variant90</color>
    <color name="m3_sys_color_dynamic_light_tertiary">@color/m3_ref_palette_dynamic_tertiary40</color>
    <color name="m3_sys_color_dynamic_light_tertiary_container">@color/m3_ref_palette_dynamic_tertiary90</color>
    <color name="m3_sys_color_dynamic_on_primary_fixed">@color/m3_ref_palette_dynamic_primary10</color>
    <color name="m3_sys_color_dynamic_on_primary_fixed_variant">@color/m3_ref_palette_dynamic_primary30</color>
    <color name="m3_sys_color_dynamic_on_secondary_fixed">@color/m3_ref_palette_dynamic_secondary10</color>
    <color name="m3_sys_color_dynamic_on_secondary_fixed_variant">@color/m3_ref_palette_dynamic_secondary30</color>
    <color name="m3_sys_color_dynamic_on_tertiary_fixed">@color/m3_ref_palette_dynamic_tertiary10</color>
    <color name="m3_sys_color_dynamic_on_tertiary_fixed_variant">@color/m3_ref_palette_dynamic_tertiary30</color>
    <color name="m3_sys_color_dynamic_primary_fixed">@color/m3_ref_palette_dynamic_primary90</color>
    <color name="m3_sys_color_dynamic_primary_fixed_dim">@color/m3_ref_palette_dynamic_primary80</color>
    <color name="m3_sys_color_dynamic_secondary_fixed">@color/m3_ref_palette_dynamic_secondary90</color>
    <color name="m3_sys_color_dynamic_secondary_fixed_dim">@color/m3_ref_palette_dynamic_secondary80</color>
    <color name="m3_sys_color_dynamic_tertiary_fixed">@color/m3_ref_palette_dynamic_tertiary90</color>
    <color name="m3_sys_color_dynamic_tertiary_fixed_dim">@color/m3_ref_palette_dynamic_tertiary80</color>
    <color name="material_dynamic_color_dark_error">@color/m3_sys_color_dark_error</color>
    <color name="material_dynamic_color_dark_error_container">@color/m3_sys_color_dark_error_container</color>
    <color name="material_dynamic_color_dark_on_error">@color/m3_sys_color_dark_on_error</color>
    <color name="material_dynamic_color_dark_on_error_container">@color/m3_sys_color_dark_on_error_container</color>
    <color name="material_dynamic_color_light_error">@color/m3_sys_color_light_error</color>
    <color name="material_dynamic_color_light_error_container">@color/m3_sys_color_light_error_container</color>
    <color name="material_dynamic_color_light_on_error">@color/m3_sys_color_light_on_error</color>
    <color name="material_dynamic_color_light_on_error_container">@color/m3_sys_color_light_on_error_container</color>
    <color name="material_dynamic_neutral0">@color/m3_ref_palette_dynamic_neutral0</color>
    <color name="material_dynamic_neutral10">@color/m3_ref_palette_dynamic_neutral10</color>
    <color name="material_dynamic_neutral100">@color/m3_ref_palette_dynamic_neutral100</color>
    <color name="material_dynamic_neutral20">@color/m3_ref_palette_dynamic_neutral20</color>
    <color name="material_dynamic_neutral30">@color/m3_ref_palette_dynamic_neutral30</color>
    <color name="material_dynamic_neutral40">@color/m3_ref_palette_dynamic_neutral40</color>
    <color name="material_dynamic_neutral50">@color/m3_ref_palette_dynamic_neutral50</color>
    <color name="material_dynamic_neutral60">@color/m3_ref_palette_dynamic_neutral60</color>
    <color name="material_dynamic_neutral70">@color/m3_ref_palette_dynamic_neutral70</color>
    <color name="material_dynamic_neutral80">@color/m3_ref_palette_dynamic_neutral80</color>
    <color name="material_dynamic_neutral90">@color/m3_ref_palette_dynamic_neutral90</color>
    <color name="material_dynamic_neutral95">@color/m3_ref_palette_dynamic_neutral95</color>
    <color name="material_dynamic_neutral99">@color/m3_ref_palette_dynamic_neutral99</color>
    <color name="material_dynamic_neutral_variant0">@color/m3_ref_palette_dynamic_neutral_variant0</color>
    <color name="material_dynamic_neutral_variant10">@color/m3_ref_palette_dynamic_neutral_variant10</color>
    <color name="material_dynamic_neutral_variant100">@color/m3_ref_palette_dynamic_neutral_variant100</color>
    <color name="material_dynamic_neutral_variant20">@color/m3_ref_palette_dynamic_neutral_variant20</color>
    <color name="material_dynamic_neutral_variant30">@color/m3_ref_palette_dynamic_neutral_variant30</color>
    <color name="material_dynamic_neutral_variant40">@color/m3_ref_palette_dynamic_neutral_variant40</color>
    <color name="material_dynamic_neutral_variant50">@color/m3_ref_palette_dynamic_neutral_variant50</color>
    <color name="material_dynamic_neutral_variant60">@color/m3_ref_palette_dynamic_neutral_variant60</color>
    <color name="material_dynamic_neutral_variant70">@color/m3_ref_palette_dynamic_neutral_variant70</color>
    <color name="material_dynamic_neutral_variant80">@color/m3_ref_palette_dynamic_neutral_variant80</color>
    <color name="material_dynamic_neutral_variant90">@color/m3_ref_palette_dynamic_neutral_variant90</color>
    <color name="material_dynamic_neutral_variant95">@color/m3_ref_palette_dynamic_neutral_variant95</color>
    <color name="material_dynamic_neutral_variant99">@color/m3_ref_palette_dynamic_neutral_variant99</color>
    <color name="material_dynamic_primary0">@color/m3_ref_palette_dynamic_primary0</color>
    <color name="material_dynamic_primary10">@color/m3_ref_palette_dynamic_primary10</color>
    <color name="material_dynamic_primary100">@color/m3_ref_palette_dynamic_primary100</color>
    <color name="material_dynamic_primary20">@color/m3_ref_palette_dynamic_primary20</color>
    <color name="material_dynamic_primary30">@color/m3_ref_palette_dynamic_primary30</color>
    <color name="material_dynamic_primary40">@color/m3_ref_palette_dynamic_primary40</color>
    <color name="material_dynamic_primary50">@color/m3_ref_palette_dynamic_primary50</color>
    <color name="material_dynamic_primary60">@color/m3_ref_palette_dynamic_primary60</color>
    <color name="material_dynamic_primary70">@color/m3_ref_palette_dynamic_primary70</color>
    <color name="material_dynamic_primary80">@color/m3_ref_palette_dynamic_primary80</color>
    <color name="material_dynamic_primary90">@color/m3_ref_palette_dynamic_primary90</color>
    <color name="material_dynamic_primary95">@color/m3_ref_palette_dynamic_primary95</color>
    <color name="material_dynamic_primary99">@color/m3_ref_palette_dynamic_primary99</color>
    <color name="material_dynamic_secondary0">@color/m3_ref_palette_dynamic_secondary0</color>
    <color name="material_dynamic_secondary10">@color/m3_ref_palette_dynamic_secondary10</color>
    <color name="material_dynamic_secondary100">@color/m3_ref_palette_dynamic_secondary100</color>
    <color name="material_dynamic_secondary20">@color/m3_ref_palette_dynamic_secondary20</color>
    <color name="material_dynamic_secondary30">@color/m3_ref_palette_dynamic_secondary30</color>
    <color name="material_dynamic_secondary40">@color/m3_ref_palette_dynamic_secondary40</color>
    <color name="material_dynamic_secondary50">@color/m3_ref_palette_dynamic_secondary50</color>
    <color name="material_dynamic_secondary60">@color/m3_ref_palette_dynamic_secondary60</color>
    <color name="material_dynamic_secondary70">@color/m3_ref_palette_dynamic_secondary70</color>
    <color name="material_dynamic_secondary80">@color/m3_ref_palette_dynamic_secondary80</color>
    <color name="material_dynamic_secondary90">@color/m3_ref_palette_dynamic_secondary90</color>
    <color name="material_dynamic_secondary95">@color/m3_ref_palette_dynamic_secondary95</color>
    <color name="material_dynamic_secondary99">@color/m3_ref_palette_dynamic_secondary99</color>
    <color name="material_dynamic_tertiary0">@color/m3_ref_palette_dynamic_tertiary0</color>
    <color name="material_dynamic_tertiary10">@color/m3_ref_palette_dynamic_tertiary10</color>
    <color name="material_dynamic_tertiary100">@color/m3_ref_palette_dynamic_tertiary100</color>
    <color name="material_dynamic_tertiary20">@color/m3_ref_palette_dynamic_tertiary20</color>
    <color name="material_dynamic_tertiary30">@color/m3_ref_palette_dynamic_tertiary30</color>
    <color name="material_dynamic_tertiary40">@color/m3_ref_palette_dynamic_tertiary40</color>
    <color name="material_dynamic_tertiary50">@color/m3_ref_palette_dynamic_tertiary50</color>
    <color name="material_dynamic_tertiary60">@color/m3_ref_palette_dynamic_tertiary60</color>
    <color name="material_dynamic_tertiary70">@color/m3_ref_palette_dynamic_tertiary70</color>
    <color name="material_dynamic_tertiary80">@color/m3_ref_palette_dynamic_tertiary80</color>
    <color name="material_dynamic_tertiary90">@color/m3_ref_palette_dynamic_tertiary90</color>
    <color name="material_dynamic_tertiary95">@color/m3_ref_palette_dynamic_tertiary95</color>
    <color name="material_dynamic_tertiary99">@color/m3_ref_palette_dynamic_tertiary99</color>
    <style name="Theme.Material3.DynamicColors.Dark" parent="Theme.Material3.Dark">
    <item name="isMaterial3DynamicColorApplied">true</item>

    
    <item name="colorPrimary">@color/m3_sys_color_dynamic_dark_primary</item>
    <item name="colorOnPrimary">@color/m3_sys_color_dynamic_dark_on_primary</item>
    <item name="colorPrimaryInverse">@color/m3_sys_color_dynamic_dark_inverse_primary</item>
    <item name="colorPrimaryContainer">@color/m3_sys_color_dynamic_dark_primary_container</item>
    <item name="colorOnPrimaryContainer">@color/m3_sys_color_dynamic_dark_on_primary_container</item>
    <item name="colorPrimaryFixed">@color/m3_sys_color_dynamic_primary_fixed</item>
    <item name="colorPrimaryFixedDim">@color/m3_sys_color_dynamic_primary_fixed_dim</item>
    <item name="colorOnPrimaryFixed">@color/m3_sys_color_dynamic_on_primary_fixed</item>
    <item name="colorOnPrimaryFixedVariant">@color/m3_sys_color_dynamic_on_primary_fixed_variant</item>
    <item name="colorSecondary">@color/m3_sys_color_dynamic_dark_secondary</item>
    <item name="colorOnSecondary">@color/m3_sys_color_dynamic_dark_on_secondary</item>
    <item name="colorSecondaryContainer">@color/m3_sys_color_dynamic_dark_secondary_container</item>
    <item name="colorOnSecondaryContainer">@color/m3_sys_color_dynamic_dark_on_secondary_container</item>
    <item name="colorSecondaryFixed">@color/m3_sys_color_dynamic_secondary_fixed</item>
    <item name="colorSecondaryFixedDim">@color/m3_sys_color_dynamic_secondary_fixed_dim</item>
    <item name="colorOnSecondaryFixed">@color/m3_sys_color_dynamic_on_secondary_fixed</item>
    <item name="colorOnSecondaryFixedVariant">@color/m3_sys_color_dynamic_on_secondary_fixed_variant</item>
    <item name="colorTertiary">@color/m3_sys_color_dynamic_dark_tertiary</item>
    <item name="colorOnTertiary">@color/m3_sys_color_dynamic_dark_on_tertiary</item>
    <item name="colorTertiaryContainer">@color/m3_sys_color_dynamic_dark_tertiary_container</item>
    <item name="colorOnTertiaryContainer">@color/m3_sys_color_dynamic_dark_on_tertiary_container</item>
    <item name="colorTertiaryFixed">@color/m3_sys_color_dynamic_tertiary_fixed</item>
    <item name="colorTertiaryFixedDim">@color/m3_sys_color_dynamic_tertiary_fixed_dim</item>
    <item name="colorOnTertiaryFixed">@color/m3_sys_color_dynamic_on_tertiary_fixed</item>
    <item name="colorOnTertiaryFixedVariant">@color/m3_sys_color_dynamic_on_tertiary_fixed_variant</item>
    <item name="android:colorBackground">@color/m3_sys_color_dynamic_dark_background</item>
    <item name="colorOnBackground">@color/m3_sys_color_dynamic_dark_on_background</item>
    <item name="colorSurface">@color/m3_sys_color_dynamic_dark_surface</item>
    <item name="colorOnSurface">@color/m3_sys_color_dynamic_dark_on_surface</item>
    <item name="colorSurfaceVariant">@color/m3_sys_color_dynamic_dark_surface_variant</item>
    <item name="colorOnSurfaceVariant">@color/m3_sys_color_dynamic_dark_on_surface_variant</item>
    <item name="colorSurfaceInverse">@color/m3_sys_color_dynamic_dark_inverse_surface</item>
    <item name="colorOnSurfaceInverse">@color/m3_sys_color_dynamic_dark_inverse_on_surface</item>
    <item name="colorSurfaceBright">@color/m3_sys_color_dynamic_dark_surface_bright</item>
    <item name="colorSurfaceDim">@color/m3_sys_color_dynamic_dark_surface_dim</item>
    <item name="colorSurfaceContainer">@color/m3_sys_color_dynamic_dark_surface_container</item>
    <item name="colorSurfaceContainerLow">@color/m3_sys_color_dynamic_dark_surface_container_low</item>
    <item name="colorSurfaceContainerHigh">@color/m3_sys_color_dynamic_dark_surface_container_high</item>
    <item name="colorSurfaceContainerLowest">@color/m3_sys_color_dynamic_dark_surface_container_lowest</item>
    <item name="colorSurfaceContainerHighest">@color/m3_sys_color_dynamic_dark_surface_container_highest</item>
    <item name="colorOutline">@color/m3_sys_color_dynamic_dark_outline</item>
    <item name="colorError">@color/m3_sys_color_dark_error</item>
    <item name="colorOnError">@color/m3_sys_color_dark_on_error</item>
    <item name="colorErrorContainer">@color/m3_sys_color_dark_error_container</item>
    <item name="colorOnErrorContainer">@color/m3_sys_color_dark_on_error_container</item>

    
    <item name="android:textColorPrimary">@color/m3_dynamic_dark_default_color_primary_text</item>
    <item name="android:textColorPrimaryInverse">@color/m3_dynamic_default_color_primary_text</item>
    <item name="android:textColorSecondary">@color/m3_dynamic_dark_default_color_secondary_text</item>
    <item name="android:textColorSecondaryInverse">@color/m3_dynamic_default_color_secondary_text</item>
    <item name="android:textColorTertiary">@color/m3_dynamic_dark_default_color_secondary_text</item>
    <item name="android:textColorTertiaryInverse">@color/m3_dynamic_default_color_secondary_text</item>
    <item name="android:textColorPrimaryDisableOnly">@color/m3_dynamic_dark_primary_text_disable_only</item>
    <item name="android:textColorPrimaryInverseDisableOnly">@color/m3_dynamic_primary_text_disable_only</item>
    <item name="android:textColorHint">@color/m3_dynamic_dark_hint_foreground</item>
    <item name="android:textColorHintInverse">@color/m3_dynamic_hint_foreground</item>
    <item name="android:textColorHighlight">@color/m3_dynamic_dark_highlighted_text</item>
    <item name="android:textColorHighlightInverse">@color/m3_dynamic_highlighted_text</item>
    <item name="android:textColorAlertDialogListItem">@color/m3_dynamic_dark_default_color_primary_text</item>
  </style>
    <style name="Theme.Material3.DynamicColors.Light" parent="Theme.Material3.Light">
    <item name="isMaterial3DynamicColorApplied">true</item>

    
    <item name="colorPrimary">@color/m3_sys_color_dynamic_light_primary</item>
    <item name="colorOnPrimary">@color/m3_sys_color_dynamic_light_on_primary</item>
    <item name="colorPrimaryInverse">@color/m3_sys_color_dynamic_light_inverse_primary</item>
    <item name="colorPrimaryContainer">@color/m3_sys_color_dynamic_light_primary_container</item>
    <item name="colorOnPrimaryContainer">@color/m3_sys_color_dynamic_light_on_primary_container</item>
    <item name="colorPrimaryFixed">@color/m3_sys_color_dynamic_primary_fixed</item>
    <item name="colorPrimaryFixedDim">@color/m3_sys_color_dynamic_primary_fixed_dim</item>
    <item name="colorOnPrimaryFixed">@color/m3_sys_color_dynamic_on_primary_fixed</item>
    <item name="colorOnPrimaryFixedVariant">@color/m3_sys_color_dynamic_on_primary_fixed_variant</item>
    <item name="colorSecondary">@color/m3_sys_color_dynamic_light_secondary</item>
    <item name="colorOnSecondary">@color/m3_sys_color_dynamic_light_on_secondary</item>
    <item name="colorSecondaryContainer">@color/m3_sys_color_dynamic_light_secondary_container</item>
    <item name="colorOnSecondaryContainer">@color/m3_sys_color_dynamic_light_on_secondary_container</item>
    <item name="colorSecondaryFixed">@color/m3_sys_color_dynamic_secondary_fixed</item>
    <item name="colorSecondaryFixedDim">@color/m3_sys_color_dynamic_secondary_fixed_dim</item>
    <item name="colorOnSecondaryFixed">@color/m3_sys_color_dynamic_on_secondary_fixed</item>
    <item name="colorOnSecondaryFixedVariant">@color/m3_sys_color_dynamic_on_secondary_fixed_variant</item>
    <item name="colorTertiary">@color/m3_sys_color_dynamic_light_tertiary</item>
    <item name="colorOnTertiary">@color/m3_sys_color_dynamic_light_on_tertiary</item>
    <item name="colorTertiaryContainer">@color/m3_sys_color_dynamic_light_tertiary_container</item>
    <item name="colorOnTertiaryContainer">@color/m3_sys_color_dynamic_light_on_tertiary_container</item>
    <item name="colorTertiaryFixed">@color/m3_sys_color_dynamic_tertiary_fixed</item>
    <item name="colorTertiaryFixedDim">@color/m3_sys_color_dynamic_tertiary_fixed_dim</item>
    <item name="colorOnTertiaryFixed">@color/m3_sys_color_dynamic_on_tertiary_fixed</item>
    <item name="colorOnTertiaryFixedVariant">@color/m3_sys_color_dynamic_on_tertiary_fixed_variant</item>
    <item name="android:colorBackground">@color/m3_sys_color_dynamic_light_background</item>
    <item name="colorOnBackground">@color/m3_sys_color_dynamic_light_on_background</item>
    <item name="colorSurface">@color/m3_sys_color_dynamic_light_surface</item>
    <item name="colorOnSurface">@color/m3_sys_color_dynamic_light_on_surface</item>
    <item name="colorSurfaceVariant">@color/m3_sys_color_dynamic_light_surface_variant</item>
    <item name="colorOnSurfaceVariant">@color/m3_sys_color_dynamic_light_on_surface_variant</item>
    <item name="colorSurfaceInverse">@color/m3_sys_color_dynamic_light_inverse_surface</item>
    <item name="colorOnSurfaceInverse">@color/m3_sys_color_dynamic_light_inverse_on_surface</item>
    <item name="colorSurfaceBright">@color/m3_sys_color_dynamic_light_surface_bright</item>
    <item name="colorSurfaceDim">@color/m3_sys_color_dynamic_light_surface_dim</item>
    <item name="colorSurfaceContainer">@color/m3_sys_color_dynamic_light_surface_container</item>
    <item name="colorSurfaceContainerLow">@color/m3_sys_color_dynamic_light_surface_container_low</item>
    <item name="colorSurfaceContainerHigh">@color/m3_sys_color_dynamic_light_surface_container_high</item>
    <item name="colorSurfaceContainerLowest">@color/m3_sys_color_dynamic_light_surface_container_lowest</item>
    <item name="colorSurfaceContainerHighest">@color/m3_sys_color_dynamic_light_surface_container_highest</item>
    <item name="colorOutline">@color/m3_sys_color_dynamic_light_outline</item>
    <item name="colorError">@color/m3_sys_color_light_error</item>
    <item name="colorOnError">@color/m3_sys_color_light_on_error</item>
    <item name="colorErrorContainer">@color/m3_sys_color_light_error_container</item>
    <item name="colorOnErrorContainer">@color/m3_sys_color_light_on_error_container</item>

    
    <item name="android:textColorPrimary">@color/m3_dynamic_default_color_primary_text</item>
    <item name="android:textColorPrimaryInverse">@color/m3_dynamic_dark_default_color_primary_text</item>
    <item name="android:textColorSecondary">@color/m3_dynamic_default_color_secondary_text</item>
    <item name="android:textColorSecondaryInverse">@color/m3_dynamic_dark_default_color_secondary_text</item>
    <item name="android:textColorTertiary">@color/m3_dynamic_default_color_secondary_text</item>
    <item name="android:textColorTertiaryInverse">@color/m3_dynamic_dark_default_color_secondary_text</item>
    <item name="android:textColorPrimaryDisableOnly">@color/m3_dynamic_primary_text_disable_only</item>
    <item name="android:textColorPrimaryInverseDisableOnly">@color/m3_dynamic_dark_primary_text_disable_only</item>
    <item name="android:textColorHint">@color/m3_dynamic_hint_foreground</item>
    <item name="android:textColorHintInverse">@color/m3_dynamic_dark_hint_foreground</item>
    <item name="android:textColorHighlight">@color/m3_dynamic_highlighted_text</item>
    <item name="android:textColorHighlightInverse">@color/m3_dynamic_dark_highlighted_text</item>
    <item name="android:textColorAlertDialogListItem">@color/m3_dynamic_default_color_primary_text</item>
  </style>
    <style name="Theme.SplashScreen" parent="Base.Theme.SplashScreen.DayNight">
        <item name="android:windowSplashScreenAnimatedIcon">?windowSplashScreenAnimatedIcon</item>
        <item name="android:windowSplashScreenBackground">?windowSplashScreenBackground</item>
        <item name="android:windowSplashScreenAnimationDuration">
            ?windowSplashScreenAnimationDuration
        </item>
    </style>
    <style name="Theme.SplashScreen.IconBackground" parent="Theme.SplashScreen">
        <item name="android:windowSplashScreenIconBackgroundColor">
            ?windowSplashScreenIconBackgroundColor
        </item>
    </style>
    <style name="ThemeOverlay.Material3.DynamicColors.Dark" parent="">
    <item name="isMaterial3DynamicColorApplied">true</item>

    
    <item name="colorPrimary">@color/m3_sys_color_dynamic_dark_primary</item>
    <item name="colorOnPrimary">@color/m3_sys_color_dynamic_dark_on_primary</item>
    <item name="colorPrimaryInverse">@color/m3_sys_color_dynamic_dark_inverse_primary</item>
    <item name="colorPrimaryContainer">@color/m3_sys_color_dynamic_dark_primary_container</item>
    <item name="colorOnPrimaryContainer">@color/m3_sys_color_dynamic_dark_on_primary_container</item>
    <item name="colorPrimaryFixed">@color/m3_sys_color_dynamic_primary_fixed</item>
    <item name="colorPrimaryFixedDim">@color/m3_sys_color_dynamic_primary_fixed_dim</item>
    <item name="colorOnPrimaryFixed">@color/m3_sys_color_dynamic_on_primary_fixed</item>
    <item name="colorOnPrimaryFixedVariant">@color/m3_sys_color_dynamic_on_primary_fixed_variant</item>
    <item name="colorSecondary">@color/m3_sys_color_dynamic_dark_secondary</item>
    <item name="colorOnSecondary">@color/m3_sys_color_dynamic_dark_on_secondary</item>
    <item name="colorSecondaryContainer">@color/m3_sys_color_dynamic_dark_secondary_container</item>
    <item name="colorOnSecondaryContainer">@color/m3_sys_color_dynamic_dark_on_secondary_container</item>
    <item name="colorSecondaryFixed">@color/m3_sys_color_dynamic_secondary_fixed</item>
    <item name="colorSecondaryFixedDim">@color/m3_sys_color_dynamic_secondary_fixed_dim</item>
    <item name="colorOnSecondaryFixed">@color/m3_sys_color_dynamic_on_secondary_fixed</item>
    <item name="colorOnSecondaryFixedVariant">@color/m3_sys_color_dynamic_on_secondary_fixed_variant</item>
    <item name="colorTertiary">@color/m3_sys_color_dynamic_dark_tertiary</item>
    <item name="colorOnTertiary">@color/m3_sys_color_dynamic_dark_on_tertiary</item>
    <item name="colorTertiaryContainer">@color/m3_sys_color_dynamic_dark_tertiary_container</item>
    <item name="colorOnTertiaryContainer">@color/m3_sys_color_dynamic_dark_on_tertiary_container</item>
    <item name="colorTertiaryFixed">@color/m3_sys_color_dynamic_tertiary_fixed</item>
    <item name="colorTertiaryFixedDim">@color/m3_sys_color_dynamic_tertiary_fixed_dim</item>
    <item name="colorOnTertiaryFixed">@color/m3_sys_color_dynamic_on_tertiary_fixed</item>
    <item name="colorOnTertiaryFixedVariant">@color/m3_sys_color_dynamic_on_tertiary_fixed_variant</item>
    <item name="android:colorBackground">@color/m3_sys_color_dynamic_dark_background</item>
    <item name="colorOnBackground">@color/m3_sys_color_dynamic_dark_on_background</item>
    <item name="colorSurface">@color/m3_sys_color_dynamic_dark_surface</item>
    <item name="colorOnSurface">@color/m3_sys_color_dynamic_dark_on_surface</item>
    <item name="colorSurfaceVariant">@color/m3_sys_color_dynamic_dark_surface_variant</item>
    <item name="colorOnSurfaceVariant">@color/m3_sys_color_dynamic_dark_on_surface_variant</item>
    <item name="colorSurfaceInverse">@color/m3_sys_color_dynamic_dark_inverse_surface</item>
    <item name="colorOnSurfaceInverse">@color/m3_sys_color_dynamic_dark_inverse_on_surface</item>
    <item name="colorSurfaceBright">@color/m3_sys_color_dynamic_dark_surface_bright</item>
    <item name="colorSurfaceDim">@color/m3_sys_color_dynamic_dark_surface_dim</item>
    <item name="colorSurfaceContainer">@color/m3_sys_color_dynamic_dark_surface_container</item>
    <item name="colorSurfaceContainerLow">@color/m3_sys_color_dynamic_dark_surface_container_low</item>
    <item name="colorSurfaceContainerHigh">@color/m3_sys_color_dynamic_dark_surface_container_high</item>
    <item name="colorSurfaceContainerLowest">@color/m3_sys_color_dynamic_dark_surface_container_lowest</item>
    <item name="colorSurfaceContainerHighest">@color/m3_sys_color_dynamic_dark_surface_container_highest</item>
    <item name="colorOutline">@color/m3_sys_color_dynamic_dark_outline</item>
    <item name="colorOutlineVariant">@color/m3_sys_color_dynamic_dark_outline_variant</item>
    <item name="colorError">@color/material_dynamic_color_dark_error</item>
    <item name="colorOnError">@color/material_dynamic_color_dark_on_error</item>
    <item name="colorErrorContainer">@color/material_dynamic_color_dark_error_container</item>
    <item name="colorOnErrorContainer">@color/material_dynamic_color_dark_on_error_container</item>

    
    <item name="android:textColorPrimary">@color/m3_dynamic_dark_default_color_primary_text</item>
    <item name="android:textColorPrimaryInverse">@color/m3_dynamic_default_color_primary_text</item>
    <item name="android:textColorSecondary">@color/m3_dynamic_dark_default_color_secondary_text</item>
    <item name="android:textColorSecondaryInverse">@color/m3_dynamic_default_color_secondary_text</item>
    <item name="android:textColorTertiary">@color/m3_dynamic_dark_default_color_secondary_text</item>
    <item name="android:textColorTertiaryInverse">@color/m3_dynamic_default_color_secondary_text</item>
    <item name="android:textColorPrimaryDisableOnly">@color/m3_dynamic_dark_primary_text_disable_only</item>
    <item name="android:textColorPrimaryInverseDisableOnly">@color/m3_dynamic_primary_text_disable_only</item>
    <item name="android:textColorHint">@color/m3_dynamic_dark_hint_foreground</item>
    <item name="android:textColorHintInverse">@color/m3_dynamic_hint_foreground</item>
    <item name="android:textColorHighlight">@color/m3_dynamic_dark_highlighted_text</item>
    <item name="android:textColorHighlightInverse">@color/m3_dynamic_highlighted_text</item>
    <item name="android:textColorAlertDialogListItem">@color/m3_dynamic_dark_default_color_primary_text</item>
  </style>
    <style name="ThemeOverlay.Material3.DynamicColors.Light" parent="">
    <item name="isMaterial3DynamicColorApplied">true</item>

    
    <item name="colorPrimary">@color/m3_sys_color_dynamic_light_primary</item>
    <item name="colorOnPrimary">@color/m3_sys_color_dynamic_light_on_primary</item>
    <item name="colorPrimaryInverse">@color/m3_sys_color_dynamic_light_inverse_primary</item>
    <item name="colorPrimaryContainer">@color/m3_sys_color_dynamic_light_primary_container</item>
    <item name="colorOnPrimaryContainer">@color/m3_sys_color_dynamic_light_on_primary_container</item>
    <item name="colorPrimaryFixed">@color/m3_sys_color_dynamic_primary_fixed</item>
    <item name="colorPrimaryFixedDim">@color/m3_sys_color_dynamic_primary_fixed_dim</item>
    <item name="colorOnPrimaryFixed">@color/m3_sys_color_dynamic_on_primary_fixed</item>
    <item name="colorOnPrimaryFixedVariant">@color/m3_sys_color_dynamic_on_primary_fixed_variant</item>
    <item name="colorSecondary">@color/m3_sys_color_dynamic_light_secondary</item>
    <item name="colorOnSecondary">@color/m3_sys_color_dynamic_light_on_secondary</item>
    <item name="colorSecondaryContainer">@color/m3_sys_color_dynamic_light_secondary_container</item>
    <item name="colorOnSecondaryContainer">@color/m3_sys_color_dynamic_light_on_secondary_container</item>
    <item name="colorSecondaryFixed">@color/m3_sys_color_dynamic_secondary_fixed</item>
    <item name="colorSecondaryFixedDim">@color/m3_sys_color_dynamic_secondary_fixed_dim</item>
    <item name="colorOnSecondaryFixed">@color/m3_sys_color_dynamic_on_secondary_fixed</item>
    <item name="colorOnSecondaryFixedVariant">@color/m3_sys_color_dynamic_on_secondary_fixed_variant</item>
    <item name="colorTertiary">@color/m3_sys_color_dynamic_light_tertiary</item>
    <item name="colorOnTertiary">@color/m3_sys_color_dynamic_light_on_tertiary</item>
    <item name="colorTertiaryContainer">@color/m3_sys_color_dynamic_light_tertiary_container</item>
    <item name="colorOnTertiaryContainer">@color/m3_sys_color_dynamic_light_on_tertiary_container</item>
    <item name="colorTertiaryFixed">@color/m3_sys_color_dynamic_tertiary_fixed</item>
    <item name="colorTertiaryFixedDim">@color/m3_sys_color_dynamic_tertiary_fixed_dim</item>
    <item name="colorOnTertiaryFixed">@color/m3_sys_color_dynamic_on_tertiary_fixed</item>
    <item name="colorOnTertiaryFixedVariant">@color/m3_sys_color_dynamic_on_tertiary_fixed_variant</item>
    <item name="android:colorBackground">@color/m3_sys_color_dynamic_light_background</item>
    <item name="colorOnBackground">@color/m3_sys_color_dynamic_light_on_background</item>
    <item name="colorSurface">@color/m3_sys_color_dynamic_light_surface</item>
    <item name="colorOnSurface">@color/m3_sys_color_dynamic_light_on_surface</item>
    <item name="colorSurfaceVariant">@color/m3_sys_color_dynamic_light_surface_variant</item>
    <item name="colorOnSurfaceVariant">@color/m3_sys_color_dynamic_light_on_surface_variant</item>
    <item name="colorSurfaceInverse">@color/m3_sys_color_dynamic_light_inverse_surface</item>
    <item name="colorOnSurfaceInverse">@color/m3_sys_color_dynamic_light_inverse_on_surface</item>
    <item name="colorSurfaceBright">@color/m3_sys_color_dynamic_light_surface_bright</item>
    <item name="colorSurfaceDim">@color/m3_sys_color_dynamic_light_surface_dim</item>
    <item name="colorSurfaceContainer">@color/m3_sys_color_dynamic_light_surface_container</item>
    <item name="colorSurfaceContainerLow">@color/m3_sys_color_dynamic_light_surface_container_low</item>
    <item name="colorSurfaceContainerHigh">@color/m3_sys_color_dynamic_light_surface_container_high</item>
    <item name="colorSurfaceContainerLowest">@color/m3_sys_color_dynamic_light_surface_container_lowest</item>
    <item name="colorSurfaceContainerHighest">@color/m3_sys_color_dynamic_light_surface_container_highest</item>
    <item name="colorOutline">@color/m3_sys_color_dynamic_light_outline</item>
    <item name="colorOutlineVariant">@color/m3_sys_color_dynamic_light_outline_variant</item>
    <item name="colorError">@color/material_dynamic_color_light_error</item>
    <item name="colorOnError">@color/material_dynamic_color_light_on_error</item>
    <item name="colorErrorContainer">@color/material_dynamic_color_light_error_container</item>
    <item name="colorOnErrorContainer">@color/material_dynamic_color_light_on_error_container</item>

    
    <item name="android:textColorPrimary">@color/m3_dynamic_default_color_primary_text</item>
    <item name="android:textColorPrimaryInverse">@color/m3_dynamic_dark_default_color_primary_text</item>
    <item name="android:textColorSecondary">@color/m3_dynamic_default_color_secondary_text</item>
    <item name="android:textColorSecondaryInverse">@color/m3_dynamic_dark_default_color_secondary_text</item>
    <item name="android:textColorTertiary">@color/m3_dynamic_default_color_secondary_text</item>
    <item name="android:textColorTertiaryInverse">@color/m3_dynamic_dark_default_color_secondary_text</item>
    <item name="android:textColorPrimaryDisableOnly">@color/m3_dynamic_primary_text_disable_only</item>
    <item name="android:textColorPrimaryInverseDisableOnly">@color/m3_dynamic_dark_primary_text_disable_only</item>
    <item name="android:textColorHint">@color/m3_dynamic_hint_foreground</item>
    <item name="android:textColorHintInverse">@color/m3_dynamic_dark_hint_foreground</item>
    <item name="android:textColorHighlight">@color/m3_dynamic_highlighted_text</item>
    <item name="android:textColorHighlightInverse">@color/m3_dynamic_dark_highlighted_text</item>
    <item name="android:textColorAlertDialogListItem">@color/m3_dynamic_default_color_primary_text</item>
  </style>
</resources>