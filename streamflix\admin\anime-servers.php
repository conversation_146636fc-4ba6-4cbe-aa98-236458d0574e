<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $db = new Database();
        $conn = $db->connect();
        
        if ($action === 'add_server') {
            $name = sanitizeInput($_POST['name'] ?? '');
            $url = sanitizeInput($_POST['url'] ?? '');
            
            if (!empty($name) && !empty($url)) {
                $stmt = $conn->prepare("INSERT INTO servers (name, url, type, is_active) VALUES (?, ?, 'anime', 1)");
                $stmt->execute([$name, $url]);
                $message = "Anime server '{$name}' added successfully!";
            } else {
                $error = 'Name and URL are required.';
            }
        }
        
        if ($action === 'toggle_server') {
            $server_id = (int)($_POST['server_id'] ?? 0);
            
            if ($server_id > 0) {
                $stmt = $conn->prepare("UPDATE servers SET is_active = NOT is_active WHERE id = ? AND type = 'anime'");
                $stmt->execute([$server_id]);
                $message = 'Server status updated successfully!';
            }
        }
        
        if ($action === 'delete_server') {
            $server_id = (int)($_POST['server_id'] ?? 0);
            
            if ($server_id > 0) {
                // Delete server associations first
                $stmt = $conn->prepare("DELETE FROM anime_servers WHERE server_id = ?");
                $stmt->execute([$server_id]);
                
                // Delete the server
                $stmt = $conn->prepare("DELETE FROM servers WHERE id = ? AND type = 'anime'");
                $stmt->execute([$server_id]);
                
                $message = 'Anime server deleted successfully!';
            }
        }
        
        if ($action === 'update_server') {
            $server_id = (int)($_POST['server_id'] ?? 0);
            $name = sanitizeInput($_POST['name'] ?? '');
            $url = sanitizeInput($_POST['url'] ?? '');
            
            if ($server_id > 0 && !empty($name) && !empty($url)) {
                $stmt = $conn->prepare("UPDATE servers SET name = ?, url = ? WHERE id = ? AND type = 'anime'");
                $stmt->execute([$name, $url, $server_id]);
                $message = 'Anime server updated successfully!';
            }
        }
        
    } catch (Exception $e) {
        $error = 'Error: ' . $e->getMessage();
    }
}

// Get anime servers
try {
    $db = new Database();
    $conn = $db->connect();
    
    $stmt = $conn->prepare("SELECT * FROM servers WHERE type = 'anime' ORDER BY name");
    $stmt->execute();
    $anime_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get anime content counts
    $stmt = $conn->prepare("
        SELECT COUNT(DISTINCT m.id) as anime_movies
        FROM movies m 
        INNER JOIN movie_genres mg ON m.id = mg.movie_id 
        INNER JOIN genres g ON mg.genre_id = g.id 
        WHERE g.name = 'Animation'
    ");
    $stmt->execute();
    $anime_movies_count = $stmt->fetchColumn();
    
    $stmt = $conn->prepare("
        SELECT COUNT(DISTINCT t.id) as anime_shows
        FROM tv_shows t 
        INNER JOIN tv_show_genres tg ON t.id = tg.tv_show_id 
        INNER JOIN genres g ON tg.genre_id = g.id 
        WHERE g.name = 'Animation'
    ");
    $stmt->execute();
    $anime_shows_count = $stmt->fetchColumn();
    
} catch (Exception $e) {
    $error = 'Database error: ' . $e->getMessage();
    $anime_servers = [];
    $anime_movies_count = 0;
    $anime_shows_count = 0;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anime Servers - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .server-form {
            background: var(--secondary-color);
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 30px;
            border: 2px solid #ff6b35;
        }
        
        .server-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .server-card {
            background: var(--secondary-color);
            border: 2px solid #ff6b35;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        .server-card.inactive {
            border-color: var(--border-color);
            opacity: 0.6;
        }
        
        .server-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(255, 107, 53, 0.3);
        }
        
        .server-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .server-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #ff6b35;
            margin: 0;
        }
        
        .server-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-active {
            background: #28a745;
            color: white;
        }
        
        .status-inactive {
            background: #dc3545;
            color: white;
        }
        
        .server-url {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 15px;
            word-break: break-all;
        }
        
        .server-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--dark-bg);
            color: var(--text-primary);
            font-size: 14px;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 1px solid #28a745;
        }
        
        .alert-error {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid #dc3545;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: var(--secondary-color);
            border: 2px solid #ff6b35;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #ff6b35;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }
        
        .anime-theme {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>

        <!-- Current Anime Servers -->
        <div>
            <h3>🖥️ Current Anime Servers</h3>

            <?php if (empty($anime_servers)): ?>
                <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
                    <h4>🎌 No Anime Servers Configured</h4>
                    <p>LetsEmbed Anime server should be automatically configured.</p>
                    <p><a href="setup-letsembed-anime-only.php" class="btn btn-primary">🔧 Setup LetsEmbed Anime</a></p>
                </div>
            <?php else: ?>
                <div class="server-grid">
                    <?php foreach ($anime_servers as $server): ?>
                        <div class="server-card <?php echo $server['is_active'] ? '' : 'inactive'; ?>">
                            <div class="server-header">
                                <h4 class="server-name"><?php echo htmlspecialchars($server['name']); ?></h4>
                                <span class="server-status <?php echo $server['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                    <?php echo $server['is_active'] ? 'Active' : 'Inactive'; ?>
                                </span>
                            </div>

                            <div class="server-url">
                                <strong>URL:</strong> <?php echo htmlspecialchars($server['url']); ?>
                            </div>

                            <?php if (strpos($server['url'], 'letsembed.cc') !== false): ?>
                                <div style="margin-bottom: 15px; padding: 10px; background: rgba(255, 107, 53, 0.1); border-radius: 6px;">
                                    <strong>🎌 LetsEmbed Anime Server</strong><br>
                                    <small>Official LetsEmbed anime streaming server</small><br>
                                    <small><strong>Format:</strong> ?id={tmdb_id}/{season}/{episode}</small>
                                </div>
                            <?php endif; ?>

                            <div style="margin-bottom: 15px; font-size: 0.9rem; color: var(--text-secondary);">
                                <strong>Added:</strong> <?php echo date('M j, Y', strtotime($server['created_at'])); ?>
                            </div>

                            <div class="server-actions">
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="toggle_server">
                                    <input type="hidden" name="server_id" value="<?php echo $server['id']; ?>">
                                    <button type="submit" class="btn btn-secondary">
                                        <?php echo $server['is_active'] ? 'Deactivate' : 'Activate'; ?>
                                    </button>
                                </form>

                                <button class="btn btn-primary" onclick="editServer(<?php echo $server['id']; ?>, '<?php echo htmlspecialchars($server['name']); ?>', '<?php echo htmlspecialchars($server['url']); ?>')">
                                    Edit
                                </button>

                                <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this anime server?')">
                                    <input type="hidden" name="action" value="delete_server">
                                    <input type="hidden" name="server_id" value="<?php echo $server['id']; ?>">
                                    <button type="submit" class="btn btn-danger">Delete</button>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <div style="margin-top: 40px; text-align: center;">
            <a href="index.php" class="btn btn-secondary">← Back to Dashboard</a>
            <a href="hentai-servers.php" class="btn btn-secondary">🔞 Hentai Servers</a>
            <a href="servers.php" class="btn btn-secondary">🖥️ Movie/TV Servers</a>
        </div>
    </div>

    <!-- Edit Server Modal -->
    <div id="editModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: var(--secondary-color); padding: 30px; border-radius: 8px; width: 90%; max-width: 500px;">
            <h3>Edit Anime Server</h3>
            <form method="POST" id="editForm">
                <input type="hidden" name="action" value="update_server">
                <input type="hidden" name="server_id" id="editServerId">

                <div class="form-group">
                    <label for="editName">Server Name</label>
                    <input type="text" id="editName" name="name" required>
                </div>

                <div class="form-group">
                    <label for="editUrl">Server URL</label>
                    <input type="url" id="editUrl" name="url" required>
                </div>

                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button type="button" class="btn btn-secondary" onclick="closeEditModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Server</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function editServer(id, name, url) {
            document.getElementById('editServerId').value = id;
            document.getElementById('editName').value = name;
            document.getElementById('editUrl').value = url;
            document.getElementById('editModal').style.display = 'block';
        }

        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        // Close modal when clicking outside
        document.getElementById('editModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeEditModal();
            }
        });
    </script>
</body>
</html>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1 class="anime-theme">🎌 LetsEmbed Anime Server Management</h1>
            <p>Manage LetsEmbed server for anime content streaming</p>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo count($anime_servers); ?></div>
                <div class="stat-label">Anime Servers</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $anime_movies_count; ?></div>
                <div class="stat-label">Anime Movies</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $anime_shows_count; ?></div>
                <div class="stat-label">Anime TV Shows</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $anime_movies_count + $anime_shows_count; ?></div>
                <div class="stat-label">Total Anime Content</div>
            </div>
        </div>

        <!-- LetsEmbed Information -->
        <div class="server-form">
            <h3>🎌 LetsEmbed Anime Server</h3>
            <div style="background: rgba(255, 107, 53, 0.1); padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <h4>📋 Server Information:</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Server Name:</strong> LetsEmbed Anime</li>
                    <li><strong>Base URL:</strong> https://letsembed.cc/embed/anime/</li>
                    <li><strong>TV Show Format:</strong> https://letsembed.cc/embed/anime/?id={tmdb_id}/{season}/{episode}</li>
                    <li><strong>Movie Format:</strong> https://letsembed.cc/embed/anime/?id={tmdb_id}</li>
                    <li><strong>Content Type:</strong> Anime Only</li>
                </ul>
            </div>

            <h4>➕ Add Additional Anime Server (Optional)</h4>
            <form method="POST">
                <input type="hidden" name="action" value="add_server">

                <div class="form-row">
                    <div class="form-group">
                        <label for="name">Server Name</label>
                        <input type="text" id="name" name="name"
                               placeholder="e.g., Backup Anime Server" required>
                    </div>

                    <div class="form-group">
                        <label for="url">Server URL</label>
                        <input type="url" id="url" name="url"
                               placeholder="https://example.com/embed/anime/" required>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">Add Backup Server</button>
            </form>
        </div>
