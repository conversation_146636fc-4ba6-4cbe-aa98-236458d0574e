<?php
require_once 'config/database.php';

echo "<h2>🧪 Player Fix Test - BDFLiX</h2>";
echo "<p>Testing if the player fix is working correctly.</p>";

try {
    $db = new Database();
    $conn = $db->connect();
    
    echo "<h3>1️⃣ Database Servers Check</h3>";
    
    $stmt = $conn->query("SELECT * FROM embed_servers WHERE is_active = 1 ORDER BY priority ASC");
    $db_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Active servers in database: " . count($db_servers) . "</strong></p>";
    
    if (!empty($db_servers)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>Name</th><th>Priority</th><th>Movie URL</th><th>TV URL</th></tr>";
        
        foreach ($db_servers as $server) {
            $movie_url = substr($server['movie_url'], 0, 40) . '...';
            $tv_url = substr($server['tv_url'], 0, 40) . '...';
            echo "<tr>";
            echo "<td><strong>{$server['name']}</strong></td>";
            echo "<td>{$server['priority']}</td>";
            echo "<td><small>{$movie_url}</small></td>";
            echo "<td><small>{$tv_url}</small></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ No active servers found in database!</p>";
        
        // Add servers if none exist
        echo "<p>Adding default servers...</p>";
        
        $default_servers = [
            ['AutoEmbed', 'https://player.autoembed.cc/embed/movie/{id}', 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}', 1],
            ['VidJoy', 'https://vidjoy.pro/embed/movie/{id}', 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}', 2],
            ['VidZee', 'https://player.vidzee.wtf/embed/movie/{id}', 'https://player.vidzee.wtf/embed/tv/{id}/{season}/{episode}', 3],
            ['LetsEmbed', 'https://letsembed.cc/embed/movie/?id={id}', 'https://letsembed.cc/embed/tv/?id={id}/{season}/{episode}', 4]
        ];
        
        foreach ($default_servers as $server) {
            $stmt = $conn->prepare("INSERT INTO embed_servers (name, movie_url, tv_url, priority, is_active) VALUES (?, ?, ?, ?, 1)");
            $stmt->execute($server);
            echo "<p style='color: green;'>✅ Added: {$server[0]}</p>";
        }
        
        // Refresh the list
        $stmt = $conn->query("SELECT * FROM embed_servers WHERE is_active = 1 ORDER BY priority ASC");
        $db_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    echo "<h3>2️⃣ getEmbedUrls Function Test</h3>";
    
    require_once 'includes/functions.php';
    $streamflix = new StreamFlix();
    
    $test_urls = $streamflix->getEmbedUrls('movie', 950387);
    
    echo "<p><strong>getEmbedUrls function returns: " . count($test_urls) . " servers</strong></p>";
    
    if (!empty($test_urls)) {
        echo "<ol>";
        foreach ($test_urls as $url) {
            echo "<li><strong>{$url['name']}</strong> (Priority: {$url['priority']})<br>";
            echo "<small>URL: " . substr($url['url'], 0, 80) . "...</small></li>";
        }
        echo "</ol>";
        echo "<p style='color: green;'>✅ Function working correctly!</p>";
    } else {
        echo "<p style='color: red;'>❌ Function returns empty array!</p>";
        echo "<p>Testing direct database query...</p>";
        
        // Test direct query
        $direct_urls = [];
        foreach ($db_servers as $server) {
            $url = str_replace('{id}', '950387', $server['movie_url']);
            $direct_urls[] = [
                'name' => $server['name'],
                'url' => $url,
                'priority' => $server['priority']
            ];
        }
        
        echo "<p><strong>Direct query would return: " . count($direct_urls) . " servers</strong></p>";
        
        if (!empty($direct_urls)) {
            echo "<p style='color: orange;'>⚠️ Database has servers but function doesn't work. Using fallback in player.</p>";
        }
    }
    
    echo "<h3>3️⃣ Player Page Simulation</h3>";
    
    // Simulate what player.php will do
    $player_urls = $test_urls;
    
    // If function failed, use fallback (like in updated player.php)
    if (empty($player_urls)) {
        echo "<p style='color: blue;'>🔄 Using fallback method...</p>";
        
        foreach ($db_servers as $server) {
            $url = str_replace('{id}', '950387', $server['movie_url']);
            $player_urls[] = [
                'name' => $server['name'],
                'url' => $url,
                'priority' => $server['priority']
            ];
        }
        
        // Sort by priority
        usort($player_urls, function($a, $b) {
            return $a['priority'] - $b['priority'];
        });
    }
    
    echo "<p><strong>Player will show: " . count($player_urls) . " servers</strong></p>";
    
    if (!empty($player_urls)) {
        echo "<ol>";
        foreach ($player_urls as $url) {
            echo "<li><strong>{$url['name']}</strong> (Priority: {$url['priority']})</li>";
        }
        echo "</ol>";
        echo "<p style='color: green;'>✅ Player should work correctly!</p>";
    } else {
        echo "<p style='color: red;'>❌ Player will show no servers!</p>";
    }
    
    echo "<h3>4️⃣ Admin Panel Sync Test</h3>";
    
    // Test admin panel changes
    echo "<p>Testing admin panel synchronization...</p>";
    
    // Temporarily disable one server
    $stmt = $conn->prepare("UPDATE embed_servers SET is_active = 0 WHERE name = 'LetsEmbed'");
    $stmt->execute();
    
    // Test again
    $stmt = $conn->query("SELECT * FROM embed_servers WHERE is_active = 1 ORDER BY priority ASC");
    $active_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>After disabling LetsEmbed: <strong>" . count($active_servers) . " active servers</strong></p>";
    
    // Re-enable it
    $stmt = $conn->prepare("UPDATE embed_servers SET is_active = 1 WHERE name = 'LetsEmbed'");
    $stmt->execute();
    
    echo "<p style='color: green;'>✅ Admin panel sync test completed</p>";
    
    echo "<h3>5️⃣ Final Status</h3>";
    
    if (count($player_urls) >= 3) {
        echo "<div style='background: #d4edda; padding: 20px; border-left: 5px solid #28a745; margin: 20px 0;'>";
        echo "<h4 style='color: #155724;'>🎉 SUCCESS!</h4>";
        echo "<p><strong>Player fix is working correctly!</strong></p>";
        echo "<ul>";
        echo "<li>✅ Database has " . count($db_servers) . " servers</li>";
        echo "<li>✅ Player will show " . count($player_urls) . " servers</li>";
        echo "<li>✅ Admin panel changes will sync</li>";
        echo "<li>✅ Fallback system in place</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 20px; border-left: 5px solid #dc3545; margin: 20px 0;'>";
        echo "<h4 style='color: #721c24;'>❌ Issues Found</h4>";
        echo "<p>Player may not work correctly. Manual intervention needed.</p>";
        echo "</div>";
    }
    
    echo "<h3>6️⃣ Test Links</h3>";
    
    echo "<div style='background: #e7f3ff; padding: 20px; border-left: 5px solid #007bff; margin: 20px 0;'>";
    echo "<h4>🔗 Test Your Player Now:</h4>";
    echo "<p><a href='player.php?id=950387&type=movie' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🎬 Test Movie Player</a></p>";
    echo "<p><a href='admin/servers.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>⚙️ Admin Panel</a></p>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-left: 5px solid #ffc107; margin: 20px 0;'>";
    echo "<h4 style='color: #856404;'>📋 Important:</h4>";
    echo "<ol>";
    echo "<li><strong>Clear browser cache</strong> before testing</li>";
    echo "<li><strong>Hard refresh</strong> the player page (Ctrl+F5)</li>";
    echo "<li><strong>Test admin changes</strong> by disabling/enabling servers</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-left: 5px solid #dc3545; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24;'>❌ Error</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
