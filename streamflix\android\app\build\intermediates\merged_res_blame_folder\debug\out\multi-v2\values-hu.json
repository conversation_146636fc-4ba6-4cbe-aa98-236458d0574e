{"logs": [{"outputFile": "com.streamflix.app-mergeDebugResources-83:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac2f50916fa650919f030349484e55e3\\transformed\\jetified-material3-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,292,400,516,611,708,822,962,1085,1232,1317,1417,1515,1617,1739,1876,1981,2121,2259,2385,2581,2704,2826,2948,3074,3173,3268,3387,3524,3626,3737,3841,3986,4133,4240,4347,4431,4529,4623,4711,4798,4899,4980,5063,5162,5268,5363,5466,5552,5661,5759,5865,5986,6067,6179", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "171,287,395,511,606,703,817,957,1080,1227,1312,1412,1510,1612,1734,1871,1976,2116,2254,2380,2576,2699,2821,2943,3069,3168,3263,3382,3519,3621,3732,3836,3981,4128,4235,4342,4426,4524,4618,4706,4793,4894,4975,5058,5157,5263,5358,5461,5547,5656,5754,5860,5981,6062,6174,6272"}, "to": {"startLines": "127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9918,10039,10155,10263,10379,10474,10571,10685,10825,10948,11095,11180,11280,11378,11480,11602,11739,11844,11984,12122,12248,12444,12567,12689,12811,12937,13036,13131,13250,13387,13489,13600,13704,13849,13996,14103,14210,14294,14392,14486,14574,14661,14762,14843,14926,15025,15131,15226,15329,15415,15524,15622,15728,15849,15930,16042", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "10034,10150,10258,10374,10469,10566,10680,10820,10943,11090,11175,11275,11373,11475,11597,11734,11839,11979,12117,12243,12439,12562,12684,12806,12932,13031,13126,13245,13382,13484,13595,13699,13844,13991,14098,14205,14289,14387,14481,14569,14656,14757,14838,14921,15020,15126,15221,15324,15410,15519,15617,15723,15844,15925,16037,16135"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ecb7be72ebd258ebb899bf8355eead54\\transformed\\jetified-ui-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,288,385,484,571,653,749,838,925,989,1053,1136,1224,1298,1377,1443", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "195,283,380,479,566,648,744,833,920,984,1048,1131,1219,1293,1372,1438,1559"}, "to": {"startLines": "65,66,67,68,69,124,125,238,239,240,241,243,244,246,248,249,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5132,5227,5315,5412,5511,9681,9763,20566,20655,20742,20806,20955,21038,21210,21385,21464,21530", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "5222,5310,5407,5506,5593,9758,9854,20650,20737,20801,20865,21033,21121,21279,21459,21525,21646"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\11b247236daf7bf1e583aa11f0369379\\transformed\\jetified-media3-ui-1.2.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,490,692,780,867,945,1031,1134,1208,1276,1373,1474,1547,1615,1680,1748,1861,1972,2082,2156,2238,2312,2385,2475,2564,2632,2695,2748,2806,2854,2915,2979,3046,3110,3178,3243,3302,3367,3433,3499,3552,3617,3699,3781", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,87,86,77,85,102,73,67,96,100,72,67,64,67,112,110,109,73,81,73,72,89,88,67,62,52,57,47,60,63,66,63,67,64,58,64,65,65,52,64,81,81,56", "endOffsets": "280,485,687,775,862,940,1026,1129,1203,1271,1368,1469,1542,1610,1675,1743,1856,1967,2077,2151,2233,2307,2380,2470,2559,2627,2690,2743,2801,2849,2910,2974,3041,3105,3173,3238,3297,3362,3428,3494,3547,3612,3694,3776,3833"}, "to": {"startLines": "2,11,15,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,585,5662,5750,5837,5915,6001,6104,6178,6246,6343,6444,6517,6585,6650,6718,6831,6942,7052,7126,7208,7282,7355,7445,7534,7602,8364,8417,8475,8523,8584,8648,8715,8779,8847,8912,8971,9036,9102,9168,9221,9286,9368,9450", "endLines": "10,14,18,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "17,12,12,87,86,77,85,102,73,67,96,100,72,67,64,67,112,110,109,73,81,73,72,89,88,67,62,52,57,47,60,63,66,63,67,64,58,64,65,65,52,64,81,81,56", "endOffsets": "375,580,782,5745,5832,5910,5996,6099,6173,6241,6338,6439,6512,6580,6645,6713,6826,6937,7047,7121,7203,7277,7350,7440,7529,7597,7660,8412,8470,8518,8579,8643,8710,8774,8842,8907,8966,9031,9097,9163,9216,9281,9363,9445,9502"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\47494bae0d5af340ce384dde3add152e\\transformed\\appcompat-1.6.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,245", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "950,1058,1150,1265,1349,1464,1587,1664,1739,1830,1923,2018,2112,2212,2305,2400,2495,2586,2677,2760,2870,2980,3080,3191,3300,3419,3601,21126", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "1053,1145,1260,1344,1459,1582,1659,1734,1825,1918,2013,2107,2207,2300,2395,2490,2581,2672,2755,2865,2975,3075,3186,3295,3414,3596,3699,21205"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1876129b9e490c2861a5ecf91d698967\\transformed\\jetified-foundation-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,96", "endOffsets": "139,236"}, "to": {"startLines": "251,252", "startColumns": "4,4", "startOffsets": "21651,21740", "endColumns": "88,96", "endOffsets": "21735,21832"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5e0eb68a5716cdfe313e221efb4d1df6\\transformed\\core-1.12.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "55,56,57,58,59,60,61,247", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4108,4205,4307,4409,4510,4613,4720,21284", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "4200,4302,4404,4505,4608,4715,4825,21380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dfc9ccab22fd45fc4cc7c7544966016d\\transformed\\material-1.11.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,344,420,497,587,667,766,886,969,1033,1132,1207,1266,1376,1438,1507,1565,1637,1698,1753,1856,1913,1973,2028,2109,2229,2312,2400,2535,2618,2698,2838,2932,3014,3067,3118,3184,3260,3342,3428,3512,3589,3664,3743,3820,3925,4021,4098,4190,4287,4361,4446,4543,4595,4678,4745,4833,4920,4982,5046,5109,5175,5273,5379,5473,5580,5637,5692", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,75,76,89,79,98,119,82,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,87,134,82,79,139,93,81,52,50,65,75,81,85,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84", "endOffsets": "258,339,415,492,582,662,761,881,964,1028,1127,1202,1261,1371,1433,1502,1560,1632,1693,1748,1851,1908,1968,2023,2104,2224,2307,2395,2530,2613,2693,2833,2927,3009,3062,3113,3179,3255,3337,3423,3507,3584,3659,3738,3815,3920,4016,4093,4185,4282,4356,4441,4538,4590,4673,4740,4828,4915,4977,5041,5104,5170,5268,5374,5468,5575,5632,5687,5772"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,70,122,123,126,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,242", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,3704,3785,3861,3938,4028,4830,4929,5049,5598,9507,9606,9859,16140,16250,16312,16381,16439,16511,16572,16627,16730,16787,16847,16902,16983,17103,17186,17274,17409,17492,17572,17712,17806,17888,17941,17992,18058,18134,18216,18302,18386,18463,18538,18617,18694,18799,18895,18972,19064,19161,19235,19320,19417,19469,19552,19619,19707,19794,19856,19920,19983,20049,20147,20253,20347,20454,20511,20870", "endLines": "22,50,51,52,53,54,62,63,64,70,122,123,126,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,242", "endColumns": "12,80,75,76,89,79,98,119,82,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,87,134,82,79,139,93,81,52,50,65,75,81,85,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84", "endOffsets": "945,3780,3856,3933,4023,4103,4924,5044,5127,5657,9601,9676,9913,16245,16307,16376,16434,16506,16567,16622,16725,16782,16842,16897,16978,17098,17181,17269,17404,17487,17567,17707,17801,17883,17936,17987,18053,18129,18211,18297,18381,18458,18533,18612,18689,18794,18890,18967,19059,19156,19230,19315,19412,19464,19547,19614,19702,19789,19851,19915,19978,20044,20142,20248,20342,20449,20506,20561,20950"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d09ac5c5e9f0e76cba4d9b249ad65917\\transformed\\jetified-media3-exoplayer-1.2.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,192,266,338,416,489,583,673", "endColumns": "74,61,73,71,77,72,93,89,80", "endOffsets": "125,187,261,333,411,484,578,668,749"}, "to": {"startLines": "95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7665,7740,7802,7876,7948,8026,8099,8193,8283", "endColumns": "74,61,73,71,77,72,93,89,80", "endOffsets": "7735,7797,7871,7943,8021,8094,8188,8278,8359"}}]}]}