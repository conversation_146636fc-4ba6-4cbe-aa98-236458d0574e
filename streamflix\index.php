<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/config/database.php')) {
    header('Location: install.php');
    exit('Please run the installer first.');
}

require_once 'includes/functions.php';

// Function to check if content is adult
function isAdultContent($content_id, $type) {
    try {
        $db = new Database();
        $conn = $db->connect();

        // Get Hentai genre ID
        $stmt = $conn->prepare("SELECT id FROM genres WHERE name = 'Hentai'");
        $stmt->execute();
        $hentai_genre = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$hentai_genre) return false;

        if ($type === 'movie') {
            $stmt = $conn->prepare("SELECT COUNT(*) FROM movie_genres WHERE movie_id = ? AND genre_id = ?");
        } else {
            $stmt = $conn->prepare("SELECT COUNT(*) FROM tv_show_genres WHERE tv_show_id = ? AND genre_id = ?");
        }

        $stmt->execute([$content_id, $hentai_genre['id']]);
        return $stmt->fetchColumn() > 0;

    } catch (Exception $e) {
        return false;
    }
}

// Get statistics
$stats = [
    'movies' => 0,
    'tv_shows' => 0,
    'users' => 0
];

try {
    $db = new Database();
    $conn = $db->connect();

    // Get movie count
    $stmt = $conn->query("SELECT COUNT(*) FROM movies");
    $stats['movies'] = $stmt->fetchColumn();

    // Get TV show count
    $stmt = $conn->query("SELECT COUNT(*) FROM tv_shows");
    $stats['tv_shows'] = $stmt->fetchColumn();

    // Get user count
    $stmt = $conn->query("SELECT COUNT(*) FROM users");
    $stats['users'] = $stmt->fetchColumn();

} catch (Exception $e) {
    // Use default values if database error
}

// Get featured content
$featured_movies = [];
$featured_tv = [];
$trending_movies = [];
$trending_tv = [];

try {
    // Get featured movies
    $stmt = $conn->prepare("SELECT * FROM movies WHERE is_featured = 1 ORDER BY created_at DESC LIMIT 15");
    $stmt->execute();
    $featured_movies = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get featured TV shows
    $stmt = $conn->prepare("SELECT * FROM tv_shows WHERE is_featured = 1 ORDER BY created_at DESC LIMIT 15");
    $stmt->execute();
    $featured_tv = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get trending movies
    $stmt = $conn->prepare("SELECT * FROM movies WHERE is_trending = 1 ORDER BY created_at DESC LIMIT 15");
    $stmt->execute();
    $trending_movies = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get trending TV shows
    $stmt = $conn->prepare("SELECT * FROM tv_shows WHERE is_trending = 1 ORDER BY created_at DESC LIMIT 15");
    $stmt->execute();
    $trending_tv = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // If no featured content, get latest
    if (empty($featured_movies)) {
        $stmt = $conn->prepare("SELECT * FROM movies ORDER BY created_at DESC LIMIT 15");
        $stmt->execute();
        $featured_movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    if (empty($featured_tv)) {
        $stmt = $conn->prepare("SELECT * FROM tv_shows ORDER BY created_at DESC LIMIT 15");
        $stmt->execute();
        $featured_tv = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // If no trending content, get popular (high rated)
    if (empty($trending_movies)) {
        $stmt = $conn->prepare("SELECT * FROM movies ORDER BY vote_average DESC LIMIT 15");
        $stmt->execute();
        $trending_movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    if (empty($trending_tv)) {
        $stmt = $conn->prepare("SELECT * FROM tv_shows ORDER BY vote_average DESC LIMIT 15");
        $stmt->execute();
        $trending_tv = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

} catch (Exception $e) {
    // Use empty arrays if database error
}

// Get hero content (random featured movie or TV show)
$hero_content = null;
try {
    $all_featured = array_merge($featured_movies, $featured_tv);
    if (!empty($all_featured)) {
        $hero_content = $all_featured[array_rand($all_featured)];
    }
} catch (Exception $e) {
    // No hero content
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?> - Stream Movies & TV Shows</title>
    <meta name="description" content="Watch the latest movies and TV shows online. Stream your favorite content anytime, anywhere.">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        /* Modern Homepage Design */
        :root {
            --primary-color: #e50914;
            --primary-dark: #b20710;
            --secondary-color: #1a1a1a;
            --dark-bg: #0a0a0a;
            --card-bg: #1f1f1f;
            --text-primary: #ffffff;
            --text-secondary: #b3b3b3;
            --text-muted: #737373;
            --border-color: #333333;
            --gradient-primary: linear-gradient(135deg, #e50914 0%, #b20710 100%);
            --gradient-dark: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            --shadow-light: 0 4px 20px rgba(229, 9, 20, 0.1);
            --shadow-medium: 0 8px 30px rgba(0, 0, 0, 0.3);
            --shadow-heavy: 0 20px 60px rgba(0, 0, 0, 0.5);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--dark-bg);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(10, 10, 10, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 4%;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 800;
            color: var(--primary-color);
            text-decoration: none;
            letter-spacing: -0.5px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }

        .nav-menu a {
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
            position: relative;
        }

        .nav-menu a:hover {
            color: var(--primary-color);
        }

        .nav-menu a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--primary-color);
            transition: width 0.3s ease;
        }

        .nav-menu a:hover::after {
            width: 100%;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* Search Container */
        .search-container {
            position: relative;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .search-input {
            padding: 0.6rem 0.8rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            font-size: 0.85rem;
            width: 200px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(229, 9, 20, 0.1);
        }

        .search-input::placeholder {
            color: var(--text-muted);
        }

        .search-btn {
            background: var(--gradient-primary);
            border: none;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .search-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
        }

        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--card-bg);
            border-radius: 12px;
            box-shadow: var(--shadow-heavy);
            max-height: 400px;
            overflow-y: auto;
            z-index: 1001;
            display: none;
            margin-top: 0.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .search-result-item {
            display: flex;
            align-items: center;
            padding: 0.8rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            cursor: pointer;
            transition: background 0.3s ease;
            text-decoration: none;
            color: var(--text-primary);
        }

        .search-result-item:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .search-result-item:last-child {
            border-bottom: none;
        }

        .search-result-poster {
            width: 40px;
            height: 60px;
            object-fit: cover;
            border-radius: 6px;
            margin-right: 0.8rem;
        }

        .search-result-info {
            flex: 1;
        }

        .search-result-title {
            font-weight: 600;
            margin-bottom: 0.2rem;
            font-size: 0.9rem;
        }

        .search-result-meta {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .search-no-results {
            padding: 1rem;
            text-align: center;
            color: var(--text-muted);
            font-size: 0.9rem;
        }

        /* 18+ Filter Toggle */
        .adult-filter-container {
            display: flex;
            align-items: center;
        }

        .adult-filter-toggle {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            user-select: none;
        }

        .adult-filter-toggle input[type="checkbox"] {
            display: none;
        }

        .toggle-slider {
            position: relative;
            width: 45px;
            height: 24px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .toggle-slider::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 18px;
            height: 18px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .adult-filter-toggle input[type="checkbox"]:checked + .toggle-slider {
            background: #ff1744;
            border-color: #ff1744;
        }

        .adult-filter-toggle input[type="checkbox"]:checked + .toggle-slider::before {
            transform: translateX(21px);
        }

        .toggle-label {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-primary);
            transition: color 0.3s ease;
        }

        .adult-filter-toggle input[type="checkbox"]:checked ~ .toggle-label {
            color: #ff1744;
        }

        /* Adult Content Blur Effect */
        .adult-content {
            transition: filter 0.3s ease;
        }

        .adult-content.blurred {
            filter: blur(10px);
            opacity: 0.5;
        }

        .adult-content.blurred:hover {
            filter: blur(5px);
            opacity: 0.7;
        }

        /* Adult Badge */
        .adult-badge {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: #ff1744;
            color: white;
            padding: 0.3rem 0.6rem;
            border-radius: 6px;
            font-size: 0.7rem;
            font-weight: 700;
            z-index: 3;
            box-shadow: 0 2px 8px rgba(255, 23, 68, 0.3);
        }

        .btn {
            padding: 0.7rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-light);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(229, 9, 20, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }
        /* Hero Section */
        .hero {
            height: 100vh;
            background: var(--gradient-dark);
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(229, 9, 20, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(229, 9, 20, 0.05) 0%, transparent 50%);
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            padding: 0 2rem;
            animation: fadeInUp 1s ease-out;
        }

        .hero-title {
            font-size: clamp(2.5rem, 6vw, 4.5rem);
            font-weight: 800;
            margin-bottom: 1.5rem;
            line-height: 1.1;
            letter-spacing: -2px;
        }

        .hero-title .brand-highlight {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            color: var(--text-secondary);
            margin-bottom: 3rem;
            font-weight: 400;
            line-height: 1.5;
        }

        .hero-buttons {
            display: flex;
            gap: 1.5rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 4rem;
        }

        .hero-buttons .btn {
            padding: 1rem 2rem;
            font-size: 1.1rem;
            border-radius: 8px;
            min-width: 180px;
        }

        .hero-stats {
            display: flex;
            justify-content: center;
            gap: 3rem;
            flex-wrap: wrap;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            display: block;
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 500;
        }

        /* Content Sections */
        .content-section {
            padding: 5rem 4%;
            max-width: 1400px;
            margin: 0 auto;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.8rem;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 2rem;
            background: var(--gradient-primary);
            border-radius: 2px;
        }

        .view-all {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: opacity 0.3s ease;
        }

        .view-all:hover {
            opacity: 0.8;
        }

        /* Content Grid */
        .content-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 1.5rem;
        }

        @media (max-width: 1200px) {
            .content-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        @media (max-width: 900px) {
            .content-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        .content-card {
            background: var(--card-bg);
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            cursor: pointer;
            position: relative;
            aspect-ratio: 2/3;
            box-shadow: var(--shadow-medium);
        }

        .content-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-heavy);
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, transparent 0%, rgba(229, 9, 20, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .content-card:hover::before {
            opacity: 1;
        }

        .content-card img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .content-card:hover img {
            transform: scale(1.05);
        }

        .card-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
            padding: 1.5rem;
            transform: translateY(100%);
            transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            z-index: 2;
        }

        .content-card:hover .card-overlay {
            transform: translateY(0);
        }

        .card-title {
            font-size: 1rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .card-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.85rem;
            color: var(--text-secondary);
        }

        .card-rating {
            display: flex;
            align-items: center;
            gap: 0.3rem;
            color: #ffd700;
            font-weight: 600;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in {
            animation: fadeInUp 0.8s ease-out;
        }

        /* Mobile Menu */
        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            color: var(--text-primary);
            font-size: 1.5rem;
            cursor: pointer;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .navbar {
                padding: 1rem 5%;
            }

            .nav-menu {
                display: none;
            }

            .mobile-menu-toggle {
                display: block;
            }

            .search-input {
                width: 180px;
            }

            .user-menu {
                gap: 0.5rem;
            }

            .user-menu span {
                display: none;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .hero-stats {
                gap: 2rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .content-section {
                padding: 3rem 5%;
            }

            .section-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            .content-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }
        }

        @media (max-width: 480px) {
            .navbar {
                padding: 0.8rem 3%;
            }

            .search-input {
                width: 140px;
            }

            .hero-title {
                font-size: 2rem;
            }

            .hero-subtitle {
                font-size: 1rem;
            }

            .content-section {
                padding: 2rem 3%;
            }

            .content-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 0.8rem;
            }

            .content-card {
                border-radius: 8px;
            }

            .card-overlay {
                padding: 0.8rem;
            }

            .card-title {
                font-size: 0.9rem;
            }

            .card-info {
                font-size: 0.8rem;
            }
        }

        @media (max-width: 480px) {
            .content-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .hero-stats {
                flex-direction: column;
                gap: 1.5rem;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Scroll Indicator */
        .scroll-indicator {
            position: absolute;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            color: var(--text-muted);
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateX(-50%) translateY(0);
            }
            40% {
                transform: translateX(-50%) translateY(-10px);
            }
            60% {
                transform: translateX(-50%) translateY(-5px);
            }
        }
    </style>
</head>

<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <a href="index.php" class="logo"><?php echo SITE_NAME; ?></a>

            <ul class="nav-menu">
                <li><a href="index.php">Home</a></li>
                <li><a href="browse.php?type=movie">Movies</a></li>
                <li><a href="browse.php?type=tv">TV Shows</a></li>
                <li><a href="anime.php">🎌 Anime</a></li>
                <li><a href="hentai.php">🔞 Hentai</a></li>
            </ul>

            <div class="user-menu">
                <!-- Search Box -->
                <div class="search-container">
                    <input type="text" id="searchInput" class="search-input" placeholder="Search movies & TV shows...">
                    <button class="search-btn" onclick="performSearch()">🔍</button>
                    <div class="search-results" id="searchResults"></div>
                </div>

                <!-- 18+ Filter Toggle -->
                <div class="adult-filter-container">
                    <label class="adult-filter-toggle">
                        <input type="checkbox" id="adultFilter" onchange="toggleAdultFilter()">
                        <span class="toggle-slider"></span>
                        <span class="toggle-label">🔞 18+</span>
                    </label>
                </div>

                <?php if (isLoggedIn()): ?>
                    <span style="color: var(--text-secondary);">Welcome, <?php echo $_SESSION['username']; ?></span>
                    <?php if (isAdmin()): ?>
                        <a href="admin/index.php" class="btn btn-secondary">Admin</a>
                    <?php endif; ?>
                    <a href="logout.php" class="btn btn-primary">Logout</a>
                <?php else: ?>
                    <a href="login.php" class="btn btn-secondary">Login</a>
                    <a href="register.php" class="btn btn-primary">Sign Up</a>
                <?php endif; ?>
            </div>

            <button class="mobile-menu-toggle">☰</button>
        </nav>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <?php if ($hero_content): ?>
        <section class="hero" style="background-image: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.7)), url('<?php echo getImageUrl($hero_content['backdrop_path'] ?? $hero_content['poster_path'], 'original'); ?>');">
            <div class="hero-content">
                <h1 class="hero-title">
                    <?php echo htmlspecialchars($hero_content['title'] ?? $hero_content['name']); ?>
                </h1>
                <p class="hero-subtitle">
                    <?php echo htmlspecialchars(substr($hero_content['overview'], 0, 200)) . '...'; ?>
                </p>
                <div class="hero-buttons">
                    <a href="player.php?id=<?php echo $hero_content['tmdb_id']; ?>&type=<?php echo isset($hero_content['title']) ? 'movie' : 'tv_show'; ?>" class="btn btn-primary">
                        ▶ Play Now
                    </a>
                    <a href="browse.php" class="btn btn-secondary">
                        📚 Browse More
                    </a>
                </div>
                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number"><?php echo number_format($stats['movies']); ?></span>
                        <span class="stat-label">Movies</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number"><?php echo number_format($stats['tv_shows']); ?></span>
                        <span class="stat-label">TV Shows</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number"><?php echo number_format($stats['users']); ?></span>
                        <span class="stat-label">Users</span>
                    </div>
                </div>
            </div>
            <div class="scroll-indicator">↓</div>
        </section>
        <?php else: ?>
        <section class="hero">
            <div class="hero-content">
                <h1 class="hero-title">
                    Welcome to <span class="brand-highlight"><?php echo SITE_NAME; ?></span>
                </h1>
                <p class="hero-subtitle">
                    Discover unlimited entertainment with thousands of movies and TV shows
                </p>
                <div class="hero-buttons">
                    <a href="browse.php?type=movie" class="btn btn-primary">
                        🎬 Browse Movies
                    </a>
                    <a href="browse.php?type=tv" class="btn btn-secondary">
                        📺 Browse TV Shows
                    </a>
                </div>
                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number"><?php echo number_format($stats['movies']); ?></span>
                        <span class="stat-label">Movies</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number"><?php echo number_format($stats['tv_shows']); ?></span>
                        <span class="stat-label">TV Shows</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number"><?php echo number_format($stats['users']); ?></span>
                        <span class="stat-label">Users</span>
                    </div>
                </div>
            </div>
            <div class="scroll-indicator">↓</div>
        </section>
        <?php endif; ?>

        <!-- Featured Movies -->
        <?php if (!empty($featured_movies)): ?>
        <section class="content-section fade-in">
            <div class="section-header">
                <h2 class="section-title">🎬 Featured Movies</h2>
                <a href="browse.php?type=movie&filter=featured" class="view-all">View All →</a>
            </div>
            <div class="content-grid">
                <?php foreach (array_slice($featured_movies, 0, 6) as $movie): ?>
                    <?php $isAdult = isAdultContent($movie['id'], 'movie'); ?>
                    <a href="player.php?id=<?php echo $movie['tmdb_id']; ?>&type=movie"
                       class="content-card<?php echo $isAdult ? ' adult-content' : ''; ?>">
                        <img src="<?php echo getImageUrl($movie['poster_path'], 'w500'); ?>"
                             alt="<?php echo htmlspecialchars($movie['title']); ?>"
                             loading="lazy">
                        <div class="card-overlay">
                            <div class="card-title"><?php echo htmlspecialchars($movie['title']); ?></div>
                            <div class="card-meta">
                                <span><?php echo date('Y', strtotime($movie['release_date'])); ?></span>
                                <div class="card-rating">
                                    <span>⭐</span>
                                    <span><?php echo number_format($movie['vote_average'], 1); ?></span>
                                </div>
                            </div>
                            <?php if ($isAdult): ?>
                                <div class="adult-badge">18+</div>
                            <?php endif; ?>
                        </div>
                    </a>
                <?php endforeach; ?>
            </div>
        </section>
        <?php endif; ?>

        <!-- Trending Movies -->
        <?php if (!empty($trending_movies)): ?>
        <section class="content-section fade-in">
            <div class="section-header">
                <h2 class="section-title">🔥 Trending Movies</h2>
                <a href="browse.php?type=movie&filter=trending" class="view-all">View All →</a>
            </div>
            <div class="content-grid">
                <?php foreach (array_slice($trending_movies, 0, 6) as $movie): ?>
                    <?php $isAdult = isAdultContent($movie['id'], 'movie'); ?>
                    <a href="player.php?id=<?php echo $movie['tmdb_id']; ?>&type=movie"
                       class="content-card<?php echo $isAdult ? ' adult-content' : ''; ?>">
                        <img src="<?php echo getImageUrl($movie['poster_path'], 'w500'); ?>"
                             alt="<?php echo htmlspecialchars($movie['title']); ?>"
                             loading="lazy">
                        <div class="card-overlay">
                            <div class="card-title"><?php echo htmlspecialchars($movie['title']); ?></div>
                            <div class="card-meta">
                                <span><?php echo date('Y', strtotime($movie['release_date'])); ?></span>
                                <div class="card-rating">
                                    <span>⭐</span>
                                    <span><?php echo number_format($movie['vote_average'], 1); ?></span>
                                </div>
                            </div>
                            <?php if ($isAdult): ?>
                                <div class="adult-badge">18+</div>
                            <?php endif; ?>
                        </div>
                    </a>
                <?php endforeach; ?>
            </div>
        </section>
        <?php endif; ?>

        <!-- Featured TV Shows -->
        <?php if (!empty($featured_tv)): ?>
        <section class="content-section fade-in">
            <div class="section-header">
                <h2 class="section-title">📺 Featured TV Shows</h2>
                <a href="browse.php?type=tv&filter=featured" class="view-all">View All →</a>
            </div>
            <div class="content-grid">
                <?php foreach (array_slice($featured_tv, 0, 6) as $show): ?>
                    <?php $isAdult = isAdultContent($show['id'], 'tv_show'); ?>
                    <a href="player.php?id=<?php echo $show['tmdb_id']; ?>&type=tv_show"
                       class="content-card<?php echo $isAdult ? ' adult-content' : ''; ?>">
                        <img src="<?php echo getImageUrl($show['poster_path'], 'w500'); ?>"
                             alt="<?php echo htmlspecialchars($show['name']); ?>"
                             loading="lazy">
                        <div class="card-overlay">
                            <div class="card-title"><?php echo htmlspecialchars($show['name']); ?></div>
                            <div class="card-meta">
                                <span><?php echo date('Y', strtotime($show['first_air_date'])); ?></span>
                                <div class="card-rating">
                                    <span>⭐</span>
                                    <span><?php echo number_format($show['vote_average'], 1); ?></span>
                                </div>
                            </div>
                            <?php if ($isAdult): ?>
                                <div class="adult-badge">18+</div>
                            <?php endif; ?>
                        </div>
                    </a>
                <?php endforeach; ?>
            </div>
        </section>
        <?php endif; ?>

        <!-- Trending TV Shows -->
        <?php if (!empty($trending_tv)): ?>
        <section class="content-section fade-in">
            <div class="section-header">
                <h2 class="section-title">🔥 Trending TV Shows</h2>
                <a href="browse.php?type=tv&filter=trending" class="view-all">View All →</a>
            </div>
            <div class="content-grid">
                <?php foreach (array_slice($trending_tv, 0, 6) as $show): ?>
                    <a href="player.php?id=<?php echo $show['tmdb_id']; ?>&type=tv_show" class="content-card">
                        <img src="<?php echo getImageUrl($show['poster_path'], 'w500'); ?>"
                             alt="<?php echo htmlspecialchars($show['name']); ?>"
                             loading="lazy">
                        <div class="card-overlay">
                            <div class="card-title"><?php echo htmlspecialchars($show['name']); ?></div>
                            <div class="card-meta">
                                <span><?php echo date('Y', strtotime($show['first_air_date'])); ?></span>
                                <div class="card-rating">
                                    <span>⭐</span>
                                    <span><?php echo number_format($show['vote_average'], 1); ?></span>
                                </div>
                            </div>
                        </div>
                    </a>
                <?php endforeach; ?>
            </div>
        </section>
        <?php endif; ?>
    </main>

    <!-- Footer -->
    <footer style="background: var(--secondary-color); padding: 3rem 4%; margin-top: 5rem; border-top: 1px solid var(--border-color);">
        <div style="max-width: 1400px; margin: 0 auto; text-align: center;">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; margin-bottom: 2rem;">
                <div>
                    <h3 style="color: var(--primary-color); margin-bottom: 1rem; font-size: 1.2rem;"><?php echo SITE_NAME; ?></h3>
                    <p style="color: var(--text-secondary); line-height: 1.6;">Your ultimate destination for streaming movies and TV shows. Enjoy unlimited entertainment anytime, anywhere.</p>
                </div>
                <div>
                    <h4 style="color: var(--text-primary); margin-bottom: 1rem;">Quick Links</h4>
                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                        <a href="browse.php?type=movie" style="color: var(--text-secondary); text-decoration: none; transition: color 0.3s ease;">Movies</a>
                        <a href="browse.php?type=tv" style="color: var(--text-secondary); text-decoration: none; transition: color 0.3s ease;">TV Shows</a>
                        <a href="browse.php" style="color: var(--text-secondary); text-decoration: none; transition: color 0.3s ease;">Browse All</a>
                    </div>
                </div>
                <div>
                    <h4 style="color: var(--text-primary); margin-bottom: 1rem;">Account</h4>
                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                        <?php if (isLoggedIn()): ?>
                            <a href="profile.php" style="color: var(--text-secondary); text-decoration: none; transition: color 0.3s ease;">My Profile</a>
                            <a href="watchlist.php" style="color: var(--text-secondary); text-decoration: none; transition: color 0.3s ease;">My Watchlist</a>
                            <a href="logout.php" style="color: var(--text-secondary); text-decoration: none; transition: color 0.3s ease;">Logout</a>
                        <?php else: ?>
                            <a href="login.php" style="color: var(--text-secondary); text-decoration: none; transition: color 0.3s ease;">Login</a>
                            <a href="register.php" style="color: var(--text-secondary); text-decoration: none; transition: color 0.3s ease;">Sign Up</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div style="border-top: 1px solid var(--border-color); padding-top: 2rem; color: var(--text-muted);">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. All rights reserved. | Powered by TMDB</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Header scroll effect
        window.addEventListener('scroll', function() {
            const header = document.querySelector('.header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(10, 10, 10, 0.98)';
            } else {
                header.style.background = 'rgba(10, 10, 10, 0.95)';
            }
        });

        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all content sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.style.opacity = '0';
            section.style.transform = 'translateY(30px)';
            section.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
            observer.observe(section);
        });

        // Mobile menu toggle
        const mobileToggle = document.querySelector('.mobile-menu-toggle');
        const navMenu = document.querySelector('.nav-menu');

        if (mobileToggle && navMenu) {
            mobileToggle.addEventListener('click', function() {
                navMenu.style.display = navMenu.style.display === 'flex' ? 'none' : 'flex';
            });
        }

        // Lazy loading for images
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src || img.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[loading="lazy"]').forEach(img => {
                imageObserver.observe(img);
            });
        }

        // Scroll indicator click
        document.querySelector('.scroll-indicator')?.addEventListener('click', function() {
            document.querySelector('.content-section').scrollIntoView({
                behavior: 'smooth'
            });
        });

        // Add hover effects to footer links
        document.querySelectorAll('footer a').forEach(link => {
            link.addEventListener('mouseenter', function() {
                this.style.color = 'var(--primary-color)';
            });
            link.addEventListener('mouseleave', function() {
                this.style.color = 'var(--text-secondary)';
            });
        });

        // Performance optimization: Preload critical images
        function preloadImage(src) {
            const img = new Image();
            img.src = src;
        }

        // Preload hero background if exists
        const heroSection = document.querySelector('.hero');
        if (heroSection && heroSection.style.backgroundImage) {
            const bgUrl = heroSection.style.backgroundImage.slice(4, -1).replace(/"/g, "");
            preloadImage(bgUrl);
        }

        // Add loading states for content cards
        document.querySelectorAll('.content-card').forEach(card => {
            card.addEventListener('click', function(e) {
                const loadingSpinner = document.createElement('div');
                loadingSpinner.className = 'loading';
                loadingSpinner.style.position = 'absolute';
                loadingSpinner.style.top = '50%';
                loadingSpinner.style.left = '50%';
                loadingSpinner.style.transform = 'translate(-50%, -50%)';
                loadingSpinner.style.zIndex = '10';

                this.style.position = 'relative';
                this.appendChild(loadingSpinner);

                // Remove spinner after 2 seconds (fallback)
                setTimeout(() => {
                    if (loadingSpinner.parentNode) {
                        loadingSpinner.parentNode.removeChild(loadingSpinner);
                    }
                }, 2000);
            });
        });

        // Search functionality
        let searchTimeout;
        const searchInput = document.getElementById('searchInput');
        const searchResults = document.getElementById('searchResults');

        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const query = this.value.trim();

                if (query.length < 2) {
                    hideSearchResults();
                    return;
                }

                searchTimeout = setTimeout(() => {
                    performSearch(query);
                }, 300);
            });

            // Hide search results when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.search-container')) {
                    hideSearchResults();
                }
            });

            // Handle Enter key
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const query = this.value.trim();
                    if (query) {
                        window.location.href = `browse.php?search=${encodeURIComponent(query)}`;
                    }
                }
            });
        }

        function performSearch(query = null) {
            if (!query) {
                query = searchInput.value.trim();
            }

            if (query.length < 2) return;

            // Show loading
            searchResults.innerHTML = '<div class="search-no-results">Searching...</div>';
            searchResults.style.display = 'block';

            fetch(`api/search.php?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    displaySearchResults(data);
                })
                .catch(error => {
                    console.error('Search error:', error);
                    searchResults.innerHTML = '<div class="search-no-results">Search failed. Please try again.</div>';
                });
        }

        function displaySearchResults(results) {
            if (!results || results.length === 0) {
                searchResults.innerHTML = '<div class="search-no-results">No results found</div>';
                return;
            }

            const html = results.slice(0, 8).map(item => {
                const year = item.release_date ? new Date(item.release_date).getFullYear() : 'N/A';
                const poster = item.poster_path ?
                    `https://image.tmdb.org/t/p/w92${item.poster_path}` :
                    'assets/images/no-poster.jpg';

                return `
                    <a href="player.php?id=${item.tmdb_id}&type=${item.type}" class="search-result-item">
                        <img src="${poster}" alt="${item.name}" class="search-result-poster" loading="lazy">
                        <div class="search-result-info">
                            <div class="search-result-title">${item.name}</div>
                            <div class="search-result-meta">${year} • ${item.type === 'movie' ? 'Movie' : 'TV Show'}</div>
                        </div>
                    </a>
                `;
            }).join('');

            searchResults.innerHTML = html;
        }

        function hideSearchResults() {
            searchResults.style.display = 'none';
        }

        // 18+ Filter functionality
        function toggleAdultFilter() {
            const adultFilter = document.getElementById('adultFilter');
            const adultContent = document.querySelectorAll('.adult-content');

            if (adultFilter.checked) {
                // Blur adult content
                adultContent.forEach(element => {
                    element.classList.add('blurred');
                });
                localStorage.setItem('adultFilterEnabled', 'true');
            } else {
                // Show adult content
                adultContent.forEach(element => {
                    element.classList.remove('blurred');
                });
                localStorage.setItem('adultFilterEnabled', 'false');
            }
        }

        // Load adult filter state from localStorage
        function loadAdultFilterState() {
            const adultFilter = document.getElementById('adultFilter');
            const savedState = localStorage.getItem('adultFilterEnabled');

            if (savedState === 'true') {
                adultFilter.checked = true;
                toggleAdultFilter();
            }
        }

        // Initialize adult filter on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadAdultFilterState();
        });

        console.log('🎬 <?php echo SITE_NAME; ?> loaded successfully!');
    </script>
</body>
</html>