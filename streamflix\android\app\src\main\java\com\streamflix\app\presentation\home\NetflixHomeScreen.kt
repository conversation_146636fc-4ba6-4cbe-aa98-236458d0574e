package com.streamflix.app.presentation.home

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.*
import androidx.compose.foundation.pager.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.util.lerp
import androidx.hilt.navigation.compose.hiltViewModel
import coil.compose.AsyncImage
import com.streamflix.app.data.model.*
import com.streamflix.app.presentation.components.*
import com.streamflix.app.ui.theme.*
import com.streamflix.app.utils.Resource
import kotlinx.coroutines.delay
import kotlin.math.absoluteValue

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun NetflixHomeScreen(
    onMovieClick: (Movie) -> Unit,
    onTvShowClick: (TvShow) -> Unit,
    onSeeAllClick: (String) -> Unit,
    viewModel: HomeViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .background(BackgroundDark),
        verticalArrangement = Arrangement.spacedBy(0.dp)
    ) {
        // Hero Banner Section
        item {
            when (val resource = uiState.featuredContent) {
                is Resource.Success -> {
                    val featuredItems = resource.data?.featuredMovies?.take(5) ?: emptyList()
                    if (featuredItems.isNotEmpty()) {
                        HeroBannerSection(
                            items = featuredItems,
                            onItemClick = onMovieClick,
                            onPlayClick = onMovieClick,
                            onWatchlistClick = { /* Handle watchlist */ },
                            modifier = Modifier.height(600.dp)
                        )
                    }
                }
                is Resource.Loading -> {
                    HeroBannerSkeleton(modifier = Modifier.height(600.dp))
                }
                is Resource.Error -> {
                    ErrorBanner(
                        message = resource.message ?: "Failed to load featured content",
                        onRetry = { viewModel.loadHomeData() }
                    )
                }
                null -> {
                    HeroBannerSkeleton(modifier = Modifier.height(600.dp))
                }
            }
        }

        // Continue Watching Section
        uiState.continueWatching?.let { resource ->
            when (resource) {
                is Resource.Success -> {
                    val items = resource.data ?: emptyList()
                    if (items.isNotEmpty()) {
                        item {
                            ContentSection(
                                title = "Continue Watching",
                                items = items.map { it.toContentItem() },
                                onItemClick = { /* Handle continue watching */ },
                                onSeeAllClick = { onSeeAllClick("continue_watching") },
                                showProgress = true
                            )
                        }
                    }
                }
                is Resource.Loading -> {
                    item {
                        ContentSectionSkeleton(title = "Continue Watching")
                    }
                }
                is Resource.Error -> { /* Handle error silently for continue watching */ }
            }
        }

        // Trending Movies Section
        uiState.trendingMovies?.let { resource ->
            when (resource) {
                is Resource.Success -> {
                    val items = resource.data ?: emptyList()
                    if (items.isNotEmpty()) {
                        item {
                            ContentSection(
                                title = "Trending Movies",
                                items = items.map { it.toContentItem() },
                                onItemClick = { movie -> onMovieClick(movie as Movie) },
                                onSeeAllClick = { onSeeAllClick("trending_movies") },
                                showTrendingBadge = true
                            )
                        }
                    }
                }
                is Resource.Loading -> {
                    item {
                        ContentSectionSkeleton(title = "Trending Movies")
                    }
                }
                is Resource.Error -> { /* Handle error */ }
            }
        }

        // Popular TV Shows Section
        uiState.popularTvShows?.let { resource ->
            when (resource) {
                is Resource.Success -> {
                    val items = resource.data ?: emptyList()
                    if (items.isNotEmpty()) {
                        item {
                            ContentSection(
                                title = "Popular TV Shows",
                                items = items.map { it.toContentItem() },
                                onItemClick = { tvShow -> onTvShowClick(tvShow as TvShow) },
                                onSeeAllClick = { onSeeAllClick("popular_tv_shows") }
                            )
                        }
                    }
                }
                is Resource.Loading -> {
                    item {
                        ContentSectionSkeleton(title = "Popular TV Shows")
                    }
                }
                is Resource.Error -> { /* Handle error */ }
            }
        }

        // Latest Movies Section
        uiState.latestMovies?.let { resource ->
            when (resource) {
                is Resource.Success -> {
                    val items = resource.data ?: emptyList()
                    if (items.isNotEmpty()) {
                        item {
                            ContentSection(
                                title = "Latest Movies",
                                items = items.map { it.toContentItem() },
                                onItemClick = { movie -> onMovieClick(movie as Movie) },
                                onSeeAllClick = { onSeeAllClick("latest_movies") },
                                showNewBadge = true
                            )
                        }
                    }
                }
                is Resource.Loading -> {
                    item {
                        ContentSectionSkeleton(title = "Latest Movies")
                    }
                }
                is Resource.Error -> { /* Handle error */ }
            }
        }

        // Top Rated Section
        uiState.topRated?.let { resource ->
            when (resource) {
                is Resource.Success -> {
                    val items = resource.data ?: emptyList()
                    if (items.isNotEmpty()) {
                        item {
                            TopRatedSection(
                                items = items.take(10),
                                onItemClick = { /* Handle click */ }
                            )
                        }
                    }
                }
                is Resource.Loading -> {
                    item {
                        ContentSectionSkeleton(title = "Top Rated")
                    }
                }
                is Resource.Error -> { /* Handle error */ }
            }
        }

        // Genres Section
        item {
            GenresSection(
                onGenreClick = { genre -> onSeeAllClick("genre_${genre.id}") }
            )
        }

        // Bottom spacing
        item {
            Spacer(modifier = Modifier.height(100.dp))
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun HeroBannerSection(
    items: List<Movie>,
    onItemClick: (Movie) -> Unit,
    onPlayClick: (Movie) -> Unit,
    onWatchlistClick: (Movie) -> Unit,
    modifier: Modifier = Modifier
) {
    val pagerState = rememberPagerState(pageCount = { items.size })
    
    // Auto-scroll effect
    LaunchedEffect(pagerState) {
        while (true) {
            delay(5000) // 5 seconds
            val nextPage = (pagerState.currentPage + 1) % items.size
            pagerState.animateScrollToPage(nextPage)
        }
    }

    Box(modifier = modifier) {
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize()
        ) { page ->
            val movie = items[page]
            
            Box(modifier = Modifier.fillMaxSize()) {
                // Background image
                AsyncImage(
                    model = movie.getFullBackdropUrl(),
                    contentDescription = movie.title,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop
                )
                
                // Gradient overlay
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Brush.verticalGradient(
                                colors = listOf(
                                    Color.Transparent,
                                    Color.Black.copy(alpha = 0.3f),
                                    Color.Black.copy(alpha = 0.8f),
                                    Color.Black
                                ),
                                startY = 0f,
                                endY = Float.POSITIVE_INFINITY
                            )
                        )
                )
                
                // Content overlay
                Column(
                    modifier = Modifier
                        .align(Alignment.BottomStart)
                        .padding(24.dp)
                        .fillMaxWidth()
                ) {
                    // Movie title
                    Text(
                        text = movie.title,
                        style = MaterialTheme.typography.headlineLarge.copy(
                            fontWeight = FontWeight.Bold,
                            fontSize = 32.sp
                        ),
                        color = Color.White,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // Movie info
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Rating
                        RatingChip(rating = movie.voteAverage)
                        
                        // Runtime
                        Text(
                            text = movie.getFormattedRuntime(),
                            color = Color.White.copy(alpha = 0.8f),
                            fontSize = 14.sp
                        )
                        
                        // Genres
                        movie.genres?.take(2)?.forEach { genre ->
                            GenreChip(genre = genre.name)
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // Overview
                    Text(
                        text = movie.overview ?: "",
                        color = Color.White.copy(alpha = 0.9f),
                        fontSize = 14.sp,
                        lineHeight = 20.sp,
                        maxLines = 3,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.fillMaxWidth(0.8f)
                    )
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    // Action buttons
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // Play button
                        Button(
                            onClick = { onPlayClick(movie) },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color.White
                            ),
                            modifier = Modifier.height(48.dp)
                        ) {
                            Icon(
                                Icons.Default.PlayArrow,
                                contentDescription = "Play",
                                tint = Color.Black,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "Play",
                                color = Color.Black,
                                fontWeight = FontWeight.Bold
                            )
                        }
                        
                        // More info button
                        OutlinedButton(
                            onClick = { onItemClick(movie) },
                            colors = ButtonDefaults.outlinedButtonColors(
                                contentColor = Color.White
                            ),
                            border = BorderStroke(1.dp, Color.White.copy(alpha = 0.5f)),
                            modifier = Modifier.height(48.dp)
                        ) {
                            Icon(
                                Icons.Default.Info,
                                contentDescription = "More Info",
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "More Info",
                                fontWeight = FontWeight.Medium
                            )
                        }
                        
                        // Watchlist button
                        IconButton(
                            onClick = { onWatchlistClick(movie) },
                            modifier = Modifier
                                .size(48.dp)
                                .background(
                                    Color.Black.copy(alpha = 0.6f),
                                    RoundedCornerShape(24.dp)
                                )
                        ) {
                            Icon(
                                if (movie.inWatchlist) Icons.Default.Check else Icons.Default.Add,
                                contentDescription = "Watchlist",
                                tint = Color.White,
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    }
                }
            }
        }
        
        // Page indicators
        Row(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 16.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            repeat(items.size) { index ->
                val isSelected = pagerState.currentPage == index
                Box(
                    modifier = Modifier
                        .size(if (isSelected) 12.dp else 8.dp)
                        .background(
                            if (isSelected) Color.White else Color.White.copy(alpha = 0.5f),
                            RoundedCornerShape(50)
                        )
                        .animateContentSize()
                )
            }
        }
    }
}

@Composable
fun ContentSection(
    title: String,
    items: List<ContentItem>,
    onItemClick: (Any) -> Unit,
    onSeeAllClick: () -> Unit,
    showProgress: Boolean = false,
    showTrendingBadge: Boolean = false,
    showNewBadge: Boolean = false,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier.padding(vertical = 16.dp)) {
        // Section header
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleLarge.copy(
                    fontWeight = FontWeight.Bold,
                    fontSize = 20.sp
                ),
                color = Color.White
            )
            
            TextButton(onClick = onSeeAllClick) {
                Text(
                    text = "See All",
                    color = StreamFlixRed,
                    fontWeight = FontWeight.Medium
                )
                Icon(
                    Icons.Default.KeyboardArrowRight,
                    contentDescription = null,
                    tint = StreamFlixRed,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // Content list
        LazyRow(
            contentPadding = PaddingValues(horizontal = 16.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(items) { item ->
                ContentCard(
                    item = item,
                    onClick = { onItemClick(item.originalItem) },
                    showProgress = showProgress,
                    showTrendingBadge = showTrendingBadge,
                    showNewBadge = showNewBadge,
                    modifier = Modifier.width(140.dp)
                )
            }
        }
    }
}

// Extension functions and data classes
data class ContentItem(
    val id: Int,
    val title: String,
    val posterUrl: String?,
    val rating: Double,
    val year: String?,
    val progress: Double = 0.0,
    val originalItem: Any
)

fun Movie.toContentItem() = ContentItem(
    id = id,
    title = title,
    posterUrl = getFullPosterUrl(),
    rating = voteAverage,
    year = releaseDate?.take(4),
    originalItem = this
)

fun TvShow.toContentItem() = ContentItem(
    id = id,
    title = name,
    posterUrl = getFullPosterUrl(),
    rating = voteAverage,
    year = firstAirDate?.take(4),
    originalItem = this
)

fun WatchHistoryItem.toContentItem() = ContentItem(
    id = contentId,
    title = title,
    posterUrl = posterUrl,
    rating = rating?.toDouble() ?: 0.0,
    year = null,
    progress = progress.toDouble(),
    originalItem = this
)
