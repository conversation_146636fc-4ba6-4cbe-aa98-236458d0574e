<?php
require_once 'config/database.php';

echo "<h2>🔍 Server Issue Diagnosis</h2>";
echo "<p>This script will help identify why your player shows 4 servers but database has only 3.</p>";

try {
    $db = new Database();
    $conn = $db->connect();
    
    echo "<h3>1️⃣ Database Server Count</h3>";
    
    // Count servers in database
    $stmt = $conn->query("SELECT COUNT(*) FROM embed_servers");
    $db_count = $stmt->fetchColumn();
    echo "<p>Servers in database: <strong>{$db_count}</strong></p>";
    
    // Show all database servers
    $stmt = $conn->query("SELECT * FROM embed_servers ORDER BY priority ASC");
    $db_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Name</th><th>Priority</th><th>Active</th><th>Movie URL</th></tr>";
    
    foreach ($db_servers as $server) {
        $status = $server['is_active'] ? '✅ Active' : '❌ Inactive';
        $movie_url = substr($server['movie_url'], 0, 60) . '...';
        $row_color = $server['is_active'] ? '' : 'style="background: #ffeeee;"';
        echo "<tr {$row_color}>";
        echo "<td>{$server['id']}</td>";
        echo "<td><strong>{$server['name']}</strong></td>";
        echo "<td>{$server['priority']}</td>";
        echo "<td>{$status}</td>";
        echo "<td><small>{$movie_url}</small></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>2️⃣ Config File Check</h3>";
    
    // Check if config file has hardcoded servers
    $config_content = file_get_contents('config/database.php');
    if (strpos($config_content, '$embed_servers = [') !== false) {
        echo "<p style='color: red;'>❌ <strong>PROBLEM FOUND:</strong> Config file still contains hardcoded servers!</p>";
        echo "<p>The config file has hardcoded server definitions that override database settings.</p>";
    } else {
        echo "<p style='color: green;'>✅ Config file is clean (no hardcoded servers)</p>";
    }
    
    echo "<h3>3️⃣ Player Function Test</h3>";
    
    // Test the getEmbedUrls function
    require_once 'includes/functions.php';
    $streamflix = new StreamFlix();
    
    echo "<h4>Testing with Movie (TMDB ID: 550):</h4>";
    $movie_urls = $streamflix->getEmbedUrls('movie', 550);
    echo "<p>Player will show <strong>" . count($movie_urls) . " servers</strong> for movies:</p>";
    
    echo "<ol>";
    foreach ($movie_urls as $index => $url) {
        echo "<li><strong>{$url['name']}</strong> (Priority: {$url['priority']})<br>";
        echo "<small>URL: " . substr($url['url'], 0, 80) . "...</small></li>";
    }
    echo "</ol>";
    
    echo "<h4>Testing with TV Show (TMDB ID: 1399, S1E1):</h4>";
    $tv_urls = $streamflix->getEmbedUrls('tv_show', 1399, 1, 1);
    echo "<p>Player will show <strong>" . count($tv_urls) . " servers</strong> for TV shows:</p>";
    
    echo "<ol>";
    foreach ($tv_urls as $index => $url) {
        echo "<li><strong>{$url['name']}</strong> (Priority: {$url['priority']})<br>";
        echo "<small>URL: " . substr($url['url'], 0, 80) . "...</small></li>";
    }
    echo "</ol>";
    
    echo "<h3>4️⃣ Problem Analysis</h3>";
    
    $player_count = count($movie_urls);
    
    if ($player_count > $db_count) {
        echo "<div style='background: #ffeeee; padding: 15px; border-left: 5px solid #ff0000; margin: 10px 0;'>";
        echo "<h4 style='color: #cc0000;'>🚨 PROBLEM IDENTIFIED</h4>";
        echo "<p><strong>Player shows {$player_count} servers but database has only {$db_count} servers.</strong></p>";
        echo "<p><strong>Root Cause:</strong> The system is loading servers from multiple sources:</p>";
        echo "<ul>";
        echo "<li>✅ {$db_count} servers from database</li>";
        echo "<li>❌ " . ($player_count - $db_count) . " additional servers from config file or fallback</li>";
        echo "</ul>";
        echo "<p><strong>Solution:</strong> Remove hardcoded servers from config files and ensure only database servers are used.</p>";
        echo "</div>";
    } elseif ($player_count < $db_count) {
        echo "<div style='background: #fff3cd; padding: 15px; border-left: 5px solid #ffc107; margin: 10px 0;'>";
        echo "<h4 style='color: #856404;'>⚠️ PARTIAL PROBLEM</h4>";
        echo "<p><strong>Database has {$db_count} servers but player shows only {$player_count} servers.</strong></p>";
        echo "<p><strong>Possible Causes:</strong></p>";
        echo "<ul>";
        echo "<li>Some servers are marked as inactive</li>";
        echo "<li>Database query is filtering servers</li>";
        echo "<li>Server URLs are malformed</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-left: 5px solid #28a745; margin: 10px 0;'>";
        echo "<h4 style='color: #155724;'>✅ SERVERS SYNCHRONIZED</h4>";
        echo "<p><strong>Database and player both show {$player_count} servers.</strong></p>";
        echo "<p>If you're still seeing different numbers in your browser, try:</p>";
        echo "<ul>";
        echo "<li>Clear browser cache and cookies</li>";
        echo "<li>Hard refresh the page (Ctrl+F5)</li>";
        echo "<li>Check if you have multiple browser tabs open</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<h3>5️⃣ Recommended Actions</h3>";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-left: 5px solid #007bff; margin: 10px 0;'>";
    echo "<h4 style='color: #004085;'>📋 Action Plan</h4>";
    
    if ($player_count > $db_count) {
        echo "<ol>";
        echo "<li><strong>Run the fix script:</strong> <a href='fix_server_sync.php' target='_blank'>fix_server_sync.php</a></li>";
        echo "<li><strong>Clear browser cache</strong> completely</li>";
        echo "<li><strong>Test the player</strong> with a movie</li>";
        echo "<li><strong>Use admin panel</strong> to manage servers going forward</li>";
        echo "</ol>";
    } else {
        echo "<ol>";
        echo "<li><strong>Clear browser cache</strong> and refresh</li>";
        echo "<li><strong>Check admin panel</strong> server settings</li>";
        echo "<li><strong>Verify server URLs</strong> are working</li>";
        echo "<li><strong>Test with different content</strong></li>";
        echo "</ol>";
    }
    echo "</div>";
    
    echo "<h3>6️⃣ Admin Panel Link</h3>";
    echo "<p>Manage your servers through the admin panel:</p>";
    echo "<p><a href='admin/servers.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 Open Server Management</a></p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-left: 5px solid #dc3545; margin: 10px 0;'>";
    echo "<h4 style='color: #721c24;'>❌ Database Connection Error</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration in <code>config/database.php</code></p>";
    echo "</div>";
}
?>
