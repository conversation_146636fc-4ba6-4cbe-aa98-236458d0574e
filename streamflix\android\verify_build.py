#!/usr/bin/env python3
"""
StreamFlix Android App Build Verification Script
This script verifies that all necessary files are present for building the Android app.
"""

import os
import sys
from pathlib import Path

def check_file_exists(file_path, description):
    """Check if a file exists and print status"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (MISSING)")
        return False

def check_directory_exists(dir_path, description):
    """Check if a directory exists and print status"""
    if os.path.isdir(dir_path):
        print(f"✅ {description}: {dir_path}")
        return True
    else:
        print(f"❌ {description}: {dir_path} (MISSING)")
        return False

def main():
    print("🎬 StreamFlix Android App Build Verification")
    print("=" * 50)
    
    # Change to android directory
    android_dir = Path(__file__).parent
    os.chdir(android_dir)
    
    all_good = True
    
    # Check essential build files
    print("\n📁 Build Configuration Files:")
    all_good &= check_file_exists("build.gradle", "Project build.gradle")
    all_good &= check_file_exists("settings.gradle", "Settings gradle")
    all_good &= check_file_exists("gradle.properties", "Gradle properties")
    all_good &= check_file_exists("app/build.gradle", "App build.gradle")
    
    # Check Gradle wrapper
    print("\n🔧 Gradle Wrapper:")
    all_good &= check_file_exists("gradlew", "Gradle wrapper (Unix)")
    all_good &= check_file_exists("gradlew.bat", "Gradle wrapper (Windows)")
    all_good &= check_file_exists("gradle/wrapper/gradle-wrapper.properties", "Wrapper properties")
    
    # Check app structure
    print("\n📱 App Structure:")
    all_good &= check_file_exists("app/src/main/AndroidManifest.xml", "Android Manifest")
    all_good &= check_directory_exists("app/src/main/java/com/streamflix/app", "Main source directory")
    all_good &= check_directory_exists("app/src/main/res", "Resources directory")
    
    # Check key source files
    print("\n🏗️ Key Source Files:")
    all_good &= check_file_exists("app/src/main/java/com/streamflix/app/StreamFlixApplication.kt", "Application class")
    all_good &= check_file_exists("app/src/main/java/com/streamflix/app/presentation/main/MainActivity.kt", "Main Activity")
    all_good &= check_file_exists("app/src/main/java/com/streamflix/app/presentation/splash/SplashActivity.kt", "Splash Activity")
    
    # Check data layer
    print("\n💾 Data Layer:")
    all_good &= check_file_exists("app/src/main/java/com/streamflix/app/data/api/StreamFlixApiService.kt", "API Service")
    all_good &= check_file_exists("app/src/main/java/com/streamflix/app/data/repository/StreamFlixRepository.kt", "Repository")
    all_good &= check_file_exists("app/src/main/java/com/streamflix/app/data/model/ApiResponse.kt", "Data Models")
    
    # Check UI layer
    print("\n🎨 UI Layer:")
    all_good &= check_file_exists("app/src/main/java/com/streamflix/app/ui/theme/Theme.kt", "Theme")
    all_good &= check_file_exists("app/src/main/java/com/streamflix/app/ui/theme/Color.kt", "Colors")
    all_good &= check_file_exists("app/src/main/java/com/streamflix/app/ui/theme/Type.kt", "Typography")
    
    # Check advanced features
    print("\n🚀 Advanced Features:")
    all_good &= check_file_exists("app/src/main/java/com/streamflix/app/presentation/player/AdvancedVideoPlayer.kt", "Video Player")
    all_good &= check_file_exists("app/src/main/java/com/streamflix/app/data/download/AdvancedDownloadManager.kt", "Download Manager")
    all_good &= check_file_exists("app/src/main/java/com/streamflix/app/data/recommendation/SmartRecommendationEngine.kt", "Recommendation Engine")
    all_good &= check_file_exists("app/src/main/java/com/streamflix/app/data/offline/OfflineModeManager.kt", "Offline Manager")
    
    # Check resources
    print("\n📦 Resources:")
    all_good &= check_file_exists("app/src/main/res/values/strings.xml", "Strings")
    all_good &= check_file_exists("app/src/main/res/values/colors.xml", "Colors")
    all_good &= check_file_exists("app/src/main/res/values/themes.xml", "Themes")
    
    # Check DI
    print("\n🔗 Dependency Injection:")
    all_good &= check_file_exists("app/src/main/java/com/streamflix/app/di/NetworkModule.kt", "Network Module")
    all_good &= check_file_exists("app/src/main/java/com/streamflix/app/di/DatabaseModule.kt", "Database Module")
    
    print("\n" + "=" * 50)
    
    if all_good:
        print("🎉 ALL FILES PRESENT! Ready to build!")
        print("\n📋 Next Steps:")
        print("1. Open Android Studio")
        print("2. Open this project folder")
        print("3. Wait for Gradle sync")
        print("4. Build > Make Project")
        print("5. Run on device/emulator")
        print("\n🔨 Command Line Build:")
        print("./gradlew assembleDebug")
        return 0
    else:
        print("⚠️  Some files are missing. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
