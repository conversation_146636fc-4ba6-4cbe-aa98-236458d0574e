<?php
require_once 'config/database.php';

echo "<h1>🔧 Complete Database Checker & Fixer</h1>";
echo "<p>Checking and fixing all database tables and columns for live site.</p>";

try {
    $db = new Database();
    $conn = $db->connect();
    
    // Get database name
    $stmt = $conn->query("SELECT DATABASE() as db_name");
    $db_info = $stmt->fetch(PDO::FETCH_ASSOC);
    $database_name = $db_info['db_name'];
    
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>📊 Database Information</h3>";
    echo "<p><strong>Database Name:</strong> {$database_name}</p>";
    echo "<p><strong>Connection:</strong> ✅ Successful</p>";
    echo "</div>";
    
    // Define required tables and their structures
    $required_tables = [
        'users' => [
            'id' => 'int(11) NOT NULL AUTO_INCREMENT',
            'username' => 'varchar(50) NOT NULL UNIQUE',
            'email' => 'varchar(100) NOT NULL UNIQUE',
            'password' => 'varchar(255) NOT NULL',
            'role' => "enum('user','admin') DEFAULT 'user'",
            'created_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            'PRIMARY KEY' => '(id)'
        ],
        'movies' => [
            'id' => 'int(11) NOT NULL AUTO_INCREMENT',
            'tmdb_id' => 'int(11) NOT NULL UNIQUE',
            'content_type' => "enum('movie','anime','documentary') DEFAULT 'movie'",
            'title' => 'varchar(255) NOT NULL',
            'overview' => 'text',
            'poster_path' => 'varchar(255)',
            'backdrop_path' => 'varchar(255)',
            'release_date' => 'date',
            'vote_average' => 'decimal(3,1) DEFAULT 0.0',
            'vote_count' => 'int(11) DEFAULT 0',
            'popularity' => 'decimal(8,3) DEFAULT 0.0',
            'runtime' => 'int(11)',
            'is_featured' => 'tinyint(1) DEFAULT 0',
            'is_trending' => 'tinyint(1) DEFAULT 0',
            'created_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            'PRIMARY KEY' => '(id)',
            'KEY tmdb_id' => '(tmdb_id)',
            'KEY content_type' => '(content_type)',
            'KEY is_featured' => '(is_featured)',
            'KEY is_trending' => '(is_trending)'
        ],
        'tv_shows' => [
            'id' => 'int(11) NOT NULL AUTO_INCREMENT',
            'tmdb_id' => 'int(11) NOT NULL UNIQUE',
            'content_type' => "enum('tv_show','anime','hentai','documentary') DEFAULT 'tv_show'",
            'name' => 'varchar(255) NOT NULL',
            'overview' => 'text',
            'poster_path' => 'varchar(255)',
            'backdrop_path' => 'varchar(255)',
            'first_air_date' => 'date',
            'last_air_date' => 'date',
            'vote_average' => 'decimal(3,1) DEFAULT 0.0',
            'vote_count' => 'int(11) DEFAULT 0',
            'popularity' => 'decimal(8,3) DEFAULT 0.0',
            'number_of_seasons' => 'int(11) DEFAULT 1',
            'number_of_episodes' => 'int(11) DEFAULT 1',
            'is_featured' => 'tinyint(1) DEFAULT 0',
            'is_trending' => 'tinyint(1) DEFAULT 0',
            'created_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            'PRIMARY KEY' => '(id)',
            'KEY tmdb_id' => '(tmdb_id)',
            'KEY content_type' => '(content_type)',
            'KEY is_featured' => '(is_featured)',
            'KEY is_trending' => '(is_trending)'
        ],
        'genres' => [
            'id' => 'int(11) NOT NULL AUTO_INCREMENT',
            'tmdb_id' => 'int(11) NOT NULL UNIQUE',
            'name' => 'varchar(100) NOT NULL',
            'created_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP',
            'PRIMARY KEY' => '(id)',
            'KEY tmdb_id' => '(tmdb_id)'
        ],
        'movie_genres' => [
            'id' => 'int(11) NOT NULL AUTO_INCREMENT',
            'movie_id' => 'int(11) NOT NULL',
            'genre_id' => 'int(11) NOT NULL',
            'PRIMARY KEY' => '(id)',
            'UNIQUE KEY movie_genre' => '(movie_id, genre_id)',
            'FOREIGN KEY movie_id' => 'REFERENCES movies(id) ON DELETE CASCADE',
            'FOREIGN KEY genre_id' => 'REFERENCES genres(id) ON DELETE CASCADE'
        ],
        'tv_show_genres' => [
            'id' => 'int(11) NOT NULL AUTO_INCREMENT',
            'tv_show_id' => 'int(11) NOT NULL',
            'genre_id' => 'int(11) NOT NULL',
            'PRIMARY KEY' => '(id)',
            'UNIQUE KEY tv_genre' => '(tv_show_id, genre_id)',
            'FOREIGN KEY tv_show_id' => 'REFERENCES tv_shows(id) ON DELETE CASCADE',
            'FOREIGN KEY genre_id' => 'REFERENCES genres(id) ON DELETE CASCADE'
        ],
        'embed_servers' => [
            'id' => 'int(11) NOT NULL AUTO_INCREMENT',
            'name' => 'varchar(100) NOT NULL',
            'movie_url' => 'text',
            'tv_url' => 'text',
            'anime_url' => 'text',
            'hentai_url' => 'text',
            'is_active' => 'tinyint(1) DEFAULT 1',
            'priority' => 'int(11) DEFAULT 1',
            'created_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            'PRIMARY KEY' => '(id)',
            'KEY is_active' => '(is_active)',
            'KEY priority' => '(priority)'
        ],
        'servers' => [
            'id' => 'int(11) NOT NULL AUTO_INCREMENT',
            'name' => 'varchar(100) NOT NULL',
            'url_pattern' => 'text NOT NULL',
            'type' => "enum('movie','tv','anime','hentai','all') DEFAULT 'all'",
            'is_active' => 'tinyint(1) DEFAULT 1',
            'priority' => 'int(11) DEFAULT 1',
            'created_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            'PRIMARY KEY' => '(id)',
            'KEY type' => '(type)',
            'KEY is_active' => '(is_active)',
            'KEY priority' => '(priority)'
        ],
        'comments' => [
            'id' => 'int(11) NOT NULL AUTO_INCREMENT',
            'content_id' => 'int(11) NOT NULL',
            'content_type' => "enum('movie','tv_show') NOT NULL",
            'user_name' => 'varchar(100) NOT NULL',
            'comment' => 'text NOT NULL',
            'rating' => 'int(1) DEFAULT 0',
            'likes' => 'int(11) DEFAULT 0',
            'dislikes' => 'int(11) DEFAULT 0',
            'created_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP',
            'PRIMARY KEY' => '(id)',
            'KEY content' => '(content_id, content_type)',
            'KEY created_at' => '(created_at)'
        ]
    ];
    
    echo "<h3>🔍 Checking Database Tables</h3>";
    
    // Get existing tables
    $stmt = $conn->query("SHOW TABLES");
    $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $missing_tables = [];
    $existing_table_status = [];
    
    foreach ($required_tables as $table_name => $columns) {
        if (in_array($table_name, $existing_tables)) {
            $existing_table_status[$table_name] = 'exists';
        } else {
            $missing_tables[] = $table_name;
            $existing_table_status[$table_name] = 'missing';
        }
    }
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>📋 Table Status:</h4>";
    foreach ($existing_table_status as $table => $status) {
        $icon = $status === 'exists' ? '✅' : '❌';
        $color = $status === 'exists' ? '#28a745' : '#dc3545';
        echo "<div style='margin: 5px 0; color: {$color};'>{$icon} <strong>{$table}</strong> - " . ucfirst($status) . "</div>";
    }
    echo "</div>";
    
    // Create missing tables
    if (!empty($missing_tables)) {
        echo "<h3>🔨 Creating Missing Tables</h3>";
        
        foreach ($missing_tables as $table_name) {
            echo "<h4>Creating table: {$table_name}</h4>";
            
            $columns = $required_tables[$table_name];
            $create_sql = "CREATE TABLE `{$table_name}` (\n";
            
            $column_definitions = [];
            $keys = [];
            
            foreach ($columns as $column_name => $definition) {
                if (strpos($column_name, 'PRIMARY KEY') === 0) {
                    $keys[] = "PRIMARY KEY {$definition}";
                } elseif (strpos($column_name, 'KEY ') === 0) {
                    $key_name = str_replace('KEY ', '', $column_name);
                    $keys[] = "KEY `{$key_name}` {$definition}";
                } elseif (strpos($column_name, 'UNIQUE KEY') === 0) {
                    $key_name = str_replace('UNIQUE KEY ', '', $column_name);
                    $keys[] = "UNIQUE KEY `{$key_name}` {$definition}";
                } elseif (strpos($column_name, 'FOREIGN KEY') === 0) {
                    $key_name = str_replace('FOREIGN KEY ', '', $column_name);
                    $keys[] = "FOREIGN KEY (`{$key_name}`) {$definition}";
                } else {
                    $column_definitions[] = "`{$column_name}` {$definition}";
                }
            }
            
            $create_sql .= implode(",\n", array_merge($column_definitions, $keys));
            $create_sql .= "\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
            
            try {
                $conn->exec($create_sql);
                echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                echo "✅ <strong>Successfully created table: {$table_name}</strong>";
                echo "</div>";
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                echo "❌ <strong>Error creating table {$table_name}:</strong> " . $e->getMessage();
                echo "</div>";
            }
        }
    }
    
    echo "<h3>🔍 Checking Table Columns</h3>";
    
    // Check columns for existing tables
    $column_issues = [];
    
    foreach ($required_tables as $table_name => $required_columns) {
        if (in_array($table_name, $existing_tables) || in_array($table_name, $missing_tables)) {
            try {
                $stmt = $conn->query("SHOW COLUMNS FROM `{$table_name}`");
                $existing_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                $existing_column_names = array_column($existing_columns, 'Field');
                
                foreach ($required_columns as $column_name => $definition) {
                    if (!in_array($column_name, ['PRIMARY KEY', 'KEY', 'UNIQUE KEY', 'FOREIGN KEY']) && 
                        !in_array($column_name, $existing_column_names) &&
                        strpos($column_name, 'KEY ') !== 0 &&
                        strpos($column_name, 'FOREIGN KEY') !== 0) {
                        
                        $column_issues[] = [
                            'table' => $table_name,
                            'column' => $column_name,
                            'definition' => $definition,
                            'action' => 'add'
                        ];
                    }
                }
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                echo "❌ <strong>Error checking columns for {$table_name}:</strong> " . $e->getMessage();
                echo "</div>";
            }
        }
    }
    
    // Fix column issues
    if (!empty($column_issues)) {
        echo "<h4>🔧 Adding Missing Columns</h4>";
        
        foreach ($column_issues as $issue) {
            $alter_sql = "ALTER TABLE `{$issue['table']}` ADD COLUMN `{$issue['column']}` {$issue['definition']}";
            
            try {
                $conn->exec($alter_sql);
                echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                echo "✅ <strong>Added column {$issue['column']} to {$issue['table']}</strong>";
                echo "</div>";
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                echo "❌ <strong>Error adding column {$issue['column']} to {$issue['table']}:</strong> " . $e->getMessage();
                echo "</div>";
            }
        }
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "✅ <strong>All required columns exist!</strong>";
        echo "</div>";
    }
    
    // Insert default data
    echo "<h3>📥 Inserting Default Data</h3>";

    // Default embed servers
    $stmt = $conn->query("SELECT COUNT(*) FROM embed_servers");
    $server_count = $stmt->fetchColumn();

    if ($server_count == 0) {
        echo "<h4>🖥️ Adding Default Embed Servers</h4>";

        $default_servers = [
            [
                'name' => 'AutoEmbed',
                'movie_url' => 'https://player.autoembed.cc/embed/movie/{id}',
                'tv_url' => 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}',
                'anime_url' => 'https://player.autoembed.cc/embed/movie/{id}',
                'hentai_url' => 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}',
                'priority' => 1
            ],
            [
                'name' => 'VidSrc',
                'movie_url' => 'https://vidsrc.to/embed/movie/{id}',
                'tv_url' => 'https://vidsrc.to/embed/tv/{id}/{season}/{episode}',
                'anime_url' => 'https://vidsrc.to/embed/movie/{id}',
                'hentai_url' => 'https://vidsrc.to/embed/tv/{id}/{season}/{episode}',
                'priority' => 2
            ],
            [
                'name' => 'SuperEmbed',
                'movie_url' => 'https://multiembed.mov/directstream.php?video_id={id}',
                'tv_url' => 'https://multiembed.mov/directstream.php?video_id={id}&s={season}&e={episode}',
                'anime_url' => 'https://multiembed.mov/directstream.php?video_id={id}',
                'hentai_url' => 'https://multiembed.mov/directstream.php?video_id={id}&s={season}&e={episode}',
                'priority' => 3
            ]
        ];

        $insert_stmt = $conn->prepare("
            INSERT INTO embed_servers (name, movie_url, tv_url, anime_url, hentai_url, is_active, priority)
            VALUES (?, ?, ?, ?, ?, 1, ?)
        ");

        $inserted = 0;
        foreach ($default_servers as $server) {
            try {
                $insert_stmt->execute([
                    $server['name'],
                    $server['movie_url'],
                    $server['tv_url'],
                    $server['anime_url'],
                    $server['hentai_url'],
                    $server['priority']
                ]);
                $inserted++;
                echo "<div style='background: #d4edda; padding: 8px; border-radius: 4px; margin: 3px 0;'>";
                echo "✅ Added server: {$server['name']}";
                echo "</div>";
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 8px; border-radius: 4px; margin: 3px 0;'>";
                echo "❌ Error adding {$server['name']}: " . $e->getMessage();
                echo "</div>";
            }
        }

        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "✅ <strong>Added {$inserted} default embed servers</strong>";
        echo "</div>";
    }

    // Default servers table data
    $stmt = $conn->query("SELECT COUNT(*) FROM servers");
    $legacy_server_count = $stmt->fetchColumn();

    if ($legacy_server_count == 0) {
        echo "<h4>🖥️ Adding Default Legacy Servers</h4>";

        $legacy_servers = [
            ['name' => 'AutoEmbed Movie', 'url_pattern' => 'https://player.autoembed.cc/embed/movie/{id}', 'type' => 'movie', 'priority' => 1],
            ['name' => 'AutoEmbed TV', 'url_pattern' => 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}', 'type' => 'tv', 'priority' => 1],
            ['name' => 'VidSrc Movie', 'url_pattern' => 'https://vidsrc.to/embed/movie/{id}', 'type' => 'movie', 'priority' => 2],
            ['name' => 'VidSrc TV', 'url_pattern' => 'https://vidsrc.to/embed/tv/{id}/{season}/{episode}', 'type' => 'tv', 'priority' => 2],
            ['name' => 'Anime Server', 'url_pattern' => 'https://2anime.xyz/embed/{id}', 'type' => 'anime', 'priority' => 1],
            ['name' => 'Hentai Server', 'url_pattern' => 'https://hentaistream.com/embed/{id}', 'type' => 'hentai', 'priority' => 1]
        ];

        $insert_legacy_stmt = $conn->prepare("
            INSERT INTO servers (name, url_pattern, type, is_active, priority)
            VALUES (?, ?, ?, 1, ?)
        ");

        $inserted_legacy = 0;
        foreach ($legacy_servers as $server) {
            try {
                $insert_legacy_stmt->execute([
                    $server['name'],
                    $server['url_pattern'],
                    $server['type'],
                    $server['priority']
                ]);
                $inserted_legacy++;
                echo "<div style='background: #d4edda; padding: 8px; border-radius: 4px; margin: 3px 0;'>";
                echo "✅ Added legacy server: {$server['name']}";
                echo "</div>";
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 8px; border-radius: 4px; margin: 3px 0;'>";
                echo "❌ Error adding {$server['name']}: " . $e->getMessage();
                echo "</div>";
            }
        }

        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "✅ <strong>Added {$inserted_legacy} default legacy servers</strong>";
        echo "</div>";
    }

    // Create admin user if no users exist
    $stmt = $conn->query("SELECT COUNT(*) FROM users");
    $user_count = $stmt->fetchColumn();

    if ($user_count == 0) {
        echo "<h4>👤 Creating Default Admin User</h4>";

        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $insert_user_stmt = $conn->prepare("
            INSERT INTO users (username, email, password, role)
            VALUES ('admin', '<EMAIL>', ?, 'admin')
        ");

        try {
            $insert_user_stmt->execute([$admin_password]);
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
            echo "✅ <strong>Created admin user</strong><br>";
            echo "Username: admin<br>";
            echo "Password: admin123<br>";
            echo "Email: <EMAIL>";
            echo "</div>";
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
            echo "❌ <strong>Error creating admin user:</strong> " . $e->getMessage();
            echo "</div>";
        }
    }

    // Final status check
    echo "<h3>✅ Final Database Status</h3>";

    $final_stats = [];
    foreach (array_keys($required_tables) as $table) {
        try {
            $stmt = $conn->query("SELECT COUNT(*) FROM `{$table}`");
            $count = $stmt->fetchColumn();
            $final_stats[$table] = $count;
        } catch (Exception $e) {
            $final_stats[$table] = 'Error: ' . $e->getMessage();
        }
    }

    echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>📊 Table Statistics:</h4>";
    foreach ($final_stats as $table => $count) {
        $display_count = is_numeric($count) ? number_format($count) . ' records' : $count;
        echo "<div style='margin: 5px 0;'><strong>{$table}:</strong> {$display_count}</div>";
    }
    echo "</div>";

    echo "<h3>🔗 Test Links</h3>";

    echo "<div style='background: #cff4fc; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🎯 Test Your Site:</h4>";

    echo "<p><a href='index.php' target='_blank' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin-right: 10px; display: inline-block; margin-bottom: 10px;'>🏠 Homepage</a></p>";

    echo "<p><a href='admin/index.php' target='_blank' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin-right: 10px; display: inline-block; margin-bottom: 10px;'>📊 Admin Panel</a></p>";

    echo "<p><a href='admin/servers.php' target='_blank' style='background: #17a2b8; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin-right: 10px; display: inline-block; margin-bottom: 10px;'>🖥️ Server Management</a></p>";

    echo "<p><a href='admin/import.php' target='_blank' style='background: #ffc107; color: #212529; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block; margin-bottom: 10px;'>📥 Import System</a></p>";
    echo "</div>";

    echo "<h3>⚠️ Important Notes</h3>";

    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>📝 What Was Fixed:</h4>";
    echo "<ul>";
    echo "<li>✅ <strong>All Required Tables:</strong> Created missing tables</li>";
    echo "<li>✅ <strong>All Required Columns:</strong> Added missing columns</li>";
    echo "<li>✅ <strong>Default Servers:</strong> Added embed and legacy servers</li>";
    echo "<li>✅ <strong>Admin User:</strong> Created default admin account</li>";
    echo "<li>✅ <strong>Database Structure:</strong> Complete and ready to use</li>";
    echo "</ul>";

    echo "<h4>🔐 Default Login:</h4>";
    echo "<ul>";
    echo "<li><strong>Username:</strong> admin</li>";
    echo "<li><strong>Password:</strong> admin123</li>";
    echo "<li><strong>Email:</strong> <EMAIL></li>";
    echo "</ul>";

    echo "<p><strong>🔒 Security:</strong> Please change the admin password after first login!</p>";
    echo "</div>";

    echo "<hr>";
    echo "<p><strong>🎉 Database Setup Complete!</strong></p>";
    echo "<p>Your live site database is now fully configured and ready to use.</p>";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4 style='color: #721c24;'>❌ Database Connection Error</h4>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
    exit;
}
?>
