<?php
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? '';

try {
    $db = new Database();
    $conn = $db->connect();
    
    switch ($action) {
        case 'backup':
            $backup_file = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
            $backup_path = '../backups/' . $backup_file;
            
            // Create backups directory if it doesn't exist
            if (!is_dir('../backups')) {
                mkdir('../backups', 0755, true);
            }
            
            // Get all tables
            $tables = [];
            $result = $conn->query("SHOW TABLES");
            while ($row = $result->fetch(PDO::FETCH_NUM)) {
                $tables[] = $row[0];
            }
            
            $backup_content = "-- StreamFlix Database Backup\n";
            $backup_content .= "-- Generated on " . date('Y-m-d H:i:s') . "\n\n";
            $backup_content .= "SET FOREIGN_KEY_CHECKS=0;\n\n";
            
            foreach ($tables as $table) {
                // Get table structure
                $result = $conn->query("SHOW CREATE TABLE `$table`");
                $row = $result->fetch(PDO::FETCH_NUM);
                $backup_content .= "DROP TABLE IF EXISTS `$table`;\n";
                $backup_content .= $row[1] . ";\n\n";
                
                // Get table data
                $result = $conn->query("SELECT * FROM `$table`");
                while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
                    $columns = array_keys($row);
                    $values = array_map(function($value) use ($conn) {
                        return $value === null ? 'NULL' : $conn->quote($value);
                    }, array_values($row));
                    
                    $backup_content .= "INSERT INTO `$table` (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $values) . ");\n";
                }
                $backup_content .= "\n";
            }
            
            $backup_content .= "SET FOREIGN_KEY_CHECKS=1;\n";
            
            file_put_contents($backup_path, $backup_content);
            
            echo json_encode([
                'success' => true,
                'message' => 'Database backup created successfully',
                'file' => $backup_file
            ]);
            break;
            
        case 'optimize':
            $tables = [];
            $result = $conn->query("SHOW TABLES");
            while ($row = $result->fetch(PDO::FETCH_NUM)) {
                $tables[] = $row[0];
            }
            
            $optimized = 0;
            foreach ($tables as $table) {
                $conn->exec("OPTIMIZE TABLE `$table`");
                $optimized++;
            }
            
            echo json_encode([
                'success' => true,
                'message' => "Optimized $optimized tables successfully"
            ]);
            break;
            
        case 'clear_cache':
            $cache_dirs = ['../cache', '../tmp'];
            $cleared = 0;
            
            foreach ($cache_dirs as $dir) {
                if (is_dir($dir)) {
                    $files = glob($dir . '/*');
                    foreach ($files as $file) {
                        if (is_file($file)) {
                            unlink($file);
                            $cleared++;
                        }
                    }
                }
            }
            
            echo json_encode([
                'success' => true,
                'message' => "Cleared $cleared cache files"
            ]);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
