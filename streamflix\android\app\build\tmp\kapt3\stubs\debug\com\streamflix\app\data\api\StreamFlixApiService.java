package com.streamflix.app.data.api;

import com.streamflix.app.data.model.*;
import retrofit2.Response;
import retrofit2.http.*;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00f4\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J$\u0010\u0002\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00010\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\u0007J$\u0010\b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00010\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ$\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00010\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\rJ$\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00010\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0010J$\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00120\u00040\u00032\b\b\u0001\u0010\u0013\u001a\u00020\u0014H\u00a7@\u00a2\u0006\u0002\u0010\u0015J\u001a\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00170\u00040\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0018J\u001a\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001a0\u00040\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0018J\u001a\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001c0\u00040\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0018J\u001a\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001e0\u00040\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0018J$\u0010\u001f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020 0\u00040\u00032\b\b\u0003\u0010!\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u0010#J$\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020%0\u00040\u00032\b\b\u0003\u0010!\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u0010#J\u001a\u0010&\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\'0\u00040\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0018J$\u0010(\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020 0\u00040\u00032\b\b\u0003\u0010!\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u0010#J$\u0010)\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020%0\u00040\u00032\b\b\u0003\u0010!\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u0010#J$\u0010*\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020+0\u00040\u00032\b\b\u0001\u0010,\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u0010#J\u001a\u0010-\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00170\u00040\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0018J$\u0010.\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020/0\u00040\u00032\b\b\u0001\u0010,\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u0010#J.\u00100\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002010\u00040\u00032\b\b\u0003\u00102\u001a\u00020\"2\b\b\u0003\u0010!\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u00103J8\u00104\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002010\u00040\u00032\b\b\u0001\u00105\u001a\u00020\"2\b\b\u0003\u00102\u001a\u00020\"2\b\b\u0003\u0010!\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u00106J$\u00107\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020 0\u00040\u00032\b\b\u0003\u0010!\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u0010#J$\u00108\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020%0\u00040\u00032\b\b\u0003\u0010!\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u0010#J\u001a\u00109\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020:0\u00040\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0018J\u001a\u0010;\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020/0\u00040\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0018J$\u0010<\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020 0\u00040\u00032\b\b\u0003\u0010!\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u0010#J$\u0010=\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020%0\u00040\u00032\b\b\u0003\u0010!\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u0010#J$\u0010>\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020?0\u00040\u00032\b\b\u0001\u0010@\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u0010#J$\u0010A\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020B0\u00040\u00032\b\b\u0001\u0010C\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u0010#J\u001a\u0010D\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00170\u00040\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0018J$\u0010E\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020F0\u00040\u00032\b\b\u0001\u0010@\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u0010#J.\u0010G\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020H0\u00040\u00032\b\b\u0003\u00102\u001a\u00020\"2\b\b\u0003\u0010!\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u00103J8\u0010I\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020H0\u00040\u00032\b\b\u0001\u00105\u001a\u00020\"2\b\b\u0003\u00102\u001a\u00020\"2\b\b\u0003\u0010!\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u00106J\u001a\u0010J\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020K0\u00040\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0018J.\u0010L\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020M0\u00040\u00032\b\b\u0003\u00102\u001a\u00020\"2\b\b\u0003\u0010!\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u00103J.\u0010N\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020O0\u00040\u00032\b\b\u0001\u0010P\u001a\u00020\u00142\b\b\u0003\u0010!\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u0010QJ$\u0010R\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020S0\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020TH\u00a7@\u00a2\u0006\u0002\u0010UJ\u001a\u0010V\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020S0\u00040\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0018J$\u0010W\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020S0\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020XH\u00a7@\u00a2\u0006\u0002\u0010YJ$\u0010Z\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00010\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020[H\u00a7@\u00a2\u0006\u0002\u0010\\J$\u0010]\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00010\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020^H\u00a7@\u00a2\u0006\u0002\u0010_J8\u0010`\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002010\u00040\u00032\b\b\u0001\u0010P\u001a\u00020\u00142\b\b\u0003\u00102\u001a\u00020\"2\b\b\u0003\u0010!\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u0010aJ8\u0010b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020H0\u00040\u00032\b\b\u0001\u0010P\u001a\u00020\u00142\b\b\u0003\u00102\u001a\u00020\"2\b\b\u0003\u0010!\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u0010aJ$\u0010c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020:0\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020dH\u00a7@\u00a2\u0006\u0002\u0010e\u00a8\u0006f"}, d2 = {"Lcom/streamflix/app/data/api/StreamFlixApiService;", "", "addToFavorites", "Lretrofit2/Response;", "Lcom/streamflix/app/data/model/ApiResponse;", "request", "Lcom/streamflix/app/data/api/AddToFavoritesRequest;", "(Lcom/streamflix/app/data/api/AddToFavoritesRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addToWatchHistory", "Lcom/streamflix/app/data/api/AddToWatchHistoryRequest;", "(Lcom/streamflix/app/data/api/AddToWatchHistoryRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addToWatchlist", "Lcom/streamflix/app/data/api/AddToWatchlistRequest;", "(Lcom/streamflix/app/data/api/AddToWatchlistRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "changePassword", "Lcom/streamflix/app/data/api/ChangePasswordRequest;", "(Lcom/streamflix/app/data/api/ChangePasswordRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "checkAppVersion", "Lcom/streamflix/app/data/model/AppVersion;", "clientVersion", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllGenres", "Lcom/streamflix/app/data/api/GenresResponse;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAppConfig", "Lcom/streamflix/app/data/api/AppConfig;", "getContinueWatching", "Lcom/streamflix/app/data/api/ContinueWatchingResponse;", "getFavorites", "Lcom/streamflix/app/data/api/FavoritesResponse;", "getFeaturedMovies", "Lcom/streamflix/app/data/api/MoviesResponse;", "limit", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getFeaturedTvShows", "Lcom/streamflix/app/data/api/TvShowsResponse;", "getHomeData", "Lcom/streamflix/app/data/model/HomeData;", "getLatestMovies", "getLatestTvShows", "getMovieDetails", "Lcom/streamflix/app/data/api/MovieDetailsResponse;", "movieId", "getMovieGenres", "getMovieServers", "Lcom/streamflix/app/data/api/ServersResponse;", "getMovies", "Lcom/streamflix/app/data/api/MovieListResponse;", "page", "(IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getMoviesByGenre", "genreId", "(IIILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPopularMovies", "getPopularTvShows", "getProfile", "Lcom/streamflix/app/data/model/User;", "getServersList", "getTrendingMovies", "getTrendingTvShows", "getTvShowDetails", "Lcom/streamflix/app/data/api/TvShowDetailsResponse;", "tvShowId", "getTvShowEpisodes", "Lcom/streamflix/app/data/api/EpisodesResponse;", "seasonId", "getTvShowGenres", "getTvShowSeasons", "Lcom/streamflix/app/data/api/SeasonsResponse;", "getTvShows", "Lcom/streamflix/app/data/api/TvShowListResponse;", "getTvShowsByGenre", "getWatchHistory", "Lcom/streamflix/app/data/api/WatchHistoryResponse;", "getWatchlist", "Lcom/streamflix/app/data/api/WatchlistResponse;", "globalSearch", "Lcom/streamflix/app/data/model/SearchResult;", "query", "(Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "login", "Lcom/streamflix/app/data/model/AuthResponse;", "Lcom/streamflix/app/data/api/LoginRequest;", "(Lcom/streamflix/app/data/api/LoginRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "refreshToken", "register", "Lcom/streamflix/app/data/api/RegisterRequest;", "(Lcom/streamflix/app/data/api/RegisterRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "removeFromFavorites", "Lcom/streamflix/app/data/api/RemoveFromFavoritesRequest;", "(Lcom/streamflix/app/data/api/RemoveFromFavoritesRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "removeFromWatchlist", "Lcom/streamflix/app/data/api/RemoveFromWatchlistRequest;", "(Lcom/streamflix/app/data/api/RemoveFromWatchlistRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchMovies", "(Ljava/lang/String;IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchTvShows", "updateProfile", "Lcom/streamflix/app/data/api/UpdateProfileRequest;", "(Lcom/streamflix/app/data/api/UpdateProfileRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface StreamFlixApiService {
    
    @retrofit2.http.POST(value = "auth.php?action=login")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object login(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.api.LoginRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.model.AuthResponse>>> $completion);
    
    @retrofit2.http.POST(value = "auth.php?action=register")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object register(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.api.RegisterRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.model.AuthResponse>>> $completion);
    
    @retrofit2.http.POST(value = "auth.php?action=refresh")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object refreshToken(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.model.AuthResponse>>> $completion);
    
    @retrofit2.http.GET(value = "auth.php?action=profile")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getProfile(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.model.User>>> $completion);
    
    @retrofit2.http.PUT(value = "auth.php?action=update-profile")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateProfile(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.api.UpdateProfileRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.model.User>>> $completion);
    
    @retrofit2.http.PUT(value = "auth.php?action=change-password")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object changePassword(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.api.ChangePasswordRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<java.lang.Object>>> $completion);
    
    @retrofit2.http.GET(value = "movies.php?action=list")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getMovies(@retrofit2.http.Query(value = "page")
    int page, @retrofit2.http.Query(value = "limit")
    int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.MovieListResponse>>> $completion);
    
    @retrofit2.http.GET(value = "movies.php?action=featured")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getFeaturedMovies(@retrofit2.http.Query(value = "limit")
    int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.MoviesResponse>>> $completion);
    
    @retrofit2.http.GET(value = "movies.php?action=trending")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTrendingMovies(@retrofit2.http.Query(value = "limit")
    int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.MoviesResponse>>> $completion);
    
    @retrofit2.http.GET(value = "movies.php?action=popular")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPopularMovies(@retrofit2.http.Query(value = "limit")
    int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.MoviesResponse>>> $completion);
    
    @retrofit2.http.GET(value = "movies.php?action=latest")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLatestMovies(@retrofit2.http.Query(value = "limit")
    int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.MoviesResponse>>> $completion);
    
    @retrofit2.http.GET(value = "movies.php?action=details")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getMovieDetails(@retrofit2.http.Query(value = "id")
    int movieId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.MovieDetailsResponse>>> $completion);
    
    @retrofit2.http.GET(value = "movies.php?action=search")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object searchMovies(@retrofit2.http.Query(value = "q")
    @org.jetbrains.annotations.NotNull()
    java.lang.String query, @retrofit2.http.Query(value = "page")
    int page, @retrofit2.http.Query(value = "limit")
    int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.MovieListResponse>>> $completion);
    
    @retrofit2.http.GET(value = "movies.php?action=genres")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getMovieGenres(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.GenresResponse>>> $completion);
    
    @retrofit2.http.GET(value = "movies.php?action=by-genre")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getMoviesByGenre(@retrofit2.http.Query(value = "genre_id")
    int genreId, @retrofit2.http.Query(value = "page")
    int page, @retrofit2.http.Query(value = "limit")
    int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.MovieListResponse>>> $completion);
    
    @retrofit2.http.GET(value = "movies.php?action=servers")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getMovieServers(@retrofit2.http.Query(value = "id")
    int movieId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.ServersResponse>>> $completion);
    
    @retrofit2.http.GET(value = "tv-shows.php?action=list")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTvShows(@retrofit2.http.Query(value = "page")
    int page, @retrofit2.http.Query(value = "limit")
    int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.TvShowListResponse>>> $completion);
    
    @retrofit2.http.GET(value = "tv-shows.php?action=featured")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getFeaturedTvShows(@retrofit2.http.Query(value = "limit")
    int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.TvShowsResponse>>> $completion);
    
    @retrofit2.http.GET(value = "tv-shows.php?action=trending")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTrendingTvShows(@retrofit2.http.Query(value = "limit")
    int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.TvShowsResponse>>> $completion);
    
    @retrofit2.http.GET(value = "tv-shows.php?action=popular")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPopularTvShows(@retrofit2.http.Query(value = "limit")
    int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.TvShowsResponse>>> $completion);
    
    @retrofit2.http.GET(value = "tv-shows.php?action=latest")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLatestTvShows(@retrofit2.http.Query(value = "limit")
    int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.TvShowsResponse>>> $completion);
    
    @retrofit2.http.GET(value = "tv-shows.php?action=details")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTvShowDetails(@retrofit2.http.Query(value = "id")
    int tvShowId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.TvShowDetailsResponse>>> $completion);
    
    @retrofit2.http.GET(value = "tv-shows.php?action=seasons")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTvShowSeasons(@retrofit2.http.Query(value = "id")
    int tvShowId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.SeasonsResponse>>> $completion);
    
    @retrofit2.http.GET(value = "tv-shows.php?action=episodes")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTvShowEpisodes(@retrofit2.http.Query(value = "season_id")
    int seasonId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.EpisodesResponse>>> $completion);
    
    @retrofit2.http.GET(value = "tv-shows.php?action=search")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object searchTvShows(@retrofit2.http.Query(value = "q")
    @org.jetbrains.annotations.NotNull()
    java.lang.String query, @retrofit2.http.Query(value = "page")
    int page, @retrofit2.http.Query(value = "limit")
    int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.TvShowListResponse>>> $completion);
    
    @retrofit2.http.GET(value = "tv-shows.php?action=genres")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTvShowGenres(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.GenresResponse>>> $completion);
    
    @retrofit2.http.GET(value = "tv-shows.php?action=by-genre")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTvShowsByGenre(@retrofit2.http.Query(value = "genre_id")
    int genreId, @retrofit2.http.Query(value = "page")
    int page, @retrofit2.http.Query(value = "limit")
    int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.TvShowListResponse>>> $completion);
    
    @retrofit2.http.GET(value = "user.php?action=watchlist")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getWatchlist(@retrofit2.http.Query(value = "page")
    int page, @retrofit2.http.Query(value = "limit")
    int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.WatchlistResponse>>> $completion);
    
    @retrofit2.http.POST(value = "user.php?action=add-to-watchlist")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addToWatchlist(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.api.AddToWatchlistRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<java.lang.Object>>> $completion);
    
    @retrofit2.http.HTTP(method = "DELETE", path = "user.php?action=remove-from-watchlist", hasBody = true)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object removeFromWatchlist(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.api.RemoveFromWatchlistRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<java.lang.Object>>> $completion);
    
    @retrofit2.http.GET(value = "user.php?action=favorites")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getFavorites(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.FavoritesResponse>>> $completion);
    
    @retrofit2.http.POST(value = "user.php?action=add-to-favorites")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addToFavorites(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.api.AddToFavoritesRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<java.lang.Object>>> $completion);
    
    @retrofit2.http.HTTP(method = "DELETE", path = "user.php?action=remove-from-favorites", hasBody = true)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object removeFromFavorites(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.api.RemoveFromFavoritesRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<java.lang.Object>>> $completion);
    
    @retrofit2.http.GET(value = "user.php?action=watch-history")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getWatchHistory(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.WatchHistoryResponse>>> $completion);
    
    @retrofit2.http.POST(value = "user.php?action=add-to-history")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addToWatchHistory(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.api.AddToWatchHistoryRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<java.lang.Object>>> $completion);
    
    @retrofit2.http.GET(value = "user.php?action=continue-watching")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getContinueWatching(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.ContinueWatchingResponse>>> $completion);
    
    @retrofit2.http.GET(value = "app.php?action=version")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object checkAppVersion(@retrofit2.http.Query(value = "client_version")
    @org.jetbrains.annotations.NotNull()
    java.lang.String clientVersion, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.model.AppVersion>>> $completion);
    
    @retrofit2.http.GET(value = "app.php?action=config")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAppConfig(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.AppConfig>>> $completion);
    
    @retrofit2.http.GET(value = "app.php?action=search")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object globalSearch(@retrofit2.http.Query(value = "q")
    @org.jetbrains.annotations.NotNull()
    java.lang.String query, @retrofit2.http.Query(value = "limit")
    int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.model.SearchResult>>> $completion);
    
    @retrofit2.http.GET(value = "app.php?action=home")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getHomeData(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.model.HomeData>>> $completion);
    
    @retrofit2.http.GET(value = "app.php?action=genres")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllGenres(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.GenresResponse>>> $completion);
    
    @retrofit2.http.GET(value = "app.php?action=servers")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getServersList(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.streamflix.app.data.model.ApiResponse<com.streamflix.app.data.api.ServersResponse>>> $completion);
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}