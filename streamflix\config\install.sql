-- StreamFlix Database Schema
CREATE DATABASE IF NOT EXISTS streamflix;
USE streamflix;

-- Users Table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'user') DEFAULT 'user',
    subscription_type ENUM('free', 'premium') DEFAULT 'free',
    subscription_expires DATE NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Movies Table
CREATE TABLE movies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tmdb_id INT UNIQUE NOT NULL,
    title VARCHAR(255) NOT NULL,
    original_title VARCHAR(255),
    overview TEXT,
    poster_path VARCHAR(255),
    backdrop_path VARCHAR(255),
    release_date DATE,
    runtime INT,
    vote_average DECIMAL(3,1),
    vote_count INT,
    popularity DECIMAL(8,3),
    adult BOOLEAN DEFAULT FALSE,
    video BOOLEAN DEFAULT FALSE,
    original_language VARCHAR(10),
    status VARCHAR(50),
    tagline TEXT,
    budget BIGINT,
    revenue BIGINT,
    imdb_id VARCHAR(20),
    homepage VARCHAR(255),
    is_featured BOOLEAN DEFAULT FALSE,
    is_trending BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- TV Shows Table
CREATE TABLE tv_shows (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tmdb_id INT UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255),
    overview TEXT,
    poster_path VARCHAR(255),
    backdrop_path VARCHAR(255),
    first_air_date DATE,
    last_air_date DATE,
    vote_average DECIMAL(3,1),
    vote_count INT,
    popularity DECIMAL(8,3),
    adult BOOLEAN DEFAULT FALSE,
    original_language VARCHAR(10),
    status VARCHAR(50),
    tagline TEXT,
    homepage VARCHAR(255),
    number_of_episodes INT,
    number_of_seasons INT,
    is_featured BOOLEAN DEFAULT FALSE,
    is_trending BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Seasons Table
CREATE TABLE seasons (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tv_show_id INT NOT NULL,
    tmdb_id INT NOT NULL,
    season_number INT NOT NULL,
    name VARCHAR(255),
    overview TEXT,
    poster_path VARCHAR(255),
    air_date DATE,
    episode_count INT,
    FOREIGN KEY (tv_show_id) REFERENCES tv_shows(id) ON DELETE CASCADE,
    UNIQUE KEY unique_season (tv_show_id, season_number)
);

-- Episodes Table
CREATE TABLE episodes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    season_id INT NOT NULL,
    tmdb_id INT NOT NULL,
    episode_number INT NOT NULL,
    name VARCHAR(255),
    overview TEXT,
    still_path VARCHAR(255),
    air_date DATE,
    runtime INT,
    vote_average DECIMAL(3,1),
    vote_count INT,
    FOREIGN KEY (season_id) REFERENCES seasons(id) ON DELETE CASCADE,
    UNIQUE KEY unique_episode (season_id, episode_number)
);

-- Genres Table
CREATE TABLE genres (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tmdb_id INT UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL
);

-- Movie Genres Junction Table
CREATE TABLE movie_genres (
    movie_id INT NOT NULL,
    genre_id INT NOT NULL,
    PRIMARY KEY (movie_id, genre_id),
    FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
    FOREIGN KEY (genre_id) REFERENCES genres(id) ON DELETE CASCADE
);

-- TV Show Genres Junction Table
CREATE TABLE tv_show_genres (
    tv_show_id INT NOT NULL,
    genre_id INT NOT NULL,
    PRIMARY KEY (tv_show_id, genre_id),
    FOREIGN KEY (tv_show_id) REFERENCES tv_shows(id) ON DELETE CASCADE,
    FOREIGN KEY (genre_id) REFERENCES genres(id) ON DELETE CASCADE
);

-- Embed Servers Table
CREATE TABLE embed_servers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    movie_url_template VARCHAR(255) NOT NULL,
    tv_url_template VARCHAR(255) NOT NULL,
    priority INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User Watchlist Table
CREATE TABLE watchlist (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    content_type ENUM('movie', 'tv_show') NOT NULL,
    content_id INT NOT NULL,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_watchlist (user_id, content_type, content_id)
);

-- User Watch History Table
CREATE TABLE watch_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    content_type ENUM('movie', 'tv_show') NOT NULL,
    content_id INT NOT NULL,
    episode_id INT NULL,
    watch_time INT DEFAULT 0,
    total_time INT DEFAULT 0,
    completed BOOLEAN DEFAULT FALSE,
    last_watched TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (episode_id) REFERENCES episodes(id) ON DELETE SET NULL
);

-- Site Settings Table
CREATE TABLE site_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert Default Admin User
INSERT INTO users (username, email, password, role) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');

-- Insert Default Embed Servers
INSERT INTO embed_servers (name, movie_url_template, tv_url_template, priority) VALUES
('AutoEmbed', 'https://player.autoembed.cc/embed/movie/{id}', 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}', 1),
('VidJoy', 'https://vidjoy.pro/embed/movie/{id}', 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}', 2),
('VidZee', 'https://player.vidzee.wtf/embed/movie/{id}', 'https://player.vidzee.wtf/embed/tv/{id}/{season}/{episode}', 3);

-- Insert Default Site Settings
INSERT INTO site_settings (setting_key, setting_value) VALUES
('site_name', 'StreamFlix'),
('site_description', 'Premium Movie & TV Show Streaming Platform'),
('tmdb_api_key', ''),
('maintenance_mode', '0'),
('registration_enabled', '1'),
('featured_content_limit', '10'),
('trending_content_limit', '20');
