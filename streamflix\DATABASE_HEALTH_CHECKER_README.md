# StreamFlix Database Health Checker

## 🎯 উদ্দেশ্য
এই স্ক্রিপ্ট দিয়ে আপনি আপনার StreamFlix সাইটের ডাটাবেস স্বাস্থ্য পরীক্ষা করতে এবং প্রয়োজনীয় আপডেট করতে পারবেন।

## 🚀 কিভাবে ব্যবহার করবেন

### ১. ফাইল আপলোড করুন
- `database-health-checker.php` ফাইলটি আপনার সাইটের root directory তে আপলোড করুন
- নিশ্চিত করুন যে `config/database.php` ফাইল সঠিকভাবে কনফিগার করা আছে

### ২. স্ক্রিপ্ট চালান
আপনার ব্রাউজারে যান:
```
https://yourdomain.com/database-health-checker.php?admin_key=streamflix_admin_2024
```

### ৩. ফিচারসমূহ

#### 🔍 Health Check
- ডাটাবেস কানেকশন চেক
- সব প্রয়োজনীয় টেবিল আছে কিনা দেখা
- টেবিল স্ট্রাকচার এবং কলাম চেক
- ইনডেক্স চেক
- ডেটা ইন্টিগ্রিটি চেক
- পারফরমেন্স মেট্রিক্স

#### 🔧 Database Updates
- `config/database-updates.sql` ফাইল থেকে আপডেট চালানো
- মিসিং টেবিল তৈরি করা
- নতুন কলাম যোগ করা
- ইনডেক্স তৈরি করা

#### 💾 Backup Creation
- সম্পূর্ণ ডাটাবেসের JSON ব্যাকআপ তৈরি
- ব্যাকআপ ফাইল ডাউনলোড করা

## 📊 স্ট্যাটাস ইন্ডিকেটর

### ✅ OK (সবুজ)
- সবকিছু ঠিক আছে
- কোন সমস্যা নেই

### ⚠️ WARNING (হলুদ)
- কিছু সমস্যা আছে কিন্তু সাইট চলবে
- উদাহরণ: মিসিং টেবিল বা কলাম

### ❌ ERROR (লাল)
- গুরুতর সমস্যা
- সাইট সঠিকভাবে কাজ নাও করতে পারে

## 🛠️ সমস্যা সমাধান

### ডাটাবেস কানেকশন এরর
1. `config/database.php` ফাইল চেক করুন
2. ডাটাবেস credentials সঠিক আছে কিনা দেখুন
3. ডাটাবেস সার্ভার চালু আছে কিনা চেক করুন

### মিসিং টেবিল
1. "Run Database Updates" বাটনে ক্লিক করুন
2. অথবা `config/database-updates.sql` ফাইল ম্যানুয়ালি রান করুন

### পারমিশন এরর
1. ফাইল পারমিশন 644 সেট করুন
2. ডাটাবেস ইউজারের সঠিক পারমিশন আছে কিনা চেক করুন

## 🔒 নিরাপত্তা

### Admin Access
- স্ক্রিপ্ট শুধুমাত্র admin ইউজার ব্যবহার করতে পারবেন
- URL এ `admin_key=streamflix_admin_2024` প্যারামিটার প্রয়োজন

### Production এ ব্যবহার
- ব্যবহারের পর ফাইলটি ডিলিট করুন অথবা rename করুন
- Admin key পরিবর্তন করুন

## 📝 লগ এবং ব্যাকআপ

### ব্যাকআপ ফাইল
- JSON ফরম্যাটে তৈরি হয়
- ফাইলের নাম: `backup_YYYY-MM-DD_HH-MM-SS.json`
- সব টেবিলের ডেটা থাকে

### এরর লগ
- সব এরর স্ক্রিনে দেখানো হয়
- বিস্তারিত এরর মেসেজ পাওয়া যায়

## 🔄 নিয়মিত ব্যবহার

### সাপ্তাহিক চেক
- সপ্তাহে একবার health check চালান
- পারফরমেন্স মেট্রিক্স দেখুন

### আপডেটের পর
- নতুন ফিচার যোগ করার পর চেক করুন
- ডাটাবেস স্ট্রাকচার পরিবর্তনের পর চেক করুন

### সমস্যার সময়
- সাইটে কোন সমস্যা হলে প্রথমে এই স্ক্রিপ্ট চালান
- ডাটাবেস সমস্যা আছে কিনা দেখুন

## 📞 সাহায্য

### সাধারণ সমস্যা
1. **"Access Denied"**: URL এ admin_key যোগ করুন
2. **"Database connection failed"**: database.php ফাইল চেক করুন
3. **"File not found"**: config/database-updates.sql ফাইল আছে কিনা দেখুন

### উন্নত সমস্যা
- ডাটাবেস লগ চেক করুন
- PHP error log দেখুন
- সার্ভার রিসোর্স চেক করুন

## ⚡ পারফরমেন্স টিপস

### বড় ডাটাবেসের জন্য
- ব্যাকআপ তৈরিতে সময় লাগতে পারে
- Health check এ timeout হতে পারে
- ছোট ছোট অংশে আপডেট করুন

### অপ্টিমাইজেশন
- নিয়মিত OPTIMIZE TABLE চালান
- অপ্রয়োজনীয় ডেটা ডিলিট করুন
- ইনডেক্স ব্যবহার করুন

---

**নোট**: এই স্ক্রিপ্ট ব্যবহারের আগে অবশ্যই ডাটাবেসের ব্যাকআপ নিন।
