<?php
/**
 * StreamFlix Database Health Checker & Updater
 * 
 * এই স্ক্রিপ্ট দিয়ে আপনি:
 * 1. ডাটাবেস কানেকশন চেক করতে পারবেন
 * 2. সব টেবিল আছে কিনা দেখতে পারবেন
 * 3. মিসিং কলাম খুঁজে বের করতে পারবেন
 * 4. ডাটাবেস আপডেট করতে পারবেন
 * 5. পারফরমেন্স চেক করতে পারবেন
 */

// Security check - শুধুমাত্র admin access
session_start();
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    // Temporary admin access for testing
    if (!isset($_GET['admin_key']) || $_GET['admin_key'] !== 'streamflix_admin_2024') {
        die('Access Denied. Admin access required.');
    }
}

// Database configuration
require_once 'config/database.php';

class DatabaseHealthChecker {
    private $pdo;
    private $results = [];
    private $errors = [];
    private $warnings = [];
    
    public function __construct() {
        try {
            $database = new Database();
            $this->pdo = $database->connect();
            if (!$this->pdo) {
                throw new Exception("Database connection failed");
            }
        } catch (Exception $e) {
            $this->errors[] = "Database Connection Error: " . $e->getMessage();
        }
    }
    
    /**
     * Main health check function
     */
    public function runHealthCheck() {
        $this->results['timestamp'] = date('Y-m-d H:i:s');
        $this->results['database_name'] = $this->getDatabaseName();
        
        // 1. Check database connection
        $this->checkDatabaseConnection();
        
        // 2. Check required tables
        $this->checkRequiredTables();
        
        // 3. Check table structures
        $this->checkTableStructures();
        
        // 4. Check indexes
        $this->checkIndexes();
        
        // 5. Check data integrity
        $this->checkDataIntegrity();
        
        // 6. Check performance
        $this->checkPerformance();
        
        // 7. Generate recommendations
        $this->generateRecommendations();
        
        return [
            'results' => $this->results,
            'errors' => $this->errors,
            'warnings' => $this->warnings
        ];
    }
    
    /**
     * Get current database name
     */
    private function getDatabaseName() {
        try {
            $stmt = $this->pdo->query("SELECT DATABASE() as db_name");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['db_name'];
        } catch (Exception $e) {
            return 'Unknown';
        }
    }
    
    /**
     * Check database connection
     */
    private function checkDatabaseConnection() {
        try {
            $stmt = $this->pdo->query("SELECT 1");
            $this->results['connection'] = [
                'status' => 'OK',
                'message' => 'Database connection successful'
            ];
        } catch (Exception $e) {
            $this->errors[] = "Connection test failed: " . $e->getMessage();
            $this->results['connection'] = [
                'status' => 'ERROR',
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Check if all required tables exist
     */
    private function checkRequiredTables() {
        $requiredTables = [
            'users', 'movies', 'tv_shows', 'genres', 'movie_genres', 'tv_show_genres',
            'seasons', 'episodes', 'watchlist', 'embed_servers', 'site_settings',
            'user_activity', 'content_views', 'user_preferences', 'user_ratings'
        ];
        
        $existingTables = $this->getExistingTables();
        $missingTables = array_diff($requiredTables, $existingTables);
        
        $this->results['tables'] = [
            'required' => count($requiredTables),
            'existing' => count($existingTables),
            'missing' => $missingTables,
            'status' => empty($missingTables) ? 'OK' : 'WARNING'
        ];
        
        if (!empty($missingTables)) {
            $this->warnings[] = "Missing tables: " . implode(', ', $missingTables);
        }
    }
    
    /**
     * Get list of existing tables
     */
    private function getExistingTables() {
        try {
            $stmt = $this->pdo->query("SHOW TABLES");
            $tables = [];
            while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
                $tables[] = $row[0];
            }
            return $tables;
        } catch (Exception $e) {
            $this->errors[] = "Failed to get table list: " . $e->getMessage();
            return [];
        }
    }
    
    /**
     * Check table structures and missing columns
     */
    private function checkTableStructures() {
        $tableStructures = [
            'users' => ['id', 'username', 'email', 'password', 'role', 'is_active', 'created_at'],
            'movies' => ['id', 'tmdb_id', 'title', 'overview', 'release_date', 'vote_average', 'poster_path', 'created_at', 'is_featured', 'is_trending'],
            'tv_shows' => ['id', 'tmdb_id', 'name', 'overview', 'first_air_date', 'vote_average', 'poster_path', 'created_at', 'is_featured', 'is_trending'],
            'embed_servers' => ['id', 'name', 'movie_url', 'tv_url', 'priority', 'is_active', 'created_at'],
            'site_settings' => ['id', 'setting_key', 'setting_value', 'setting_type', 'created_at']
        ];
        
        $structureResults = [];
        
        foreach ($tableStructures as $tableName => $requiredColumns) {
            if ($this->tableExists($tableName)) {
                $existingColumns = $this->getTableColumns($tableName);
                $missingColumns = array_diff($requiredColumns, $existingColumns);
                
                $structureResults[$tableName] = [
                    'exists' => true,
                    'required_columns' => count($requiredColumns),
                    'existing_columns' => count($existingColumns),
                    'missing_columns' => $missingColumns,
                    'status' => empty($missingColumns) ? 'OK' : 'WARNING'
                ];
                
                if (!empty($missingColumns)) {
                    $this->warnings[] = "Table '$tableName' missing columns: " . implode(', ', $missingColumns);
                }
            } else {
                $structureResults[$tableName] = [
                    'exists' => false,
                    'status' => 'ERROR'
                ];
                $this->errors[] = "Table '$tableName' does not exist";
            }
        }
        
        $this->results['table_structures'] = $structureResults;
    }
    
    /**
     * Check if table exists
     */
    private function tableExists($tableName) {
        try {
            $stmt = $this->pdo->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$tableName]);
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Get table columns
     */
    private function getTableColumns($tableName) {
        try {
            $stmt = $this->pdo->prepare("SHOW COLUMNS FROM `$tableName`");
            $stmt->execute();
            $columns = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $columns[] = $row['Field'];
            }
            return $columns;
        } catch (Exception $e) {
            $this->errors[] = "Failed to get columns for table '$tableName': " . $e->getMessage();
            return [];
        }
    }
    
    /**
     * Check indexes
     */
    private function checkIndexes() {
        $importantIndexes = [
            'users' => ['email', 'username'],
            'movies' => ['tmdb_id', 'vote_average'],
            'tv_shows' => ['tmdb_id', 'vote_average'],
            'embed_servers' => ['priority', 'is_active']
        ];
        
        $indexResults = [];
        
        foreach ($importantIndexes as $tableName => $expectedIndexes) {
            if ($this->tableExists($tableName)) {
                $existingIndexes = $this->getTableIndexes($tableName);
                $missingIndexes = [];
                
                foreach ($expectedIndexes as $indexColumn) {
                    if (!$this->hasIndexOnColumn($existingIndexes, $indexColumn)) {
                        $missingIndexes[] = $indexColumn;
                    }
                }
                
                $indexResults[$tableName] = [
                    'expected' => $expectedIndexes,
                    'missing' => $missingIndexes,
                    'status' => empty($missingIndexes) ? 'OK' : 'WARNING'
                ];
                
                if (!empty($missingIndexes)) {
                    $this->warnings[] = "Table '$tableName' missing indexes on: " . implode(', ', $missingIndexes);
                }
            }
        }
        
        $this->results['indexes'] = $indexResults;
    }
    
    /**
     * Get table indexes
     */
    private function getTableIndexes($tableName) {
        try {
            $stmt = $this->pdo->prepare("SHOW INDEX FROM `$tableName`");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Check if table has index on specific column
     */
    private function hasIndexOnColumn($indexes, $columnName) {
        foreach ($indexes as $index) {
            if ($index['Column_name'] === $columnName) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Check data integrity
     */
    private function checkDataIntegrity() {
        $integrityChecks = [];
        
        // Check for orphaned records
        if ($this->tableExists('movie_genres') && $this->tableExists('movies')) {
            $stmt = $this->pdo->query("
                SELECT COUNT(*) as count 
                FROM movie_genres mg 
                LEFT JOIN movies m ON mg.movie_id = m.id 
                WHERE m.id IS NULL
            ");
            $orphanedMovieGenres = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            $integrityChecks['orphaned_movie_genres'] = $orphanedMovieGenres;
            
            if ($orphanedMovieGenres > 0) {
                $this->warnings[] = "Found $orphanedMovieGenres orphaned movie genre records";
            }
        }
        
        // Check for duplicate TMDB IDs
        if ($this->tableExists('movies')) {
            $stmt = $this->pdo->query("
                SELECT tmdb_id, COUNT(*) as count 
                FROM movies 
                GROUP BY tmdb_id 
                HAVING COUNT(*) > 1
            ");
            $duplicateMovies = $stmt->rowCount();
            $integrityChecks['duplicate_movies'] = $duplicateMovies;
            
            if ($duplicateMovies > 0) {
                $this->warnings[] = "Found $duplicateMovies duplicate movie TMDB IDs";
            }
        }
        
        $this->results['data_integrity'] = $integrityChecks;
    }

    /**
     * Check performance metrics
     */
    private function checkPerformance() {
        $performanceMetrics = [];

        // Check table sizes
        if ($this->tableExists('movies')) {
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM movies");
            $performanceMetrics['movies_count'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        }

        if ($this->tableExists('tv_shows')) {
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM tv_shows");
            $performanceMetrics['tv_shows_count'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        }

        if ($this->tableExists('users')) {
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM users");
            $performanceMetrics['users_count'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        }

        // Check database size
        try {
            $stmt = $this->pdo->query("
                SELECT
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables
                WHERE table_schema = DATABASE()
            ");
            $performanceMetrics['database_size_mb'] = $stmt->fetch(PDO::FETCH_ASSOC)['size_mb'];
        } catch (Exception $e) {
            $performanceMetrics['database_size_mb'] = 'Unknown';
        }

        $this->results['performance'] = $performanceMetrics;
    }

    /**
     * Generate recommendations
     */
    private function generateRecommendations() {
        $recommendations = [];

        if (!empty($this->errors)) {
            $recommendations[] = "❌ Critical issues found that need immediate attention";
        }

        if (!empty($this->warnings)) {
            $recommendations[] = "⚠️ Warning issues found that should be addressed";
        }

        if (empty($this->errors) && empty($this->warnings)) {
            $recommendations[] = "✅ Database is healthy and up to date";
        }

        // Check if updates are needed
        if (isset($this->results['tables']['missing']) && !empty($this->results['tables']['missing'])) {
            $recommendations[] = "🔧 Run database updates to create missing tables";
        }

        // Performance recommendations
        if (isset($this->results['performance']['database_size_mb']) &&
            $this->results['performance']['database_size_mb'] > 100) {
            $recommendations[] = "📊 Consider database optimization for large database size";
        }

        $this->results['recommendations'] = $recommendations;
    }

    /**
     * Run database updates
     */
    public function runDatabaseUpdates() {
        $updateResults = [];

        try {
            // Read the database updates SQL file
            $sqlFile = 'config/database-updates.sql';
            if (!file_exists($sqlFile)) {
                throw new Exception("Database updates file not found: $sqlFile");
            }

            $sql = file_get_contents($sqlFile);

            // Split SQL into individual statements
            $statements = $this->splitSqlStatements($sql);

            $successCount = 0;
            $errorCount = 0;
            $errors = [];

            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (empty($statement) || strpos($statement, '--') === 0) {
                    continue;
                }

                try {
                    $this->pdo->exec($statement);
                    $successCount++;
                } catch (Exception $e) {
                    $errorCount++;
                    $errors[] = "Error executing statement: " . substr($statement, 0, 100) . "... - " . $e->getMessage();
                }
            }

            $updateResults = [
                'status' => $errorCount === 0 ? 'SUCCESS' : 'PARTIAL',
                'statements_executed' => $successCount,
                'errors_count' => $errorCount,
                'errors' => $errors,
                'message' => "Database update completed. $successCount statements executed successfully."
            ];

        } catch (Exception $e) {
            $updateResults = [
                'status' => 'ERROR',
                'message' => $e->getMessage()
            ];
        }

        return $updateResults;
    }

    /**
     * Split SQL file into individual statements
     */
    private function splitSqlStatements($sql) {
        // Remove comments
        $sql = preg_replace('/--.*$/m', '', $sql);
        $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);

        // Split by semicolon but handle stored procedures and functions
        $statements = [];
        $current = '';
        $inDelimiter = false;

        $lines = explode("\n", $sql);
        foreach ($lines as $line) {
            $line = trim($line);

            if (empty($line)) continue;

            // Handle DELIMITER statements
            if (stripos($line, 'DELIMITER') === 0) {
                $inDelimiter = !$inDelimiter;
                continue;
            }

            $current .= $line . "\n";

            // Check for statement end
            if (!$inDelimiter && substr(rtrim($line), -1) === ';') {
                $statements[] = trim($current);
                $current = '';
            }
        }

        if (!empty(trim($current))) {
            $statements[] = trim($current);
        }

        return $statements;
    }

    /**
     * Get database backup
     */
    public function createBackup() {
        try {
            $backupData = [];
            $tables = $this->getExistingTables();

            foreach ($tables as $table) {
                $stmt = $this->pdo->query("SELECT * FROM `$table`");
                $backupData[$table] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }

            $backupFile = 'backup_' . date('Y-m-d_H-i-s') . '.json';
            file_put_contents($backupFile, json_encode($backupData, JSON_PRETTY_PRINT));

            return [
                'status' => 'SUCCESS',
                'file' => $backupFile,
                'message' => "Backup created successfully: $backupFile"
            ];

        } catch (Exception $e) {
            return [
                'status' => 'ERROR',
                'message' => $e->getMessage()
            ];
        }
    }
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');

    $action = $_POST['action'] ?? '';
    $checker = new DatabaseHealthChecker();

    switch ($action) {
        case 'health_check':
            echo json_encode($checker->runHealthCheck());
            break;

        case 'run_updates':
            echo json_encode($checker->runDatabaseUpdates());
            break;

        case 'create_backup':
            echo json_encode($checker->createBackup());
            break;

        default:
            echo json_encode(['error' => 'Invalid action']);
    }
    exit;
}

// HTML Interface starts here
$checker = new DatabaseHealthChecker();
$healthData = $checker->runHealthCheck();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StreamFlix Database Health Checker</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 5px solid #28a745;
        }

        .status-card.warning {
            border-left-color: #ffc107;
        }

        .status-card.error {
            border-left-color: #dc3545;
        }

        .status-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: bold;
        }

        .status-ok {
            background: #d4edda;
            color: #155724;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .recommendations {
            background: #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .recommendations h3 {
            color: #495057;
            margin-bottom: 15px;
        }

        .recommendations ul {
            list-style: none;
        }

        .recommendations li {
            padding: 8px 0;
            font-size: 1.1em;
        }

        .details-section {
            margin: 30px 0;
        }

        .details-toggle {
            background: #6c757d;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 15px;
        }

        .details-content {
            display: none;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }

        .details-content.show {
            display: block;
        }

        .loading {
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        @media (max-width: 768px) {
            .status-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Database Health Checker</h1>
            <p>StreamFlix ডাটাবেস স্ট্যাটাস চেক এবং আপডেট টুল</p>
            <p><strong>Database:</strong> <?php echo htmlspecialchars($healthData['results']['database_name']); ?></p>
            <p><strong>Last Check:</strong> <?php echo $healthData['results']['timestamp']; ?></p>
        </div>

        <div class="content">
            <!-- Overall Status -->
            <div class="status-grid">
                <!-- Database Connection -->
                <div class="status-card <?php echo $healthData['results']['connection']['status'] === 'OK' ? '' : 'error'; ?>">
                    <h3>🔗 Database Connection</h3>
                    <div class="status-item">
                        <span>Status</span>
                        <span class="status-badge <?php echo $healthData['results']['connection']['status'] === 'OK' ? 'status-ok' : 'status-error'; ?>">
                            <?php echo $healthData['results']['connection']['status']; ?>
                        </span>
                    </div>
                    <div class="status-item">
                        <span>Message</span>
                        <span><?php echo htmlspecialchars($healthData['results']['connection']['message']); ?></span>
                    </div>
                </div>

                <!-- Tables Status -->
                <div class="status-card <?php echo $healthData['results']['tables']['status'] === 'OK' ? '' : 'warning'; ?>">
                    <h3>📋 Tables Status</h3>
                    <div class="status-item">
                        <span>Required Tables</span>
                        <span><?php echo $healthData['results']['tables']['required']; ?></span>
                    </div>
                    <div class="status-item">
                        <span>Existing Tables</span>
                        <span><?php echo $healthData['results']['tables']['existing']; ?></span>
                    </div>
                    <div class="status-item">
                        <span>Missing Tables</span>
                        <span class="status-badge <?php echo empty($healthData['results']['tables']['missing']) ? 'status-ok' : 'status-warning'; ?>">
                            <?php echo empty($healthData['results']['tables']['missing']) ? '0' : count($healthData['results']['tables']['missing']); ?>
                        </span>
                    </div>
                    <?php if (!empty($healthData['results']['tables']['missing'])): ?>
                    <div class="status-item">
                        <span>Missing:</span>
                        <span><?php echo implode(', ', $healthData['results']['tables']['missing']); ?></span>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Performance Metrics -->
                <?php if (isset($healthData['results']['performance'])): ?>
                <div class="status-card">
                    <h3>📊 Performance Metrics</h3>
                    <?php if (isset($healthData['results']['performance']['movies_count'])): ?>
                    <div class="status-item">
                        <span>Movies</span>
                        <span><?php echo number_format($healthData['results']['performance']['movies_count']); ?></span>
                    </div>
                    <?php endif; ?>
                    <?php if (isset($healthData['results']['performance']['tv_shows_count'])): ?>
                    <div class="status-item">
                        <span>TV Shows</span>
                        <span><?php echo number_format($healthData['results']['performance']['tv_shows_count']); ?></span>
                    </div>
                    <?php endif; ?>
                    <?php if (isset($healthData['results']['performance']['users_count'])): ?>
                    <div class="status-item">
                        <span>Users</span>
                        <span><?php echo number_format($healthData['results']['performance']['users_count']); ?></span>
                    </div>
                    <?php endif; ?>
                    <?php if (isset($healthData['results']['performance']['database_size_mb'])): ?>
                    <div class="status-item">
                        <span>Database Size</span>
                        <span><?php echo $healthData['results']['performance']['database_size_mb']; ?> MB</span>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <!-- Data Integrity -->
                <?php if (isset($healthData['results']['data_integrity'])): ?>
                <div class="status-card">
                    <h3>🔍 Data Integrity</h3>
                    <?php foreach ($healthData['results']['data_integrity'] as $check => $value): ?>
                    <div class="status-item">
                        <span><?php echo ucwords(str_replace('_', ' ', $check)); ?></span>
                        <span class="status-badge <?php echo $value == 0 ? 'status-ok' : 'status-warning'; ?>">
                            <?php echo $value; ?>
                        </span>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="runHealthCheck()">🔄 Refresh Health Check</button>
                <button class="btn btn-success" onclick="runDatabaseUpdates()">🔧 Run Database Updates</button>
                <button class="btn btn-warning" onclick="createBackup()">💾 Create Backup</button>
                <a href="?admin_key=streamflix_admin_2024" class="btn btn-primary">🔄 Reload Page</a>
            </div>

            <!-- Results Area -->
            <div id="results-area"></div>

            <!-- Recommendations -->
            <?php if (isset($healthData['results']['recommendations']) && !empty($healthData['results']['recommendations'])): ?>
            <div class="recommendations">
                <h3>💡 Recommendations</h3>
                <ul>
                    <?php foreach ($healthData['results']['recommendations'] as $recommendation): ?>
                    <li><?php echo htmlspecialchars($recommendation); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <!-- Errors and Warnings -->
            <?php if (!empty($healthData['errors'])): ?>
            <div class="alert alert-danger">
                <h4>❌ Errors Found:</h4>
                <ul>
                    <?php foreach ($healthData['errors'] as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <?php if (!empty($healthData['warnings'])): ?>
            <div class="alert alert-warning">
                <h4>⚠️ Warnings:</h4>
                <ul>
                    <?php foreach ($healthData['warnings'] as $warning): ?>
                    <li><?php echo htmlspecialchars($warning); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <!-- Detailed Information -->
            <div class="details-section">
                <button class="details-toggle" onclick="toggleDetails('table-structures')">
                    📋 Table Structures Details
                </button>
                <div id="table-structures" class="details-content">
                    <?php if (isset($healthData['results']['table_structures'])): ?>
                    <div class="status-grid">
                        <?php foreach ($healthData['results']['table_structures'] as $tableName => $tableInfo): ?>
                        <div class="status-card <?php echo $tableInfo['status'] === 'OK' ? '' : ($tableInfo['status'] === 'WARNING' ? 'warning' : 'error'); ?>">
                            <h4><?php echo htmlspecialchars($tableName); ?></h4>
                            <?php if ($tableInfo['exists']): ?>
                            <div class="status-item">
                                <span>Required Columns</span>
                                <span><?php echo $tableInfo['required_columns']; ?></span>
                            </div>
                            <div class="status-item">
                                <span>Existing Columns</span>
                                <span><?php echo $tableInfo['existing_columns']; ?></span>
                            </div>
                            <?php if (!empty($tableInfo['missing_columns'])): ?>
                            <div class="status-item">
                                <span>Missing Columns</span>
                                <span><?php echo implode(', ', $tableInfo['missing_columns']); ?></span>
                            </div>
                            <?php endif; ?>
                            <?php else: ?>
                            <div class="status-item">
                                <span>Status</span>
                                <span class="status-badge status-error">Table Missing</span>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="details-section">
                <button class="details-toggle" onclick="toggleDetails('indexes-details')">
                    🔍 Indexes Details
                </button>
                <div id="indexes-details" class="details-content">
                    <?php if (isset($healthData['results']['indexes'])): ?>
                    <div class="status-grid">
                        <?php foreach ($healthData['results']['indexes'] as $tableName => $indexInfo): ?>
                        <div class="status-card <?php echo $indexInfo['status'] === 'OK' ? '' : 'warning'; ?>">
                            <h4><?php echo htmlspecialchars($tableName); ?></h4>
                            <div class="status-item">
                                <span>Expected Indexes</span>
                                <span><?php echo implode(', ', $indexInfo['expected']); ?></span>
                            </div>
                            <?php if (!empty($indexInfo['missing'])): ?>
                            <div class="status-item">
                                <span>Missing Indexes</span>
                                <span><?php echo implode(', ', $indexInfo['missing']); ?></span>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Toggle details sections
        function toggleDetails(sectionId) {
            const section = document.getElementById(sectionId);
            section.classList.toggle('show');
        }

        // Show loading spinner
        function showLoading(message = 'Processing...') {
            const resultsArea = document.getElementById('results-area');
            resultsArea.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>${message}</p>
                </div>
            `;
        }

        // Show result message
        function showResult(result, type = 'success') {
            const resultsArea = document.getElementById('results-area');
            const alertClass = type === 'success' ? 'alert-success' :
                              type === 'warning' ? 'alert-warning' : 'alert-danger';

            resultsArea.innerHTML = `
                <div class="alert ${alertClass}">
                    <h4>${type === 'success' ? '✅' : type === 'warning' ? '⚠️' : '❌'} Result:</h4>
                    <p>${result}</p>
                </div>
            `;

            // Auto scroll to results
            resultsArea.scrollIntoView({ behavior: 'smooth' });
        }

        // Run health check
        function runHealthCheck() {
            showLoading('Running health check...');

            fetch(window.location.href, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=health_check'
            })
            .then(response => response.json())
            .then(data => {
                if (data.errors && data.errors.length > 0) {
                    showResult('Health check completed with errors. Check the details above.', 'warning');
                } else {
                    showResult('Health check completed successfully! Database is healthy.', 'success');
                }

                // Reload page after 2 seconds to show updated results
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            })
            .catch(error => {
                showResult('Error running health check: ' + error.message, 'error');
            });
        }

        // Run database updates
        function runDatabaseUpdates() {
            if (!confirm('Are you sure you want to run database updates? This will modify your database structure.')) {
                return;
            }

            showLoading('Running database updates...');

            fetch(window.location.href, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=run_updates'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'SUCCESS') {
                    showResult(`Database updates completed successfully! ${data.statements_executed} statements executed.`, 'success');
                } else if (data.status === 'PARTIAL') {
                    showResult(`Database updates partially completed. ${data.statements_executed} statements executed, ${data.errors_count} errors occurred.`, 'warning');
                } else {
                    showResult(`Database update failed: ${data.message}`, 'error');
                }

                // Show detailed errors if any
                if (data.errors && data.errors.length > 0) {
                    const errorDetails = data.errors.map(error => `<li>${error}</li>`).join('');
                    document.getElementById('results-area').innerHTML += `
                        <div class="alert alert-warning">
                            <h4>⚠️ Update Errors:</h4>
                            <ul>${errorDetails}</ul>
                        </div>
                    `;
                }

                // Reload page after 3 seconds to show updated results
                setTimeout(() => {
                    window.location.reload();
                }, 3000);
            })
            .catch(error => {
                showResult('Error running database updates: ' + error.message, 'error');
            });
        }

        // Create backup
        function createBackup() {
            if (!confirm('Create a backup of current database? This may take some time for large databases.')) {
                return;
            }

            showLoading('Creating database backup...');

            fetch(window.location.href, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=create_backup'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'SUCCESS') {
                    showResult(`Backup created successfully: ${data.file}`, 'success');
                } else {
                    showResult(`Backup failed: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                showResult('Error creating backup: ' + error.message, 'error');
            });
        }

        // Auto-refresh every 5 minutes
        setInterval(() => {
            console.log('Auto-refreshing health check...');
            runHealthCheck();
        }, 300000); // 5 minutes

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Database Health Checker loaded successfully');

            // Add click handlers for all toggle buttons
            document.querySelectorAll('.details-toggle').forEach(button => {
                button.addEventListener('click', function() {
                    const targetId = this.getAttribute('onclick').match(/'([^']+)'/)[1];
                    toggleDetails(targetId);
                });
            });
        });
    </script>
</body>
</html>
