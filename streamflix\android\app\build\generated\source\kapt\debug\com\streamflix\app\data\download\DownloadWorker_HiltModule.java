package com.streamflix.app.data.download;

import androidx.hilt.work.WorkerAssistedFactory;
import androidx.work.ListenableWorker;
import dagger.Binds;
import dagger.Module;
import dagger.hilt.InstallIn;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.components.SingletonComponent;
import dagger.multibindings.IntoMap;
import dagger.multibindings.StringKey;

@Module
@InstallIn(SingletonComponent.class)
@OriginatingElement(
    topLevelClass = DownloadWorker.class
)
public interface DownloadWorker_HiltModule {
  @Binds
  @IntoMap
  @StringKey("com.streamflix.app.data.download.DownloadWorker")
  WorkerAssistedFactory<? extends ListenableWorker> bind(DownloadWorker_AssistedFactory factory);
}
