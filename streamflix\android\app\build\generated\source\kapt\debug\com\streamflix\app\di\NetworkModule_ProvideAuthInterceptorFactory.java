// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.streamflix.app.di;

import com.streamflix.app.data.local.UserPreferences;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;
import okhttp3.Interceptor;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvideAuthInterceptorFactory implements Factory<Interceptor> {
  private final Provider<UserPreferences> userPreferencesProvider;

  public NetworkModule_ProvideAuthInterceptorFactory(
      Provider<UserPreferences> userPreferencesProvider) {
    this.userPreferencesProvider = userPreferencesProvider;
  }

  @Override
  public Interceptor get() {
    return provideAuthInterceptor(userPreferencesProvider.get());
  }

  public static NetworkModule_ProvideAuthInterceptorFactory create(
      Provider<UserPreferences> userPreferencesProvider) {
    return new NetworkModule_ProvideAuthInterceptorFactory(userPreferencesProvider);
  }

  public static Interceptor provideAuthInterceptor(UserPreferences userPreferences) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideAuthInterceptor(userPreferences));
  }
}
