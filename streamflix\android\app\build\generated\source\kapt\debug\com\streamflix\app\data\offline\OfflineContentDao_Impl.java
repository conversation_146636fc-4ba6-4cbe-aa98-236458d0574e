package com.streamflix.app.data.offline;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@SuppressWarnings({"unchecked", "deprecation"})
public final class OfflineContentDao_Impl implements OfflineContentDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<OfflineContentItem> __insertionAdapterOfOfflineContentItem;

  private final SharedSQLiteStatement __preparedStmtOfDeleteContent;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllContent;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldContent;

  private final SharedSQLiteStatement __preparedStmtOfUpdateLastAccessed;

  public OfflineContentDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfOfflineContentItem = new EntityInsertionAdapter<OfflineContentItem>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `offline_content` (`id`,`contentId`,`contentType`,`title`,`posterPath`,`backdropPath`,`overview`,`rating`,`releaseDate`,`runtime`,`genres`,`cachedAt`,`lastAccessed`,`fileSize`,`isDownloaded`,`numberOfSeasons`,`numberOfEpisodes`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final OfflineContentItem entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getContentId());
        if (entity.getContentType() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getContentType());
        }
        if (entity.getTitle() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getTitle());
        }
        if (entity.getPosterPath() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getPosterPath());
        }
        if (entity.getBackdropPath() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getBackdropPath());
        }
        if (entity.getOverview() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getOverview());
        }
        statement.bindDouble(8, entity.getRating());
        if (entity.getReleaseDate() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getReleaseDate());
        }
        if (entity.getRuntime() == null) {
          statement.bindNull(10);
        } else {
          statement.bindLong(10, entity.getRuntime());
        }
        if (entity.getGenres() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getGenres());
        }
        statement.bindLong(12, entity.getCachedAt());
        statement.bindLong(13, entity.getLastAccessed());
        statement.bindLong(14, entity.getFileSize());
        final int _tmp = entity.isDownloaded() ? 1 : 0;
        statement.bindLong(15, _tmp);
        if (entity.getNumberOfSeasons() == null) {
          statement.bindNull(16);
        } else {
          statement.bindLong(16, entity.getNumberOfSeasons());
        }
        if (entity.getNumberOfEpisodes() == null) {
          statement.bindNull(17);
        } else {
          statement.bindLong(17, entity.getNumberOfEpisodes());
        }
      }
    };
    this.__preparedStmtOfDeleteContent = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM offline_content WHERE contentId = ? AND contentType = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllContent = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM offline_content";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteOldContent = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM offline_content WHERE lastAccessed < ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateLastAccessed = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE offline_content SET lastAccessed = ? WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertContent(final OfflineContentItem content,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfOfflineContentItem.insert(content);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteContent(final int contentId, final String contentType,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteContent.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, contentId);
        _argIndex = 2;
        if (contentType == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, contentType);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteContent.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllContent(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllContent.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllContent.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteOldContent(final long cutoffTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldContent.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, cutoffTime);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldContent.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateLastAccessed(final long id, final long timestamp,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateLastAccessed.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, timestamp);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateLastAccessed.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getAllContent(final Continuation<? super List<OfflineContentItem>> $completion) {
    final String _sql = "SELECT * FROM offline_content ORDER BY lastAccessed DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<OfflineContentItem>>() {
      @Override
      @NonNull
      public List<OfflineContentItem> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfContentId = CursorUtil.getColumnIndexOrThrow(_cursor, "contentId");
          final int _cursorIndexOfContentType = CursorUtil.getColumnIndexOrThrow(_cursor, "contentType");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfPosterPath = CursorUtil.getColumnIndexOrThrow(_cursor, "posterPath");
          final int _cursorIndexOfBackdropPath = CursorUtil.getColumnIndexOrThrow(_cursor, "backdropPath");
          final int _cursorIndexOfOverview = CursorUtil.getColumnIndexOrThrow(_cursor, "overview");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfReleaseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "releaseDate");
          final int _cursorIndexOfRuntime = CursorUtil.getColumnIndexOrThrow(_cursor, "runtime");
          final int _cursorIndexOfGenres = CursorUtil.getColumnIndexOrThrow(_cursor, "genres");
          final int _cursorIndexOfCachedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "cachedAt");
          final int _cursorIndexOfLastAccessed = CursorUtil.getColumnIndexOrThrow(_cursor, "lastAccessed");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfIsDownloaded = CursorUtil.getColumnIndexOrThrow(_cursor, "isDownloaded");
          final int _cursorIndexOfNumberOfSeasons = CursorUtil.getColumnIndexOrThrow(_cursor, "numberOfSeasons");
          final int _cursorIndexOfNumberOfEpisodes = CursorUtil.getColumnIndexOrThrow(_cursor, "numberOfEpisodes");
          final List<OfflineContentItem> _result = new ArrayList<OfflineContentItem>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final OfflineContentItem _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpContentId;
            _tmpContentId = _cursor.getInt(_cursorIndexOfContentId);
            final String _tmpContentType;
            if (_cursor.isNull(_cursorIndexOfContentType)) {
              _tmpContentType = null;
            } else {
              _tmpContentType = _cursor.getString(_cursorIndexOfContentType);
            }
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final String _tmpPosterPath;
            if (_cursor.isNull(_cursorIndexOfPosterPath)) {
              _tmpPosterPath = null;
            } else {
              _tmpPosterPath = _cursor.getString(_cursorIndexOfPosterPath);
            }
            final String _tmpBackdropPath;
            if (_cursor.isNull(_cursorIndexOfBackdropPath)) {
              _tmpBackdropPath = null;
            } else {
              _tmpBackdropPath = _cursor.getString(_cursorIndexOfBackdropPath);
            }
            final String _tmpOverview;
            if (_cursor.isNull(_cursorIndexOfOverview)) {
              _tmpOverview = null;
            } else {
              _tmpOverview = _cursor.getString(_cursorIndexOfOverview);
            }
            final double _tmpRating;
            _tmpRating = _cursor.getDouble(_cursorIndexOfRating);
            final String _tmpReleaseDate;
            if (_cursor.isNull(_cursorIndexOfReleaseDate)) {
              _tmpReleaseDate = null;
            } else {
              _tmpReleaseDate = _cursor.getString(_cursorIndexOfReleaseDate);
            }
            final Integer _tmpRuntime;
            if (_cursor.isNull(_cursorIndexOfRuntime)) {
              _tmpRuntime = null;
            } else {
              _tmpRuntime = _cursor.getInt(_cursorIndexOfRuntime);
            }
            final String _tmpGenres;
            if (_cursor.isNull(_cursorIndexOfGenres)) {
              _tmpGenres = null;
            } else {
              _tmpGenres = _cursor.getString(_cursorIndexOfGenres);
            }
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            final long _tmpLastAccessed;
            _tmpLastAccessed = _cursor.getLong(_cursorIndexOfLastAccessed);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final boolean _tmpIsDownloaded;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsDownloaded);
            _tmpIsDownloaded = _tmp != 0;
            final Integer _tmpNumberOfSeasons;
            if (_cursor.isNull(_cursorIndexOfNumberOfSeasons)) {
              _tmpNumberOfSeasons = null;
            } else {
              _tmpNumberOfSeasons = _cursor.getInt(_cursorIndexOfNumberOfSeasons);
            }
            final Integer _tmpNumberOfEpisodes;
            if (_cursor.isNull(_cursorIndexOfNumberOfEpisodes)) {
              _tmpNumberOfEpisodes = null;
            } else {
              _tmpNumberOfEpisodes = _cursor.getInt(_cursorIndexOfNumberOfEpisodes);
            }
            _item = new OfflineContentItem(_tmpId,_tmpContentId,_tmpContentType,_tmpTitle,_tmpPosterPath,_tmpBackdropPath,_tmpOverview,_tmpRating,_tmpReleaseDate,_tmpRuntime,_tmpGenres,_tmpCachedAt,_tmpLastAccessed,_tmpFileSize,_tmpIsDownloaded,_tmpNumberOfSeasons,_tmpNumberOfEpisodes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getContentByIdAndType(final int contentId, final String contentType,
      final Continuation<? super OfflineContentItem> $completion) {
    final String _sql = "SELECT * FROM offline_content WHERE contentId = ? AND contentType = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, contentId);
    _argIndex = 2;
    if (contentType == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, contentType);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<OfflineContentItem>() {
      @Override
      @Nullable
      public OfflineContentItem call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfContentId = CursorUtil.getColumnIndexOrThrow(_cursor, "contentId");
          final int _cursorIndexOfContentType = CursorUtil.getColumnIndexOrThrow(_cursor, "contentType");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfPosterPath = CursorUtil.getColumnIndexOrThrow(_cursor, "posterPath");
          final int _cursorIndexOfBackdropPath = CursorUtil.getColumnIndexOrThrow(_cursor, "backdropPath");
          final int _cursorIndexOfOverview = CursorUtil.getColumnIndexOrThrow(_cursor, "overview");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfReleaseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "releaseDate");
          final int _cursorIndexOfRuntime = CursorUtil.getColumnIndexOrThrow(_cursor, "runtime");
          final int _cursorIndexOfGenres = CursorUtil.getColumnIndexOrThrow(_cursor, "genres");
          final int _cursorIndexOfCachedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "cachedAt");
          final int _cursorIndexOfLastAccessed = CursorUtil.getColumnIndexOrThrow(_cursor, "lastAccessed");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfIsDownloaded = CursorUtil.getColumnIndexOrThrow(_cursor, "isDownloaded");
          final int _cursorIndexOfNumberOfSeasons = CursorUtil.getColumnIndexOrThrow(_cursor, "numberOfSeasons");
          final int _cursorIndexOfNumberOfEpisodes = CursorUtil.getColumnIndexOrThrow(_cursor, "numberOfEpisodes");
          final OfflineContentItem _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpContentId;
            _tmpContentId = _cursor.getInt(_cursorIndexOfContentId);
            final String _tmpContentType;
            if (_cursor.isNull(_cursorIndexOfContentType)) {
              _tmpContentType = null;
            } else {
              _tmpContentType = _cursor.getString(_cursorIndexOfContentType);
            }
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final String _tmpPosterPath;
            if (_cursor.isNull(_cursorIndexOfPosterPath)) {
              _tmpPosterPath = null;
            } else {
              _tmpPosterPath = _cursor.getString(_cursorIndexOfPosterPath);
            }
            final String _tmpBackdropPath;
            if (_cursor.isNull(_cursorIndexOfBackdropPath)) {
              _tmpBackdropPath = null;
            } else {
              _tmpBackdropPath = _cursor.getString(_cursorIndexOfBackdropPath);
            }
            final String _tmpOverview;
            if (_cursor.isNull(_cursorIndexOfOverview)) {
              _tmpOverview = null;
            } else {
              _tmpOverview = _cursor.getString(_cursorIndexOfOverview);
            }
            final double _tmpRating;
            _tmpRating = _cursor.getDouble(_cursorIndexOfRating);
            final String _tmpReleaseDate;
            if (_cursor.isNull(_cursorIndexOfReleaseDate)) {
              _tmpReleaseDate = null;
            } else {
              _tmpReleaseDate = _cursor.getString(_cursorIndexOfReleaseDate);
            }
            final Integer _tmpRuntime;
            if (_cursor.isNull(_cursorIndexOfRuntime)) {
              _tmpRuntime = null;
            } else {
              _tmpRuntime = _cursor.getInt(_cursorIndexOfRuntime);
            }
            final String _tmpGenres;
            if (_cursor.isNull(_cursorIndexOfGenres)) {
              _tmpGenres = null;
            } else {
              _tmpGenres = _cursor.getString(_cursorIndexOfGenres);
            }
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            final long _tmpLastAccessed;
            _tmpLastAccessed = _cursor.getLong(_cursorIndexOfLastAccessed);
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final boolean _tmpIsDownloaded;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsDownloaded);
            _tmpIsDownloaded = _tmp != 0;
            final Integer _tmpNumberOfSeasons;
            if (_cursor.isNull(_cursorIndexOfNumberOfSeasons)) {
              _tmpNumberOfSeasons = null;
            } else {
              _tmpNumberOfSeasons = _cursor.getInt(_cursorIndexOfNumberOfSeasons);
            }
            final Integer _tmpNumberOfEpisodes;
            if (_cursor.isNull(_cursorIndexOfNumberOfEpisodes)) {
              _tmpNumberOfEpisodes = null;
            } else {
              _tmpNumberOfEpisodes = _cursor.getInt(_cursorIndexOfNumberOfEpisodes);
            }
            _result = new OfflineContentItem(_tmpId,_tmpContentId,_tmpContentType,_tmpTitle,_tmpPosterPath,_tmpBackdropPath,_tmpOverview,_tmpRating,_tmpReleaseDate,_tmpRuntime,_tmpGenres,_tmpCachedAt,_tmpLastAccessed,_tmpFileSize,_tmpIsDownloaded,_tmpNumberOfSeasons,_tmpNumberOfEpisodes);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
