package com.streamflix.app.presentation.home;

import androidx.compose.animation.*;
import androidx.compose.animation.core.*;
import androidx.compose.foundation.*;
import androidx.compose.foundation.layout.*;
import androidx.compose.foundation.lazy.*;
import androidx.compose.foundation.pager.*;
import androidx.compose.material.icons.Icons;
import androidx.compose.material.icons.filled.*;
import androidx.compose.material3.*;
import androidx.compose.runtime.*;
import androidx.compose.ui.Alignment;
import androidx.compose.ui.Modifier;
import androidx.compose.ui.graphics.Brush;
import androidx.compose.ui.layout.ContentScale;
import androidx.compose.ui.text.font.FontWeight;
import androidx.compose.ui.text.style.TextOverflow;
import com.streamflix.app.data.model.*;
import com.streamflix.app.presentation.components.*;
import com.streamflix.app.ui.theme.*;
import com.streamflix.app.utils.Resource;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000P\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001ah\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\u000b2\b\b\u0002\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u000e\u001a\u00020\r2\b\b\u0002\u0010\u000f\u001a\u00020\r2\b\b\u0002\u0010\u0010\u001a\u00020\u0011H\u0007\u001a\\\u0010\u0012\u001a\u00020\u00012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00130\u00052\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00010\b2\u0012\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00010\b2\u0012\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00010\b2\b\b\u0002\u0010\u0010\u001a\u00020\u0011H\u0007\u001aN\u0010\u0016\u001a\u00020\u00012\u0012\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00010\b2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0019\u0012\u0004\u0012\u00020\u00010\b2\u0012\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\b2\b\b\u0002\u0010\u001a\u001a\u00020\u001bH\u0007\u001a\n\u0010\u001c\u001a\u00020\u0006*\u00020\u0013\u001a\n\u0010\u001c\u001a\u00020\u0006*\u00020\u0019\u001a\n\u0010\u001c\u001a\u00020\u0006*\u00020\u001d\u00a8\u0006\u001e"}, d2 = {"ContentSection", "", "title", "", "items", "", "Lcom/streamflix/app/presentation/home/<USER>", "onItemClick", "Lkotlin/Function1;", "", "onSeeAllClick", "Lkotlin/Function0;", "showProgress", "", "showTrendingBadge", "showNewBadge", "modifier", "Landroidx/compose/ui/Modifier;", "HeroBannerSection", "Lcom/streamflix/app/data/model/Movie;", "onPlayClick", "onWatchlistClick", "NetflixHomeScreen", "onMovieClick", "onTvShowClick", "Lcom/streamflix/app/data/model/TvShow;", "viewModel", "Lcom/streamflix/app/presentation/home/<USER>", "toContentItem", "Lcom/streamflix/app/data/model/WatchHistoryItem;", "app_debug"})
public final class NetflixHomeScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.foundation.ExperimentalFoundationApi.class})
    @androidx.compose.runtime.Composable()
    public static final void NetflixHomeScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.streamflix.app.data.model.Movie, kotlin.Unit> onMovieClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.streamflix.app.data.model.TvShow, kotlin.Unit> onTvShowClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onSeeAllClick, @org.jetbrains.annotations.NotNull()
    com.streamflix.app.presentation.home.HomeViewModel viewModel) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.foundation.ExperimentalFoundationApi.class})
    @androidx.compose.runtime.Composable()
    public static final void HeroBannerSection(@org.jetbrains.annotations.NotNull()
    java.util.List<com.streamflix.app.data.model.Movie> items, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.streamflix.app.data.model.Movie, kotlin.Unit> onItemClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.streamflix.app.data.model.Movie, kotlin.Unit> onPlayClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.streamflix.app.data.model.Movie, kotlin.Unit> onWatchlistClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ContentSection(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.util.List<com.streamflix.app.presentation.home.ContentItem> items, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<java.lang.Object, kotlin.Unit> onItemClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSeeAllClick, boolean showProgress, boolean showTrendingBadge, boolean showNewBadge, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final com.streamflix.app.presentation.home.ContentItem toContentItem(@org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.model.Movie $this$toContentItem) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final com.streamflix.app.presentation.home.ContentItem toContentItem(@org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.model.TvShow $this$toContentItem) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final com.streamflix.app.presentation.home.ContentItem toContentItem(@org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.model.WatchHistoryItem $this$toContentItem) {
        return null;
    }
}