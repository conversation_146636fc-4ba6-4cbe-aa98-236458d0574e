package com.streamflix.app.data.offline;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import androidx.room.*;
import com.google.gson.Gson;
import com.streamflix.app.data.local.UserPreferences;
import com.streamflix.app.data.model.*;
import dagger.hilt.android.qualifiers.ApplicationContext;
import kotlinx.coroutines.flow.*;
import java.io.File;
import javax.inject.Inject;
import javax.inject.Singleton;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u008a\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\t\n\u0002\b\b\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\n\b\u0007\u0018\u00002\u00020\u0001B)\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\u001e\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001bH\u0082@\u00a2\u0006\u0002\u0010\u001dJ$\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00190\u001f2\u0006\u0010 \u001a\u00020!H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\"\u0010#J\u0016\u0010$\u001a\u00020\u00192\u0006\u0010%\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010&J$\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00190\u001f2\u0006\u0010(\u001a\u00020)H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b*\u0010+J\u000e\u0010,\u001a\u00020\u0019H\u0086@\u00a2\u0006\u0002\u0010-J\u0018\u0010.\u001a\u00020\u00192\b\b\u0002\u0010/\u001a\u000200H\u0086@\u00a2\u0006\u0002\u00101J\b\u00102\u001a\u00020\u0019H\u0002J\u000e\u00103\u001a\u00020\u0019H\u0086@\u00a2\u0006\u0002\u0010-J\u000e\u00104\u001a\u00020\u0019H\u0086@\u00a2\u0006\u0002\u0010-J\b\u00105\u001a\u000200H\u0002J\u0014\u00106\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fH\u0086@\u00a2\u0006\u0002\u0010-J\u0018\u00107\u001a\u0004\u0018\u00010!2\u0006\u00108\u001a\u000209H\u0086@\u00a2\u0006\u0002\u0010:J\u0014\u0010;\u001a\b\u0012\u0004\u0012\u00020\u001b0\u000fH\u0086@\u00a2\u0006\u0002\u0010-J\u000e\u0010<\u001a\u00020=H\u0086@\u00a2\u0006\u0002\u0010-J\u0018\u0010>\u001a\u0004\u0018\u00010)2\u0006\u0010?\u001a\u000209H\u0086@\u00a2\u0006\u0002\u0010:J\u0010\u0010@\u001a\u0004\u0018\u00010AH\u0086@\u00a2\u0006\u0002\u0010-J\b\u0010B\u001a\u00020\rH\u0002J\b\u0010C\u001a\u00020\u0019H\u0002J\b\u0010D\u001a\u00020\u0019H\u0002J\u0010\u0010E\u001a\u00020\u00192\u0006\u0010\u001c\u001a\u00020\u001bH\u0002J\u001e\u0010F\u001a\u00020\u00192\u0006\u0010G\u001a\u0002092\u0006\u0010H\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010IJ\b\u0010J\u001a\u00020\u0019H\u0002R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u000f0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\r0\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0015R\u001d\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u000f0\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0015R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006K"}, d2 = {"Lcom/streamflix/app/data/offline/OfflineModeManager;", "", "context", "Landroid/content/Context;", "offlineDatabase", "Lcom/streamflix/app/data/offline/OfflineDatabase;", "userPreferences", "Lcom/streamflix/app/data/local/UserPreferences;", "gson", "Lcom/google/gson/Gson;", "(Landroid/content/Context;Lcom/streamflix/app/data/offline/OfflineDatabase;Lcom/streamflix/app/data/local/UserPreferences;Lcom/google/gson/Gson;)V", "_isOnline", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_offlineContent", "", "Lcom/streamflix/app/data/offline/OfflineContentItem;", "connectivityManager", "Landroid/net/ConnectivityManager;", "isOnline", "Lkotlinx/coroutines/flow/StateFlow;", "()Lkotlinx/coroutines/flow/StateFlow;", "offlineContent", "getOfflineContent", "cacheImage", "", "imagePath", "", "fileName", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cacheMovieForOffline", "Lkotlin/Result;", "movie", "Lcom/streamflix/app/data/model/Movie;", "cacheMovieForOffline-gIAlu-s", "(Lcom/streamflix/app/data/model/Movie;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cacheSearchQuery", "query", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cacheTvShowForOffline", "tvShow", "Lcom/streamflix/app/data/model/TvShow;", "cacheTvShowForOffline-gIAlu-s", "(Lcom/streamflix/app/data/model/TvShow;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cacheUserPreferences", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cleanupOldOfflineContent", "maxAgeMs", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearImageCache", "clearOfflineCache", "clearSearchHistory", "getAvailableStorageSpace", "getOfflineContentList", "getOfflineMovie", "movieId", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getOfflineSearchHistory", "getOfflineStorageUsage", "Lcom/streamflix/app/data/offline/OfflineStorageInfo;", "getOfflineTvShow", "tvShowId", "getOfflineUserPreferences", "Lcom/streamflix/app/data/local/UserPreferences$AppSettings;", "isNetworkAvailable", "loadOfflineContent", "registerNetworkCallback", "removeCachedImage", "removeFromOfflineCache", "contentId", "contentType", "(ILjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "syncOfflineData", "app_debug"})
public final class OfflineModeManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.streamflix.app.data.offline.OfflineDatabase offlineDatabase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.streamflix.app.data.local.UserPreferences userPreferences = null;
    @org.jetbrains.annotations.NotNull()
    private final com.google.gson.Gson gson = null;
    @org.jetbrains.annotations.NotNull()
    private final android.net.ConnectivityManager connectivityManager = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isOnline = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isOnline = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.streamflix.app.data.offline.OfflineContentItem>> _offlineContent = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.streamflix.app.data.offline.OfflineContentItem>> offlineContent = null;
    
    @javax.inject.Inject()
    public OfflineModeManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.offline.OfflineDatabase offlineDatabase, @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.local.UserPreferences userPreferences, @org.jetbrains.annotations.NotNull()
    com.google.gson.Gson gson) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isOnline() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.streamflix.app.data.offline.OfflineContentItem>> getOfflineContent() {
        return null;
    }
    
    private final void registerNetworkCallback() {
    }
    
    private final boolean isNetworkAvailable() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getOfflineMovie(int movieId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.streamflix.app.data.model.Movie> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getOfflineTvShow(int tvShowId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.streamflix.app.data.model.TvShow> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object removeFromOfflineCache(int contentId, @org.jetbrains.annotations.NotNull()
    java.lang.String contentType, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getOfflineContentList(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.streamflix.app.data.offline.OfflineContentItem>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clearOfflineCache(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object cacheSearchQuery(@org.jetbrains.annotations.NotNull()
    java.lang.String query, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getOfflineSearchHistory(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clearSearchHistory(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object cacheUserPreferences(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getOfflineUserPreferences(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.streamflix.app.data.local.UserPreferences.AppSettings> $completion) {
        return null;
    }
    
    private final java.lang.Object cacheImage(java.lang.String imagePath, java.lang.String fileName, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final void removeCachedImage(java.lang.String fileName) {
    }
    
    private final void clearImageCache() {
    }
    
    private final void syncOfflineData() {
    }
    
    private final void loadOfflineContent() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getOfflineStorageUsage(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.streamflix.app.data.offline.OfflineStorageInfo> $completion) {
        return null;
    }
    
    private final long getAvailableStorageSpace() {
        return 0L;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object cleanupOldOfflineContent(long maxAgeMs, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}