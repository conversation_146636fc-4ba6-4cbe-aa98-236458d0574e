package com.streamflix.app.data.local;

import android.content.Context;
import androidx.datastore.core.DataStore;
import androidx.datastore.preferences.core.*;
import com.google.gson.Gson;
import com.streamflix.app.data.model.User;
import dagger.hilt.android.qualifiers.ApplicationContext;
import kotlinx.coroutines.flow.Flow;
import javax.inject.Inject;
import javax.inject.Singleton;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\'\b\u0007\u0018\u0000 :2\u00020\u0001:\u00029:B\u0019\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u000e\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u000e\u0010\n\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fJ\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\fJ\u000e\u0010\u0010\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000f0\fJ\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\fJ\u000e\u0010\u0013\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00140\fJ\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u000f0\fJ\u000e\u0010\u0016\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000f0\fJ\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00120\fJ\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00120\fJ\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u000f0\fJ\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u000f0\fJ\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u000f0\fJ\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00120\fJ\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00120\fJ\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00120\fJ\u000e\u0010\u001f\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u0016\u0010 \u001a\u00020\b2\u0006\u0010!\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\"J\u0016\u0010#\u001a\u00020\b2\u0006\u0010$\u001a\u00020\u0014H\u0086@\u00a2\u0006\u0002\u0010%J\u0016\u0010&\u001a\u00020\b2\u0006\u0010\'\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\"J\u0016\u0010(\u001a\u00020\b2\u0006\u0010)\u001a\u00020\u0012H\u0086@\u00a2\u0006\u0002\u0010*J\u0016\u0010+\u001a\u00020\b2\u0006\u0010,\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\"J\u0016\u0010-\u001a\u00020\b2\u0006\u0010\u001d\u001a\u00020\u0012H\u0086@\u00a2\u0006\u0002\u0010*J\u0016\u0010.\u001a\u00020\b2\u0006\u0010/\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\"J\u0016\u00100\u001a\u00020\b2\u0006\u00101\u001a\u00020\u0012H\u0086@\u00a2\u0006\u0002\u0010*J\u0016\u00102\u001a\u00020\b2\u0006\u00101\u001a\u00020\u0012H\u0086@\u00a2\u0006\u0002\u0010*J\u0016\u00103\u001a\u00020\b2\u0006\u0010\'\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\"J\u0016\u00104\u001a\u00020\b2\u0006\u00105\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\"J\u0016\u00106\u001a\u00020\b2\u0006\u0010,\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\"J\u0016\u00107\u001a\u00020\b2\u0006\u00108\u001a\u00020\u0012H\u0086@\u00a2\u0006\u0002\u0010*R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006;"}, d2 = {"Lcom/streamflix/app/data/local/UserPreferences;", "", "context", "Landroid/content/Context;", "gson", "Lcom/google/gson/Gson;", "(Landroid/content/Context;Lcom/google/gson/Gson;)V", "clearAllData", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearAuthData", "getAllSettings", "Lkotlinx/coroutines/flow/Flow;", "Lcom/streamflix/app/data/local/UserPreferences$AppSettings;", "getAudioLanguage", "", "getAuthToken", "getAutoPlay", "", "getCurrentUser", "Lcom/streamflix/app/data/model/User;", "getDownloadQuality", "getLastAppVersion", "getNotificationsEnabled", "getParentalControl", "getSubtitleLanguage", "getThemeMode", "getVideoQuality", "getWifiOnlyDownload", "isFirstLaunch", "isLoggedIn", "resetToDefaults", "saveAuthToken", "token", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveUser", "user", "(Lcom/streamflix/app/data/model/User;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setAudioLanguage", "language", "setAutoPlay", "autoPlay", "(ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setDownloadQuality", "quality", "setFirstLaunch", "setLastAppVersion", "version", "setNotificationsEnabled", "enabled", "setParentalControl", "setSubtitleLanguage", "setThemeMode", "themeMode", "setVideoQuality", "setWifiOnlyDownload", "wifiOnly", "AppSettings", "Companion", "app_debug"})
public final class UserPreferences {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.google.gson.Gson gson = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> AUTH_TOKEN = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> USER_DATA = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> IS_FIRST_LAUNCH = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> THEME_MODE = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> VIDEO_QUALITY = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> AUTO_PLAY = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> DOWNLOAD_QUALITY = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> WIFI_ONLY_DOWNLOAD = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> NOTIFICATIONS_ENABLED = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> PARENTAL_CONTROL = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> SUBTITLE_LANGUAGE = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> AUDIO_LANGUAGE = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> LAST_APP_VERSION = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.streamflix.app.data.local.UserPreferences.Companion Companion = null;
    
    @javax.inject.Inject()
    public UserPreferences(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.google.gson.Gson gson) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveAuthToken(@org.jetbrains.annotations.NotNull()
    java.lang.String token, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.String> getAuthToken() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveUser(@org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.model.User user, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.streamflix.app.data.model.User> getCurrentUser() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> isLoggedIn() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clearAuthData(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setFirstLaunch(boolean isFirstLaunch, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> isFirstLaunch() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setThemeMode(@org.jetbrains.annotations.NotNull()
    java.lang.String themeMode, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.String> getThemeMode() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setVideoQuality(@org.jetbrains.annotations.NotNull()
    java.lang.String quality, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.String> getVideoQuality() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setAutoPlay(boolean autoPlay, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> getAutoPlay() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setSubtitleLanguage(@org.jetbrains.annotations.NotNull()
    java.lang.String language, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.String> getSubtitleLanguage() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setAudioLanguage(@org.jetbrains.annotations.NotNull()
    java.lang.String language, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.String> getAudioLanguage() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setDownloadQuality(@org.jetbrains.annotations.NotNull()
    java.lang.String quality, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.String> getDownloadQuality() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setWifiOnlyDownload(boolean wifiOnly, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> getWifiOnlyDownload() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setNotificationsEnabled(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> getNotificationsEnabled() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setParentalControl(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> getParentalControl() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setLastAppVersion(@org.jetbrains.annotations.NotNull()
    java.lang.String version, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.String> getLastAppVersion() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clearAllData(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object resetToDefaults(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.streamflix.app.data.local.UserPreferences.AppSettings> getAllSettings() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u001f\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B_\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0003\u0012\b\b\u0002\u0010\b\u001a\u00020\u0006\u0012\b\b\u0002\u0010\t\u001a\u00020\u0006\u0012\b\b\u0002\u0010\n\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0003\u0012\b\b\u0002\u0010\f\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\rJ\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0006H\u00c6\u0003J\t\u0010 \u001a\u00020\u0003H\u00c6\u0003J\t\u0010!\u001a\u00020\u0003H\u00c6\u0003Jc\u0010\"\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\u00062\b\b\u0002\u0010\t\u001a\u00020\u00062\b\b\u0002\u0010\n\u001a\u00020\u00062\b\b\u0002\u0010\u000b\u001a\u00020\u00032\b\b\u0002\u0010\f\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010#\u001a\u00020\u00062\b\u0010$\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010%\u001a\u00020&H\u00d6\u0001J\t\u0010\'\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u000fR\u0011\u0010\t\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0011R\u0011\u0010\n\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0011R\u0011\u0010\u000b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u000fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u000fR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u000fR\u0011\u0010\b\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0011\u00a8\u0006("}, d2 = {"Lcom/streamflix/app/data/local/UserPreferences$AppSettings;", "", "themeMode", "", "videoQuality", "autoPlay", "", "downloadQuality", "wifiOnlyDownload", "notificationsEnabled", "parentalControl", "subtitleLanguage", "audioLanguage", "(Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;ZZZLjava/lang/String;Ljava/lang/String;)V", "getAudioLanguage", "()Ljava/lang/String;", "getAutoPlay", "()Z", "getDownloadQuality", "getNotificationsEnabled", "getParentalControl", "getSubtitleLanguage", "getThemeMode", "getVideoQuality", "getWifiOnlyDownload", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
    public static final class AppSettings {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String themeMode = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String videoQuality = null;
        private final boolean autoPlay = false;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String downloadQuality = null;
        private final boolean wifiOnlyDownload = false;
        private final boolean notificationsEnabled = false;
        private final boolean parentalControl = false;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String subtitleLanguage = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String audioLanguage = null;
        
        public AppSettings(@org.jetbrains.annotations.NotNull()
        java.lang.String themeMode, @org.jetbrains.annotations.NotNull()
        java.lang.String videoQuality, boolean autoPlay, @org.jetbrains.annotations.NotNull()
        java.lang.String downloadQuality, boolean wifiOnlyDownload, boolean notificationsEnabled, boolean parentalControl, @org.jetbrains.annotations.NotNull()
        java.lang.String subtitleLanguage, @org.jetbrains.annotations.NotNull()
        java.lang.String audioLanguage) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getThemeMode() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getVideoQuality() {
            return null;
        }
        
        public final boolean getAutoPlay() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getDownloadQuality() {
            return null;
        }
        
        public final boolean getWifiOnlyDownload() {
            return false;
        }
        
        public final boolean getNotificationsEnabled() {
            return false;
        }
        
        public final boolean getParentalControl() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getSubtitleLanguage() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getAudioLanguage() {
            return null;
        }
        
        public AppSettings() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        public final boolean component3() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component4() {
            return null;
        }
        
        public final boolean component5() {
            return false;
        }
        
        public final boolean component6() {
            return false;
        }
        
        public final boolean component7() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component8() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component9() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.streamflix.app.data.local.UserPreferences.AppSettings copy(@org.jetbrains.annotations.NotNull()
        java.lang.String themeMode, @org.jetbrains.annotations.NotNull()
        java.lang.String videoQuality, boolean autoPlay, @org.jetbrains.annotations.NotNull()
        java.lang.String downloadQuality, boolean wifiOnlyDownload, boolean notificationsEnabled, boolean parentalControl, @org.jetbrains.annotations.NotNull()
        java.lang.String subtitleLanguage, @org.jetbrains.annotations.NotNull()
        java.lang.String audioLanguage) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u000b\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/streamflix/app/data/local/UserPreferences$Companion;", "", "()V", "AUDIO_LANGUAGE", "Landroidx/datastore/preferences/core/Preferences$Key;", "", "AUTH_TOKEN", "AUTO_PLAY", "", "DOWNLOAD_QUALITY", "IS_FIRST_LAUNCH", "LAST_APP_VERSION", "NOTIFICATIONS_ENABLED", "PARENTAL_CONTROL", "SUBTITLE_LANGUAGE", "THEME_MODE", "USER_DATA", "VIDEO_QUALITY", "WIFI_ONLY_DOWNLOAD", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}