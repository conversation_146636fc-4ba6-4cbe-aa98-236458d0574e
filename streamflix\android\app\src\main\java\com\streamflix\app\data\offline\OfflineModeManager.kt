package com.streamflix.app.data.offline

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import androidx.room.*
import com.google.gson.Gson
import com.streamflix.app.data.local.UserPreferences
import com.streamflix.app.data.model.*
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.*
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class OfflineModeManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val offlineDatabase: OfflineDatabase,
    private val userPreferences: UserPreferences,
    private val gson: Gson
) {

    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    
    private val _isOnline = MutableStateFlow(isNetworkAvailable())
    val isOnline: StateFlow<Boolean> = _isOnline.asStateFlow()
    
    private val _offlineContent = MutableStateFlow<List<OfflineContentItem>>(emptyList())
    val offlineContent: StateFlow<List<OfflineContentItem>> = _offlineContent.asStateFlow()

    init {
        registerNetworkCallback()
        loadOfflineContent()
    }

    // ==================== Network Monitoring ====================

    private fun registerNetworkCallback() {
        val networkRequest = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()

        connectivityManager.registerNetworkCallback(
            networkRequest,
            object : ConnectivityManager.NetworkCallback() {
                override fun onAvailable(network: Network) {
                    _isOnline.value = true
                    syncOfflineData()
                }

                override fun onLost(network: Network) {
                    _isOnline.value = false
                }
            }
        )
    }

    private fun isNetworkAvailable(): Boolean {
        val network = connectivityManager.activeNetwork ?: return false
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
        
        return capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
               capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
               capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)
    }

    // ==================== Offline Content Management ====================

    suspend fun cacheMovieForOffline(movie: Movie): Result<Unit> {
        return try {
            val offlineItem = OfflineContentItem(
                id = 0,
                contentId = movie.id,
                contentType = "movie",
                title = movie.title,
                posterPath = movie.posterPath,
                backdropPath = movie.backdropPath,
                overview = movie.overview,
                rating = movie.voteAverage,
                releaseDate = movie.releaseDate,
                runtime = movie.runtime,
                genres = gson.toJson(movie.genres),
                cachedAt = System.currentTimeMillis(),
                lastAccessed = System.currentTimeMillis(),
                fileSize = 0,
                isDownloaded = false
            )

            offlineDatabase.offlineContentDao().insertContent(offlineItem)
            
            // Cache poster image
            movie.posterPath?.let { cacheImage(it, "poster_${movie.id}") }
            movie.backdropPath?.let { cacheImage(it, "backdrop_${movie.id}") }
            
            loadOfflineContent()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun cacheTvShowForOffline(tvShow: TvShow): Result<Unit> {
        return try {
            val offlineItem = OfflineContentItem(
                id = 0,
                contentId = tvShow.id,
                contentType = "tv_show",
                title = tvShow.name,
                posterPath = tvShow.posterPath,
                backdropPath = tvShow.backdropPath,
                overview = tvShow.overview,
                rating = tvShow.voteAverage,
                releaseDate = tvShow.firstAirDate,
                runtime = 0,
                genres = gson.toJson(tvShow.genres),
                cachedAt = System.currentTimeMillis(),
                lastAccessed = System.currentTimeMillis(),
                fileSize = 0,
                isDownloaded = false,
                numberOfSeasons = tvShow.numberOfSeasons,
                numberOfEpisodes = tvShow.numberOfEpisodes
            )

            offlineDatabase.offlineContentDao().insertContent(offlineItem)
            
            // Cache images
            tvShow.posterPath?.let { cacheImage(it, "poster_${tvShow.id}") }
            tvShow.backdropPath?.let { cacheImage(it, "backdrop_${tvShow.id}") }
            
            loadOfflineContent()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun getOfflineMovie(movieId: Int): Movie? {
        return try {
            val offlineItem = offlineDatabase.offlineContentDao()
                .getContentByIdAndType(movieId, "movie")
            
            offlineItem?.let { item ->
                // Update last accessed time
                offlineDatabase.offlineContentDao().updateLastAccessed(item.id, System.currentTimeMillis())
                
                // Convert to Movie object
                Movie(
                    id = item.contentId,
                    title = item.title,
                    overview = item.overview,
                    posterPath = item.posterPath,
                    backdropPath = item.backdropPath,
                    releaseDate = item.releaseDate,
                    voteAverage = item.rating,
                    voteCount = 0,
                    popularity = 0.0,
                    runtime = item.runtime ?: 0,
                    isFeatured = false,
                    isTrending = false,
                    createdAt = "",
                    genres = try {
                        gson.fromJson(item.genres, Array<Genre>::class.java)?.toList()
                    } catch (e: Exception) {
                        emptyList()
                    }
                )
            }
        } catch (e: Exception) {
            null
        }
    }

    suspend fun getOfflineTvShow(tvShowId: Int): TvShow? {
        return try {
            val offlineItem = offlineDatabase.offlineContentDao()
                .getContentByIdAndType(tvShowId, "tv_show")
            
            offlineItem?.let { item ->
                // Update last accessed time
                offlineDatabase.offlineContentDao().updateLastAccessed(item.id, System.currentTimeMillis())
                
                // Convert to TvShow object
                TvShow(
                    id = item.contentId,
                    name = item.title,
                    overview = item.overview,
                    posterPath = item.posterPath,
                    backdropPath = item.backdropPath,
                    firstAirDate = item.releaseDate,
                    lastAirDate = null,
                    voteAverage = item.rating,
                    voteCount = 0,
                    popularity = 0.0,
                    numberOfSeasons = item.numberOfSeasons ?: 0,
                    numberOfEpisodes = item.numberOfEpisodes ?: 0,
                    isFeatured = false,
                    isTrending = false,
                    createdAt = "",
                    genres = try {
                        gson.fromJson(item.genres, Array<Genre>::class.java)?.toList()
                    } catch (e: Exception) {
                        emptyList()
                    }
                )
            }
        } catch (e: Exception) {
            null
        }
    }

    suspend fun removeFromOfflineCache(contentId: Int, contentType: String) {
        try {
            offlineDatabase.offlineContentDao().deleteContent(contentId, contentType)
            
            // Remove cached images
            removeCachedImage("poster_$contentId")
            removeCachedImage("backdrop_$contentId")
            
            loadOfflineContent()
        } catch (e: Exception) {
            // Handle error silently
        }
    }

    suspend fun getOfflineContentList(): List<OfflineContentItem> {
        return try {
            offlineDatabase.offlineContentDao().getAllContent()
        } catch (e: Exception) {
            emptyList()
        }
    }

    suspend fun clearOfflineCache() {
        try {
            offlineDatabase.offlineContentDao().deleteAllContent()
            clearImageCache()
            _offlineContent.value = emptyList()
        } catch (e: Exception) {
            // Handle error silently
        }
    }

    // ==================== Search History Offline ====================

    suspend fun cacheSearchQuery(query: String) {
        try {
            val searchHistory = OfflineSearchHistory(
                id = 0,
                query = query,
                timestamp = System.currentTimeMillis()
            )
            offlineDatabase.searchHistoryDao().insertSearch(searchHistory)
        } catch (e: Exception) {
            // Handle error silently
        }
    }

    suspend fun getOfflineSearchHistory(): List<String> {
        return try {
            offlineDatabase.searchHistoryDao()
                .getRecentSearches(20)
                .map { it.query }
        } catch (e: Exception) {
            emptyList()
        }
    }

    suspend fun clearSearchHistory() {
        try {
            offlineDatabase.searchHistoryDao().deleteAllSearches()
        } catch (e: Exception) {
            // Handle error silently
        }
    }

    // ==================== User Preferences Offline ====================

    suspend fun cacheUserPreferences() {
        try {
            val settings = userPreferences.getAllSettings().first()
            val prefsJson = gson.toJson(settings)
            
            val offlinePrefs = OfflineUserPreferences(
                id = 1, // Single row
                preferencesJson = prefsJson,
                lastUpdated = System.currentTimeMillis()
            )
            
            offlineDatabase.userPreferencesDao().insertOrUpdatePreferences(offlinePrefs)
        } catch (e: Exception) {
            // Handle error silently
        }
    }

    suspend fun getOfflineUserPreferences(): UserPreferences.AppSettings? {
        return try {
            val offlinePrefs = offlineDatabase.userPreferencesDao().getPreferences()
            offlinePrefs?.let {
                gson.fromJson(it.preferencesJson, UserPreferences.AppSettings::class.java)
            }
        } catch (e: Exception) {
            null
        }
    }

    // ==================== Image Caching ====================

    private suspend fun cacheImage(imagePath: String, fileName: String) {
        try {
            val cacheDir = File(context.cacheDir, "images")
            if (!cacheDir.exists()) {
                cacheDir.mkdirs()
            }
            
            // This would typically download and cache the image
            // Implementation depends on your image loading library (Coil, Glide, etc.)
        } catch (e: Exception) {
            // Handle error silently
        }
    }

    private fun removeCachedImage(fileName: String) {
        try {
            val cacheDir = File(context.cacheDir, "images")
            val imageFile = File(cacheDir, "$fileName.jpg")
            if (imageFile.exists()) {
                imageFile.delete()
            }
        } catch (e: Exception) {
            // Handle error silently
        }
    }

    private fun clearImageCache() {
        try {
            val cacheDir = File(context.cacheDir, "images")
            if (cacheDir.exists()) {
                cacheDir.deleteRecursively()
            }
        } catch (e: Exception) {
            // Handle error silently
        }
    }

    // ==================== Data Sync ====================

    private fun syncOfflineData() {
        // Sync offline data when network becomes available
        // This would typically upload any pending data and download updates
    }

    private fun loadOfflineContent() {
        // Load offline content from database
        // This would be called to refresh the offline content list
    }

    // ==================== Storage Management ====================

    suspend fun getOfflineStorageUsage(): OfflineStorageInfo {
        return try {
            val contentItems = offlineDatabase.offlineContentDao().getAllContent()
            val totalSize = contentItems.sumOf { it.fileSize }
            val itemCount = contentItems.size
            
            val cacheDir = File(context.cacheDir, "images")
            val imageCacheSize = if (cacheDir.exists()) {
                cacheDir.walkTopDown().filter { it.isFile }.map { it.length() }.sum()
            } else {
                0L
            }
            
            OfflineStorageInfo(
                totalContentSize = totalSize,
                imageCacheSize = imageCacheSize,
                totalItems = itemCount,
                availableSpace = getAvailableStorageSpace()
            )
        } catch (e: Exception) {
            OfflineStorageInfo(0, 0, 0, 0)
        }
    }

    private fun getAvailableStorageSpace(): Long {
        return try {
            val cacheDir = context.cacheDir
            cacheDir.freeSpace
        } catch (e: Exception) {
            0L
        }
    }

    suspend fun cleanupOldOfflineContent(maxAgeMs: Long = 30L * 24 * 60 * 60 * 1000) { // 30 days
        try {
            val cutoffTime = System.currentTimeMillis() - maxAgeMs
            offlineDatabase.offlineContentDao().deleteOldContent(cutoffTime)
            loadOfflineContent()
        } catch (e: Exception) {
            // Handle error silently
        }
    }
}

// ==================== Data Classes ====================

data class OfflineStorageInfo(
    val totalContentSize: Long,
    val imageCacheSize: Long,
    val totalItems: Int,
    val availableSpace: Long
) {
    val totalUsedSpace: Long get() = totalContentSize + imageCacheSize
    
    fun formatSize(bytes: Long): String {
        val kb = bytes / 1024.0
        val mb = kb / 1024.0
        val gb = mb / 1024.0
        
        return when {
            gb >= 1 -> String.format("%.1f GB", gb)
            mb >= 1 -> String.format("%.1f MB", mb)
            else -> String.format("%.1f KB", kb)
        }
    }
}

// ==================== Database Entities ====================

@Entity(tableName = "offline_content")
data class OfflineContentItem(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val contentId: Int,
    val contentType: String, // "movie" or "tv_show"
    val title: String,
    val posterPath: String?,
    val backdropPath: String?,
    val overview: String?,
    val rating: Double,
    val releaseDate: String?,
    val runtime: Int?,
    val genres: String?, // JSON string
    val cachedAt: Long,
    val lastAccessed: Long,
    val fileSize: Long,
    val isDownloaded: Boolean,
    val numberOfSeasons: Int? = null,
    val numberOfEpisodes: Int? = null
)

@Entity(tableName = "offline_search_history")
data class OfflineSearchHistory(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val query: String,
    val timestamp: Long
)

@Entity(tableName = "offline_user_preferences")
data class OfflineUserPreferences(
    @PrimaryKey
    val id: Int = 1,
    val preferencesJson: String,
    val lastUpdated: Long
)

// ==================== DAOs ====================

@Dao
interface OfflineContentDao {
    @Query("SELECT * FROM offline_content ORDER BY lastAccessed DESC")
    suspend fun getAllContent(): List<OfflineContentItem>
    
    @Query("SELECT * FROM offline_content WHERE contentId = :contentId AND contentType = :contentType")
    suspend fun getContentByIdAndType(contentId: Int, contentType: String): OfflineContentItem?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertContent(content: OfflineContentItem)
    
    @Query("DELETE FROM offline_content WHERE contentId = :contentId AND contentType = :contentType")
    suspend fun deleteContent(contentId: Int, contentType: String)
    
    @Query("DELETE FROM offline_content")
    suspend fun deleteAllContent()
    
    @Query("DELETE FROM offline_content WHERE lastAccessed < :cutoffTime")
    suspend fun deleteOldContent(cutoffTime: Long)
    
    @Query("UPDATE offline_content SET lastAccessed = :timestamp WHERE id = :id")
    suspend fun updateLastAccessed(id: Long, timestamp: Long)
}

@Dao
interface OfflineSearchHistoryDao {
    @Query("SELECT * FROM offline_search_history ORDER BY timestamp DESC LIMIT :limit")
    suspend fun getRecentSearches(limit: Int): List<OfflineSearchHistory>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSearch(search: OfflineSearchHistory)
    
    @Query("DELETE FROM offline_search_history")
    suspend fun deleteAllSearches()
}

@Dao
interface OfflineUserPreferencesDao {
    @Query("SELECT * FROM offline_user_preferences WHERE id = 1")
    suspend fun getPreferences(): OfflineUserPreferences?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdatePreferences(preferences: OfflineUserPreferences)
}

// ==================== Database ====================

@Database(
    entities = [
        OfflineContentItem::class,
        OfflineSearchHistory::class,
        OfflineUserPreferences::class
    ],
    version = 1,
    exportSchema = false
)
abstract class OfflineDatabase : RoomDatabase() {
    abstract fun offlineContentDao(): OfflineContentDao
    abstract fun searchHistoryDao(): OfflineSearchHistoryDao
    abstract fun userPreferencesDao(): OfflineUserPreferencesDao
}
