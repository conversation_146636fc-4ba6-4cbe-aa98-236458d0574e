<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/config/database.php')) {
    header('Location: install.php');
    exit('Please run the installer first.');
}

require_once 'includes/functions.php';

// Get parameters
$type = isset($_GET['type']) ? sanitizeInput($_GET['type']) : 'all';
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$genre = isset($_GET['genre']) ? (int)$_GET['genre'] : 0;
$sort = isset($_GET['sort']) ? sanitizeInput($_GET['sort']) : 'popularity';
$filter = isset($_GET['filter']) ? sanitizeInput($_GET['filter']) : '';
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = 24;
$offset = ($page - 1) * $limit;

// Initialize variables
$content = [];
$total_count = 0;
$genres = [];
$tables = [];
$union_query = '';

try {
    $db = new Database();
    $conn = $db->connect();

    // Get genres for filter
    $stmt = $conn->query("SELECT * FROM genres ORDER BY name");
    $genres = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Build query based on type and filters
    $where_conditions = [];
    $params = [];
    $select_fields = [];

    // Simple approach - get content directly
    if ($type === 'movie' || $type === 'all') {
        $movie_where = ["1=1"];
        $movie_params = [];

        if ($search) {
            $movie_where[] = "(title LIKE ? OR overview LIKE ?)";
            $movie_params[] = "%{$search}%";
            $movie_params[] = "%{$search}%";
        }

        if ($genre > 0) {
            $movie_where[] = "id IN (SELECT movie_id FROM movie_genres WHERE genre_id = ?)";
            $movie_params[] = $genre;
        }

        if ($filter === 'featured') {
            $movie_where[] = "is_featured = 1";
        } elseif ($filter === 'trending') {
            $movie_where[] = "is_trending = 1";
        }

        $movie_order = "ORDER BY ";
        switch ($sort) {
            case 'title':
                $movie_order .= "title ASC";
                break;
            case 'year':
                $movie_order .= "release_date DESC";
                break;
            case 'rating':
                $movie_order .= "vote_average DESC";
                break;
            default:
                $movie_order .= "popularity DESC";
        }

        $movie_query = "
            SELECT id, tmdb_id, title as name, poster_path, backdrop_path,
                   release_date as air_date, vote_average, vote_count, popularity,
                   overview, 'movie' as content_type
            FROM movies
            WHERE " . implode(' AND ', $movie_where) . "
            {$movie_order}
        ";

        // Get movies
        $stmt = $conn->prepare($movie_query);
        $stmt->execute($movie_params);
        $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($movies as $movie) {
            $content[] = $movie;
        }
    }

    if ($type === 'tv' || $type === 'all') {
        $tv_where = ["1=1"];
        $tv_params = [];

        if ($search) {
            $tv_where[] = "(name LIKE ? OR overview LIKE ?)";
            $tv_params[] = "%{$search}%";
            $tv_params[] = "%{$search}%";
        }

        if ($genre > 0) {
            $tv_where[] = "id IN (SELECT tv_show_id FROM tv_show_genres WHERE genre_id = ?)";
            $tv_params[] = $genre;
        }

        if ($filter === 'featured') {
            $tv_where[] = "is_featured = 1";
        } elseif ($filter === 'trending') {
            $tv_where[] = "is_trending = 1";
        }

        $tv_order = "ORDER BY ";
        switch ($sort) {
            case 'title':
                $tv_order .= "name ASC";
                break;
            case 'year':
                $tv_order .= "first_air_date DESC";
                break;
            case 'rating':
                $tv_order .= "vote_average DESC";
                break;
            default:
                $tv_order .= "popularity DESC";
        }

        $tv_query = "
            SELECT id, tmdb_id, name, poster_path, backdrop_path,
                   first_air_date as air_date, vote_average, vote_count, popularity,
                   overview, 'tv_show' as content_type
            FROM tv_shows
            WHERE " . implode(' AND ', $tv_where) . "
            {$tv_order}
        ";

        // Get TV shows
        $stmt = $conn->prepare($tv_query);
        $stmt->execute($tv_params);
        $tv_shows = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($tv_shows as $show) {
            $content[] = $show;
        }
    }

    // Calculate total count
    $total_count = count($content);

    // Sort content
    if (!empty($content)) {
        usort($content, function($a, $b) use ($sort) {
            switch ($sort) {
                case 'title':
                    return strcmp($a['name'], $b['name']);
                case 'year':
                    return strtotime($b['air_date']) - strtotime($a['air_date']);
                case 'rating':
                    return $b['vote_average'] - $a['vote_average'];
                default:
                    return $b['popularity'] - $a['popularity'];
            }
        });

        // Apply pagination
        $content = array_slice($content, $offset, $limit);
    }

} catch (Exception $e) {
    error_log("Browse page error: " . $e->getMessage());
}

// Calculate pagination
$total_pages = ceil($total_count / $limit);
$page_title = 'Browse';
if ($type === 'movie') {
    $page_title = 'Movies';
} elseif ($type === 'tv') {
    $page_title = 'TV Shows';
}

if ($search) {
    $page_title .= " - Search: " . htmlspecialchars($search);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <meta name="description" content="Browse and discover movies and TV shows on <?php echo SITE_NAME; ?>">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        /* Modern Browse Page Design */
        :root {
            --primary-color: #e50914;
            --primary-dark: #b20710;
            --secondary-color: #1a1a1a;
            --dark-bg: #0a0a0a;
            --card-bg: #1f1f1f;
            --text-primary: #ffffff;
            --text-secondary: #b3b3b3;
            --text-muted: #737373;
            --border-color: #333333;
            --gradient-primary: linear-gradient(135deg, #e50914 0%, #b20710 100%);
            --gradient-dark: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            --shadow-light: 0 4px 20px rgba(229, 9, 20, 0.1);
            --shadow-medium: 0 8px 30px rgba(0, 0, 0, 0.3);
            --shadow-heavy: 0 20px 60px rgba(0, 0, 0, 0.5);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--dark-bg);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(10, 10, 10, 0.98);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 4%;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 800;
            color: var(--primary-color);
            text-decoration: none;
            letter-spacing: -0.5px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }

        .nav-menu a {
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
            position: relative;
        }

        .nav-menu a:hover,
        .nav-menu a.active {
            color: var(--primary-color);
        }

        .nav-menu a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--primary-color);
            transition: width 0.3s ease;
        }

        .nav-menu a:hover::after,
        .nav-menu a.active::after {
            width: 100%;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* Search Container */
        .search-container {
            position: relative;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .search-input {
            padding: 0.6rem 0.8rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            font-size: 0.85rem;
            width: 200px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(229, 9, 20, 0.1);
        }

        .search-input::placeholder {
            color: var(--text-muted);
        }

        .search-btn {
            background: var(--gradient-primary);
            border: none;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .search-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
        }

        .btn {
            padding: 0.7rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-light);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(229, 9, 20, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        /* Main Content */
        .main-content {
            margin-top: 80px;
            padding: 2rem 4%;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem 0;
        }

        .page-title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .page-subtitle {
            font-size: 1.2rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Filters */
        .filters {
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
            align-items: center;
            justify-content: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: var(--card-bg);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 0.8rem;
        }

        .filter-group label {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.9rem;
        }

        .filter-select {
            padding: 0.7rem 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            font-size: 0.9rem;
            min-width: 150px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(229, 9, 20, 0.1);
        }

        .filter-select option {
            background: var(--card-bg);
            color: var(--text-primary);
        }

        /* Content Grid */
        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .content-card {
            background: var(--card-bg);
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            cursor: pointer;
            position: relative;
            aspect-ratio: 2/3;
            box-shadow: var(--shadow-medium);
        }

        .content-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-heavy);
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, transparent 0%, rgba(229, 9, 20, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .content-card:hover::before {
            opacity: 1;
        }

        .content-card img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .content-card:hover img {
            transform: scale(1.05);
        }

        .card-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
            padding: 1.5rem;
            transform: translateY(100%);
            transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            z-index: 2;
        }

        .content-card:hover .card-overlay {
            transform: translateY(0);
        }

        .card-title {
            font-size: 1rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .card-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.85rem;
            color: var(--text-secondary);
        }

        .card-rating {
            display: flex;
            align-items: center;
            gap: 0.3rem;
            color: #ffd700;
            font-weight: 600;
        }

        .card-type {
            background: var(--primary-color);
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        /* 18+ Filter Toggle */
        .adult-filter-container {
            display: flex;
            align-items: center;
        }

        .adult-filter-toggle {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            user-select: none;
        }

        .adult-filter-toggle input[type="checkbox"] {
            display: none;
        }

        .toggle-slider {
            position: relative;
            width: 45px;
            height: 24px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .toggle-slider::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 18px;
            height: 18px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .adult-filter-toggle input[type="checkbox"]:checked + .toggle-slider {
            background: #ff1744;
            border-color: #ff1744;
        }

        .adult-filter-toggle input[type="checkbox"]:checked + .toggle-slider::before {
            transform: translateX(21px);
        }

        .toggle-label {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-primary);
            transition: color 0.3s ease;
        }

        .adult-filter-toggle input[type="checkbox"]:checked ~ .toggle-label {
            color: #ff1744;
        }

        /* Adult Content Blur Effect */
        .adult-content {
            transition: filter 0.3s ease;
        }

        .adult-content.blurred {
            filter: blur(10px);
            opacity: 0.5;
        }

        .adult-content.blurred:hover {
            filter: blur(5px);
            opacity: 0.7;
        }

        /* Adult Badge */
        .adult-badge {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: #ff1744;
            color: white;
            padding: 0.3rem 0.6rem;
            border-radius: 6px;
            font-size: 0.7rem;
            font-weight: 700;
            z-index: 3;
            box-shadow: 0 2px 8px rgba(255, 23, 68, 0.3);
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin: 3rem 0;
        }

        .pagination a,
        .pagination span {
            padding: 0.8rem 1.2rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .pagination a {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .pagination a:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .pagination .current {
            background: var(--gradient-primary);
            color: white;
            border: 1px solid var(--primary-color);
        }

        .pagination .disabled {
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-muted);
            border: 1px solid rgba(255, 255, 255, 0.1);
            cursor: not-allowed;
        }

        /* No Results */
        .no-results {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--text-secondary);
        }

        .no-results h3 {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .no-results p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        /* Results Info */
        .results-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .results-count {
            font-size: 1.1rem;
            color: var(--text-secondary);
        }

        .results-count strong {
            color: var(--primary-color);
        }

        /* Mobile Menu */
        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            color: var(--text-primary);
            font-size: 1.5rem;
            cursor: pointer;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .navbar {
                padding: 1rem 5%;
            }

            .nav-menu {
                display: none;
            }

            .mobile-menu-toggle {
                display: block;
            }

            .main-content {
                padding: 2rem 5%;
            }

            .page-title {
                font-size: 2rem;
            }

            .filters {
                flex-direction: column;
                gap: 1rem;
            }

            .content-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }

            .search-input {
                width: 200px;
            }
        }

        @media (max-width: 480px) {
            .content-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .pagination {
                flex-wrap: wrap;
                gap: 0.5rem;
            }

            .pagination a,
            .pagination span {
                padding: 0.6rem 1rem;
                font-size: 0.9rem;
            }
        }
    </style>
</head>

<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <a href="index.php" class="logo"><?php echo SITE_NAME; ?></a>

            <ul class="nav-menu">
                <li><a href="index.php">Home</a></li>
                <li><a href="browse.php?type=movie" <?php echo $type === 'movie' ? 'class="active"' : ''; ?>>Movies</a></li>
                <li><a href="browse.php?type=tv" <?php echo $type === 'tv' ? 'class="active"' : ''; ?>>TV Shows</a></li>
                <li><a href="anime.php">🎌 Anime</a></li>
                <li><a href="hentai.php">🔞 Hentai</a></li>
            </ul>

            <div class="user-menu">
                <!-- Search Box -->
                <div class="search-container">
                    <input type="text" id="searchInput" class="search-input"
                           placeholder="Search movies & TV shows..."
                           value="<?php echo htmlspecialchars($search); ?>">
                    <button class="search-btn" onclick="performSearch()">🔍</button>
                </div>

                <!-- 18+ Filter Toggle -->
                <div class="adult-filter-container">
                    <label class="adult-filter-toggle">
                        <input type="checkbox" id="adultFilter" onchange="toggleAdultFilter()">
                        <span class="toggle-slider"></span>
                        <span class="toggle-label">🔞 18+</span>
                    </label>
                </div>

                <?php if (isLoggedIn()): ?>
                    <span style="color: var(--text-secondary);">Welcome, <?php echo $_SESSION['username']; ?></span>
                    <?php if (isAdmin()): ?>
                        <a href="admin/index.php" class="btn btn-secondary">Admin</a>
                    <?php endif; ?>
                    <a href="logout.php" class="btn btn-primary">Logout</a>
                <?php else: ?>
                    <a href="login.php" class="btn btn-secondary">Login</a>
                    <a href="register.php" class="btn btn-primary">Sign Up</a>
                <?php endif; ?>

                <button class="mobile-menu-toggle">☰</button>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title"><?php echo $page_title; ?></h1>
            <p class="page-subtitle">
                <?php if ($search): ?>
                    Search results for "<?php echo htmlspecialchars($search); ?>"
                <?php elseif ($type === 'movie'): ?>
                    Discover amazing movies to watch
                <?php elseif ($type === 'tv'): ?>
                    Find your next binge-worthy TV series
                <?php else: ?>
                    Explore our complete collection of movies and TV shows
                <?php endif; ?>
            </p>
        </div>

        <!-- Filters -->
        <div class="filters">
            <div class="filter-group">
                <label for="typeFilter">Type:</label>
                <select id="typeFilter" class="filter-select" onchange="updateFilters()">
                    <option value="all" <?php echo $type === 'all' ? 'selected' : ''; ?>>All Content</option>
                    <option value="movie" <?php echo $type === 'movie' ? 'selected' : ''; ?>>Movies</option>
                    <option value="tv" <?php echo $type === 'tv' ? 'selected' : ''; ?>>TV Shows</option>
                </select>
            </div>

            <div class="filter-group">
                <label for="genreFilter">Genre:</label>
                <select id="genreFilter" class="filter-select" onchange="updateFilters()">
                    <option value="0">All Genres</option>
                    <?php foreach ($genres as $g): ?>
                        <option value="<?php echo $g['id']; ?>" <?php echo $genre == $g['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($g['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="filter-group">
                <label for="sortFilter">Sort by:</label>
                <select id="sortFilter" class="filter-select" onchange="updateFilters()">
                    <option value="popularity" <?php echo $sort === 'popularity' ? 'selected' : ''; ?>>Popularity</option>
                    <option value="title" <?php echo $sort === 'title' ? 'selected' : ''; ?>>Title</option>
                    <option value="year" <?php echo $sort === 'year' ? 'selected' : ''; ?>>Year</option>
                    <option value="rating" <?php echo $sort === 'rating' ? 'selected' : ''; ?>>Rating</option>
                </select>
            </div>

            <?php if ($search || $genre || $filter): ?>
                <div class="filter-group">
                    <a href="browse.php?type=<?php echo $type; ?>" class="btn btn-secondary">Clear Filters</a>
                </div>
            <?php endif; ?>
        </div>

        <!-- Results Info -->
        <?php if (!empty($content)): ?>
            <div class="results-info">
                <div class="results-count">
                    Showing <strong><?php echo count($content); ?></strong> of <strong><?php echo number_format($total_count); ?></strong> results
                </div>
                <div class="page-info">
                    Page <strong><?php echo $page; ?></strong> of <strong><?php echo $total_pages; ?></strong>
                </div>
            </div>
        <?php endif; ?>

        <!-- Content Grid -->
        <?php if (!empty($content)): ?>
            <div class="content-grid">
                <?php foreach ($content as $item): ?>
                    <a href="player.php?id=<?php echo $item['tmdb_id']; ?>&type=<?php echo $item['content_type']; ?>" class="content-card">
                        <img src="<?php echo getImageUrl($item['poster_path'], 'w500'); ?>"
                             alt="<?php echo htmlspecialchars($item['name']); ?>"
                             loading="lazy">
                        <div class="card-overlay">
                            <div class="card-title"><?php echo htmlspecialchars($item['name']); ?></div>
                            <div class="card-meta">
                                <span><?php echo date('Y', strtotime($item['air_date'])); ?></span>
                                <div class="card-rating">
                                    <span>⭐</span>
                                    <span><?php echo number_format($item['vote_average'], 1); ?></span>
                                </div>
                            </div>
                            <div class="card-type"><?php echo $item['content_type'] === 'movie' ? 'Movie' : 'TV Show'; ?></div>
                        </div>
                    </a>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="pagination">
                    <?php
                    $base_url = "browse.php?type={$type}";
                    if ($search) $base_url .= "&search=" . urlencode($search);
                    if ($genre) $base_url .= "&genre={$genre}";
                    if ($sort !== 'popularity') $base_url .= "&sort={$sort}";
                    if ($filter) $base_url .= "&filter={$filter}";
                    ?>

                    <?php if ($page > 1): ?>
                        <a href="<?php echo $base_url; ?>&page=1">First</a>
                        <a href="<?php echo $base_url; ?>&page=<?php echo $page - 1; ?>">Previous</a>
                    <?php else: ?>
                        <span class="disabled">First</span>
                        <span class="disabled">Previous</span>
                    <?php endif; ?>

                    <?php
                    $start_page = max(1, $page - 2);
                    $end_page = min($total_pages, $page + 2);

                    for ($i = $start_page; $i <= $end_page; $i++):
                    ?>
                        <?php if ($i == $page): ?>
                            <span class="current"><?php echo $i; ?></span>
                        <?php else: ?>
                            <a href="<?php echo $base_url; ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                        <?php endif; ?>
                    <?php endfor; ?>

                    <?php if ($page < $total_pages): ?>
                        <a href="<?php echo $base_url; ?>&page=<?php echo $page + 1; ?>">Next</a>
                        <a href="<?php echo $base_url; ?>&page=<?php echo $total_pages; ?>">Last</a>
                    <?php else: ?>
                        <span class="disabled">Next</span>
                        <span class="disabled">Last</span>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

        <?php else: ?>
            <!-- No Results -->
            <div class="no-results">
                <h3>No Results Found</h3>
                <?php if ($search): ?>
                    <p>Sorry, we couldn't find any content matching "<?php echo htmlspecialchars($search); ?>"</p>
                    <p>Try adjusting your search terms or browse our collection.</p>
                <?php else: ?>
                    <p>No content available with the selected filters.</p>
                    <p>Try changing your filters or browse all content.</p>
                <?php endif; ?>
                <a href="browse.php" class="btn btn-primary">Browse All Content</a>
            </div>
        <?php endif; ?>
    </main>

    <!-- JavaScript -->
    <script>
        function updateFilters() {
            const type = document.getElementById('typeFilter').value;
            const genre = document.getElementById('genreFilter').value;
            const sort = document.getElementById('sortFilter').value;

            let url = `browse.php?type=${type}`;
            if (genre && genre !== '0') url += `&genre=${genre}`;
            if (sort && sort !== 'popularity') url += `&sort=${sort}`;

            const search = document.getElementById('searchInput').value.trim();
            if (search) url += `&search=${encodeURIComponent(search)}`;

            window.location.href = url;
        }

        function performSearch() {
            const search = document.getElementById('searchInput').value.trim();
            if (search) {
                const type = document.getElementById('typeFilter').value;
                window.location.href = `browse.php?type=${type}&search=${encodeURIComponent(search)}`;
            }
        }

        // Handle Enter key in search
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                performSearch();
            }
        });

        // Auto-submit search after typing stops
        let searchTimeout;
        document.getElementById('searchInput').addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length >= 3) {
                searchTimeout = setTimeout(() => {
                    performSearch();
                }, 1000);
            }
        });

        // 18+ Filter functionality
        function toggleAdultFilter() {
            const adultFilter = document.getElementById('adultFilter');
            const adultContent = document.querySelectorAll('.adult-content');

            if (adultFilter.checked) {
                // Blur adult content
                adultContent.forEach(element => {
                    element.classList.add('blurred');
                });
                localStorage.setItem('adultFilterEnabled', 'true');
            } else {
                // Show adult content
                adultContent.forEach(element => {
                    element.classList.remove('blurred');
                });
                localStorage.setItem('adultFilterEnabled', 'false');
            }
        }

        // Load adult filter state from localStorage
        function loadAdultFilterState() {
            const adultFilter = document.getElementById('adultFilter');
            const savedState = localStorage.getItem('adultFilterEnabled');

            if (savedState === 'true') {
                adultFilter.checked = true;
                toggleAdultFilter();
            }
        }

        // Initialize adult filter on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadAdultFilterState();
        });

        console.log('🎬 Browse page loaded successfully!');
    </script>
</body>
</html>
