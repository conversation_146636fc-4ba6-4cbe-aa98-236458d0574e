<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

// Handle settings update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $site_name = sanitizeInput($_POST['site_name'] ?? '');
    $site_url = sanitizeInput($_POST['site_url'] ?? '');
    $tmdb_api_key = sanitizeInput($_POST['tmdb_api_key'] ?? '');
    $admin_email = sanitizeInput($_POST['admin_email'] ?? '');
    
    if (!empty($site_name) && !empty($site_url) && !empty($tmdb_api_key) && !empty($admin_email)) {
        try {
            // Read current config
            $config_file = '../config/database.php';
            $config_content = file_get_contents($config_file);
            
            // Update values
            $config_content = preg_replace("/define\('SITE_NAME', '.*?'\);/", "define('SITE_NAME', '{$site_name}');", $config_content);
            $config_content = preg_replace("/define\('SITE_URL', '.*?'\);/", "define('SITE_URL', '{$site_url}');", $config_content);
            $config_content = preg_replace("/define\('TMDB_API_KEY', '.*?'\);/", "define('TMDB_API_KEY', '{$tmdb_api_key}');", $config_content);
            $config_content = preg_replace("/define\('ADMIN_EMAIL', '.*?'\);/", "define('ADMIN_EMAIL', '{$admin_email}');", $config_content);
            
            // Write back to file
            file_put_contents($config_file, $config_content);
            
            $message = 'Settings updated successfully!';
        } catch (Exception $e) {
            $error = 'Failed to update settings: ' . $e->getMessage();
        }
    } else {
        $error = 'Please fill in all required fields.';
    }
}

// Get current settings
$current_settings = [
    'site_name' => defined('SITE_NAME') ? SITE_NAME : '',
    'site_url' => defined('SITE_URL') ? SITE_URL : '',
    'tmdb_api_key' => defined('TMDB_API_KEY') ? TMDB_API_KEY : '',
    'admin_email' => defined('ADMIN_EMAIL') ? ADMIN_EMAIL : ''
];

// Get system info
try {
    $db = new Database();
    $conn = $db->connect();
    
    $system_info = [
        'php_version' => phpversion(),
        'mysql_version' => $conn->query('SELECT VERSION()')->fetchColumn(),
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'disk_space' => function_exists('disk_free_space') ? formatBytes(disk_free_space('.')) : 'Unknown',
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'upload_max_filesize' => ini_get('upload_max_filesize')
    ];
} catch (Exception $e) {
    $system_info = [];
}

function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="assets/admin-style.css">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .settings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }
        
        .settings-section {
            background: var(--secondary-color);
            padding: 30px;
            border-radius: 8px;
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--text-primary);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            background: var(--dark-bg);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-primary);
            font-size: 1rem;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .info-item {
            background: var(--dark-bg);
            padding: 15px;
            border-radius: 4px;
        }
        
        .info-label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 5px;
        }
        
        .info-value {
            color: var(--text-secondary);
            font-family: monospace;
        }
        
        .test-connection {
            margin-top: 20px;
        }
        
        .connection-status {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-weight: 500;
        }
        
        .status-success {
            background: #28a745;
            color: white;
        }
        
        .status-error {
            background: #dc3545;
            color: white;
        }
        
        .backup-section {
            margin-top: 20px;
        }
        
        .backup-btn {
            margin: 5px;
        }
        
        @media (max-width: 768px) {
            .settings-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <span>Welcome, <?php echo $_SESSION['username']; ?></span>
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>⚙️ Settings</h1>
            <p>Configure your StreamFlix installation</p>
        </div>

        <nav class="admin-nav">
            <a href="index.php">📊 Dashboard</a>
            <a href="movies.php">🎬 Movies</a>
            <a href="tv-shows.php">📺 TV Shows</a>
            <a href="users.php">👥 Users</a>
            <a href="servers.php">🖥️ Servers</a>
            <a href="analytics.php">📈 Analytics</a>
            <a href="import.php">📥 Import</a>
            <a href="maintenance.php">🔧 Maintenance</a>
            <a href="database-updater.php">🗄️ DB Updater</a>
            <a href="quick-setup.php">🚀 Quick Setup</a>
            <a href="settings.php" class="active">⚙️ Settings</a>
        </nav>

        <?php if (isset($message)): ?>
            <div class="success-message"><?php echo $message; ?></div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="error-message"><?php echo $error; ?></div>
        <?php endif; ?>

        <div class="settings-grid">
            <!-- Site Settings -->
            <div class="settings-section">
                <h2 class="section-title">Site Configuration</h2>
                
                <form method="POST">
                    <div class="form-group">
                        <label for="site_name">Site Name</label>
                        <input type="text" id="site_name" name="site_name" 
                               value="<?php echo htmlspecialchars($current_settings['site_name']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="site_url">Site URL</label>
                        <input type="url" id="site_url" name="site_url" 
                               value="<?php echo htmlspecialchars($current_settings['site_url']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="tmdb_api_key">TMDB API Key</label>
                        <input type="text" id="tmdb_api_key" name="tmdb_api_key" 
                               value="<?php echo htmlspecialchars($current_settings['tmdb_api_key']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="admin_email">Admin Email</label>
                        <input type="email" id="admin_email" name="admin_email" 
                               value="<?php echo htmlspecialchars($current_settings['admin_email']); ?>" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Save Settings</button>
                </form>
                
                <div class="test-connection">
                    <button class="btn btn-secondary" onclick="testTMDBConnection()">Test TMDB Connection</button>
                    <div id="connectionStatus"></div>
                </div>
            </div>

            <!-- System Information -->
            <div class="settings-section">
                <h2 class="section-title">System Information</h2>
                
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">PHP Version</div>
                        <div class="info-value"><?php echo $system_info['php_version'] ?? 'Unknown'; ?></div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">MySQL Version</div>
                        <div class="info-value"><?php echo $system_info['mysql_version'] ?? 'Unknown'; ?></div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Server Software</div>
                        <div class="info-value"><?php echo $system_info['server_software'] ?? 'Unknown'; ?></div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Free Disk Space</div>
                        <div class="info-value"><?php echo $system_info['disk_space'] ?? 'Unknown'; ?></div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Memory Limit</div>
                        <div class="info-value"><?php echo $system_info['memory_limit'] ?? 'Unknown'; ?></div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Max Execution Time</div>
                        <div class="info-value"><?php echo $system_info['max_execution_time'] ?? 'Unknown'; ?>s</div>
                    </div>
                </div>
                
                <div class="backup-section">
                    <h3 style="color: var(--text-primary); margin-bottom: 15px;">Database Management</h3>
                    <button class="btn btn-secondary backup-btn" onclick="backupDatabase()">Backup Database</button>
                    <button class="btn btn-secondary backup-btn" onclick="optimizeDatabase()">Optimize Database</button>
                    <button class="btn btn-primary backup-btn" onclick="clearCache()">Clear Cache</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function testTMDBConnection() {
            const statusDiv = document.getElementById('connectionStatus');
            statusDiv.innerHTML = '<div class="connection-status">Testing connection...</div>';
            
            try {
                const response = await fetch('../api/test-tmdb.php');
                const data = await response.json();
                
                if (data.success) {
                    statusDiv.innerHTML = '<div class="connection-status status-success">✓ TMDB connection successful!</div>';
                } else {
                    statusDiv.innerHTML = '<div class="connection-status status-error">✗ TMDB connection failed: ' + data.message + '</div>';
                }
            } catch (error) {
                statusDiv.innerHTML = '<div class="connection-status status-error">✗ Connection test failed: ' + error.message + '</div>';
            }
        }
        
        async function backupDatabase() {
            if (!confirm('Create a database backup? This may take a while.')) {
                return;
            }
            
            try {
                const response = await fetch('../api/backup.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'backup' })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert('Database backup created successfully!');
                } else {
                    alert('Backup failed: ' + data.message);
                }
            } catch (error) {
                alert('Backup failed: ' + error.message);
            }
        }
        
        async function optimizeDatabase() {
            if (!confirm('Optimize database tables? This may take a while.')) {
                return;
            }
            
            try {
                const response = await fetch('../api/backup.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'optimize' })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert('Database optimized successfully!');
                } else {
                    alert('Optimization failed: ' + data.message);
                }
            } catch (error) {
                alert('Optimization failed: ' + error.message);
            }
        }
        
        async function clearCache() {
            if (!confirm('Clear all cache files?')) {
                return;
            }
            
            try {
                const response = await fetch('../api/backup.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'clear_cache' })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert('Cache cleared successfully!');
                } else {
                    alert('Cache clear failed: ' + data.message);
                }
            } catch (error) {
                alert('Cache clear failed: ' + error.message);
            }
        }
    </script>
</body>
</html>
