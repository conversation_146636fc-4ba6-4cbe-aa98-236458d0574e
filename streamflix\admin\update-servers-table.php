<?php
require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

try {
    $db = new Database();
    $conn = $db->connect();
    
    echo "<h2>Updating Servers Table...</h2>";
    
    // Step 1: Add hentai_url column if not exists
    echo "<p>1. Adding hentai_url column...</p>";
    try {
        $stmt = $conn->query("SHOW COLUMNS FROM embed_servers LIKE 'hentai_url'");
        if ($stmt->rowCount() == 0) {
            $conn->exec("ALTER TABLE embed_servers ADD COLUMN hentai_url TEXT NULL AFTER tv_url");
            echo "<p style='color: green;'>✅ Added hentai_url column</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ hentai_url column already exists</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
    
    // Step 2: Add is_active_movies column
    echo "<p>2. Adding is_active_movies column...</p>";
    try {
        $stmt = $conn->query("SHOW COLUMNS FROM embed_servers LIKE 'is_active_movies'");
        if ($stmt->rowCount() == 0) {
            $conn->exec("ALTER TABLE embed_servers ADD COLUMN is_active_movies BOOLEAN DEFAULT 1 AFTER is_active");
            echo "<p style='color: green;'>✅ Added is_active_movies column</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ is_active_movies column already exists</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
    
    // Step 3: Add is_active_tv column
    echo "<p>3. Adding is_active_tv column...</p>";
    try {
        $stmt = $conn->query("SHOW COLUMNS FROM embed_servers LIKE 'is_active_tv'");
        if ($stmt->rowCount() == 0) {
            $conn->exec("ALTER TABLE embed_servers ADD COLUMN is_active_tv BOOLEAN DEFAULT 1 AFTER is_active_movies");
            echo "<p style='color: green;'>✅ Added is_active_tv column</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ is_active_tv column already exists</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
    
    // Step 4: Update LetsEmbed with hentai URL
    echo "<p>4. Updating LetsEmbed server...</p>";
    try {
        $stmt = $conn->prepare("UPDATE embed_servers SET hentai_url = ? WHERE name = 'LetsEmbed'");
        $hentai_url = 'https://letsembed.cc/embed/hentai/?id={tmdb_id}/{season_number}/{episode_number}';
        $stmt->execute([$hentai_url]);
        
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✅ Updated LetsEmbed server with hentai URL</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ LetsEmbed server not found or already updated</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
    
    // Step 5: Show current table structure
    echo "<h3>Current Table Structure:</h3>";
    try {
        $stmt = $conn->query("DESCRIBE embed_servers");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error showing table structure: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3 style='color: green;'>✅ Server table update complete!</h3>";
    echo "<p><a href='servers.php'>← Back to Servers Management</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Update Servers Table - StreamFlix Admin</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #1a1a1a; color: white; }
        h2, h3 { color: #e50914; }
        p { margin: 10px 0; }
        a { color: #e50914; text-decoration: none; }
        a:hover { text-decoration: underline; }
        table { background: #333; color: white; padding: 10px; }
        th, td { padding: 8px; text-align: left; }
        th { background: #555; }
    </style>
</head>
<body>
</body>
</html>
