<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$message = '';
$error = '';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $db = new Database();
        $conn = $db->connect();
        
        if ($action === 'delete_movie') {
            $movie_id = (int)($_POST['movie_id'] ?? 0);
            if ($movie_id > 0) {
                $stmt = $conn->prepare("DELETE FROM movies WHERE id = ?");
                $stmt->execute([$movie_id]);
                $message = 'Movie deleted successfully!';
            }
        } elseif ($action === 'toggle_featured') {
            $movie_id = (int)($_POST['movie_id'] ?? 0);
            if ($movie_id > 0) {
                $stmt = $conn->prepare("UPDATE movies SET is_featured = NOT is_featured WHERE id = ?");
                $stmt->execute([$movie_id]);
                $message = 'Movie featured status updated!';
            }
        } elseif ($action === 'toggle_trending') {
            $movie_id = (int)($_POST['movie_id'] ?? 0);
            if ($movie_id > 0) {
                $stmt = $conn->prepare("UPDATE movies SET is_trending = NOT is_trending WHERE id = ?");
                $stmt->execute([$movie_id]);
                $message = 'Movie trending status updated!';
            }
        }
    } catch (Exception $e) {
        $error = 'Error: ' . $e->getMessage();
    }
}

// Get movies with pagination
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;
$search = sanitizeInput($_GET['search'] ?? '');

try {
    $db = new Database();
    $conn = $db->connect();
    
    // Count total movies
    $count_sql = "SELECT COUNT(*) FROM movies";
    $params = [];
    
    if (!empty($search)) {
        $count_sql .= " WHERE title LIKE ?";
        $params[] = "%{$search}%";
    }
    
    $stmt = $conn->prepare($count_sql);
    $stmt->execute($params);
    $total_movies = $stmt->fetchColumn();
    $total_pages = ceil($total_movies / $limit);
    
    // Get movies
    $sql = "SELECT * FROM movies";
    if (!empty($search)) {
        $sql .= " WHERE title LIKE ?";
    }
    $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";

    $stmt = $conn->prepare($sql);

    // Bind parameters with correct types
    if (!empty($search)) {
        $stmt->bindValue(1, "%{$search}%", PDO::PARAM_STR);
        $stmt->bindValue(2, $limit, PDO::PARAM_INT);
        $stmt->bindValue(3, $offset, PDO::PARAM_INT);
    } else {
        $stmt->bindValue(1, $limit, PDO::PARAM_INT);
        $stmt->bindValue(2, $offset, PDO::PARAM_INT);
    }

    $stmt->execute();
    $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = 'Database error: ' . $e->getMessage();
    $movies = [];
    $total_movies = 0;
    $total_pages = 0;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Movies - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="assets/admin-style.css">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            color: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .admin-nav {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 30px;
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .admin-nav a {
            padding: 12px 20px;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background: #e50914;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(229, 9, 20, 0.4);
        }
        
        .search-section {
            background: var(--secondary-color);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .search-form {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-form input {
            flex: 1;
            min-width: 250px;
            padding: 12px 15px;
            border: 2px solid #333;
            border-radius: 8px;
            background: var(--bg-color);
            color: var(--text-color);
            font-size: 16px;
        }
        
        .search-form input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .movies-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .movie-card {
            background: var(--secondary-color);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        
        .movie-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        }
        
        .movie-poster {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: linear-gradient(45deg, #333, #555);
        }
        
        .movie-info {
            padding: 20px;
        }
        
        .movie-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: var(--text-color);
        }
        
        .movie-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 14px;
            color: #888;
        }
        
        .movie-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn-small {
            padding: 8px 12px;
            font-size: 12px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-edit {
            background: #17a2b8;
            color: white;
        }
        
        .btn-edit:hover {
            background: #138496;
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        
        .btn-delete:hover {
            background: #c82333;
        }
        
        .btn-toggle {
            background: #28a745;
            color: white;
        }
        
        .btn-toggle:hover {
            background: #218838;
        }
        
        .btn-toggle.inactive {
            background: #6c757d;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }
        
        .pagination a,
        .pagination span {
            padding: 10px 15px;
            background: var(--secondary-color);
            color: var(--text-color);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .pagination a:hover,
        .pagination .current {
            background: var(--primary-color);
            color: white;
        }
        
        .stats-bar {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .success-message,
        .error-message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        @media (max-width: 768px) {
            .admin-container {
                margin-top: 80px;
                padding: 0 15px;
            }
            
            .admin-nav {
                justify-content: center;
            }
            
            .admin-nav a {
                padding: 10px 15px;
                font-size: 14px;
            }
            
            .search-form {
                flex-direction: column;
            }
            
            .search-form input {
                min-width: 100%;
            }
            
            .movies-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .movie-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>🎬 Manage Movies</h1>
            <p>View, edit and manage all movies in your database</p>
        </div>

        <nav class="admin-nav">
            <a href="index.php">📊 Dashboard</a>
            <a href="movies.php" class="active">🎬 Movies</a>
            <a href="tv-shows.php">📺 TV Shows</a>
            <a href="users.php">👥 Users</a>
            <a href="servers.php">🖥️ Servers</a>
            <a href="analytics.php">📈 Analytics</a>
            <a href="import.php">📥 Import</a>
            <a href="app-management.php">📱 App Management</a>
            <a href="user-management.php">👤 User Management</a>
            <a href="system-logs.php">📋 System Logs</a>
            <a href="maintenance.php">🔧 Maintenance</a>
            <a href="database-updater.php">🗄️ DB Updater</a>
            <a href="quick-setup.php">🚀 Quick Setup</a>
            <a href="settings.php">⚙️ Settings</a>
        </nav>

        <?php if (isset($message)): ?>
            <div class="success-message"><?php echo $message; ?></div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="error-message"><?php echo $error; ?></div>
        <?php endif; ?>

        <div class="stats-bar">
            <h3>📊 Total Movies: <?php echo number_format($total_movies); ?></h3>
        </div>

        <!-- Search Section -->
        <div class="search-section">
            <form method="GET" class="search-form">
                <input type="text" name="search" placeholder="🔍 Search movies..." value="<?php echo htmlspecialchars($search); ?>">
                <button type="submit" class="btn btn-primary">Search</button>
                <?php if (!empty($search)): ?>
                    <a href="movies.php" class="btn btn-secondary">Clear</a>
                <?php endif; ?>
            </form>
        </div>

        <!-- Movies Grid -->
        <div class="movies-grid">
            <?php foreach ($movies as $movie): ?>
            <div class="movie-card">
                <?php if (!empty($movie['poster_path'])): ?>
                    <img src="<?php echo getImageUrl($movie['poster_path'], 'w500'); ?>" alt="<?php echo htmlspecialchars($movie['title']); ?>" class="movie-poster">
                <?php else: ?>
                    <div class="movie-poster" style="display: flex; align-items: center; justify-content: center; color: #888;">
                        🎬 No Poster
                    </div>
                <?php endif; ?>
                
                <div class="movie-info">
                    <div class="movie-title"><?php echo htmlspecialchars($movie['title']); ?></div>
                    
                    <div class="movie-meta">
                        <span>⭐ <?php echo number_format($movie['vote_average'], 1); ?></span>
                        <span>📅 <?php echo date('Y', strtotime($movie['release_date'])); ?></span>
                    </div>
                    
                    <div class="movie-actions">
                        <a href="edit-movie.php?id=<?php echo $movie['id']; ?>" class="btn-small btn-edit">✏️ Edit</a>
                        
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="toggle_featured">
                            <input type="hidden" name="movie_id" value="<?php echo $movie['id']; ?>">
                            <button type="submit" class="btn-small btn-toggle <?php echo $movie['is_featured'] ? '' : 'inactive'; ?>">
                                <?php echo $movie['is_featured'] ? '⭐ Featured' : '☆ Feature'; ?>
                            </button>
                        </form>
                        
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="toggle_trending">
                            <input type="hidden" name="movie_id" value="<?php echo $movie['id']; ?>">
                            <button type="submit" class="btn-small btn-toggle <?php echo $movie['is_trending'] ? '' : 'inactive'; ?>">
                                <?php echo $movie['is_trending'] ? '🔥 Trending' : '📈 Trend'; ?>
                            </button>
                        </form>
                        
                        <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this movie?');">
                            <input type="hidden" name="action" value="delete_movie">
                            <input type="hidden" name="movie_id" value="<?php echo $movie['id']; ?>">
                            <button type="submit" class="btn-small btn-delete">🗑️ Delete</button>
                        </form>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <?php if (empty($movies)): ?>
            <div style="text-align: center; padding: 50px; color: #888;">
                <h3>🎬 No movies found</h3>
                <p>Try adjusting your search or import some movies.</p>
                <a href="import.php" class="btn btn-primary">📥 Import Movies</a>
            </div>
        <?php endif; ?>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <div class="pagination">
            <?php if ($page > 1): ?>
                <a href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">← Previous</a>
            <?php endif; ?>
            
            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                <?php if ($i == $page): ?>
                    <span class="current"><?php echo $i; ?></span>
                <?php else: ?>
                    <a href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>"><?php echo $i; ?></a>
                <?php endif; ?>
            <?php endfor; ?>
            
            <?php if ($page < $total_pages): ?>
                <a href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">Next →</a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
