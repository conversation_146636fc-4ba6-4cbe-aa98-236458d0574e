package com.streamflix.app.presentation.main

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.animation.*
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
// import com.streamflix.app.presentation.home.NetflixHomeScreen
// import com.streamflix.app.presentation.search.AdvancedSearchScreen
import com.streamflix.app.ui.theme.*
import dagger.hilt.android.AndroidEntryPoint

class MainActivity : ComponentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContent {
            StreamFlixTheme {
                MainScreen()
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen() {
    val navController = rememberNavController()
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination

    Scaffold(
        bottomBar = {
            NavigationBar(
                containerColor = SurfaceDark,
                contentColor = TextPrimary
            ) {
                bottomNavItems.forEach { item ->
                    NavigationBarItem(
                        icon = {
                            Icon(
                                item.icon,
                                contentDescription = item.label,
                                modifier = Modifier.size(24.dp)
                            )
                        },
                        label = {
                            Text(
                                text = item.label,
                                style = MaterialTheme.typography.labelSmall
                            )
                        },
                        selected = currentDestination?.hierarchy?.any { it.route == item.route } == true,
                        onClick = {
                            navController.navigate(item.route) {
                                popUpTo(navController.graph.findStartDestination().id) {
                                    saveState = true
                                }
                                launchSingleTop = true
                                restoreState = true
                            }
                        },
                        colors = NavigationBarItemDefaults.colors(
                            selectedIconColor = StreamFlixRed,
                            selectedTextColor = StreamFlixRed,
                            unselectedIconColor = TextSecondary,
                            unselectedTextColor = TextSecondary,
                            indicatorColor = StreamFlixRed.copy(alpha = 0.2f)
                        )
                    )
                }
            }
        }
    ) { paddingValues ->
        NavHost(
            navController = navController,
            startDestination = "home",
            modifier = Modifier.padding(paddingValues)
        ) {
            composable("home") {
                HomeScreen()
            }
            
            composable("movies") {
                MoviesScreen(
                    onMovieClick = { movie ->
                        navController.navigate("movie_details/${(movie as com.streamflix.app.data.model.Movie).id}")
                    }
                )
            }
            
            composable("tv_shows") {
                TvShowsScreen(
                    onTvShowClick = { tvShow ->
                        navController.navigate("tv_details/${(tvShow as com.streamflix.app.data.model.TvShow).id}")
                    }
                )
            }
            
            composable("search") {
                SearchScreen()
            }
            
            composable("profile") {
                ProfileScreen(
                    onNavigateToSettings = {
                        navController.navigate("settings")
                    },
                    onNavigateToWatchlist = {
                        navController.navigate("watchlist")
                    },
                    onNavigateToDownloads = {
                        navController.navigate("downloads")
                    }
                )
            }
        }
    }
}

@Composable
fun HomeScreen() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = androidx.compose.ui.Alignment.Center
    ) {
        Text(
            text = "Welcome to StreamFlix",
            style = MaterialTheme.typography.headlineMedium,
            color = TextPrimary
        )
    }
}

@Composable
fun SearchScreen() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = androidx.compose.ui.Alignment.Center
    ) {
        Text(
            text = "Search Screen",
            style = MaterialTheme.typography.headlineMedium,
            color = TextPrimary
        )
    }
}

@Composable
fun MoviesScreen(onMovieClick: (Any) -> Unit) {
    // Placeholder for movies screen
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = androidx.compose.ui.Alignment.Center
    ) {
        Text(
            text = "Movies Screen",
            style = MaterialTheme.typography.headlineMedium,
            color = TextPrimary
        )
    }
}

@Composable
fun TvShowsScreen(onTvShowClick: (Any) -> Unit) {
    // Placeholder for TV shows screen
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = androidx.compose.ui.Alignment.Center
    ) {
        Text(
            text = "TV Shows Screen",
            style = MaterialTheme.typography.headlineMedium,
            color = TextPrimary
        )
    }
}

@Composable
fun ProfileScreen(
    onNavigateToSettings: () -> Unit,
    onNavigateToWatchlist: () -> Unit,
    onNavigateToDownloads: () -> Unit
) {
    // Placeholder for profile screen
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = androidx.compose.ui.Alignment.Center
    ) {
        Text(
            text = "Profile Screen",
            style = MaterialTheme.typography.headlineMedium,
            color = TextPrimary
        )
    }
}

data class BottomNavItem(
    val route: String,
    val icon: ImageVector,
    val label: String
)

val bottomNavItems = listOf(
    BottomNavItem("home", Icons.Default.Home, "Home"),
    BottomNavItem("movies", Icons.Default.PlayArrow, "Movies"),
    BottomNavItem("tv_shows", Icons.Default.PlayArrow, "TV Shows"),
    BottomNavItem("search", Icons.Default.Search, "Search"),
    BottomNavItem("profile", Icons.Default.Person, "Profile")
)
