<?php
require_once 'config/database.php';

echo "<h2>⚡ Quick Database Status Check</h2>";
echo "<p>Fast check of database tables and basic functionality.</p>";

try {
    $db = new Database();
    $conn = $db->connect();
    
    // Get database info
    $stmt = $conn->query("SELECT DATABASE() as db_name, VERSION() as version");
    $db_info = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>📊 Database Info</h3>";
    echo "<p><strong>Database:</strong> {$db_info['db_name']}</p>";
    echo "<p><strong>MySQL Version:</strong> {$db_info['version']}</p>";
    echo "<p><strong>Connection:</strong> ✅ Active</p>";
    echo "</div>";
    
    // Required tables
    $required_tables = [
        'users', 'movies', 'tv_shows', 'genres', 'movie_genres', 
        'tv_show_genres', 'embed_servers', 'servers', 'comments'
    ];
    
    // Check tables
    $stmt = $conn->query("SHOW TABLES");
    $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $table_status = [];
    $missing_tables = [];
    
    foreach ($required_tables as $table) {
        if (in_array($table, $existing_tables)) {
            try {
                $stmt = $conn->query("SELECT COUNT(*) FROM `{$table}`");
                $count = $stmt->fetchColumn();
                $table_status[$table] = ['status' => 'exists', 'count' => $count];
            } catch (Exception $e) {
                $table_status[$table] = ['status' => 'error', 'error' => $e->getMessage()];
            }
        } else {
            $table_status[$table] = ['status' => 'missing'];
            $missing_tables[] = $table;
        }
    }
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>📋 Table Status</h3>";
    
    foreach ($table_status as $table => $info) {
        if ($info['status'] === 'exists') {
            echo "<div style='color: #28a745; margin: 5px 0;'>✅ <strong>{$table}</strong> - {$info['count']} records</div>";
        } elseif ($info['status'] === 'missing') {
            echo "<div style='color: #dc3545; margin: 5px 0;'>❌ <strong>{$table}</strong> - Missing</div>";
        } else {
            echo "<div style='color: #ffc107; margin: 5px 0;'>⚠️ <strong>{$table}</strong> - Error: {$info['error']}</div>";
        }
    }
    echo "</div>";
    
    // Check critical columns
    $critical_columns = [
        'movies' => ['content_type'],
        'tv_shows' => ['content_type'],
        'embed_servers' => ['hentai_url', 'anime_url']
    ];
    
    $column_issues = [];
    
    foreach ($critical_columns as $table => $columns) {
        if (in_array($table, $existing_tables)) {
            try {
                $stmt = $conn->query("SHOW COLUMNS FROM `{$table}`");
                $existing_columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
                
                foreach ($columns as $column) {
                    if (!in_array($column, $existing_columns)) {
                        $column_issues[] = "{$table}.{$column}";
                    }
                }
            } catch (Exception $e) {
                $column_issues[] = "{$table} - Error checking columns";
            }
        }
    }
    
    if (!empty($column_issues)) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h3>⚠️ Missing Critical Columns</h3>";
        foreach ($column_issues as $issue) {
            echo "<div style='color: #856404; margin: 5px 0;'>❌ {$issue}</div>";
        }
        echo "</div>";
    }
    
    // Overall status
    $total_tables = count($required_tables);
    $existing_count = count($existing_tables);
    $missing_count = count($missing_tables);
    $column_issue_count = count($column_issues);
    
    $overall_status = 'good';
    if ($missing_count > 0 || $column_issue_count > 0) {
        $overall_status = 'needs_fix';
    }
    
    $status_color = $overall_status === 'good' ? '#d4edda' : '#f8d7da';
    $status_icon = $overall_status === 'good' ? '✅' : '❌';
    $status_text = $overall_status === 'good' ? 'Database is Ready' : 'Database Needs Fixing';
    
    echo "<div style='background: {$status_color}; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>{$status_icon} Overall Status: {$status_text}</h3>";
    echo "<ul>";
    echo "<li><strong>Required Tables:</strong> {$total_tables}</li>";
    echo "<li><strong>Existing Tables:</strong> {$existing_count}</li>";
    echo "<li><strong>Missing Tables:</strong> {$missing_count}</li>";
    echo "<li><strong>Column Issues:</strong> {$column_issue_count}</li>";
    echo "</ul>";
    echo "</div>";
    
    // Action buttons
    echo "<div style='background: #cff4fc; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🔧 Actions</h3>";
    
    if ($overall_status === 'needs_fix') {
        echo "<p><a href='database_checker_fixer.php' style='background: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin-right: 10px; display: inline-block; margin-bottom: 10px;'>🔧 Fix Database Issues</a></p>";
    }
    
    echo "<p><a href='admin/index.php' target='_blank' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin-right: 10px; display: inline-block; margin-bottom: 10px;'>📊 Admin Panel</a></p>";
    
    echo "<p><a href='index.php' target='_blank' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin-right: 10px; display: inline-block; margin-bottom: 10px;'>🏠 Homepage</a></p>";
    
    echo "<p><a href='admin/servers.php' target='_blank' style='background: #17a2b8; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block; margin-bottom: 10px;'>🖥️ Server Management</a></p>";
    echo "</div>";
    
    // Quick tests
    if ($overall_status === 'good') {
        echo "<h3>🧪 Quick Functionality Tests</h3>";
        
        $tests = [];
        
        // Test embed servers
        try {
            $stmt = $conn->query("SELECT COUNT(*) FROM embed_servers WHERE is_active = 1");
            $active_servers = $stmt->fetchColumn();
            $tests['embed_servers'] = $active_servers > 0 ? "✅ {$active_servers} active servers" : "❌ No active servers";
        } catch (Exception $e) {
            $tests['embed_servers'] = "❌ Error: " . $e->getMessage();
        }
        
        // Test content
        try {
            $stmt = $conn->query("SELECT COUNT(*) FROM movies");
            $movie_count = $stmt->fetchColumn();
            $stmt = $conn->query("SELECT COUNT(*) FROM tv_shows");
            $tv_count = $stmt->fetchColumn();
            $tests['content'] = "✅ {$movie_count} movies, {$tv_count} TV shows";
        } catch (Exception $e) {
            $tests['content'] = "❌ Error: " . $e->getMessage();
        }
        
        // Test users
        try {
            $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'admin'");
            $admin_count = $stmt->fetchColumn();
            $tests['users'] = $admin_count > 0 ? "✅ {$admin_count} admin user(s)" : "❌ No admin users";
        } catch (Exception $e) {
            $tests['users'] = "❌ Error: " . $e->getMessage();
        }
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        foreach ($tests as $test_name => $result) {
            echo "<div style='margin: 5px 0;'><strong>" . ucfirst(str_replace('_', ' ', $test_name)) . ":</strong> {$result}</div>";
        }
        echo "</div>";
    }
    
    echo "<hr>";
    echo "<p><strong>⚡ Quick Status Check Complete!</strong></p>";
    echo "<p>Last checked: " . date('Y-m-d H:i:s') . "</p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4 style='color: #721c24;'>❌ Database Connection Error</h4>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
