package com.streamflix.app.presentation.splash

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.streamflix.app.BuildConfig
import com.streamflix.app.data.repository.StreamFlixRepository
import com.streamflix.app.utils.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SplashViewModel @Inject constructor(
    private val repository: StreamFlixRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(SplashUiState())
    val uiState: StateFlow<SplashUiState> = _uiState.asStateFlow()

    private val _isLoading = MutableStateFlow(true)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    fun initialize() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isLoading = true,
                error = null
            )
            _isLoading.value = true

            try {
                // Minimum splash duration
                delay(2000)

                // Check app version and maintenance
                checkAppStatus()

                // Check authentication status
                checkAuthenticationStatus()

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.localizedMessage ?: "Unknown error occurred"
                )
                _isLoading.value = false
            }
        }
    }

    private suspend fun checkAppStatus() {
        repository.checkAppVersion(BuildConfig.VERSION_NAME).collect { resource ->
            when (resource) {
                is Resource.Success -> {
                    val appVersion = resource.data!!
                    
                    _uiState.value = _uiState.value.copy(
                        appVersion = BuildConfig.VERSION_NAME,
                        updateAvailable = appVersion.updateAvailable,
                        updateRequired = appVersion.updateRequired,
                        maintenanceMode = appVersion.maintenanceMode,
                        maintenanceMessage = appVersion.maintenanceMessage
                    )

                    // Handle maintenance mode
                    if (appVersion.maintenanceMode) {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false
                        )
                        _isLoading.value = false
                        return@collect
                    }

                    // Handle required update
                    if (appVersion.updateRequired) {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false
                        )
                        _isLoading.value = false
                        return@collect
                    }
                }
                is Resource.Error -> {
                    // Continue without version check if API fails
                    _uiState.value = _uiState.value.copy(
                        appVersion = BuildConfig.VERSION_NAME
                    )
                }
                is Resource.Loading -> {
                    // Keep loading
                }
            }
        }
    }

    private suspend fun checkAuthenticationStatus() {
        repository.isLoggedIn().collect { isLoggedIn ->
            if (isLoggedIn) {
                // Try to refresh token to validate it
                repository.refreshToken().collect { resource ->
                    when (resource) {
                        is Resource.Success -> {
                            // Token is valid, navigate to main
                            navigateToMain()
                        }
                        is Resource.Error -> {
                            // Token is invalid, navigate to auth
                            navigateToAuth()
                        }
                        is Resource.Loading -> {
                            // Keep loading
                        }
                    }
                }
            } else {
                // Not logged in, navigate to auth
                navigateToAuth()
            }
        }
    }

    private fun navigateToMain() {
        _uiState.value = _uiState.value.copy(
            isLoading = false,
            navigationDestination = SplashNavigationDestination.Main
        )
        _isLoading.value = false
    }

    private fun navigateToAuth() {
        _uiState.value = _uiState.value.copy(
            isLoading = false,
            navigationDestination = SplashNavigationDestination.Auth
        )
        _isLoading.value = false
    }
}

data class SplashUiState(
    val isLoading: Boolean = true,
    val error: String? = null,
    val appVersion: String = "",
    val updateAvailable: Boolean = false,
    val updateRequired: Boolean = false,
    val maintenanceMode: Boolean = false,
    val maintenanceMessage: String = "",
    val navigationDestination: SplashNavigationDestination = SplashNavigationDestination.None
)

enum class SplashNavigationDestination {
    None,
    Main,
    Auth
}
