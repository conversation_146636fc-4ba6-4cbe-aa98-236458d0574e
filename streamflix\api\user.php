<?php
/**
 * User API Endpoints
 * /api/user.php
 */

require_once 'config.php';

$method = getRequestMethod();
$data = getRequestData();
$path = $_GET['action'] ?? '';

logAPIRequest("user/{$path}", $method);

try {
    $db = new Database();
    $conn = $db->connect();
    
    switch ($path) {
        case 'watchlist':
            handleWatchlist($conn);
            break;
            
        case 'add-to-watchlist':
            handleAddToWatchlist($conn, $data);
            break;
            
        case 'remove-from-watchlist':
            handleRemoveFromWatchlist($conn, $data);
            break;
            
        case 'favorites':
            handleFavorites($conn);
            break;
            
        case 'add-to-favorites':
            handleAddToFavorites($conn, $data);
            break;
            
        case 'remove-from-favorites':
            handleRemoveFromFavorites($conn, $data);
            break;
            
        case 'watch-history':
            handleWatchHistory($conn);
            break;
            
        case 'add-to-history':
            handleAddToHistory($conn, $data);
            break;
            
        case 'continue-watching':
            handleContinueWatching($conn);
            break;
            
        default:
            APIResponse::notFound('Endpoint not found');
    }
    
} catch (Exception $e) {
    APIResponse::serverError('Database connection failed');
}

/**
 * Handle get watchlist
 */
function handleWatchlist($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $user = APIAuth::authenticate();
    
    $page = max(1, (int)($_GET['page'] ?? 1));
    $limit = min(50, max(1, (int)($_GET['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    try {
        // Create watchlist table if not exists
        $conn->exec("
            CREATE TABLE IF NOT EXISTS watchlist (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                movie_id INT NULL,
                tv_show_id INT NULL,
                added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
                FOREIGN KEY (tv_show_id) REFERENCES tv_shows(id) ON DELETE CASCADE,
                UNIQUE KEY unique_user_movie (user_id, movie_id),
                UNIQUE KEY unique_user_tv (user_id, tv_show_id)
            )
        ");
        
        // Get total count
        $stmt = $conn->prepare("SELECT COUNT(*) FROM watchlist WHERE user_id = ?");
        $stmt->execute([$user['id']]);
        $total = $stmt->fetchColumn();
        
        // Get watchlist items
        $stmt = $conn->prepare("
            SELECT 
                w.*,
                m.title as movie_title, m.poster_path as movie_poster, m.vote_average as movie_rating,
                t.name as tv_title, t.poster_path as tv_poster, t.vote_average as tv_rating
            FROM watchlist w
            LEFT JOIN movies m ON w.movie_id = m.id
            LEFT JOIN tv_shows t ON w.tv_show_id = t.id
            WHERE w.user_id = ?
            ORDER BY w.added_at DESC
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$user['id'], $limit, $offset]);
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $formattedItems = array_map(function($item) {
            $result = [
                'id' => (int)$item['id'],
                'added_at' => $item['added_at']
            ];
            
            if ($item['movie_id']) {
                $result['type'] = 'movie';
                $result['content'] = [
                    'id' => (int)$item['movie_id'],
                    'title' => $item['movie_title'],
                    'poster_path' => $item['movie_poster'],
                    'vote_average' => (float)$item['movie_rating']
                ];
            } else {
                $result['type'] = 'tv_show';
                $result['content'] = [
                    'id' => (int)$item['tv_show_id'],
                    'title' => $item['tv_title'],
                    'poster_path' => $item['tv_poster'],
                    'vote_average' => (float)$item['tv_rating']
                ];
            }
            
            return $result;
        }, $items);
        
        APIResponse::success([
            'watchlist' => $formattedItems,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => (int)$total,
                'total_pages' => ceil($total / $limit),
                'has_next' => $page < ceil($total / $limit),
                'has_prev' => $page > 1
            ]
        ]);
        
    } catch (Exception $e) {
        APIResponse::serverError('Failed to fetch watchlist');
    }
}

/**
 * Handle add to watchlist
 */
function handleAddToWatchlist($conn, $data) {
    if (getRequestMethod() !== 'POST') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $user = APIAuth::authenticate();
    
    validateRequired($data, ['type', 'content_id']);
    
    $type = sanitizeApiInput($data['type']);
    $contentId = (int)$data['content_id'];
    
    if (!in_array($type, ['movie', 'tv_show'])) {
        APIResponse::error('Invalid content type. Must be movie or tv_show');
    }
    
    try {
        // Create watchlist table if not exists
        $conn->exec("
            CREATE TABLE IF NOT EXISTS watchlist (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                movie_id INT NULL,
                tv_show_id INT NULL,
                added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_user_movie (user_id, movie_id),
                UNIQUE KEY unique_user_tv (user_id, tv_show_id)
            )
        ");
        
        // Verify content exists
        if ($type === 'movie') {
            $stmt = $conn->prepare("SELECT id FROM movies WHERE id = ?");
            $stmt->execute([$contentId]);
            if (!$stmt->fetch()) {
                APIResponse::notFound('Movie not found');
            }
            
            // Check if already in watchlist
            $stmt = $conn->prepare("SELECT id FROM watchlist WHERE user_id = ? AND movie_id = ?");
            $stmt->execute([$user['id'], $contentId]);
            if ($stmt->fetch()) {
                APIResponse::error('Movie already in watchlist');
            }
            
            // Add to watchlist
            $stmt = $conn->prepare("INSERT INTO watchlist (user_id, movie_id) VALUES (?, ?)");
            $stmt->execute([$user['id'], $contentId]);
            
        } else {
            $stmt = $conn->prepare("SELECT id FROM tv_shows WHERE id = ?");
            $stmt->execute([$contentId]);
            if (!$stmt->fetch()) {
                APIResponse::notFound('TV Show not found');
            }
            
            // Check if already in watchlist
            $stmt = $conn->prepare("SELECT id FROM watchlist WHERE user_id = ? AND tv_show_id = ?");
            $stmt->execute([$user['id'], $contentId]);
            if ($stmt->fetch()) {
                APIResponse::error('TV Show already in watchlist');
            }
            
            // Add to watchlist
            $stmt = $conn->prepare("INSERT INTO watchlist (user_id, tv_show_id) VALUES (?, ?)");
            $stmt->execute([$user['id'], $contentId]);
        }
        
        logUserManagement('Watchlist Add', "Added {$type} ID {$contentId} to watchlist", $user['id']);
        
        APIResponse::success(null, 'Added to watchlist successfully', 201);
        
    } catch (Exception $e) {
        APIResponse::serverError('Failed to add to watchlist');
    }
}

/**
 * Handle remove from watchlist
 */
function handleRemoveFromWatchlist($conn, $data) {
    if (getRequestMethod() !== 'DELETE') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $user = APIAuth::authenticate();
    
    validateRequired($data, ['type', 'content_id']);
    
    $type = sanitizeApiInput($data['type']);
    $contentId = (int)$data['content_id'];
    
    if (!in_array($type, ['movie', 'tv_show'])) {
        APIResponse::error('Invalid content type. Must be movie or tv_show');
    }
    
    try {
        if ($type === 'movie') {
            $stmt = $conn->prepare("DELETE FROM watchlist WHERE user_id = ? AND movie_id = ?");
            $stmt->execute([$user['id'], $contentId]);
        } else {
            $stmt = $conn->prepare("DELETE FROM watchlist WHERE user_id = ? AND tv_show_id = ?");
            $stmt->execute([$user['id'], $contentId]);
        }
        
        if ($stmt->rowCount() === 0) {
            APIResponse::notFound('Item not found in watchlist');
        }
        
        logUserManagement('Watchlist Remove', "Removed {$type} ID {$contentId} from watchlist", $user['id']);
        
        APIResponse::success(null, 'Removed from watchlist successfully');
        
    } catch (Exception $e) {
        APIResponse::serverError('Failed to remove from watchlist');
    }
}

/**
 * Handle get favorites
 */
function handleFavorites($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $user = APIAuth::authenticate();
    
    try {
        // Create favorites table if not exists
        $conn->exec("
            CREATE TABLE IF NOT EXISTS favorites (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                movie_id INT NULL,
                tv_show_id INT NULL,
                added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_user_movie (user_id, movie_id),
                UNIQUE KEY unique_user_tv (user_id, tv_show_id)
            )
        ");
        
        $stmt = $conn->prepare("
            SELECT 
                f.*,
                m.title as movie_title, m.poster_path as movie_poster,
                t.name as tv_title, t.poster_path as tv_poster
            FROM favorites f
            LEFT JOIN movies m ON f.movie_id = m.id
            LEFT JOIN tv_shows t ON f.tv_show_id = t.id
            WHERE f.user_id = ?
            ORDER BY f.added_at DESC
        ");
        $stmt->execute([$user['id']]);
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $formattedItems = array_map(function($item) {
            $result = [
                'id' => (int)$item['id'],
                'added_at' => $item['added_at']
            ];
            
            if ($item['movie_id']) {
                $result['type'] = 'movie';
                $result['content'] = [
                    'id' => (int)$item['movie_id'],
                    'title' => $item['movie_title'],
                    'poster_path' => $item['movie_poster']
                ];
            } else {
                $result['type'] = 'tv_show';
                $result['content'] = [
                    'id' => (int)$item['tv_show_id'],
                    'title' => $item['tv_title'],
                    'poster_path' => $item['tv_poster']
                ];
            }
            
            return $result;
        }, $items);
        
        APIResponse::success(['favorites' => $formattedItems]);
        
    } catch (Exception $e) {
        APIResponse::success(['favorites' => []]);
    }
}

/**
 * Handle add to favorites
 */
function handleAddToFavorites($conn, $data) {
    if (getRequestMethod() !== 'POST') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $user = APIAuth::authenticate();
    
    validateRequired($data, ['type', 'content_id']);
    
    $type = sanitizeApiInput($data['type']);
    $contentId = (int)$data['content_id'];
    
    if (!in_array($type, ['movie', 'tv_show'])) {
        APIResponse::error('Invalid content type. Must be movie or tv_show');
    }
    
    try {
        // Create favorites table if not exists
        $conn->exec("
            CREATE TABLE IF NOT EXISTS favorites (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                movie_id INT NULL,
                tv_show_id INT NULL,
                added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_user_movie (user_id, movie_id),
                UNIQUE KEY unique_user_tv (user_id, tv_show_id)
            )
        ");
        
        if ($type === 'movie') {
            $stmt = $conn->prepare("INSERT IGNORE INTO favorites (user_id, movie_id) VALUES (?, ?)");
            $stmt->execute([$user['id'], $contentId]);
        } else {
            $stmt = $conn->prepare("INSERT IGNORE INTO favorites (user_id, tv_show_id) VALUES (?, ?)");
            $stmt->execute([$user['id'], $contentId]);
        }
        
        APIResponse::success(null, 'Added to favorites successfully', 201);
        
    } catch (Exception $e) {
        APIResponse::serverError('Failed to add to favorites');
    }
}

/**
 * Handle remove from favorites
 */
function handleRemoveFromFavorites($conn, $data) {
    if (getRequestMethod() !== 'DELETE') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $user = APIAuth::authenticate();
    
    validateRequired($data, ['type', 'content_id']);
    
    $type = sanitizeApiInput($data['type']);
    $contentId = (int)$data['content_id'];
    
    try {
        if ($type === 'movie') {
            $stmt = $conn->prepare("DELETE FROM favorites WHERE user_id = ? AND movie_id = ?");
            $stmt->execute([$user['id'], $contentId]);
        } else {
            $stmt = $conn->prepare("DELETE FROM favorites WHERE user_id = ? AND tv_show_id = ?");
            $stmt->execute([$user['id'], $contentId]);
        }
        
        APIResponse::success(null, 'Removed from favorites successfully');
        
    } catch (Exception $e) {
        APIResponse::serverError('Failed to remove from favorites');
    }
}

/**
 * Handle watch history
 */
function handleWatchHistory($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $user = APIAuth::authenticate();
    
    try {
        // Create watch_history table if not exists
        $conn->exec("
            CREATE TABLE IF NOT EXISTS watch_history (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                movie_id INT NULL,
                tv_show_id INT NULL,
                episode_id INT NULL,
                watch_time INT DEFAULT 0,
                duration INT DEFAULT 0,
                completed BOOLEAN DEFAULT FALSE,
                last_watched TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_user_movie (user_id, movie_id),
                UNIQUE KEY unique_user_episode (user_id, episode_id)
            )
        ");
        
        $stmt = $conn->prepare("
            SELECT 
                wh.*,
                m.title as movie_title, m.poster_path as movie_poster,
                t.name as tv_title, t.poster_path as tv_poster
            FROM watch_history wh
            LEFT JOIN movies m ON wh.movie_id = m.id
            LEFT JOIN tv_shows t ON wh.tv_show_id = t.id
            WHERE wh.user_id = ?
            ORDER BY wh.last_watched DESC
            LIMIT 50
        ");
        $stmt->execute([$user['id']]);
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $formattedItems = array_map(function($item) {
            $result = [
                'id' => (int)$item['id'],
                'watch_time' => (int)$item['watch_time'],
                'duration' => (int)$item['duration'],
                'completed' => (bool)$item['completed'],
                'last_watched' => $item['last_watched'],
                'progress' => $item['duration'] > 0 ? round(($item['watch_time'] / $item['duration']) * 100, 2) : 0
            ];
            
            if ($item['movie_id']) {
                $result['type'] = 'movie';
                $result['content'] = [
                    'id' => (int)$item['movie_id'],
                    'title' => $item['movie_title'],
                    'poster_path' => $item['movie_poster']
                ];
            } else {
                $result['type'] = 'tv_show';
                $result['content'] = [
                    'id' => (int)$item['tv_show_id'],
                    'title' => $item['tv_title'],
                    'poster_path' => $item['tv_poster']
                ];
            }
            
            return $result;
        }, $items);
        
        APIResponse::success(['watch_history' => $formattedItems]);
        
    } catch (Exception $e) {
        APIResponse::success(['watch_history' => []]);
    }
}

/**
 * Handle add to watch history
 */
function handleAddToHistory($conn, $data) {
    if (getRequestMethod() !== 'POST') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $user = APIAuth::authenticate();
    
    validateRequired($data, ['type', 'content_id', 'watch_time', 'duration']);
    
    $type = sanitizeApiInput($data['type']);
    $contentId = (int)$data['content_id'];
    $watchTime = (int)$data['watch_time'];
    $duration = (int)$data['duration'];
    $completed = $data['completed'] ?? false;
    
    try {
        // Create watch_history table if not exists
        $conn->exec("
            CREATE TABLE IF NOT EXISTS watch_history (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                movie_id INT NULL,
                tv_show_id INT NULL,
                episode_id INT NULL,
                watch_time INT DEFAULT 0,
                duration INT DEFAULT 0,
                completed BOOLEAN DEFAULT FALSE,
                last_watched TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_user_movie (user_id, movie_id),
                UNIQUE KEY unique_user_episode (user_id, episode_id)
            )
        ");
        
        if ($type === 'movie') {
            $stmt = $conn->prepare("
                INSERT INTO watch_history (user_id, movie_id, watch_time, duration, completed) 
                VALUES (?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                watch_time = VALUES(watch_time), 
                duration = VALUES(duration), 
                completed = VALUES(completed),
                last_watched = CURRENT_TIMESTAMP
            ");
            $stmt->execute([$user['id'], $contentId, $watchTime, $duration, $completed]);
        } else {
            $stmt = $conn->prepare("
                INSERT INTO watch_history (user_id, tv_show_id, watch_time, duration, completed) 
                VALUES (?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                watch_time = VALUES(watch_time), 
                duration = VALUES(duration), 
                completed = VALUES(completed),
                last_watched = CURRENT_TIMESTAMP
            ");
            $stmt->execute([$user['id'], $contentId, $watchTime, $duration, $completed]);
        }
        
        APIResponse::success(null, 'Watch progress saved');
        
    } catch (Exception $e) {
        APIResponse::serverError('Failed to save watch progress');
    }
}

/**
 * Handle continue watching
 */
function handleContinueWatching($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $user = APIAuth::authenticate();
    
    try {
        $stmt = $conn->prepare("
            SELECT 
                wh.*,
                m.title as movie_title, m.poster_path as movie_poster,
                t.name as tv_title, t.poster_path as tv_poster
            FROM watch_history wh
            LEFT JOIN movies m ON wh.movie_id = m.id
            LEFT JOIN tv_shows t ON wh.tv_show_id = t.id
            WHERE wh.user_id = ? AND wh.completed = 0 AND wh.watch_time > 0
            ORDER BY wh.last_watched DESC
            LIMIT 10
        ");
        $stmt->execute([$user['id']]);
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $formattedItems = array_map(function($item) {
            $result = [
                'id' => (int)$item['id'],
                'watch_time' => (int)$item['watch_time'],
                'duration' => (int)$item['duration'],
                'progress' => $item['duration'] > 0 ? round(($item['watch_time'] / $item['duration']) * 100, 2) : 0,
                'last_watched' => $item['last_watched']
            ];
            
            if ($item['movie_id']) {
                $result['type'] = 'movie';
                $result['content'] = [
                    'id' => (int)$item['movie_id'],
                    'title' => $item['movie_title'],
                    'poster_path' => $item['movie_poster']
                ];
            } else {
                $result['type'] = 'tv_show';
                $result['content'] = [
                    'id' => (int)$item['tv_show_id'],
                    'title' => $item['tv_title'],
                    'poster_path' => $item['tv_poster']
                ];
            }
            
            return $result;
        }, $items);
        
        APIResponse::success(['continue_watching' => $formattedItems]);
        
    } catch (Exception $e) {
        APIResponse::success(['continue_watching' => []]);
    }
}
?>
