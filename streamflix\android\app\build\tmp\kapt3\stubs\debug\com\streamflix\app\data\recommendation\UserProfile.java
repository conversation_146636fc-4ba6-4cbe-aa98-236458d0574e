package com.streamflix.app.data.recommendation;

import com.streamflix.app.data.local.UserPreferences;
import com.streamflix.app.data.model.*;
import com.streamflix.app.data.repository.StreamFlixRepository;
import com.streamflix.app.utils.Resource;
import kotlinx.coroutines.flow.*;
import javax.inject.Inject;
import javax.inject.Singleton;
import kotlin.math.*;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B7\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0003\u0012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u0003\u00a2\u0006\u0002\u0010\u000bJ\u000f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0006H\u00c6\u0003J\u000f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\b0\u0003H\u00c6\u0003J\u000f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\n0\u0003H\u00c6\u0003JC\u0010\u0016\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u00032\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u0003H\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001a\u001a\u00020\u0004H\u00d6\u0001J\t\u0010\u001b\u001a\u00020\bH\u00d6\u0001R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0017\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000fR\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000f\u00a8\u0006\u001c"}, d2 = {"Lcom/streamflix/app/data/recommendation/UserProfile;", "", "favoriteGenres", "", "", "averageRating", "", "preferredContentTypes", "", "watchHistory", "Lcom/streamflix/app/data/model/WatchHistoryItem;", "(Ljava/util/List;DLjava/util/List;Ljava/util/List;)V", "getAverageRating", "()D", "getFavoriteGenres", "()Ljava/util/List;", "getPreferredContentTypes", "getWatchHistory", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "toString", "app_debug"})
public final class UserProfile {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.Integer> favoriteGenres = null;
    private final double averageRating = 0.0;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> preferredContentTypes = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.streamflix.app.data.model.WatchHistoryItem> watchHistory = null;
    
    public UserProfile(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Integer> favoriteGenres, double averageRating, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> preferredContentTypes, @org.jetbrains.annotations.NotNull()
    java.util.List<com.streamflix.app.data.model.WatchHistoryItem> watchHistory) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.Integer> getFavoriteGenres() {
        return null;
    }
    
    public final double getAverageRating() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getPreferredContentTypes() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.streamflix.app.data.model.WatchHistoryItem> getWatchHistory() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.Integer> component1() {
        return null;
    }
    
    public final double component2() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.streamflix.app.data.model.WatchHistoryItem> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.streamflix.app.data.recommendation.UserProfile copy(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Integer> favoriteGenres, double averageRating, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> preferredContentTypes, @org.jetbrains.annotations.NotNull()
    java.util.List<com.streamflix.app.data.model.WatchHistoryItem> watchHistory) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}