<?php
require_once 'config/database.php';

echo "<h2>🔍 Database Table Structure Check</h2>";

try {
    $db = new Database();
    $conn = $db->connect();
    
    echo "<h3>📊 embed_servers Table Structure</h3>";
    
    // Check if embed_servers table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'embed_servers'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ embed_servers table does not exist!</p>";
        echo "<p>Creating embed_servers table...</p>";
        
        // Create the table with correct structure
        $create_sql = "
        CREATE TABLE embed_servers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            movie_url TEXT NOT NULL,
            tv_url TEXT NOT NULL,
            priority INT DEFAULT 1,
            is_active BOOLEAN DEFAULT 1,
            is_active_movies BOOLEAN DEFAULT 1,
            is_active_tv BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $conn->exec($create_sql);
        echo "<p style='color: green;'>✅ embed_servers table created successfully!</p>";
    } else {
        echo "<p style='color: green;'>✅ embed_servers table exists</p>";
    }
    
    // Show current table structure
    $stmt = $conn->query("DESCRIBE embed_servers");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $has_movie_url = false;
    $has_tv_url = false;
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td><strong>{$column['Field']}</strong></td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
        
        if ($column['Field'] == 'movie_url') $has_movie_url = true;
        if ($column['Field'] == 'tv_url') $has_tv_url = true;
    }
    echo "</table>";
    
    echo "<h3>🔧 Missing Columns Check</h3>";
    
    $missing_columns = [];
    
    if (!$has_movie_url) {
        $missing_columns[] = 'movie_url';
        echo "<p style='color: red;'>❌ Missing column: movie_url</p>";
    } else {
        echo "<p style='color: green;'>✅ movie_url column exists</p>";
    }
    
    if (!$has_tv_url) {
        $missing_columns[] = 'tv_url';
        echo "<p style='color: red;'>❌ Missing column: tv_url</p>";
    } else {
        echo "<p style='color: green;'>✅ tv_url column exists</p>";
    }
    
    // Add missing columns
    if (!empty($missing_columns)) {
        echo "<h3>🔨 Adding Missing Columns</h3>";
        
        foreach ($missing_columns as $column) {
            try {
                if ($column == 'movie_url') {
                    $conn->exec("ALTER TABLE embed_servers ADD COLUMN movie_url TEXT NOT NULL DEFAULT ''");
                    echo "<p style='color: green;'>✅ Added movie_url column</p>";
                } elseif ($column == 'tv_url') {
                    $conn->exec("ALTER TABLE embed_servers ADD COLUMN tv_url TEXT NOT NULL DEFAULT ''");
                    echo "<p style='color: green;'>✅ Added tv_url column</p>";
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error adding {$column}: " . $e->getMessage() . "</p>";
            }
        }
        
        // Also add other useful columns if they don't exist
        $additional_columns = [
            'is_active_movies' => 'BOOLEAN DEFAULT 1',
            'is_active_tv' => 'BOOLEAN DEFAULT 1',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ];
        
        foreach ($additional_columns as $col_name => $col_definition) {
            try {
                $stmt = $conn->query("SHOW COLUMNS FROM embed_servers LIKE '{$col_name}'");
                if ($stmt->rowCount() == 0) {
                    $conn->exec("ALTER TABLE embed_servers ADD COLUMN {$col_name} {$col_definition}");
                    echo "<p style='color: green;'>✅ Added {$col_name} column</p>";
                }
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ Could not add {$col_name}: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<h3>📊 Current Data in embed_servers</h3>";
    
    // Show current data
    $stmt = $conn->query("SELECT * FROM embed_servers ORDER BY priority ASC");
    $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($servers)) {
        echo "<p style='color: orange;'>⚠️ No servers found in database</p>";
        echo "<p>Adding default servers...</p>";
        
        // Insert default servers
        $default_servers = [
            ['AutoEmbed', 'https://player.autoembed.cc/embed/movie/{id}', 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}', 1],
            ['VidJoy', 'https://vidjoy.pro/embed/movie/{id}', 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}', 2],
            ['VidZee', 'https://player.vidzee.wtf/embed/movie/{id}', 'https://player.vidzee.wtf/embed/tv/{id}/{season}/{episode}', 3],
            ['LetsEmbed', 'https://letsembed.cc/embed/movie/?id={id}', 'https://letsembed.cc/embed/tv/?id={id}/{season}/{episode}', 4]
        ];
        
        foreach ($default_servers as $server) {
            try {
                $stmt = $conn->prepare("
                    INSERT INTO embed_servers (name, movie_url, tv_url, priority, is_active, created_at)
                    VALUES (?, ?, ?, ?, 1, NOW())
                ");
                $stmt->execute($server);
                echo "<p style='color: green;'>✅ Added server: {$server[0]}</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error adding {$server[0]}: " . $e->getMessage() . "</p>";
            }
        }
        
        // Refresh data
        $stmt = $conn->query("SELECT * FROM embed_servers ORDER BY priority ASC");
        $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    echo "<p>Total servers: " . count($servers) . "</p>";
    
    if (!empty($servers)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Name</th><th>Priority</th><th>Active</th><th>Movie URL</th></tr>";
        
        foreach ($servers as $server) {
            $status = $server['is_active'] ? '✅ Active' : '❌ Inactive';
            $movie_url = isset($server['movie_url']) ? substr($server['movie_url'], 0, 50) . '...' : 'N/A';
            echo "<tr>";
            echo "<td>{$server['id']}</td>";
            echo "<td><strong>{$server['name']}</strong></td>";
            echo "<td>{$server['priority']}</td>";
            echo "<td>{$status}</td>";
            echo "<td><small>{$movie_url}</small></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>✅ Next Steps</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-left: 5px solid #28a745; margin: 10px 0;'>";
    echo "<p><strong>Table structure has been fixed!</strong></p>";
    echo "<p>Now you can run the fix script:</p>";
    echo "<p><a href='fix_server_sync.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 Run Server Sync Fix</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>
