<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$message = '';
$error = '';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    try {
        $db = new Database();
        $conn = $db->connect();

        if ($action === 'edit_user') {
            $user_id = (int)($_POST['user_id'] ?? 0);
            $username = sanitizeInput($_POST['username'] ?? '');
            $email = sanitizeInput($_POST['email'] ?? '');
            $role = sanitizeInput($_POST['role'] ?? 'user');
            $is_active = isset($_POST['is_active']) ? 1 : 0;
            $is_premium = isset($_POST['is_premium']) ? 1 : 0;

            if ($user_id > 0 && !empty($username) && !empty($email)) {
                // Check if username/email already exists for other users
                $stmt = $conn->prepare("SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?");
                $stmt->execute([$username, $email, $user_id]);

                if ($stmt->rowCount() > 0) {
                    $error = 'Username or email already exists for another user.';
                } else {
                    $stmt = $conn->prepare("
                        UPDATE users
                        SET username = ?, email = ?, role = ?, is_active = ?, is_premium = ?
                        WHERE id = ?
                    ");
                    $stmt->execute([$username, $email, $role, $is_active, $is_premium, $user_id]);
                    $message = 'User updated successfully!';
                }
            }
        }

        elseif ($action === 'ban_user') {
            $user_id = (int)($_POST['user_id'] ?? 0);
            $ban_reason = sanitizeInput($_POST['ban_reason'] ?? '');

            if ($user_id > 0 && $user_id != $_SESSION['user_id']) {
                // Create user_bans table if not exists
                $conn->exec("
                    CREATE TABLE IF NOT EXISTS user_bans (
                        id INT PRIMARY KEY AUTO_INCREMENT,
                        user_id INT,
                        reason TEXT,
                        banned_by INT,
                        banned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        is_active BOOLEAN DEFAULT TRUE,
                        FOREIGN KEY (user_id) REFERENCES users(id),
                        FOREIGN KEY (banned_by) REFERENCES users(id)
                    )
                ");

                // Deactivate user
                $stmt = $conn->prepare("UPDATE users SET is_active = 0 WHERE id = ?");
                $stmt->execute([$user_id]);

                // Add ban record
                $stmt = $conn->prepare("
                    INSERT INTO user_bans (user_id, reason, banned_by)
                    VALUES (?, ?, ?)
                ");
                $stmt->execute([$user_id, $ban_reason, $_SESSION['user_id']]);

                $message = 'User banned successfully!';
            }
        }

        elseif ($action === 'unban_user') {
            $user_id = (int)($_POST['user_id'] ?? 0);

            if ($user_id > 0) {
                // Activate user
                $stmt = $conn->prepare("UPDATE users SET is_active = 1 WHERE id = ?");
                $stmt->execute([$user_id]);

                // Deactivate ban record
                $stmt = $conn->prepare("UPDATE user_bans SET is_active = 0 WHERE user_id = ? AND is_active = 1");
                $stmt->execute([$user_id]);

                $message = 'User unbanned successfully!';
            }
        }

        elseif ($action === 'delete_user') {
            $user_id = (int)($_POST['user_id'] ?? 0);

            if ($user_id > 0 && $user_id != $_SESSION['user_id']) {
                // Delete user's watchlist first
                $stmt = $conn->prepare("DELETE FROM watchlist WHERE user_id = ?");
                $stmt->execute([$user_id]);

                // Delete user
                $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
                $stmt->execute([$user_id]);

                $message = 'User deleted successfully!';
            }
        }

        elseif ($action === 'bulk_action') {
            $selected_users = $_POST['selected_users'] ?? [];
            $bulk_action = $_POST['bulk_action'] ?? '';

            if (!empty($selected_users) && !empty($bulk_action)) {
                $user_ids = array_map('intval', $selected_users);
                $user_ids = array_filter($user_ids, function($id) {
                    return $id > 0 && $id != $_SESSION['user_id'];
                });

                if (!empty($user_ids)) {
                    $placeholders = str_repeat('?,', count($user_ids) - 1) . '?';

                    switch ($bulk_action) {
                        case 'activate':
                            $stmt = $conn->prepare("UPDATE users SET is_active = 1 WHERE id IN ($placeholders)");
                            $stmt->execute($user_ids);
                            $message = count($user_ids) . ' users activated!';
                            break;

                        case 'deactivate':
                            $stmt = $conn->prepare("UPDATE users SET is_active = 0 WHERE id IN ($placeholders)");
                            $stmt->execute($user_ids);
                            $message = count($user_ids) . ' users deactivated!';
                            break;

                        case 'make_premium':
                            $stmt = $conn->prepare("UPDATE users SET is_premium = 1 WHERE id IN ($placeholders)");
                            $stmt->execute($user_ids);
                            $message = count($user_ids) . ' users made premium!';
                            break;

                        case 'remove_premium':
                            $stmt = $conn->prepare("UPDATE users SET is_premium = 0 WHERE id IN ($placeholders)");
                            $stmt->execute($user_ids);
                            $message = count($user_ids) . ' users premium removed!';
                            break;

                        case 'delete':
                            // Delete watchlists first
                            $stmt = $conn->prepare("DELETE FROM watchlist WHERE user_id IN ($placeholders)");
                            $stmt->execute($user_ids);

                            // Delete users
                            $stmt = $conn->prepare("DELETE FROM users WHERE id IN ($placeholders)");
                            $stmt->execute($user_ids);
                            $message = count($user_ids) . ' users deleted!';
                            break;
                    }
                }
            }
        }

    } catch (Exception $e) {
        $error = 'Error: ' . $e->getMessage();
    }
}

// Get users with pagination and search
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;
$search = sanitizeInput($_GET['search'] ?? '');
$filter = sanitizeInput($_GET['filter'] ?? '');

try {
    $db = new Database();
    $conn = $db->connect();

    // Build query
    $where_conditions = [];
    $params = [];

    if (!empty($search)) {
        $where_conditions[] = "(username LIKE ? OR email LIKE ?)";
        $params[] = "%{$search}%";
        $params[] = "%{$search}%";
    }

    if ($filter === 'active') {
        $where_conditions[] = "is_active = 1";
    } elseif ($filter === 'inactive') {
        $where_conditions[] = "is_active = 0";
    } elseif ($filter === 'premium') {
        $where_conditions[] = "is_premium = 1";
    } elseif ($filter === 'admin') {
        $where_conditions[] = "role = 'admin'";
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    // Count total users
    $count_sql = "SELECT COUNT(*) FROM users $where_clause";
    $stmt = $conn->prepare($count_sql);
    $stmt->execute($params);
    $total_users = $stmt->fetchColumn();
    $total_pages = ceil($total_users / $limit);

    // Get users with additional info (with error handling for missing tables)
    $sql = "
        SELECT u.*,
               COALESCE(COUNT(w.id), 0) as watchlist_count,
               0 as is_banned
        FROM users u
        LEFT JOIN watchlist w ON u.id = w.user_id
        $where_clause
        GROUP BY u.id
        ORDER BY u.created_at DESC
        LIMIT ? OFFSET ?
    ";

    // Try to include ban information if table exists
    try {
        $conn->query("SELECT 1 FROM user_bans LIMIT 1");
        // Table exists, use the full query
        $sql = "
            SELECT u.*,
                   COALESCE(COUNT(w.id), 0) as watchlist_count,
                   COALESCE((SELECT COUNT(*) FROM user_bans ub WHERE ub.user_id = u.id AND ub.is_active = 1), 0) as is_banned
            FROM users u
            LEFT JOIN watchlist w ON u.id = w.user_id
            $where_clause
            GROUP BY u.id
            ORDER BY u.created_at DESC
            LIMIT ? OFFSET ?
        ";
    } catch (Exception $e) {
        // user_bans table doesn't exist, use simpler query
        error_log("user_bans table not found, using simplified query");
    }

    $stmt = $conn->prepare($sql);

    // Bind search parameters first
    $param_index = 1;
    foreach ($params as $param) {
        $stmt->bindValue($param_index, $param, PDO::PARAM_STR);
        $param_index++;
    }

    // Bind LIMIT and OFFSET as integers
    $stmt->bindValue($param_index, $limit, PDO::PARAM_INT);
    $stmt->bindValue($param_index + 1, $offset, PDO::PARAM_INT);

    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get user statistics
    $stats = [];
    $stmt = $conn->query("SELECT COUNT(*) FROM users");
    $stats['total'] = $stmt->fetchColumn();

    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE is_active = 1");
    $stats['active'] = $stmt->fetchColumn();

    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE is_premium = 1");
    $stats['premium'] = $stmt->fetchColumn();

    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'admin'");
    $stats['admin'] = $stmt->fetchColumn();

    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
    $stats['new_users'] = $stmt->fetchColumn();

} catch (Exception $e) {
    $error = 'Database error: ' . $e->getMessage();
    $users = [];
    $total_users = 0;
    $total_pages = 0;
    $stats = ['total' => 0, 'active' => 0, 'premium' => 0, 'admin' => 0, 'new_users' => 0];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="assets/admin-style.css">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }

        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            color: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .admin-nav {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 30px;
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .admin-nav a {
            padding: 12px 20px;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .admin-nav a:hover,
        .admin-nav a.active {
            background: #e50914;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(229, 9, 20, 0.4);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-label {
            color: var(--text-color);
            font-size: 0.9rem;
        }

        .controls-section {
            background: var(--secondary-color);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .controls-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .search-box {
            flex: 1;
            min-width: 250px;
            padding: 10px 15px;
            border: 2px solid #333;
            border-radius: 8px;
            background: var(--bg-color);
            color: var(--text-color);
        }

        .filter-select {
            padding: 10px 15px;
            border: 2px solid #333;
            border-radius: 8px;
            background: var(--bg-color);
            color: var(--text-color);
        }

        .bulk-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .users-table {
            background: var(--secondary-color);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .table th {
            background: rgba(255,255,255,0.1);
            font-weight: 600;
            color: var(--text-color);
        }

        .table td {
            color: var(--text-color);
        }

        .table tr:hover {
            background: rgba(255,255,255,0.05);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), #ff6b6b);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 10px;
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            margin-bottom: 2px;
        }

        .user-email {
            font-size: 0.85rem;
            color: #888;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .badge-active {
            background: #d4edda;
            color: #155724;
        }

        .badge-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .badge-premium {
            background: #fff3cd;
            color: #856404;
        }

        .badge-admin {
            background: #d1ecf1;
            color: #0c5460;
        }

        .badge-banned {
            background: #f5c6cb;
            color: #721c24;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .btn-small {
            padding: 6px 10px;
            font-size: 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .btn-edit {
            background: #17a2b8;
            color: white;
        }

        .btn-ban {
            background: #ffc107;
            color: #212529;
        }

        .btn-unban {
            background: #28a745;
            color: white;
        }

        .btn-delete {
            background: #dc3545;
            color: white;
        }

        .btn-small:hover {
            transform: translateY(-1px);
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }

        .pagination a,
        .pagination span {
            padding: 10px 15px;
            background: var(--secondary-color);
            color: var(--text-color);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .pagination a:hover,
        .pagination .current {
            background: var(--primary-color);
            color: white;
        }

        .success-message,
        .error-message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: var(--secondary-color);
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            color: var(--text-color);
            font-size: 1.5rem;
            font-weight: 600;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: var(--text-color);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-color);
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #333;
            border-radius: 8px;
            background: var(--bg-color);
            color: var(--text-color);
            font-size: 14px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
        }

        @media (max-width: 768px) {
            .admin-container {
                margin-top: 80px;
                padding: 0 15px;
            }

            .controls-row {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                min-width: 100%;
            }

            .bulk-controls {
                justify-content: center;
            }

            .table {
                font-size: 14px;
            }

            .table th,
            .table td {
                padding: 10px;
            }

            .action-buttons {
                flex-direction: column;
            }

            .admin-nav {
                justify-content: center;
            }

            .admin-nav a {
                padding: 10px 15px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>👤 User Management</h1>
            <p>Advanced user management with detailed controls</p>
        </div>

        <nav class="admin-nav">
            <a href="index.php">📊 Dashboard</a>
            <a href="movies.php">🎬 Movies</a>
            <a href="tv-shows.php">📺 TV Shows</a>
            <a href="users.php">👥 Users</a>
            <a href="servers.php">🖥️ Servers</a>
            <a href="analytics.php">📈 Analytics</a>
            <a href="import.php">📥 Import</a>
            <a href="app-management.php">📱 App Management</a>
            <a href="user-management.php" class="active">👤 User Management</a>
            <a href="system-logs.php">📋 System Logs</a>
            <a href="maintenance.php">🔧 Maintenance</a>
            <a href="database-updater.php">🗄️ DB Updater</a>
            <a href="quick-setup.php">🚀 Quick Setup</a>
            <a href="settings.php">⚙️ Settings</a>
        </nav>

        <?php if (isset($message)): ?>
            <div class="success-message"><?php echo $message; ?></div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="error-message"><?php echo $error; ?></div>
        <?php endif; ?>

        <!-- User Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['total']); ?></div>
                <div class="stat-label">Total Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['active']); ?></div>
                <div class="stat-label">Active Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['premium']); ?></div>
                <div class="stat-label">Premium Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['admin']); ?></div>
                <div class="stat-label">Admin Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['new_users']); ?></div>
                <div class="stat-label">New Users (30d)</div>
            </div>
        </div>

        <!-- Search and Filter Controls -->
        <div class="controls-section">
            <form method="GET" class="controls-row">
                <input type="text" name="search" class="search-box" placeholder="🔍 Search users..."
                       value="<?php echo htmlspecialchars($search); ?>">

                <select name="filter" class="filter-select">
                    <option value="">All Users</option>
                    <option value="active" <?php echo $filter === 'active' ? 'selected' : ''; ?>>Active Only</option>
                    <option value="inactive" <?php echo $filter === 'inactive' ? 'selected' : ''; ?>>Inactive Only</option>
                    <option value="premium" <?php echo $filter === 'premium' ? 'selected' : ''; ?>>Premium Only</option>
                    <option value="admin" <?php echo $filter === 'admin' ? 'selected' : ''; ?>>Admin Only</option>
                </select>

                <button type="submit" class="btn btn-primary">Filter</button>

                <?php if (!empty($search) || !empty($filter)): ?>
                    <a href="user-management.php" class="btn btn-secondary">Clear</a>
                <?php endif; ?>
            </form>

            <!-- Bulk Actions -->
            <form method="POST" id="bulkForm" class="bulk-controls">
                <input type="hidden" name="action" value="bulk_action">
                <select name="bulk_action" class="filter-select">
                    <option value="">Bulk Actions</option>
                    <option value="activate">Activate Selected</option>
                    <option value="deactivate">Deactivate Selected</option>
                    <option value="make_premium">Make Premium</option>
                    <option value="remove_premium">Remove Premium</option>
                    <option value="delete">Delete Selected</option>
                </select>
                <button type="submit" class="btn btn-secondary" onclick="return confirmBulkAction()">Apply</button>
            </form>
        </div>

        <!-- Users Table -->
        <div class="users-table">
            <table class="table">
                <thead>
                    <tr>
                        <th><input type="checkbox" id="selectAll"></th>
                        <th>User</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Watchlist</th>
                        <th>Joined</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user): ?>
                    <tr>
                        <td>
                            <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                <input type="checkbox" name="selected_users[]" value="<?php echo $user['id']; ?>" form="bulkForm">
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="user-info">
                                <div class="user-avatar">
                                    <?php echo strtoupper(substr($user['username'], 0, 1)); ?>
                                </div>
                                <div class="user-details">
                                    <div class="user-name"><?php echo htmlspecialchars($user['username']); ?></div>
                                    <div class="user-email"><?php echo htmlspecialchars($user['email']); ?></div>
                                    <small>ID: <?php echo $user['id']; ?></small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge <?php echo $user['role'] === 'admin' ? 'badge-admin' : 'badge-active'; ?>">
                                <?php echo ucfirst($user['role']); ?>
                            </span>
                            <?php if ($user['is_premium']): ?>
                                <span class="status-badge badge-premium">Premium</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($user['is_banned']): ?>
                                <span class="status-badge badge-banned">Banned</span>
                            <?php else: ?>
                                <span class="status-badge <?php echo $user['is_active'] ? 'badge-active' : 'badge-inactive'; ?>">
                                    <?php echo $user['is_active'] ? 'Active' : 'Inactive'; ?>
                                </span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo $user['watchlist_count']; ?> items</td>
                        <td><?php echo date('M j, Y', strtotime($user['created_at'])); ?></td>
                        <td>
                            <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                <div class="action-buttons">
                                    <a href="edit-user.php?id=<?php echo $user['id']; ?>" class="btn-small btn-edit">
                                        ✏️ Edit
                                    </a>

                                    <?php if ($user['is_banned']): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="unban_user">
                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                            <button type="submit" class="btn-small btn-unban">🔓 Unban</button>
                                        </form>
                                    <?php else: ?>
                                        <button class="btn-small btn-ban" onclick="banUser(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">
                                            🔒 Ban
                                        </button>
                                    <?php endif; ?>

                                    <a href="delete-user.php?id=<?php echo $user['id']; ?>"
                                       class="btn-small btn-delete"
                                       onclick="return confirm('Are you sure you want to delete this user?')">
                                        🗑️ Delete
                                    </a>
                                </div>
                            <?php else: ?>
                                <span style="color: #888;">Current User</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <?php if (empty($users)): ?>
            <div style="text-align: center; padding: 50px; color: #888;">
                <h3>👥 No users found</h3>
                <p>Try adjusting your search or filter criteria.</p>
            </div>
        <?php endif; ?>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <div class="pagination">
            <?php if ($page > 1): ?>
                <a href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($filter) ? '&filter=' . urlencode($filter) : ''; ?>">← Previous</a>
            <?php endif; ?>

            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                <?php if ($i == $page): ?>
                    <span class="current"><?php echo $i; ?></span>
                <?php else: ?>
                    <a href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($filter) ? '&filter=' . urlencode($filter) : ''; ?>"><?php echo $i; ?></a>
                <?php endif; ?>
            <?php endfor; ?>

            <?php if ($page < $total_pages): ?>
                <a href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($filter) ? '&filter=' . urlencode($filter) : ''; ?>">Next →</a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>

    <!-- Edit User Modal -->
    <div id="editUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">✏️ Edit User</h2>
                <span class="close" onclick="closeModal('editUserModal')">&times;</span>
            </div>

            <form method="POST" id="editUserForm">
                <input type="hidden" name="action" value="edit_user">
                <input type="hidden" name="user_id" id="editUserId">

                <div class="form-group">
                    <label for="editUsername">Username</label>
                    <input type="text" id="editUsername" name="username" required>
                </div>

                <div class="form-group">
                    <label for="editEmail">Email</label>
                    <input type="email" id="editEmail" name="email" required>
                </div>

                <div class="form-group">
                    <label for="editRole">Role</label>
                    <select id="editRole" name="role">
                        <option value="user">User</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="editIsActive" name="is_active">
                    <label for="editIsActive">Active</label>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="editIsPremium" name="is_premium">
                    <label for="editIsPremium">Premium</label>
                </div>

                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('editUserModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">💾 Save Changes</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Ban User Modal -->
    <div id="banUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">🔒 Ban User</h2>
                <span class="close" onclick="closeModal('banUserModal')">&times;</span>
            </div>

            <form method="POST" id="banUserForm">
                <input type="hidden" name="action" value="ban_user">
                <input type="hidden" name="user_id" id="banUserId">

                <p>Are you sure you want to ban <strong id="banUsername"></strong>?</p>

                <div class="form-group">
                    <label for="banReason">Ban Reason</label>
                    <textarea id="banReason" name="ban_reason" rows="3" placeholder="Enter reason for banning this user..." required></textarea>
                </div>

                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('banUserModal')">Cancel</button>
                    <button type="submit" class="btn btn-danger">🔒 Ban User</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Select All functionality
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('input[name="selected_users[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // Edit User Modal
        function editUser(id, username, email, role, isActive, isPremium) {
            document.getElementById('editUserId').value = id;
            document.getElementById('editUsername').value = username;
            document.getElementById('editEmail').value = email;
            document.getElementById('editRole').value = role;
            document.getElementById('editIsActive').checked = isActive == 1;
            document.getElementById('editIsPremium').checked = isPremium == 1;

            document.getElementById('editUserModal').style.display = 'block';
        }

        // Ban User Modal
        function banUser(id, username) {
            document.getElementById('banUserId').value = id;
            document.getElementById('banUsername').textContent = username;
            document.getElementById('banReason').value = '';

            document.getElementById('banUserModal').style.display = 'block';
        }

        // Close Modal
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Confirm bulk action
        function confirmBulkAction() {
            const selectedUsers = document.querySelectorAll('input[name="selected_users[]"]:checked');
            const bulkAction = document.querySelector('select[name="bulk_action"]').value;

            if (selectedUsers.length === 0) {
                alert('Please select at least one user.');
                return false;
            }

            if (!bulkAction) {
                alert('Please select an action.');
                return false;
            }

            const actionText = {
                'activate': 'activate',
                'deactivate': 'deactivate',
                'make_premium': 'make premium',
                'remove_premium': 'remove premium from',
                'delete': 'delete'
            };

            return confirm(`Are you sure you want to ${actionText[bulkAction]} ${selectedUsers.length} selected user(s)?`);
        }

        // Auto-submit filter form on change
        document.querySelector('select[name="filter"]').addEventListener('change', function() {
            this.form.submit();
        });
    </script>
</body>
</html>