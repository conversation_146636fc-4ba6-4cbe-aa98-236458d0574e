# 🎬 StreamFlix Android App

A Netflix-level streaming application built with modern Android technologies.

## ✨ Features

### 🎯 Core Features
- **Netflix-Style UI** - Modern Material 3 design
- **Advanced Video Player** - ExoPlayer with custom controls
- **Ad-Block Technology** - Built-in ad blocking for web players
- **Smart Downloads** - Background downloading with queue management
- **Offline Mode** - Watch content without internet
- **AI Recommendations** - Personalized content suggestions

### 🔐 Authentication & Security
- **JWT Authentication** - Secure token-based auth
- **Biometric Login** - Fingerprint/Face unlock
- **Encrypted Storage** - Secure local data storage
- **Network Security** - HTTPS enforcement

### 📱 User Experience
- **Dark/Light Theme** - System-aware theming
- **Smooth Animations** - 60fps animations throughout
- **Gesture Controls** - Intuitive navigation
- **Voice Search** - Speech-to-text search
- **Picture-in-Picture** - Background video playback

### 🎮 Advanced Player Features
- **Multiple Servers** - Automatic failover
- **Quality Selection** - Auto/Manual quality control
- **Subtitle Support** - Multiple languages
- **Playback Speed** - 0.5x to 2.0x speed control
- **Auto-Next Episode** - Seamless TV show watching
- **Resume Playback** - Continue where you left off

### 📊 Smart Features
- **Watch Analytics** - Viewing statistics
- **Content Caching** - Intelligent content pre-loading
- **Search History** - Recent and trending searches
- **Watchlist Management** - Personal content lists
- **Download Manager** - Advanced download controls

## 🛠️ Tech Stack

### Architecture
- **MVVM Pattern** - Clean architecture
- **Jetpack Compose** - Modern UI toolkit
- **Hilt** - Dependency injection
- **Coroutines & Flow** - Reactive programming

### Networking
- **Retrofit** - REST API client
- **OkHttp** - HTTP client with interceptors
- **Gson** - JSON serialization

### Storage
- **Room Database** - Local database
- **DataStore** - Preferences storage
- **File Management** - Download storage

### Media
- **ExoPlayer** - Video playback
- **Coil** - Image loading
- **Lottie** - Animations

### UI/UX
- **Material 3** - Design system
- **Compose Navigation** - Screen navigation
- **Accompanist** - Compose utilities

## 🚀 Getting Started

### Prerequisites
- Android Studio Hedgehog (2023.1.1) or later
- JDK 17 or later
- Android SDK 34
- Minimum Android 7.0 (API 24)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/streamflix-android.git
   cd streamflix-android/android
   ```

2. **Open in Android Studio**
   - Open Android Studio
   - Select "Open an existing project"
   - Navigate to the `android` folder

3. **Configure API**
   - Update `BASE_URL` in `app/build.gradle`
   - Add your API endpoints

4. **Build the app**
   ```bash
   # Windows
   build_app.bat
   
   # Linux/Mac
   chmod +x build_app.sh
   ./build_app.sh
   ```

### Quick Build Commands

```bash
# Clean build
./gradlew clean

# Debug build
./gradlew assembleDebug

# Release build
./gradlew assembleRelease

# Install debug APK
adb install app/build/outputs/apk/debug/app-debug.apk
```

## 📁 Project Structure

```
android/
├── app/
│   ├── src/main/java/com/streamflix/app/
│   │   ├── data/                    # Data layer
│   │   │   ├── api/                 # API services
│   │   │   ├── local/               # Local storage
│   │   │   ├── model/               # Data models
│   │   │   ├── repository/          # Repositories
│   │   │   ├── download/            # Download manager
│   │   │   ├── offline/             # Offline mode
│   │   │   └── recommendation/      # AI recommendations
│   │   ├── di/                      # Dependency injection
│   │   ├── presentation/            # UI layer
│   │   │   ├── splash/              # Splash screen
│   │   │   ├── auth/                # Authentication
│   │   │   ├── main/                # Main activity
│   │   │   ├── home/                # Home screen
│   │   │   ├── search/              # Search functionality
│   │   │   ├── player/              # Video player
│   │   │   ├── profile/             # User profile
│   │   │   └── components/          # Reusable components
│   │   ├── ui/theme/                # App theming
│   │   └── utils/                   # Utilities
│   └── src/main/res/                # Resources
├── build.gradle                     # Project build config
├── settings.gradle                  # Project settings
└── gradle.properties               # Gradle properties
```

## 🎨 Design System

### Colors
- **Primary**: StreamFlix Red (#E50914)
- **Background**: Dark (#000000)
- **Surface**: Dark Gray (#141414)
- **Text**: White (#FFFFFF)

### Typography
- **Font Family**: Netflix Sans (fallback to system)
- **Scales**: Display, Headline, Title, Body, Label

### Components
- **Cards**: Rounded corners, elevation
- **Buttons**: Material 3 style
- **Navigation**: Bottom navigation bar
- **Player**: Custom video controls

## 🔧 Configuration

### API Configuration
Update `app/build.gradle`:
```gradle
buildConfigField "String", "BASE_URL", "\"https://yourdomain.com/api/\""
buildConfigField "String", "IMAGE_BASE_URL", "\"https://image.tmdb.org/t/p/w500\""
```

### Theme Configuration
Modify `ui/theme/Color.kt` for custom colors.

### Network Configuration
Update `di/NetworkModule.kt` for API settings.

## 📱 Features in Detail

### Ad-Block System
- **Pattern Matching**: Regex-based ad detection
- **Domain Filtering**: Block known ad networks
- **Resource Blocking**: Block ad scripts and trackers
- **Pop-up Prevention**: Block pop-ups and redirects

### Download Manager
- **Background Downloads**: WorkManager integration
- **Queue Management**: Priority-based downloading
- **WiFi-Only Option**: Data saving mode
- **Auto-Resume**: Network reconnection handling

### Recommendation Engine
- **Machine Learning**: User behavior analysis
- **Content-Based**: Similar content suggestions
- **Collaborative**: User preference matching
- **Trending Analysis**: Real-time popularity tracking

## 🧪 Testing

```bash
# Unit tests
./gradlew test

# Instrumented tests
./gradlew connectedAndroidTest

# Lint checks
./gradlew lint
```

## 📦 Building Release

1. **Generate signing key**
   ```bash
   keytool -genkey -v -keystore streamflix-release-key.keystore -alias streamflix -keyalg RSA -keysize 2048 -validity 10000
   ```

2. **Configure signing in `app/build.gradle`**
   ```gradle
   signingConfigs {
       release {
           storeFile file('streamflix-release-key.keystore')
           storePassword 'your_store_password'
           keyAlias 'streamflix'
           keyPassword 'your_key_password'
       }
   }
   ```

3. **Build release APK**
   ```bash
   ./gradlew assembleRelease
   ```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Material Design** - Google's design system
- **Jetpack Compose** - Modern Android UI toolkit
- **ExoPlayer** - Google's media player
- **Netflix** - UI/UX inspiration

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Email: <EMAIL>
- Documentation: [docs.streamflix.app](https://docs.streamflix.app)

---

**Made with ❤️ for the streaming community**
