{"logs": [{"outputFile": "com.streamflix.app-mergeDebugResources-83:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d09ac5c5e9f0e76cba4d9b249ad65917\\transformed\\jetified-media3-exoplayer-1.2.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,193,258,327,404,478,567,655", "endColumns": "74,62,64,68,76,73,88,87,68", "endOffsets": "125,188,253,322,399,473,562,650,719"}, "to": {"startLines": "95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7658,7733,7796,7861,7930,8007,8081,8170,8258", "endColumns": "74,62,64,68,76,73,88,87,68", "endOffsets": "7728,7791,7856,7925,8002,8076,8165,8253,8322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\47494bae0d5af340ce384dde3add152e\\transformed\\appcompat-1.6.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,2899"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,245", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "935,1055,1164,1272,1357,1459,1575,1660,1740,1831,1924,2019,2113,2212,2305,2404,2500,2591,2682,2764,2871,2970,3069,3177,3285,3392,3551,21187", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "1050,1159,1267,1352,1454,1570,1655,1735,1826,1919,2014,2108,2207,2300,2399,2495,2586,2677,2759,2866,2965,3064,3172,3280,3387,3546,3646,21265"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5e0eb68a5716cdfe313e221efb4d1df6\\transformed\\core-1.12.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "55,56,57,58,59,60,61,247", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4100,4199,4301,4401,4499,4606,4712,21347", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "4194,4296,4396,4494,4601,4707,4827,21443"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ecb7be72ebd258ebb899bf8355eead54\\transformed\\jetified-ui-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,286,384,487,576,655,751,843,930,994,1058,1145,1235,1312,1390,1460", "endColumns": "98,81,97,102,88,78,95,91,86,63,63,86,89,76,77,69,122", "endOffsets": "199,281,379,482,571,650,746,838,925,989,1053,1140,1230,1307,1385,1455,1578"}, "to": {"startLines": "65,66,67,68,69,124,125,238,239,240,241,243,244,246,248,249,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5143,5242,5324,5422,5525,9633,9712,20620,20712,20799,20863,21010,21097,21270,21448,21526,21596", "endColumns": "98,81,97,102,88,78,95,91,86,63,63,86,89,76,77,69,122", "endOffsets": "5237,5319,5417,5520,5609,9707,9803,20707,20794,20858,20922,21092,21182,21342,21521,21591,21714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1876129b9e490c2861a5ecf91d698967\\transformed\\jetified-foundation-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "251,252", "startColumns": "4,4", "startOffsets": "21719,21819", "endColumns": "99,101", "endOffsets": "21814,21916"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dfc9ccab22fd45fc4cc7c7544966016d\\transformed\\material-1.11.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,360,440,526,631,727,829,957,1038,1103,1198,1268,1331,1424,1488,1560,1623,1697,1761,1817,1935,1993,2055,2111,2191,2325,2414,2495,2636,2717,2797,2948,3038,3115,3171,3227,3293,3369,3451,3539,3628,3701,3778,3848,3925,4031,4120,4194,4288,4390,4462,4543,4647,4700,4785,4852,4945,5034,5096,5160,5223,5291,5402,5513,5615,5720,5780,5840", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,81,79,85,104,95,101,127,80,64,94,69,62,92,63,71,62,73,63,55,117,57,61,55,79,133,88,80,140,80,79,150,89,76,55,55,65,75,81,87,88,72,76,69,76,105,88,73,93,101,71,80,103,52,84,66,92,88,61,63,62,67,110,110,101,104,59,59,82", "endOffsets": "273,355,435,521,626,722,824,952,1033,1098,1193,1263,1326,1419,1483,1555,1618,1692,1756,1812,1930,1988,2050,2106,2186,2320,2409,2490,2631,2712,2792,2943,3033,3110,3166,3222,3288,3364,3446,3534,3623,3696,3773,3843,3920,4026,4115,4189,4283,4385,4457,4538,4642,4695,4780,4847,4940,5029,5091,5155,5218,5286,5397,5508,5610,5715,5775,5835,5918"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,70,122,123,126,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,242", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "757,3651,3733,3813,3899,4004,4832,4934,5062,5614,9468,9563,9808,16111,16204,16268,16340,16403,16477,16541,16597,16715,16773,16835,16891,16971,17105,17194,17275,17416,17497,17577,17728,17818,17895,17951,18007,18073,18149,18231,18319,18408,18481,18558,18628,18705,18811,18900,18974,19068,19170,19242,19323,19427,19480,19565,19632,19725,19814,19876,19940,20003,20071,20182,20293,20395,20500,20560,20927", "endLines": "22,50,51,52,53,54,62,63,64,70,122,123,126,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,242", "endColumns": "12,81,79,85,104,95,101,127,80,64,94,69,62,92,63,71,62,73,63,55,117,57,61,55,79,133,88,80,140,80,79,150,89,76,55,55,65,75,81,87,88,72,76,69,76,105,88,73,93,101,71,80,103,52,84,66,92,88,61,63,62,67,110,110,101,104,59,59,82", "endOffsets": "930,3728,3808,3894,3999,4095,4929,5057,5138,5674,9558,9628,9866,16199,16263,16335,16398,16472,16536,16592,16710,16768,16830,16886,16966,17100,17189,17270,17411,17492,17572,17723,17813,17890,17946,18002,18068,18144,18226,18314,18403,18476,18553,18623,18700,18806,18895,18969,19063,19165,19237,19318,19422,19475,19560,19627,19720,19809,19871,19935,19998,20066,20177,20288,20390,20495,20555,20615,21005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac2f50916fa650919f030349484e55e3\\transformed\\jetified-material3-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,299,422,542,642,740,855,998,1116,1268,1353,1455,1552,1654,1772,1895,2002,2138,2271,2410,2592,2723,2843,2965,3092,3190,3286,3407,3540,3641,3746,3861,3996,4137,4248,4353,4430,4526,4621,4708,4797,4908,4988,5072,5173,5279,5379,5478,5566,5681,5782,5886,6009,6089,6196", "endColumns": "121,121,122,119,99,97,114,142,117,151,84,101,96,101,117,122,106,135,132,138,181,130,119,121,126,97,95,120,132,100,104,114,134,140,110,104,76,95,94,86,88,110,79,83,100,105,99,98,87,114,100,103,122,79,106,98", "endOffsets": "172,294,417,537,637,735,850,993,1111,1263,1348,1450,1547,1649,1767,1890,1997,2133,2266,2405,2587,2718,2838,2960,3087,3185,3281,3402,3535,3636,3741,3856,3991,4132,4243,4348,4425,4521,4616,4703,4792,4903,4983,5067,5168,5274,5374,5473,5561,5676,5777,5881,6004,6084,6191,6290"}, "to": {"startLines": "127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9871,9993,10115,10238,10358,10458,10556,10671,10814,10932,11084,11169,11271,11368,11470,11588,11711,11818,11954,12087,12226,12408,12539,12659,12781,12908,13006,13102,13223,13356,13457,13562,13677,13812,13953,14064,14169,14246,14342,14437,14524,14613,14724,14804,14888,14989,15095,15195,15294,15382,15497,15598,15702,15825,15905,16012", "endColumns": "121,121,122,119,99,97,114,142,117,151,84,101,96,101,117,122,106,135,132,138,181,130,119,121,126,97,95,120,132,100,104,114,134,140,110,104,76,95,94,86,88,110,79,83,100,105,99,98,87,114,100,103,122,79,106,98", "endOffsets": "9988,10110,10233,10353,10453,10551,10666,10809,10927,11079,11164,11266,11363,11465,11583,11706,11813,11949,12082,12221,12403,12534,12654,12776,12903,13001,13097,13218,13351,13452,13557,13672,13807,13948,14059,14164,14241,14337,14432,14519,14608,14719,14799,14883,14984,15090,15190,15289,15377,15492,15593,15697,15820,15900,16007,16106"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\11b247236daf7bf1e583aa11f0369379\\transformed\\jetified-media3-ui-1.2.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,475,662,748,835,908,1004,1100,1180,1248,1347,1446,1512,1581,1647,1718,1813,1908,2003,2074,2158,2234,2314,2412,2511,2577,2641,2694,2752,2800,2861,2926,2988,3054,3126,3190,3251,3317,3382,3448,3501,3566,3645,3724", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,72,95,95,79,67,98,98,65,68,65,70,94,94,94,70,83,75,79,97,98,65,63,52,57,47,60,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "280,470,657,743,830,903,999,1095,1175,1243,1342,1441,1507,1576,1642,1713,1808,1903,1998,2069,2153,2229,2309,2407,2506,2572,2636,2689,2747,2795,2856,2921,2983,3049,3121,3185,3246,3312,3377,3443,3496,3561,3640,3719,3777"}, "to": {"startLines": "2,11,15,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,570,5679,5765,5852,5925,6021,6117,6197,6265,6364,6463,6529,6598,6664,6735,6830,6925,7020,7091,7175,7251,7331,7429,7528,7594,8327,8380,8438,8486,8547,8612,8674,8740,8812,8876,8937,9003,9068,9134,9187,9252,9331,9410", "endLines": "10,14,18,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "17,12,12,85,86,72,95,95,79,67,98,98,65,68,65,70,94,94,94,70,83,75,79,97,98,65,63,52,57,47,60,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "375,565,752,5760,5847,5920,6016,6112,6192,6260,6359,6458,6524,6593,6659,6730,6825,6920,7015,7086,7170,7246,7326,7424,7523,7589,7653,8375,8433,8481,8542,8607,8669,8735,8807,8871,8932,8998,9063,9129,9182,9247,9326,9405,9463"}}]}]}