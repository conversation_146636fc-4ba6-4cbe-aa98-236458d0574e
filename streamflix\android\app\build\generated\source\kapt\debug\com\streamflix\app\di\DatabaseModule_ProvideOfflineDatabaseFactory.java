// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.streamflix.app.di;

import android.content.Context;
import com.streamflix.app.data.offline.OfflineDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideOfflineDatabaseFactory implements Factory<OfflineDatabase> {
  private final Provider<Context> contextProvider;

  public DatabaseModule_ProvideOfflineDatabaseFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public OfflineDatabase get() {
    return provideOfflineDatabase(contextProvider.get());
  }

  public static DatabaseModule_ProvideOfflineDatabaseFactory create(
      Provider<Context> contextProvider) {
    return new DatabaseModule_ProvideOfflineDatabaseFactory(contextProvider);
  }

  public static OfflineDatabase provideOfflineDatabase(Context context) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideOfflineDatabase(context));
  }
}
