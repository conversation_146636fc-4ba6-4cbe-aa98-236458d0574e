<?php
session_start();
require_once '../config/database.php';

// Simple admin check
$is_admin = false;
if (isset($_SESSION['user_id'])) {
    if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
        $is_admin = true;
    } elseif (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
        $is_admin = true;
    }
}

if (!$is_admin) {
    header('Location: ../login.php');
    exit;
}

echo "<h1>🧪 JSON URL Tester</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
    .container { max-width: 1000px; margin: 0 auto; }
    .section { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    .btn { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; border: none; cursor: pointer; }
    .btn-success { background: #28a745; }
    .btn-danger { background: #dc3545; }
    .btn-warning { background: #ffc107; color: #212529; }
    table { width: 100%; border-collapse: collapse; margin: 15px 0; }
    th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
    th { background: #f8f9fa; }
    .status-ok { color: #28a745; font-weight: bold; }
    .status-error { color: #dc3545; font-weight: bold; }
    .status-warning { color: #ffc107; font-weight: bold; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 0.9rem; }
</style>";

echo "<div class='container'>";

// Test URLs
$test_urls = [
    'LetsEmbed Movie' => [
        'https://letsembed.cc/list/movie.json',
        'https://api.letsembed.cc/movie.json',
        'https://raw.githubusercontent.com/letsembed/database/main/movie.json'
    ],
    'LetsEmbed TV' => [
        'https://letsembed.cc/list/tv.json',
        'https://api.letsembed.cc/tv.json',
        'https://raw.githubusercontent.com/letsembed/database/main/tv.json'
    ],
    'LetsEmbed Anime' => [
        'https://letsembed.cc/list/anime.json',
        'https://api.letsembed.cc/anime.json',
        'https://raw.githubusercontent.com/letsembed/database/main/anime.json'
    ],
    'LetsEmbed Hentai' => [
        'https://letsembed.cc/list/hentai.json',
        'https://api.letsembed.cc/hentai.json',
        'https://raw.githubusercontent.com/letsembed/database/main/hentai.json'
    ],
    'Alternative Sources' => [
        'https://vidsrc.to/vapi/movie/new/1',
        'https://api.themoviedb.org/3/movie/popular?api_key=test',
        'https://jsonplaceholder.typicode.com/posts'
    ]
];

function testURL($url) {
    $result = [
        'url' => $url,
        'status' => 'error',
        'http_code' => 0,
        'content_type' => '',
        'size' => 0,
        'time' => 0,
        'error' => '',
        'sample_data' => ''
    ];
    
    $start_time = microtime(true);
    
    if (function_exists('curl_init')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Accept: application/json, text/plain, */*',
            'Accept-Language: en-US,en;q=0.9'
        ]);
        
        $data = curl_exec($ch);
        $result['http_code'] = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $result['content_type'] = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        $result['size'] = curl_getinfo($ch, CURLINFO_SIZE_DOWNLOAD);
        $result['error'] = curl_error($ch);
        curl_close($ch);
        
        $result['time'] = round((microtime(true) - $start_time) * 1000, 2);
        
        if ($result['error']) {
            $result['status'] = 'error';
            $result['error'] = 'cURL Error: ' . $result['error'];
        } elseif ($result['http_code'] === 200) {
            $result['status'] = 'success';
            
            // Try to parse JSON
            if ($data) {
                $json_data = json_decode($data, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $result['sample_data'] = 'Valid JSON with ' . count($json_data) . ' items';
                    if (is_array($json_data) && count($json_data) > 0) {
                        $first_item = reset($json_data);
                        if (is_array($first_item)) {
                            $result['sample_data'] .= '. Keys: ' . implode(', ', array_keys($first_item));
                        }
                    }
                } else {
                    $result['sample_data'] = 'Invalid JSON: ' . json_last_error_msg();
                    $result['status'] = 'warning';
                }
            }
        } else {
            $result['status'] = 'error';
            $result['error'] = 'HTTP Error: ' . $result['http_code'];
        }
    } else {
        $result['error'] = 'cURL not available';
        $result['status'] = 'error';
    }
    
    return $result;
}

echo "<div class='section'>";
echo "<h2>🔍 JSON URL Testing Results</h2>";
echo "<p>Testing various JSON URLs for content import functionality.</p>";
echo "</div>";

foreach ($test_urls as $category => $urls) {
    echo "<div class='section'>";
    echo "<h3>📂 $category</h3>";
    
    echo "<table>";
    echo "<tr>";
    echo "<th>URL</th>";
    echo "<th>Status</th>";
    echo "<th>HTTP Code</th>";
    echo "<th>Content Type</th>";
    echo "<th>Size</th>";
    echo "<th>Time (ms)</th>";
    echo "<th>Details</th>";
    echo "</tr>";
    
    foreach ($urls as $url) {
        $result = testURL($url);
        
        echo "<tr>";
        echo "<td><a href='" . htmlspecialchars($url) . "' target='_blank'>" . htmlspecialchars($url) . "</a></td>";
        
        // Status
        $status_class = 'status-error';
        if ($result['status'] === 'success') {
            $status_class = 'status-ok';
        } elseif ($result['status'] === 'warning') {
            $status_class = 'status-warning';
        }
        echo "<td class='$status_class'>" . strtoupper($result['status']) . "</td>";
        
        // HTTP Code
        $code_class = $result['http_code'] === 200 ? 'status-ok' : 'status-error';
        echo "<td class='$code_class'>" . $result['http_code'] . "</td>";
        
        // Content Type
        echo "<td>" . htmlspecialchars($result['content_type']) . "</td>";
        
        // Size
        echo "<td>" . ($result['size'] > 0 ? number_format($result['size']) . ' bytes' : 'N/A') . "</td>";
        
        // Time
        echo "<td>" . $result['time'] . "ms</td>";
        
        // Details
        $details = $result['error'] ?: $result['sample_data'];
        echo "<td>" . htmlspecialchars($details) . "</td>";
        
        echo "</tr>";
    }
    
    echo "</table>";
    echo "</div>";
}

// Working URLs section
echo "<div class='section success'>";
echo "<h3>✅ Working URLs Found</h3>";
echo "<p>These URLs are currently accessible and can be used for import:</p>";

$working_urls = [];
foreach ($test_urls as $category => $urls) {
    foreach ($urls as $url) {
        $result = testURL($url);
        if ($result['status'] === 'success') {
            $working_urls[] = [
                'category' => $category,
                'url' => $url,
                'details' => $result['sample_data']
            ];
        }
    }
}

if (!empty($working_urls)) {
    echo "<ul>";
    foreach ($working_urls as $working) {
        echo "<li><strong>{$working['category']}:</strong> ";
        echo "<a href='{$working['url']}' target='_blank'>" . htmlspecialchars($working['url']) . "</a>";
        echo "<br><small>{$working['details']}</small></li>";
    }
    echo "</ul>";
} else {
    echo "<p class='error'>❌ No working URLs found. All tested URLs are currently inaccessible.</p>";
}
echo "</div>";

// Alternative solutions
echo "<div class='section info'>";
echo "<h3>💡 Alternative Solutions</h3>";
echo "<ul>";
echo "<li><strong>Custom JSON Upload:</strong> Upload your own JSON file with movie/TV data</li>";
echo "<li><strong>TMDB API:</strong> Use The Movie Database API for reliable content import</li>";
echo "<li><strong>Manual Import:</strong> Add content manually through the admin panel</li>";
echo "<li><strong>Local JSON Files:</strong> Host JSON files on your own server</li>";
echo "</ul>";
echo "</div>";

// Quick actions
echo "<div class='section'>";
echo "<h3>🔧 Quick Actions</h3>";
echo "<a href='import.php' class='btn btn-success'>📥 Back to Import</a>";
echo "<a href='index.php' class='btn'>📊 Admin Dashboard</a>";
echo "<a href='test-import.php' class='btn btn-warning'>🧪 Test Import System</a>";
echo "</div>";

echo "</div>";
?>
