package com.streamflix.app.data.api

import com.streamflix.app.data.model.*
import retrofit2.Response
import retrofit2.http.*

interface StreamFlixApiService {

    // ==================== Authentication ====================
    
    @POST("auth.php?action=login")
    suspend fun login(
        @Body request: LoginRequest
    ): Response<ApiResponse<AuthResponse>>
    
    @POST("auth.php?action=register")
    suspend fun register(
        @Body request: RegisterRequest
    ): Response<ApiResponse<AuthResponse>>
    
    @POST("auth.php?action=refresh")
    suspend fun refreshToken(): Response<ApiResponse<AuthResponse>>
    
    @GET("auth.php?action=profile")
    suspend fun getProfile(): Response<ApiResponse<User>>
    
    @PUT("auth.php?action=update-profile")
    suspend fun updateProfile(
        @Body request: UpdateProfileRequest
    ): Response<ApiResponse<User>>
    
    @PUT("auth.php?action=change-password")
    suspend fun changePassword(
        @Body request: ChangePasswordRequest
    ): Response<ApiResponse<Any>>

    // ==================== Movies ====================
    
    @GET("movies.php?action=list")
    suspend fun getMovies(
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 20
    ): Response<ApiResponse<MovieListResponse>>
    
    @GET("movies.php?action=featured")
    suspend fun getFeaturedMovies(
        @Query("limit") limit: Int = 10
    ): Response<ApiResponse<MoviesResponse>>
    
    @GET("movies.php?action=trending")
    suspend fun getTrendingMovies(
        @Query("limit") limit: Int = 10
    ): Response<ApiResponse<MoviesResponse>>
    
    @GET("movies.php?action=popular")
    suspend fun getPopularMovies(
        @Query("limit") limit: Int = 20
    ): Response<ApiResponse<MoviesResponse>>
    
    @GET("movies.php?action=latest")
    suspend fun getLatestMovies(
        @Query("limit") limit: Int = 20
    ): Response<ApiResponse<MoviesResponse>>
    
    @GET("movies.php?action=details")
    suspend fun getMovieDetails(
        @Query("id") movieId: Int
    ): Response<ApiResponse<MovieDetailsResponse>>
    
    @GET("movies.php?action=search")
    suspend fun searchMovies(
        @Query("q") query: String,
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 20
    ): Response<ApiResponse<MovieListResponse>>
    
    @GET("movies.php?action=genres")
    suspend fun getMovieGenres(): Response<ApiResponse<GenresResponse>>
    
    @GET("movies.php?action=by-genre")
    suspend fun getMoviesByGenre(
        @Query("genre_id") genreId: Int,
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 20
    ): Response<ApiResponse<MovieListResponse>>
    
    @GET("movies.php?action=servers")
    suspend fun getMovieServers(
        @Query("id") movieId: Int
    ): Response<ApiResponse<ServersResponse>>

    // ==================== TV Shows ====================
    
    @GET("tv-shows.php?action=list")
    suspend fun getTvShows(
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 20
    ): Response<ApiResponse<TvShowListResponse>>
    
    @GET("tv-shows.php?action=featured")
    suspend fun getFeaturedTvShows(
        @Query("limit") limit: Int = 10
    ): Response<ApiResponse<TvShowsResponse>>
    
    @GET("tv-shows.php?action=trending")
    suspend fun getTrendingTvShows(
        @Query("limit") limit: Int = 10
    ): Response<ApiResponse<TvShowsResponse>>
    
    @GET("tv-shows.php?action=popular")
    suspend fun getPopularTvShows(
        @Query("limit") limit: Int = 20
    ): Response<ApiResponse<TvShowsResponse>>
    
    @GET("tv-shows.php?action=latest")
    suspend fun getLatestTvShows(
        @Query("limit") limit: Int = 20
    ): Response<ApiResponse<TvShowsResponse>>
    
    @GET("tv-shows.php?action=details")
    suspend fun getTvShowDetails(
        @Query("id") tvShowId: Int
    ): Response<ApiResponse<TvShowDetailsResponse>>
    
    @GET("tv-shows.php?action=seasons")
    suspend fun getTvShowSeasons(
        @Query("id") tvShowId: Int
    ): Response<ApiResponse<SeasonsResponse>>
    
    @GET("tv-shows.php?action=episodes")
    suspend fun getTvShowEpisodes(
        @Query("season_id") seasonId: Int
    ): Response<ApiResponse<EpisodesResponse>>
    
    @GET("tv-shows.php?action=search")
    suspend fun searchTvShows(
        @Query("q") query: String,
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 20
    ): Response<ApiResponse<TvShowListResponse>>
    
    @GET("tv-shows.php?action=genres")
    suspend fun getTvShowGenres(): Response<ApiResponse<GenresResponse>>
    
    @GET("tv-shows.php?action=by-genre")
    suspend fun getTvShowsByGenre(
        @Query("genre_id") genreId: Int,
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 20
    ): Response<ApiResponse<TvShowListResponse>>

    // ==================== User ====================
    
    @GET("user.php?action=watchlist")
    suspend fun getWatchlist(
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 20
    ): Response<ApiResponse<WatchlistResponse>>
    
    @POST("user.php?action=add-to-watchlist")
    suspend fun addToWatchlist(
        @Body request: AddToWatchlistRequest
    ): Response<ApiResponse<Any>>
    
    @HTTP(method = "DELETE", path = "user.php?action=remove-from-watchlist", hasBody = true)
    suspend fun removeFromWatchlist(
        @Body request: RemoveFromWatchlistRequest
    ): Response<ApiResponse<Any>>
    
    @GET("user.php?action=favorites")
    suspend fun getFavorites(): Response<ApiResponse<FavoritesResponse>>
    
    @POST("user.php?action=add-to-favorites")
    suspend fun addToFavorites(
        @Body request: AddToFavoritesRequest
    ): Response<ApiResponse<Any>>
    
    @HTTP(method = "DELETE", path = "user.php?action=remove-from-favorites", hasBody = true)
    suspend fun removeFromFavorites(
        @Body request: RemoveFromFavoritesRequest
    ): Response<ApiResponse<Any>>
    
    @GET("user.php?action=watch-history")
    suspend fun getWatchHistory(): Response<ApiResponse<WatchHistoryResponse>>
    
    @POST("user.php?action=add-to-history")
    suspend fun addToWatchHistory(
        @Body request: AddToWatchHistoryRequest
    ): Response<ApiResponse<Any>>
    
    @GET("user.php?action=continue-watching")
    suspend fun getContinueWatching(): Response<ApiResponse<ContinueWatchingResponse>>

    // ==================== App ====================
    
    @GET("app.php?action=version")
    suspend fun checkAppVersion(
        @Query("client_version") clientVersion: String
    ): Response<ApiResponse<AppVersion>>
    
    @GET("app.php?action=config")
    suspend fun getAppConfig(): Response<ApiResponse<AppConfig>>
    
    @GET("app.php?action=search")
    suspend fun globalSearch(
        @Query("q") query: String,
        @Query("limit") limit: Int = 10
    ): Response<ApiResponse<SearchResult>>
    
    @GET("app.php?action=home")
    suspend fun getHomeData(): Response<ApiResponse<HomeData>>
    
    @GET("app.php?action=genres")
    suspend fun getAllGenres(): Response<ApiResponse<GenresResponse>>
    
    @GET("app.php?action=servers")
    suspend fun getServersList(): Response<ApiResponse<ServersResponse>>
}

// ==================== Request Models ====================

data class LoginRequest(
    val username: String,
    val password: String
)

data class RegisterRequest(
    val username: String,
    val email: String,
    val password: String
)

data class UpdateProfileRequest(
    val username: String?,
    val email: String?
)

data class ChangePasswordRequest(
    val current_password: String,
    val new_password: String
)

data class AddToWatchlistRequest(
    val type: String, // "movie" or "tv_show"
    val content_id: Int
)

data class RemoveFromWatchlistRequest(
    val type: String,
    val content_id: Int
)

data class AddToFavoritesRequest(
    val type: String,
    val content_id: Int
)

data class RemoveFromFavoritesRequest(
    val type: String,
    val content_id: Int
)

data class AddToWatchHistoryRequest(
    val type: String,
    val content_id: Int,
    val watch_time: Int,
    val duration: Int,
    val completed: Boolean = false
)

// ==================== Response Models ====================

data class MoviesResponse(
    val movies: List<Movie>
)

data class MovieListResponse(
    val movies: List<Movie>,
    val pagination: PaginatedResponse<Movie>
)

data class MovieDetailsResponse(
    val movie: Movie
)

data class TvShowsResponse(
    val tv_shows: List<TvShow>
)

data class TvShowListResponse(
    val tv_shows: List<TvShow>,
    val pagination: PaginatedResponse<TvShow>
)

data class TvShowDetailsResponse(
    val tv_show: TvShow
)

data class SeasonsResponse(
    val seasons: List<Season>
)

data class EpisodesResponse(
    val episodes: List<Episode>
)

data class GenresResponse(
    val genres: List<Genre>
)

data class ServersResponse(
    val servers: List<Server>
)

data class WatchlistResponse(
    val watchlist: List<WatchlistItem>,
    val pagination: PaginatedResponse<WatchlistItem>
)

data class FavoritesResponse(
    val favorites: List<WatchlistItem>
)

data class WatchHistoryResponse(
    val watch_history: List<WatchHistoryItem>
)

data class ContinueWatchingResponse(
    val continue_watching: List<WatchHistoryItem>
)

data class AppConfig(
    val app_name: String,
    val api_version: String,
    val features: AppFeatures,
    val content_types: List<String>,
    val supported_qualities: List<String>,
    val pagination: PaginationConfig
)

data class AppFeatures(
    val watchlist: Boolean,
    val favorites: Boolean,
    val watch_history: Boolean,
    val user_profiles: Boolean,
    val search: Boolean,
    val genres: Boolean,
    val trending: Boolean,
    val featured: Boolean
)

data class PaginationConfig(
    val default_limit: Int,
    val max_limit: Int
)
