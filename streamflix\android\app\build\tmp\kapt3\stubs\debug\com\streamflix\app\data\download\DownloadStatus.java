package com.streamflix.app.data.download;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkCapabilities;
import android.os.Build;
import androidx.core.app.NotificationCompat;
import androidx.work.*;
import com.streamflix.app.R;
import com.streamflix.app.StreamFlixApplication;
import com.streamflix.app.data.local.UserPreferences;
import com.streamflix.app.data.model.Movie;
import com.streamflix.app.data.model.TvShow;
import com.streamflix.app.data.model.Episode;
import com.streamflix.app.presentation.main.MainActivity;
import dagger.hilt.android.qualifiers.ApplicationContext;
import kotlinx.coroutines.flow.*;
import java.io.File;
import java.util.*;
import javax.inject.Inject;
import javax.inject.Singleton;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\b\u00a8\u0006\t"}, d2 = {"Lcom/streamflix/app/data/download/DownloadStatus;", "", "(Ljava/lang/String;I)V", "QUEUED", "DOWNLOADING", "PAUSED", "COMPLETED", "FAILED", "CANCELLED", "app_debug"})
public enum DownloadStatus {
    /*public static final*/ QUEUED /* = new QUEUED() */,
    /*public static final*/ DOWNLOADING /* = new DOWNLOADING() */,
    /*public static final*/ PAUSED /* = new PAUSED() */,
    /*public static final*/ COMPLETED /* = new COMPLETED() */,
    /*public static final*/ FAILED /* = new FAILED() */,
    /*public static final*/ CANCELLED /* = new CANCELLED() */;
    
    DownloadStatus() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.streamflix.app.data.download.DownloadStatus> getEntries() {
        return null;
    }
}