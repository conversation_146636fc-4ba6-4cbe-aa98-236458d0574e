<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$reset_results = [];
$reset_errors = [];

// Handle database reset
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    try {
        $db = new Database();
        $conn = $db->connect();
        
        if ($action === 'drop_new_tables') {
            // Drop only new tables, keep existing content
            $tables_to_drop = ['embed_servers', 'site_settings', 'user_activity', 'watchlist'];
            
            foreach ($tables_to_drop as $table) {
                try {
                    $conn->exec("DROP TABLE IF EXISTS {$table}");
                    $reset_results[] = "✅ Dropped table: {$table}";
                } catch (Exception $e) {
                    $reset_errors[] = "❌ Error dropping {$table}: " . $e->getMessage();
                }
            }
        }
        
        if ($action === 'remove_columns') {
            // Remove added columns from existing tables
            $columns_to_remove = [
                'movies' => ['is_featured', 'is_trending'],
                'tv_shows' => ['is_featured', 'is_trending']
            ];
            
            foreach ($columns_to_remove as $table => $columns) {
                foreach ($columns as $column) {
                    try {
                        // Check if column exists first
                        $stmt = $conn->prepare("SHOW COLUMNS FROM {$table} LIKE ?");
                        $stmt->execute([$column]);
                        
                        if ($stmt->rowCount() > 0) {
                            $conn->exec("ALTER TABLE {$table} DROP COLUMN {$column}");
                            $reset_results[] = "✅ Removed column {$column} from {$table}";
                        }
                    } catch (Exception $e) {
                        $reset_errors[] = "❌ Error removing {$column} from {$table}: " . $e->getMessage();
                    }
                }
            }
        }
        
        if ($action === 'fresh_setup') {
            // Fresh setup - create all tables and data
            
            // First drop existing new tables
            $tables_to_drop = ['embed_servers', 'site_settings', 'user_activity', 'watchlist'];
            foreach ($tables_to_drop as $table) {
                try {
                    $conn->exec("DROP TABLE IF EXISTS {$table}");
                } catch (Exception $e) {
                    // Ignore errors
                }
            }
            
            // Create embed_servers table
            $conn->exec("
                CREATE TABLE embed_servers (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    movie_url TEXT NOT NULL,
                    tv_url TEXT NOT NULL,
                    priority INT DEFAULT 1,
                    is_active BOOLEAN DEFAULT 1,
                    last_tested TIMESTAMP NULL,
                    last_response_time INT NULL,
                    last_status_code INT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ");
            $reset_results[] = "✅ Created embed_servers table";
            
            // Create site_settings table
            $conn->exec("
                CREATE TABLE site_settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    setting_key VARCHAR(100) NOT NULL UNIQUE,
                    setting_value TEXT,
                    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ");
            $reset_results[] = "✅ Created site_settings table";
            
            // Create user_activity table
            $conn->exec("
                CREATE TABLE user_activity (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT,
                    activity_type ENUM('login', 'logout', 'view_movie', 'view_tv_show', 'search') NOT NULL,
                    content_type ENUM('movie', 'tv_show') NULL,
                    content_id INT NULL,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ");
            $reset_results[] = "✅ Created user_activity table";
            
            // Create watchlist table
            $conn->exec("
                CREATE TABLE watchlist (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    content_type ENUM('movie', 'tv_show') NOT NULL,
                    content_id INT NOT NULL,
                    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ");
            $reset_results[] = "✅ Created watchlist table";
            
            // Add missing columns
            $columns_to_add = [
                'movies' => [
                    'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                    'is_featured' => 'BOOLEAN DEFAULT 0',
                    'is_trending' => 'BOOLEAN DEFAULT 0'
                ],
                'tv_shows' => [
                    'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                    'is_featured' => 'BOOLEAN DEFAULT 0',
                    'is_trending' => 'BOOLEAN DEFAULT 0'
                ],
                'users' => [
                    'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
                ]
            ];
            
            foreach ($columns_to_add as $table => $columns) {
                foreach ($columns as $column => $definition) {
                    try {
                        // Check if column exists
                        $stmt = $conn->prepare("SHOW COLUMNS FROM {$table} LIKE ?");
                        $stmt->execute([$column]);
                        
                        if ($stmt->rowCount() == 0) {
                            $conn->exec("ALTER TABLE {$table} ADD COLUMN {$column} {$definition}");
                            $reset_results[] = "✅ Added {$column} to {$table}";
                        }
                    } catch (Exception $e) {
                        $reset_errors[] = "❌ Error adding {$column} to {$table}: " . $e->getMessage();
                    }
                }
            }
            
            // Insert default servers
            $servers = [
                ['AutoEmbed', 'https://player.autoembed.cc/embed/movie/{id}', 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}', 1],
                ['VidJoy', 'https://vidjoy.pro/embed/movie/{id}', 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}', 2],
                ['VidZee', 'https://player.vidzee.wtf/embed/movie/{id}', 'https://player.vidzee.wtf/embed/tv/{id}/{season}/{episode}', 3],
                ['SuperEmbed', 'https://multiembed.mov/directstream.php?video_id={id}', 'https://multiembed.mov/directstream.php?video_id={id}&s={season}&e={episode}', 4],
                ['EmbedSu', 'https://embed.su/embed/movie/{id}', 'https://embed.su/embed/tv/{id}/{season}/{episode}', 5],
                ['LetsEmbed', 'https://letsembed.cc/embed/movie/?id={id}', 'https://letsembed.cc/embed/tv/?id={id}/{season}/{episode}', 6]
            ];
            
            foreach ($servers as $server) {
                $stmt = $conn->prepare("INSERT INTO embed_servers (name, movie_url, tv_url, priority, is_active) VALUES (?, ?, ?, ?, 1)");
                $stmt->execute($server);
            }
            $reset_results[] = "✅ Inserted " . count($servers) . " embed servers";
            
            // Insert default settings
            $settings = [
                ['site_name', 'StreamFlix', 'string', 'Website name'],
                ['site_description', 'Premium Movie & TV Show Streaming Platform', 'string', 'Website description'],
                ['tmdb_api_key', '', 'string', 'TMDB API Key for content import'],
                ['maintenance_mode', '0', 'boolean', 'Enable maintenance mode'],
                ['registration_enabled', '1', 'boolean', 'Allow new user registrations'],
                ['featured_content_limit', '10', 'number', 'Number of featured content items'],
                ['trending_content_limit', '20', 'number', 'Number of trending content items'],
                ['max_servers_per_content', '6', 'number', 'Maximum embed servers per content'],
                ['cache_duration', '3600', 'number', 'Cache duration in seconds'],
                ['auto_import_trending', '0', 'boolean', 'Auto import trending content daily']
            ];
            
            foreach ($settings as $setting) {
                $stmt = $conn->prepare("INSERT INTO site_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)");
                $stmt->execute($setting);
            }
            $reset_results[] = "✅ Inserted " . count($settings) . " site settings";
        }
        
    } catch (Exception $e) {
        $reset_errors[] = "❌ Database error: " . $e->getMessage();
    }
}

// Check current status
try {
    $db = new Database();
    $conn = $db->connect();
    
    $status = [];
    
    // Check new tables
    $new_tables = ['embed_servers', 'site_settings', 'user_activity', 'watchlist'];
    foreach ($new_tables as $table) {
        $stmt = $conn->query("SHOW TABLES LIKE '{$table}'");
        $status['tables'][$table] = $stmt->rowCount() > 0;
    }
    
    // Check new columns
    $new_columns = [
        'movies' => ['is_featured', 'is_trending'],
        'tv_shows' => ['is_featured', 'is_trending']
    ];
    
    foreach ($new_columns as $table => $columns) {
        foreach ($columns as $column) {
            $stmt = $conn->query("SHOW COLUMNS FROM {$table} LIKE '{$column}'");
            $status['columns'][$table][$column] = $stmt->rowCount() > 0;
        }
    }
    
} catch (Exception $e) {
    $status = ['error' => $e->getMessage()];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Reset - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container {
            max-width: 800px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .reset-section {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .reset-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-weight: 500;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #000;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .result-item {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 4px;
            background: var(--dark-bg);
        }
        
        .success {
            border-left: 4px solid #28a745;
        }
        
        .error {
            border-left: 4px solid #dc3545;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .status-ok {
            color: #28a745;
        }
        
        .status-missing {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <span>Welcome, <?php echo $_SESSION['username']; ?></span>
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <h1>🔄 Database Reset & Setup</h1>
        <p>Reset database structure and start fresh</p>
        
        <!-- Current Status -->
        <div class="reset-section">
            <h3>📊 Current Status</h3>
            
            <h4>New Tables:</h4>
            <?php foreach ($status['tables'] ?? [] as $table => $exists): ?>
                <div class="status-item">
                    <span><?php echo $table; ?></span>
                    <span class="<?php echo $exists ? 'status-ok' : 'status-missing'; ?>">
                        <?php echo $exists ? '✅ Exists' : '❌ Missing'; ?>
                    </span>
                </div>
            <?php endforeach; ?>
            
            <h4>New Columns:</h4>
            <?php foreach ($status['columns'] ?? [] as $table => $columns): ?>
                <?php foreach ($columns as $column => $exists): ?>
                    <div class="status-item">
                        <span><?php echo $table; ?>.<?php echo $column; ?></span>
                        <span class="<?php echo $exists ? 'status-ok' : 'status-missing'; ?>">
                            <?php echo $exists ? '✅' : '❌'; ?>
                        </span>
                    </div>
                <?php endforeach; ?>
            <?php endforeach; ?>
        </div>

        <!-- Reset Actions -->
        <div class="reset-section">
            <h3>🔧 Reset Actions</h3>
            
            <form method="POST" style="display: inline;" onsubmit="return confirm('This will drop all new tables. Continue?')">
                <input type="hidden" name="action" value="drop_new_tables">
                <button type="submit" class="reset-btn btn-danger">🗑️ Drop New Tables</button>
            </form>
            
            <form method="POST" style="display: inline;" onsubmit="return confirm('This will remove new columns. Continue?')">
                <input type="hidden" name="action" value="remove_columns">
                <button type="submit" class="reset-btn btn-warning">📝 Remove New Columns</button>
            </form>
            
            <form method="POST" style="display: inline;" onsubmit="return confirm('This will create fresh database structure. Continue?')">
                <input type="hidden" name="action" value="fresh_setup">
                <button type="submit" class="reset-btn btn-success">🚀 Fresh Setup</button>
            </form>
        </div>

        <!-- Results -->
        <?php if (!empty($reset_results)): ?>
            <div class="reset-section">
                <h3>✅ Reset Results</h3>
                <?php foreach ($reset_results as $result): ?>
                    <div class="result-item success"><?php echo htmlspecialchars($result); ?></div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($reset_errors)): ?>
            <div class="reset-section">
                <h3>❌ Reset Errors</h3>
                <?php foreach ($reset_errors as $error): ?>
                    <div class="result-item error"><?php echo htmlspecialchars($error); ?></div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <div class="reset-section">
            <p><a href="quick-setup.php">← Back to Quick Setup</a> | <a href="test-database.php">Test Database →</a></p>
        </div>
    </div>
</body>
</html>
