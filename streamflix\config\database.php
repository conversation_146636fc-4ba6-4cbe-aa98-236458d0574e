<?php
// Database Configuration
class Database {
    private $host = 'localhost';
    private $db_name = 'streamflix';
    private $username = 'root';
    private $password = '';
    private $conn;

    public function connect() {
        $this->conn = null;
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
            );
        } catch(PDOException $e) {
            echo "Connection Error: " . $e->getMessage();
        }
        return $this->conn;
    }
}

// TMDB API Configuration
define('TMDB_API_KEY', '6a22df4196745281fa0beba769ad867f');
define('TMDB_BASE_URL', 'https://api.themoviedb.org/3');
define('TMDB_IMAGE_BASE', 'https://image.tmdb.org/t/p/');

// Embed Servers Configuration
$embed_servers = [
    'autoembed' => [
        'name' => 'AutoEmbed',
        'movie_url' => 'https://player.autoembed.cc/embed/movie/{id}',
        'tv_url' => 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}',
        'priority' => 1
    ],
    'vidjoy' => [
        'name' => 'VidJoy',
        'movie_url' => 'https://vidjoy.pro/embed/movie/{id}',
        'tv_url' => 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}',
        'priority' => 2
    ],
    'vidzee' => [
        'name' => 'VidZee',
        'movie_url' => 'https://player.vidzee.wtf/embed/movie/{id}',
        'tv_url' => 'https://player.vidzee.wtf/embed/tv/{id}/{season}/{episode}',
        'priority' => 3
    ]
];

// Site Configuration
define('SITE_NAME', 'StreamFlix');
define('SITE_URL', 'http://localhost/streamflix');
define('ADMIN_EMAIL', '<EMAIL>');

// Security
define('JWT_SECRET', '6757326c40d35f242bd56e89720cbe38acb1f93569c150841d723f23b2c5b240');
define('ENCRYPTION_KEY', '35395fd6c14ded8fe790d3591ad6ccf9ad3aec482de32bea4bef3b81a16d6177');

session_start();
?>