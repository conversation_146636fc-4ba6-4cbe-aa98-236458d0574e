// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.streamflix.app.data.local;

import android.content.Context;
import com.google.gson.Gson;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserPreferences_Factory implements Factory<UserPreferences> {
  private final Provider<Context> contextProvider;

  private final Provider<Gson> gsonProvider;

  public UserPreferences_Factory(Provider<Context> contextProvider, Provider<Gson> gsonProvider) {
    this.contextProvider = contextProvider;
    this.gsonProvider = gsonProvider;
  }

  @Override
  public UserPreferences get() {
    return newInstance(contextProvider.get(), gsonProvider.get());
  }

  public static UserPreferences_Factory create(Provider<Context> contextProvider,
      Provider<Gson> gsonProvider) {
    return new UserPreferences_Factory(contextProvider, gsonProvider);
  }

  public static UserPreferences newInstance(Context context, Gson gson) {
    return new UserPreferences(context, gson);
  }
}
