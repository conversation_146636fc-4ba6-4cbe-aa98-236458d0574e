<?php
require_once 'config/database.php';

echo "<h2>🔧 Complete Player Fix for BDFLiX</h2>";
echo "<p>This will fix both the database and the player functionality.</p>";

try {
    $db = new Database();
    $conn = $db->connect();
    
    echo "<h3>Step 1: 🗃️ Database Setup</h3>";
    
    // Ensure table exists and has data
    $conn->exec("
        CREATE TABLE IF NOT EXISTS embed_servers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            movie_url TEXT NOT NULL,
            tv_url TEXT NOT NULL,
            priority INT DEFAULT 1,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // Clear and insert fresh servers
    $conn->exec("DELETE FROM embed_servers");
    
    $servers = [
        ['AutoEmbed', 'https://player.autoembed.cc/embed/movie/{id}', 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}', 1],
        ['VidJoy', 'https://vidjoy.pro/embed/movie/{id}', 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}', 2],
        ['VidZee', 'https://player.vidzee.wtf/embed/movie/{id}', 'https://player.vidzee.wtf/embed/tv/{id}/{season}/{episode}', 3],
        ['LetsEmbed', 'https://letsembed.cc/embed/movie/?id={id}', 'https://letsembed.cc/embed/tv/?id={id}/{season}/{episode}', 4]
    ];
    
    foreach ($servers as $server) {
        $stmt = $conn->prepare("INSERT INTO embed_servers (name, movie_url, tv_url, priority, is_active) VALUES (?, ?, ?, ?, 1)");
        $stmt->execute($server);
        echo "<p style='color: green;'>✅ Added: {$server[0]}</p>";
    }
    
    echo "<h3>Step 2: 🔧 Fix getEmbedUrls Function</h3>";
    
    // Read current functions.php
    $functions_file = 'includes/functions.php';
    $functions_content = file_get_contents($functions_file);
    
    // Find and replace the getEmbedUrls function
    $old_function_pattern = '/public function getEmbedUrls\(.*?\n    \}/s';
    
    $new_function = 'public function getEmbedUrls($content_type, $tmdb_id, $season = null, $episode = null, $is_hentai = false, $is_anime = false) {
        $urls = [];
        
        try {
            $db = new Database();
            $conn = $db->connect();
            
            // Get servers based on content type
            if ($is_hentai) {
                // Get hentai servers only
                $stmt = $conn->prepare("SELECT * FROM servers WHERE type = \'hentai\' AND is_active = 1 ORDER BY name ASC");
                $stmt->execute();
                $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } elseif ($is_anime) {
                // Get anime servers only
                $stmt = $conn->prepare("SELECT * FROM servers WHERE type = \'anime\' AND is_active = 1 ORDER BY name ASC");
                $stmt->execute();
                $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } else {
                // Get movie/TV embed servers from database
                $stmt = $conn->prepare("SELECT * FROM embed_servers WHERE is_active = 1 ORDER BY priority ASC");
                $stmt->execute();
                $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
            
        } catch (Exception $e) {
            error_log("getEmbedUrls error: " . $e->getMessage());
            $servers = [];
        }

        foreach ($servers as $server) {
            $url = \'\';

            if ($is_hentai) {
                // For hentai content, use hentai servers
                if (isset($server[\'url\'])) {
                    if (strpos($server[\'url\'], \'letsembed.cc\') !== false) {
                        if ($content_type === \'movie\') {
                            $url = $server[\'url\'] . "?id={$tmdb_id}";
                        } else {
                            $url = $server[\'url\'] . "?id={$tmdb_id}/{$season}/{$episode}";
                        }
                    } else {
                        if ($content_type === \'movie\') {
                            $url = $server[\'url\'] . "/movie/{$tmdb_id}";
                        } else {
                            $url = $server[\'url\'] . "/tv/{$tmdb_id}/{$season}/{$episode}";
                        }
                    }
                }
            } elseif ($is_anime) {
                // For anime content, use anime servers
                if (isset($server[\'url\'])) {
                    if (strpos($server[\'url\'], \'letsembed.cc\') !== false) {
                        if ($content_type === \'movie\') {
                            $url = $server[\'url\'] . "?id={$tmdb_id}";
                        } else {
                            $url = $server[\'url\'] . "?id={$tmdb_id}/{$season}/{$episode}";
                        }
                    } else {
                        if ($content_type === \'movie\') {
                            $url = $server[\'url\'] . "/movie/{$tmdb_id}";
                        } else {
                            $url = $server[\'url\'] . "/tv/{$tmdb_id}/{$season}/{$episode}";
                        }
                    }
                }
            } else {
                // For movie/TV content, use embed servers
                if ($content_type === \'movie\' && isset($server[\'movie_url\'])) {
                    $url = str_replace(\'{id}\', $tmdb_id, $server[\'movie_url\']);
                } elseif ($content_type === \'tv_show\' && isset($server[\'tv_url\'])) {
                    $url = str_replace([\'{id}\', \'{season}\', \'{episode}\'], [$tmdb_id, $season, $episode], $server[\'tv_url\']);
                }
            }

            if (!empty($url)) {
                $urls[] = [
                    \'name\' => $server[\'name\'],
                    \'url\' => $url,
                    \'priority\' => $server[\'priority\'] ?? 1
                ];
            }
        }

        // Sort by priority
        usort($urls, function($a, $b) {
            return $a[\'priority\'] - $b[\'priority\'];
        });

        return $urls;
    }';
    
    // Replace the function
    $new_functions_content = preg_replace($old_function_pattern, $new_function, $functions_content);
    
    if ($new_functions_content !== $functions_content) {
        file_put_contents($functions_file, $new_functions_content);
        echo "<p style='color: green;'>✅ Fixed getEmbedUrls function</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Could not automatically fix function. Manual fix needed.</p>";
    }
    
    echo "<h3>Step 3: 🎬 Fix Player Page</h3>";
    
    // Read current player.php
    $player_file = 'player.php';
    $player_content = file_get_contents($player_file);
    
    // Add error handling for empty embed_urls
    $iframe_fix = '<?php if (!empty($embed_urls)): ?>
                <iframe 
                    id="playerIframe" 
                    class="player-iframe" 
                    src="<?php echo $embed_urls[0][\'url\']; ?>"
                    allowfullscreen
                    webkitallowfullscreen
                    mozallowfullscreen
                    allow="autoplay; fullscreen; picture-in-picture"
                    sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-presentation"
                    referrerpolicy="no-referrer">
                </iframe>
                <?php else: ?>
                <div class="no-servers-message">
                    <h3>No Servers Available</h3>
                    <p>Please contact administrator to configure servers.</p>
                    <a href="admin/servers.php" class="btn btn-primary">Configure Servers</a>
                </div>
                <?php endif; ?>';
    
    // Replace iframe section
    $player_content = preg_replace(
        '/<iframe[^>]*id="playerIframe"[^>]*>.*?<\/iframe>/s',
        $iframe_fix,
        $player_content
    );
    
    // Fix server buttons section
    $server_buttons_fix = '<?php if (!empty($embed_urls)): ?>
                    <?php foreach ($embed_urls as $index => $server): ?>
                        <button 
                            class="server-btn <?php echo $index === 0 ? \'active\' : \'\'; ?>" 
                            data-url="<?php echo htmlspecialchars($server[\'url\']); ?>"
                            data-server="<?php echo htmlspecialchars($server[\'name\']); ?>">
                            <?php echo htmlspecialchars($server[\'name\']); ?>
                        </button>
                    <?php endforeach; ?>
                    <?php else: ?>
                    <div class="no-servers-warning">
                        <p>⚠️ No servers configured. Please configure servers in admin panel.</p>
                        <a href="admin/servers.php" class="btn btn-sm btn-primary">Configure Servers</a>
                    </div>
                    <?php endif; ?>';
    
    // Replace server buttons section
    $player_content = preg_replace(
        '/\<\?php foreach \(\$embed_urls as \$index.*?\<\?php endforeach; \?\>/s',
        $server_buttons_fix,
        $player_content
    );
    
    file_put_contents($player_file, $player_content);
    echo "<p style='color: green;'>✅ Fixed player page error handling</p>";
    
    echo "<h3>Step 4: 🧪 Testing</h3>";
    
    // Test the fixed function
    require_once 'includes/functions.php';
    $streamflix = new StreamFlix();
    
    $test_urls = $streamflix->getEmbedUrls('movie', 950387);
    echo "<p><strong>Function now returns: " . count($test_urls) . " servers</strong></p>";
    
    if (!empty($test_urls)) {
        echo "<ol>";
        foreach ($test_urls as $url) {
            echo "<li><strong>{$url['name']}</strong> (Priority: {$url['priority']})</li>";
        }
        echo "</ol>";
        echo "<p style='color: green;'>✅ SUCCESS! Player should work now!</p>";
    } else {
        echo "<p style='color: red;'>❌ Still not working. Manual intervention needed.</p>";
    }
    
    echo "<h3>Step 5: 📋 Final Steps</h3>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-left: 5px solid #28a745; margin: 20px 0;'>";
    echo "<h4 style='color: #155724;'>✅ Fix Applied Successfully!</h4>";
    echo "<p><strong>What was fixed:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Database servers restored (4 servers)</li>";
    echo "<li>✅ getEmbedUrls function fixed</li>";
    echo "<li>✅ Player page error handling added</li>";
    echo "<li>✅ Admin panel integration restored</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #e7f3ff; padding: 20px; border-left: 5px solid #007bff; margin: 20px 0;'>";
    echo "<h4>🎯 Test Now:</h4>";
    echo "<ol>";
    echo "<li><strong>Clear browser cache</strong> (Ctrl+Shift+Delete)</li>";
    echo "<li><strong>Test player:</strong> <a href='player.php?id=950387&type=movie' target='_blank' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Open Player</a></li>";
    echo "<li><strong>Check admin:</strong> <a href='admin/servers.php' target='_blank' style='background: #28a745; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Admin Panel</a></li>";
    echo "<li><strong>Test changes:</strong> Disable a server in admin and check player</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-left: 5px solid #dc3545; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24;'>❌ Error</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
