// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.streamflix.app.data.download;

import android.content.Context;
import androidx.work.WorkerParameters;
import dagger.internal.DaggerGenerated;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DownloadWorker_Factory {
  public DownloadWorker_Factory() {
  }

  public DownloadWorker get(Context context, WorkerParameters workerParams) {
    return newInstance(context, workerParams);
  }

  public static DownloadWorker_Factory create() {
    return new DownloadWorker_Factory();
  }

  public static DownloadWorker newInstance(Context context, WorkerParameters workerParams) {
    return new DownloadWorker(context, workerParams);
  }
}
