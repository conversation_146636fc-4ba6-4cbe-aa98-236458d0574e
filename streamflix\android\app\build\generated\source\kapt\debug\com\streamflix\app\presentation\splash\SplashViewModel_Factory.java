// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.streamflix.app.presentation.splash;

import com.streamflix.app.data.repository.StreamFlixRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SplashViewModel_Factory implements Factory<SplashViewModel> {
  private final Provider<StreamFlixRepository> repositoryProvider;

  public SplashViewModel_Factory(Provider<StreamFlixRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public SplashViewModel get() {
    return newInstance(repositoryProvider.get());
  }

  public static SplashViewModel_Factory create(Provider<StreamFlixRepository> repositoryProvider) {
    return new SplashViewModel_Factory(repositoryProvider);
  }

  public static SplashViewModel newInstance(StreamFlixRepository repository) {
    return new SplashViewModel(repository);
  }
}
