package com.streamflix.app.ui.theme;

import android.app.Activity;
import android.os.Build;
import androidx.compose.runtime.Composable;
import androidx.core.view.WindowCompat;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000$\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0002\u001a\u001b\u0010\u0003\u001a\u00020\u00042\u0011\u0010\u0005\u001a\r\u0012\u0004\u0012\u00020\u00040\u0006\u00a2\u0006\u0002\b\u0007H\u0007\u001a\u001b\u0010\b\u001a\u00020\u00042\u0011\u0010\u0005\u001a\r\u0012\u0004\u0012\u00020\u00040\u0006\u00a2\u0006\u0002\b\u0007H\u0007\u001a/\u0010\t\u001a\u00020\u00042\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\u000b2\u0011\u0010\u0005\u001a\r\u0012\u0004\u0012\u00020\u00040\u0006\u00a2\u0006\u0002\b\u0007H\u0007\"\u000e\u0010\u0000\u001a\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000\"\u000e\u0010\u0002\u001a\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"DarkColorScheme", "Landroidx/compose/material3/ColorScheme;", "LightColorScheme", "StreamFlixPlayerTheme", "", "content", "Lkotlin/Function0;", "Landroidx/compose/runtime/Composable;", "StreamFlixSplashTheme", "StreamFlixTheme", "darkTheme", "", "dynamicColor", "app_debug"})
public final class ThemeKt {
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.material3.ColorScheme DarkColorScheme = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.material3.ColorScheme LightColorScheme = null;
    
    @androidx.compose.runtime.Composable()
    public static final void StreamFlixTheme(boolean darkTheme, boolean dynamicColor, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void StreamFlixPlayerTheme(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void StreamFlixSplashTheme(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
}