package com.streamflix.app.data.download

import android.content.Context
import androidx.hilt.work.HiltWorker
import androidx.work.*
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.net.URL

@HiltWorker
class DownloadWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {

    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        try {
            val downloadId = inputData.getString("download_id") ?: return@withContext Result.failure()
            val contentId = inputData.getInt("content_id", -1)
            val contentType = inputData.getString("content_type") ?: return@withContext Result.failure()
            val title = inputData.getString("title") ?: return@withContext Result.failure()
            val serverUrl = inputData.getString("server_url") ?: return@withContext Result.failure()
            val quality = inputData.getString("quality") ?: "HD"

            // Create download directory
            val downloadDir = File(applicationContext.getExternalFilesDir(null), "downloads")
            if (!downloadDir.exists()) {
                downloadDir.mkdirs()
            }

            val outputFile = File(downloadDir, "$downloadId.mp4")

            // Download the file
            val url = URL(serverUrl)
            val connection = url.openConnection()
            val totalBytes = connection.contentLength.toLong()
            
            connection.getInputStream().use { input ->
                FileOutputStream(outputFile).use { output ->
                    val buffer = ByteArray(8192)
                    var downloadedBytes = 0L
                    var bytesRead: Int

                    while (input.read(buffer).also { bytesRead = it } != -1) {
                        output.write(buffer, 0, bytesRead)
                        downloadedBytes += bytesRead

                        // Update progress
                        val progress = ((downloadedBytes * 100) / totalBytes).toInt()
                        val downloadProgress = DownloadProgress(
                            downloadId = downloadId,
                            title = title,
                            percentage = progress,
                            downloadedBytes = downloadedBytes,
                            totalBytes = totalBytes,
                            speed = 0L, // Calculate speed if needed
                            remainingTime = 0L // Calculate remaining time if needed
                        )

                        // Update progress (simplified - in real implementation, use DownloadManager)
                    setProgress(workDataOf("progress" to progress))

                        // Check if work is cancelled
                        if (isStopped) {
                            return@withContext Result.failure()
                        }
                    }
                }
            }

            Result.success()
        } catch (e: Exception) {
            Result.failure()
        }
    }
}
