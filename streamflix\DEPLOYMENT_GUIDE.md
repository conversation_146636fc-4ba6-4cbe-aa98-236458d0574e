# StreamFlix Live Site Deployment Guide

## Database: tipsbdxy_bdflix2025

### Step 1: Database Setup

1. **Import Database Schema**
   ```sql
   -- Go to phpMyAdmin or your database management tool
   -- Select database: tipsbdxy_bdflix2025
   -- Import: streamflix_complete_schema.sql
   ```

2. **Verify Tables Created**
   - users
   - movies
   - tv_shows
   - genres
   - movie_genres
   - tv_show_genres
   - seasons
   - episodes
   - watchlist
   - watch_history
   - embed_servers
   - anime_servers
   - hentai_servers
   - site_settings

### Step 2: Configuration Files

1. **Update config/database.php**
   ```php
   <?php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'tipsbdxy_bdflix2025');
   define('DB_USER', 'tipsbdxy_bdflix2025'); // Usually same as DB_NAME
   define('DB_PASS', 'your_database_password');
   
   class Database {
       // Copy the Database class from live_site_config.php
   }
   ?>
   ```

2. **Update includes/config.php**
   ```php
   <?php
   define('SITE_NAME', 'StreamFlix');
   define('SITE_URL', 'https://yourdomain.com');
   define('TMDB_API_KEY', 'your_tmdb_api_key');
   // Add other configurations from live_site_config.php
   ?>
   ```

### Step 3: File Permissions

Set proper permissions for these folders:
```bash
chmod 755 uploads/
chmod 755 cache/
chmod 755 logs/
chmod 755 assets/
chmod 644 *.php
```

### Step 4: TMDB API Setup

1. **Get TMDB API Key**
   - Visit: https://www.themoviedb.org/settings/api
   - Create account and request API key
   - Add key to config files

2. **Test API Connection**
   - Go to admin panel
   - Try importing a movie/TV show
   - Verify data is fetched correctly

### Step 5: Default Login

**Admin Credentials:**
- Email: `<EMAIL>`
- Password: `password`

**⚠️ IMPORTANT: Change password immediately after first login!**

### Step 6: Content Setup

1. **Import Movies**
   - Admin Panel → Import → Movies
   - Search and import popular movies
   - Test player functionality

2. **Import TV Shows**
   - Admin Panel → Import → TV Shows
   - Import popular series
   - Test episode navigation

3. **Import Anime**
   - Admin Panel → Import → Anime Movies/TV Shows
   - Verify anime servers work correctly

4. **Import Hentai** (if enabled)
   - Admin Panel → Import → Hentai
   - Test hentai-specific servers

### Step 7: Server Configuration

**Default Servers Included:**
- VidSrc, VidSrc Pro
- SuperEmbed, EmbedSu
- SmashyStream, VidLink
- MovieAPI
- LetsEmbed Anime
- LetsEmbed Hentai

**Test All Servers:**
1. Play content from each server
2. Verify video loads correctly
3. Remove non-working servers
4. Add new servers if needed

### Step 8: Site Customization

1. **Update Site Settings**
   - Admin Panel → Settings
   - Site name, description
   - Contact information
   - Social media links

2. **Upload Logo/Favicon**
   - Add logo to assets/images/
   - Update logo path in settings
   - Upload favicon.ico to root

3. **Customize Design**
   - Modify CSS in assets/css/
   - Update colors, fonts
   - Add custom styling

### Step 9: Security Setup

1. **Change Default Passwords**
   ```sql
   UPDATE users SET password = '$2y$10$newhashedpassword' WHERE email = '<EMAIL>';
   ```

2. **Generate Security Keys**
   ```php
   define('JWT_SECRET', 'generate_random_string_here');
   define('ENCRYPTION_KEY', 'generate_random_string_here');
   ```

3. **Enable SSL**
   - Configure SSL certificate
   - Force HTTPS redirects
   - Update SITE_URL to https://

### Step 10: Performance Optimization

1. **Enable Caching**
   ```php
   define('ENABLE_CACHE', true);
   define('CACHE_DURATION', 3600);
   ```

2. **Optimize Images**
   - Compress poster images
   - Use WebP format when possible
   - Setup CDN for images

3. **Database Optimization**
   - Add indexes for frequently queried columns
   - Regular database cleanup
   - Monitor query performance

### Step 11: Backup Setup

1. **Database Backup**
   ```bash
   # Daily backup cron job
   0 2 * * * mysqldump -u username -p password tipsbdxy_bdflix2025 > backup_$(date +%Y%m%d).sql
   ```

2. **File Backup**
   - Backup uploads folder
   - Backup configuration files
   - Store backups off-site

### Step 12: Monitoring & Maintenance

1. **Error Logging**
   - Monitor error logs
   - Setup log rotation
   - Alert on critical errors

2. **Performance Monitoring**
   - Monitor server resources
   - Track page load times
   - Monitor database performance

3. **Content Updates**
   - Regular content imports
   - Update server URLs
   - Remove broken links

### Troubleshooting

**Common Issues:**

1. **Database Connection Error**
   - Check database credentials
   - Verify database exists
   - Check hosting provider settings

2. **TMDB API Not Working**
   - Verify API key is correct
   - Check API rate limits
   - Test API endpoints manually

3. **Videos Not Playing**
   - Test embed server URLs
   - Check for CORS issues
   - Verify server availability

4. **Import Not Working**
   - Check TMDB API connection
   - Verify database permissions
   - Check error logs

5. **Admin Panel Access Issues**
   - Clear browser cache
   - Check session settings
   - Verify admin credentials

### Support

For technical support:
- Check error logs first
- Test on localhost
- Compare with working installation
- Contact hosting provider for server issues

### Final Checklist

- [ ] Database imported successfully
- [ ] Configuration files updated
- [ ] File permissions set correctly
- [ ] TMDB API key configured
- [ ] Admin login working
- [ ] Content import working
- [ ] Video players working
- [ ] All servers tested
- [ ] SSL certificate installed
- [ ] Backup system setup
- [ ] Error monitoring enabled

**Your StreamFlix site should now be fully functional!**
