<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';
require_once '../includes/database-check.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$streamflix = new StreamFlix();

// Handle TMDB import
if ($_POST['action'] ?? '' === 'import_content') {
    $tmdb_id = (int)($_POST['tmdb_id'] ?? 0);
    $content_type = sanitizeInput($_POST['content_type'] ?? 'movie');
    
    if ($tmdb_id > 0) {
        if ($content_type === 'movie') {
            $result = $streamflix->importMovie($tmdb_id);
        } else {
            $result = $streamflix->importTVShow($tmdb_id);
        }
        
        $message = $result ? 'Content imported successfully!' : 'Failed to import content.';
    }
}

// Get statistics
try {
    $db = new Database();
    $conn = $db->connect();
    
    $stats = [];
    $stmt = $conn->query("SELECT COUNT(*) as count FROM movies");
    $stats['movies'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $stmt = $conn->query("SELECT COUNT(*) as count FROM tv_shows");
    $stats['tv_shows'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $stmt = $conn->query("SELECT COUNT(*) as count FROM users");
    $stats['users'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

    // Get server statistics
    $stmt = $conn->query("SELECT COUNT(*) as count FROM embed_servers");
    $stats['servers'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

    $stmt = $conn->query("SELECT COUNT(*) as count FROM embed_servers WHERE is_active = 1");
    $stats['active_servers'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

    // Get hentai content statistics
    $stmt = $conn->query("SELECT COUNT(*) as count FROM tv_shows WHERE content_type = 'hentai'");
    $stats['hentai_content'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

    // Get hentai servers count
    $stmt = $conn->query("SELECT COUNT(*) as count FROM embed_servers WHERE hentai_url IS NOT NULL AND hentai_url != ''");
    $stats['hentai_servers'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

    // Get anime content statistics
    $stmt = $conn->query("SELECT COUNT(*) as count FROM movies WHERE content_type = 'anime'");
    $stats['anime_movies'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

    $stmt = $conn->query("SELECT COUNT(*) as count FROM tv_shows WHERE content_type = 'anime'");
    $stats['anime_tv'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

    $stats['total_anime'] = $stats['anime_movies'] + $stats['anime_tv'];

    // Get anime servers count
    $stmt = $conn->query("SELECT COUNT(*) as count FROM embed_servers WHERE anime_url IS NOT NULL AND anime_url != ''");
    $stats['anime_servers'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

} catch (Exception $e) {
    $stats = ['movies' => 0, 'tv_shows' => 0, 'users' => 0, 'servers' => 0, 'active_servers' => 0];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="assets/admin-style.css">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }

        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px;
            border-radius: 20px;
            margin-bottom: 40px;
            text-align: center;
            color: white;
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        }

        .admin-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .admin-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: var(--secondary-color);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.1);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), #ff6b6b);
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .stat-label {
            color: var(--text-color);
            font-size: 1rem;
            font-weight: 500;
            opacity: 0.8;
        }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.7;
        }

        .import-section {
            background: var(--secondary-color);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.1);
        }

        .import-section h2 {
            color: var(--text-color);
            margin-bottom: 15px;
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
            align-items: end;
        }
        
        .form-group {
            flex: 1;
        }
        
        .success-message {
            background: #4CAF50;
            color: white;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .error-message {
            background: var(--primary-color);
            color: white;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .admin-nav {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 40px;
            background: var(--secondary-color);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .admin-nav a {
            padding: 15px 25px;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .admin-nav a:hover,
        .admin-nav a.active {
            background: #e50914;
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.4);
        }

        @media (max-width: 768px) {
            .admin-container {
                margin-top: 80px;
                padding: 0 15px;
            }

            .admin-nav {
                justify-content: center;
            }

            .admin-nav a {
                padding: 12px 18px;
                font-size: 13px;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
            }

            .import-section {
                padding: 20px;
            }

            .form-row {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>Admin Panel</h1>
            <p>Manage your streaming platform</p>
        </div>

        <nav class="admin-nav">
            <a href="index.php" class="active">📊 Dashboard</a>
            <a href="movies.php">🎬 Movies</a>
            <a href="tv-shows.php">📺 TV Shows</a>
            <a href="hentai-content.php" style="background: linear-gradient(135deg, #ff1744, #e91e63);">🔞 Hentai Content</a>
            <a href="anime-content.php" style="background: linear-gradient(135deg, #ff6b35, #f7931e);">🎌 Anime Content</a>
            <a href="users.php">👥 Users</a>
            <a href="servers.php">🖥️ Movie/TV Servers</a>
            <a href="anime-servers.php">🎌 Anime Servers</a>
            <a href="hentai-servers.php">🔞 Hentai Servers</a>
            <a href="analytics.php">📈 Analytics</a>
            <a href="import.php">📥 Import</a>
            <a href="app-management.php">📱 App Management</a>
            <a href="user-management.php">👤 User Management</a>
            <a href="system-logs.php">📋 System Logs</a>
            <a href="maintenance.php">🔧 Maintenance</a>
            <a href="database-updater.php">🗄️ DB Updater</a>
            <a href="quick-setup.php">🚀 Quick Setup</a>
            <a href="settings.php">⚙️ Settings</a>
        </nav>

        <?php if (isset($message)): ?>
            <div class="<?php echo $result ? 'success' : 'error'; ?>-message">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <!-- Database Update Notification -->
        <?php showDatabaseUpdateNotification(); ?>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">🎬</div>
                <div class="stat-number"><?php echo number_format($stats['movies']); ?></div>
                <div class="stat-label">Movies</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📺</div>
                <div class="stat-number"><?php echo number_format($stats['tv_shows']); ?></div>
                <div class="stat-label">TV Shows</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-number"><?php echo number_format($stats['users']); ?></div>
                <div class="stat-label">Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🖥️</div>
                <div class="stat-number"><?php echo number_format($stats['servers']); ?></div>
                <div class="stat-label">Total Servers</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">✅</div>
                <div class="stat-number"><?php echo number_format($stats['active_servers']); ?></div>
                <div class="stat-label">Active Servers</div>
            </div>
            <div class="stat-card" style="background: linear-gradient(135deg, #ff1744, #e91e63);">
                <div class="stat-icon" style="color: white;">🔞</div>
                <div class="stat-number" style="color: white;"><?php echo number_format($stats['hentai_content']); ?></div>
                <div class="stat-label" style="color: rgba(255,255,255,0.9);">Hentai Content</div>
            </div>
            <div class="stat-card" style="background: linear-gradient(135deg, #ff6b35, #f7931e);">
                <div class="stat-icon" style="color: white;">🎌</div>
                <div class="stat-number" style="color: white;"><?php echo number_format($stats['total_anime']); ?></div>
                <div class="stat-label" style="color: rgba(255,255,255,0.9);">Anime Content</div>
            </div>
        </div>

        <!-- Import Content -->
        <div class="import-section">
            <h2>Import Content from TMDB</h2>
            <p>Enter TMDB ID to import movies or TV shows automatically</p>
            
            <form method="POST" action="">
                <input type="hidden" name="action" value="import_content">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="tmdb_id">TMDB ID</label>
                        <input type="number" id="tmdb_id" name="tmdb_id" required 
                               placeholder="e.g., 385687 for Fast X">
                    </div>
                    
                    <div class="form-group">
                        <label for="content_type">Type</label>
                        <select id="content_type" name="content_type" required>
                            <option value="movie">Movie</option>
                            <option value="tv_show">TV Show</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">Import</button>
                    </div>
                </div>
            </form>
            
            <div style="margin-top: 20px; padding: 15px; background: var(--dark-bg); border-radius: 4px;">
                <h4>How to find TMDB ID:</h4>
                <ol>
                    <li>Go to <a href="https://www.themoviedb.org" target="_blank" style="color: var(--primary-color);">themoviedb.org</a></li>
                    <li>Search for the movie or TV show</li>
                    <li>The ID is in the URL: themoviedb.org/movie/<strong>385687</strong>-fast-x</li>
                </ol>
            </div>
        </div>

        <!-- Content Management -->
        <div class="import-section">
            <h2>📚 Content Management</h2>
            <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-bottom: 20px;">
                <a href="movies.php" class="btn btn-primary">🎬 Manage Movies</a>
                <a href="tv-shows.php" class="btn btn-primary">📺 Manage TV Shows</a>
                <a href="hentai-content.php" class="btn btn-secondary" style="background: linear-gradient(135deg, #ff1744, #e91e63); color: white;">🔞 Hentai Content</a>
                <a href="anime-content.php" class="btn btn-secondary" style="background: linear-gradient(135deg, #ff6b35, #f7931e); color: white;">🎌 Anime Content</a>
                <a href="import.php" class="btn btn-primary">📥 Import Content</a>
            </div>

            <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-bottom: 20px;">
                <a href="bulk-delete-tv-series.php" class="btn btn-danger" style="background: #dc3545; color: white; font-weight: bold;">🗑️ Bulk Delete TV Series</a>
                <a href="debug-content-display.php" class="btn btn-info">🔍 Debug Content</a>
                <a href="quick-hentai-anime-converter.php" class="btn btn-warning">🔄 Quick Converter</a>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px;">
                <div style="background: var(--dark-bg); padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.5rem; color: var(--primary-color); margin-bottom: 5px;"><?php echo number_format($stats['movies']); ?></div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Movies</div>
                </div>
                <div style="background: var(--dark-bg); padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.5rem; color: #17a2b8; margin-bottom: 5px;"><?php echo number_format($stats['tv_shows']); ?></div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">TV Shows</div>
                </div>
                <div style="background: var(--dark-bg); padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.5rem; color: #ff1744; margin-bottom: 5px;"><?php echo number_format($stats['hentai_content']); ?></div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Hentai Content</div>
                </div>
                <div style="background: var(--dark-bg); padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.5rem; color: #28a745; margin-bottom: 5px;"><?php echo number_format($stats['hentai_servers']); ?></div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Hentai Servers</div>
                </div>
                <div style="background: var(--dark-bg); padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.5rem; color: #ff6b35; margin-bottom: 5px;"><?php echo number_format($stats['anime_movies']); ?></div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Anime Movies</div>
                </div>
                <div style="background: var(--dark-bg); padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.5rem; color: #f7931e; margin-bottom: 5px;"><?php echo number_format($stats['anime_tv']); ?></div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Anime Series</div>
                </div>
                <div style="background: var(--dark-bg); padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.5rem; color: #17a2b8; margin-bottom: 5px;"><?php echo number_format($stats['anime_servers']); ?></div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Anime Servers</div>
                </div>
            </div>
        </div>

        <!-- Server Management -->
        <div class="import-section">
            <h2>🖥️ Server Management</h2>
            <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-bottom: 20px;">
                <a href="servers.php" class="btn btn-primary">🎬 Movie/TV Servers</a>
                <a href="anime-servers.php" class="btn btn-secondary" style="background: linear-gradient(135deg, #ff6b35, #f7931e);">🎌 Anime Servers</a>
                <a href="hentai-servers.php" class="btn btn-secondary" style="background: linear-gradient(135deg, #ff1744, #e91e63);">🔞 Hentai Servers</a>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px;">
                <div style="background: var(--dark-bg); padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.5rem; color: var(--primary-color); margin-bottom: 5px;">
                        <?php
                        try {
                            $stmt = $conn->query("SELECT COUNT(*) FROM embed_servers WHERE is_active = 1");
                            echo number_format($stmt->fetchColumn());
                        } catch (Exception $e) {
                            echo "0";
                        }
                        ?>
                    </div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Movie/TV Servers</div>
                </div>
                <div style="background: var(--dark-bg); padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.5rem; color: #ff6b35; margin-bottom: 5px;">
                        <?php
                        try {
                            // Count anime servers (assuming they have 'anime' in name or separate table)
                            $stmt = $conn->query("SELECT COUNT(*) FROM embed_servers WHERE name LIKE '%anime%' OR name LIKE '%Anime%'");
                            echo number_format($stmt->fetchColumn());
                        } catch (Exception $e) {
                            echo "0";
                        }
                        ?>
                    </div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Anime Servers</div>
                </div>
                <div style="background: var(--dark-bg); padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.5rem; color: #ff1744; margin-bottom: 5px;">
                        <?php
                        try {
                            // Count hentai servers (assuming they have 'hentai' in name or separate table)
                            $stmt = $conn->query("SELECT COUNT(*) FROM embed_servers WHERE name LIKE '%hentai%' OR name LIKE '%Hentai%'");
                            echo number_format($stmt->fetchColumn());
                        } catch (Exception $e) {
                            echo "0";
                        }
                        ?>
                    </div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Hentai Servers</div>
                </div>
            </div>
        </div>

        <!-- User Management -->
        <div class="import-section">
            <h2>👥 User Management</h2>
            <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-bottom: 20px;">
                <a href="users.php" class="btn btn-primary">👥 Basic User Management</a>
                <a href="user-management.php" class="btn btn-primary">🔧 Advanced User Management</a>
                <a href="analytics.php" class="btn btn-secondary">📈 User Analytics</a>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px;">
                <div style="background: var(--dark-bg); padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.5rem; color: var(--primary-color); margin-bottom: 5px;"><?php echo number_format($stats['users']); ?></div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Total Users</div>
                </div>
                <div style="background: var(--dark-bg); padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.5rem; color: #28a745; margin-bottom: 5px;">
                        <?php
                        try {
                            $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE is_active = 1");
                            echo number_format($stmt->fetchColumn());
                        } catch (Exception $e) {
                            echo "N/A";
                        }
                        ?>
                    </div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Active Users</div>
                </div>
                <div style="background: var(--dark-bg); padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.5rem; color: #ffc107; margin-bottom: 5px;">
                        <?php
                        try {
                            $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE is_premium = 1");
                            echo number_format($stmt->fetchColumn());
                        } catch (Exception $e) {
                            echo "0";
                        }
                        ?>
                    </div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Premium Users</div>
                </div>
                <div style="background: var(--dark-bg); padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.5rem; color: #17a2b8; margin-bottom: 5px;">
                        <?php
                        try {
                            $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'admin'");
                            echo number_format($stmt->fetchColumn());
                        } catch (Exception $e) {
                            echo "1";
                        }
                        ?>
                    </div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Admin Users</div>
                </div>
            </div>
        </div>

        <!-- Hentai Management -->
        <div class="import-section">
            <h2>🔞 Hentai Content Management</h2>
            <p>Manage and convert content between TV shows and hentai categories</p>
            <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-top: 20px;">
                <a href="hentai-management.php" class="btn btn-secondary" style="background: linear-gradient(135deg, #ff1744, #e91e63);">
                    🔞 Hentai Management Panel
                </a>
                <a href="../convert_hentai_content.php" target="_blank" class="btn btn-secondary">
                    🔍 Auto-Detect Hentai Content
                </a>
                <a href="../test_hentai_servers.php" target="_blank" class="btn btn-secondary">
                    🖥️ Test Hentai Servers
                </a>
                <a href="hentai-servers.php" class="btn btn-secondary">
                    ⚙️ Configure Hentai Servers
                </a>
            </div>

            <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-top: 20px; border-left: 4px solid #ffc107;">
                <h4 style="margin: 0 0 10px 0; color: #856404;">
                    <i class="fas fa-info-circle"></i> How to Move Content to Hentai Servers:
                </h4>
                <ol style="margin: 0; padding-left: 20px; color: #856404;">
                    <li><strong>Use Hentai Management Panel</strong> - Browse and convert content with visual interface</li>
                    <li><strong>Auto-Detect</strong> - Automatically find potential hentai content by keywords</li>
                    <li><strong>Test Servers</strong> - Verify hentai servers are working properly</li>
                    <li><strong>Configure Servers</strong> - Add or modify hentai server URLs</li>
                </ol>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="import-section">
            <h2>⚡ Quick Actions</h2>
            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                <button class="btn btn-secondary" onclick="importTrending('movie')">🔥 Trending Movies</button>
                <button class="btn btn-secondary" onclick="importTrending('tv')">🔥 Trending TV Shows</button>
                <button class="btn btn-secondary" onclick="importPopular('movie')">⭐ Popular Movies</button>
                <button class="btn btn-secondary" onclick="importPopular('tv')">⭐ Popular TV Shows</button>
                <button class="btn btn-secondary" onclick="importNowPlaying()">🎬 Now Playing</button>
                <button class="btn btn-secondary" onclick="importUpcoming()">🆕 Upcoming Movies</button>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="import-section">
            <h2>Recent Activity</h2>
            <div id="recentActivity">
                <p style="color: var(--text-secondary);">Loading recent activity...</p>
            </div>
        </div>
    </div>

    <script>
        async function importTrending(type) {
            if (!confirm(`Import trending ${type === 'movie' ? 'movies' : 'TV shows'}? This may take a while.`)) {
                return;
            }
            
            try {
                const response = await fetch('api/bulk-import.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'import_trending',
                        type: type
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert(`Successfully imported ${data.count} ${type === 'movie' ? 'movies' : 'TV shows'}`);
                    location.reload();
                } else {
                    alert('Import failed: ' + data.message);
                }
            } catch (error) {
                alert('Import failed: ' + error.message);
            }
        }
        
        async function importPopular(type) {
            if (!confirm(`Import popular ${type === 'movie' ? 'movies' : 'TV shows'}? This may take a while.`)) {
                return;
            }

            try {
                const response = await fetch('api/bulk-import.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'import_popular',
                        type: type
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert(`Successfully imported ${data.count} ${type === 'movie' ? 'movies' : 'TV shows'}`);
                    location.reload();
                } else {
                    alert('Import failed: ' + data.message);
                }
            } catch (error) {
                alert('Import failed: ' + error.message);
            }
        }

        async function importNowPlaying() {
            if (!confirm('Import now playing movies? This may take a while.')) {
                return;
            }

            try {
                const response = await fetch('api/bulk-import.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'import_now_playing',
                        type: 'movie'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert(`Successfully imported ${data.count} movies`);
                    location.reload();
                } else {
                    alert('Import failed: ' + data.message);
                }
            } catch (error) {
                alert('Import failed: ' + error.message);
            }
        }

        async function importUpcoming() {
            if (!confirm('Import upcoming movies? This may take a while.')) {
                return;
            }

            try {
                const response = await fetch('api/bulk-import.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'import_upcoming',
                        type: 'movie'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert(`Successfully imported ${data.count} movies`);
                    location.reload();
                } else {
                    alert('Import failed: ' + data.message);
                }
            } catch (error) {
                alert('Import failed: ' + error.message);
            }
        }

        // Load recent activity
        async function loadRecentActivity() {
            try {
                const response = await fetch('api/recent-activity.php');
                const data = await response.json();

                if (data.success) {
                    const activityDiv = document.getElementById('recentActivity');
                    if (data.activities.length > 0) {
                        activityDiv.innerHTML = data.activities.map(activity => `
                            <div style="padding: 10px; border-bottom: 1px solid var(--border-color); color: var(--text-secondary);">
                                <strong style="color: var(--text-primary);">${activity.type}</strong>: ${activity.title}
                                <small style="float: right;">${activity.date}</small>
                            </div>
                        `).join('');
                    } else {
                        activityDiv.innerHTML = '<p style="color: var(--text-secondary);">No recent activity</p>';
                    }
                }
            } catch (error) {
                console.error('Failed to load recent activity:', error);
            }
        }

        // Load recent activity on page load
        document.addEventListener('DOMContentLoaded', loadRecentActivity);
    </script>
</body>
</html>
