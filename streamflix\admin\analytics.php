<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

try {
    $db = new Database();
    $conn = $db->connect();
    
    // Get analytics data
    $analytics = [];
    
    // Content statistics - with error handling
    $analytics['overview'] = [
        'total_movies' => 0,
        'total_tv_shows' => 0,
        'total_users' => 0,
        'active_servers' => 0
    ];

    try {
        $stmt = $conn->query("SELECT COUNT(*) FROM movies");
        $analytics['overview']['total_movies'] = $stmt->fetchColumn();
    } catch (Exception $e) {
        // Table doesn't exist
    }

    try {
        $stmt = $conn->query("SELECT COUNT(*) FROM tv_shows");
        $analytics['overview']['total_tv_shows'] = $stmt->fetchColumn();
    } catch (Exception $e) {
        // Table doesn't exist
    }

    try {
        $stmt = $conn->query("SELECT COUNT(*) FROM users");
        $analytics['overview']['total_users'] = $stmt->fetchColumn();
    } catch (Exception $e) {
        // Table doesn't exist
    }

    try {
        $stmt = $conn->query("SELECT COUNT(*) FROM embed_servers WHERE is_active = 1");
        $analytics['overview']['active_servers'] = $stmt->fetchColumn();
    } catch (Exception $e) {
        try {
            $stmt = $conn->query("SELECT COUNT(*) FROM servers WHERE is_active = 1");
            $analytics['overview']['active_servers'] = $stmt->fetchColumn();
        } catch (Exception $e2) {
            // Neither table exists
        }
    }
    
    // Popular content - with error handling
    $analytics['popular_movies'] = [];
    $analytics['popular_tv_shows'] = [];

    try {
        $stmt = $conn->query("
            SELECT title, vote_average, popularity, release_date
            FROM movies
            ORDER BY vote_average DESC
            LIMIT 10
        ");
        $analytics['popular_movies'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // Movies table doesn't exist or has different structure
    }

    try {
        $stmt = $conn->query("
            SELECT name as title, vote_average, popularity, first_air_date as release_date
            FROM tv_shows
            ORDER BY vote_average DESC
            LIMIT 10
        ");
        $analytics['popular_tv_shows'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // TV shows table doesn't exist or has different structure
    }
    
    // Recent additions - with error handling
    $recent_movies = [];
    $recent_tv_shows = [];

    try {
        $stmt = $conn->query("
            SELECT title, created_at, 'movie' as type
            FROM movies
            ORDER BY created_at DESC
            LIMIT 10
        ");
        $recent_movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // Movies table doesn't exist
    }

    try {
        $stmt = $conn->query("
            SELECT name as title, created_at, 'tv_show' as type
            FROM tv_shows
            ORDER BY created_at DESC
            LIMIT 10
        ");
        $recent_tv_shows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // TV shows table doesn't exist
    }

    $analytics['recent_content'] = array_merge($recent_movies, $recent_tv_shows);
    if (!empty($analytics['recent_content'])) {
        usort($analytics['recent_content'], function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        $analytics['recent_content'] = array_slice($analytics['recent_content'], 0, 15);
    }
    
    // User statistics - with error handling
    $analytics['user_registrations'] = [];

    try {
        $stmt = $conn->query("
            SELECT
                DATE(created_at) as date,
                COUNT(*) as count
            FROM users
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY DATE(created_at)
            ORDER BY date DESC
        ");
        $analytics['user_registrations'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // Users table doesn't exist
    }
    
    // Content by genre - with error handling
    $analytics['content_by_genre'] = [];

    try {
        $stmt = $conn->query("
            SELECT
                g.name,
                COALESCE(movie_counts.movie_count, 0) as movie_count,
                COALESCE(tv_counts.tv_count, 0) as tv_count
            FROM genres g
            LEFT JOIN (
                SELECT genre_id, COUNT(*) as movie_count
                FROM movie_genres
                GROUP BY genre_id
            ) movie_counts ON g.id = movie_counts.genre_id
            LEFT JOIN (
                SELECT genre_id, COUNT(*) as tv_count
                FROM tv_show_genres
                GROUP BY genre_id
            ) tv_counts ON g.id = tv_counts.genre_id
            WHERE (COALESCE(movie_counts.movie_count, 0) > 0 OR COALESCE(tv_counts.tv_count, 0) > 0)
            ORDER BY (COALESCE(movie_counts.movie_count, 0) + COALESCE(tv_counts.tv_count, 0)) DESC
            LIMIT 10
        ");
        $analytics['content_by_genre'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // Genre tables don't exist, create some sample data
        $analytics['content_by_genre'] = [
            ['name' => 'Action', 'movie_count' => 0, 'tv_count' => 0],
            ['name' => 'Comedy', 'movie_count' => 0, 'tv_count' => 0],
            ['name' => 'Drama', 'movie_count' => 0, 'tv_count' => 0],
            ['name' => 'Horror', 'movie_count' => 0, 'tv_count' => 0],
            ['name' => 'Romance', 'movie_count' => 0, 'tv_count' => 0]
        ];
    }
    
} catch (Exception $e) {
    $analytics = [];
    $error = 'Failed to load analytics: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="assets/admin-style.css">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }

        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            color: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .admin-nav {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 30px;
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .admin-nav a {
            padding: 12px 20px;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .admin-nav a:hover,
        .admin-nav a.active {
            background: #e50914;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(229, 9, 20, 0.4);
        }

        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .analytics-card {
            background: var(--secondary-color);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.1);
        }

        .analytics-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--text-color);
            border-bottom: 3px solid var(--primary-color);
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .overview-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            color: white;
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: scale(1.05);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
        }

        .stat-label {
            color: rgba(255,255,255,0.9);
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .content-list {
            max-height: 350px;
            overflow-y: auto;
            padding-right: 5px;
        }

        .content-list::-webkit-scrollbar {
            width: 6px;
        }

        .content-list::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
            border-radius: 3px;
        }

        .content-list::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        .content-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            margin-bottom: 8px;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .content-item:hover {
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }

        .content-item:last-child {
            margin-bottom: 0;
        }

        .content-title {
            color: var(--text-color);
            font-weight: 500;
            flex: 1;
            font-size: 14px;
        }

        .content-meta {
            color: #888;
            font-size: 0.85rem;
            background: rgba(255,255,255,0.1);
            padding: 4px 8px;
            border-radius: 4px;
        }

        .genre-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            margin-bottom: 8px;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .genre-stats:hover {
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }

        .genre-name {
            color: var(--text-color);
            font-weight: 500;
        }

        .genre-count {
            color: #888;
            font-size: 0.9rem;
            background: rgba(255,255,255,0.1);
            padding: 4px 8px;
            border-radius: 4px;
        }

        .chart-container {
            height: 220px;
            display: flex;
            align-items: end;
            gap: 3px;
            padding: 15px 0;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            margin-top: 10px;
        }

        .chart-bar {
            background: linear-gradient(to top, var(--primary-color), #ff6b6b);
            border-radius: 4px 4px 0 0;
            min-height: 8px;
            flex: 1;
            position: relative;
            transition: all 0.3s ease;
        }

        .chart-bar:hover {
            background: linear-gradient(to top, #e50914, #ff4757);
            transform: scaleY(1.1);
        }

        .chart-bar:hover::after {
            content: attr(data-value);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 6px 10px;
            border-radius: 6px;
            font-size: 0.8rem;
            white-space: nowrap;
            z-index: 10;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .admin-container {
                margin-top: 80px;
                padding: 0 15px;
            }

            .analytics-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .overview-stats {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .admin-nav {
                justify-content: center;
            }

            .admin-nav a {
                padding: 10px 15px;
                font-size: 14px;
            }

            .analytics-card {
                padding: 20px;
            }

            .card-title {
                font-size: 1.1rem;
            }
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <span>Welcome, <?php echo $_SESSION['username']; ?></span>
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>📈 Analytics Dashboard</h1>
            <p>Monitor your platform's performance and content statistics</p>
        </div>

        <nav class="admin-nav">
            <a href="index.php">📊 Dashboard</a>
            <a href="movies.php">🎬 Movies</a>
            <a href="tv-shows.php">📺 TV Shows</a>
            <a href="users.php">👥 Users</a>
            <a href="servers.php">🖥️ Servers</a>
            <a href="analytics.php" class="active">📈 Analytics</a>
            <a href="import.php">📥 Import</a>
            <a href="app-management.php">📱 App Management</a>
            <a href="user-management.php">👤 User Management</a>
            <a href="system-logs.php">📋 System Logs</a>
            <a href="maintenance.php">🔧 Maintenance</a>
            <a href="database-updater.php">🗄️ DB Updater</a>
            <a href="quick-setup.php">🚀 Quick Setup</a>
            <a href="settings.php">⚙️ Settings</a>
        </nav>

        <?php if (isset($error)): ?>
            <div class="error-message"><?php echo $error; ?></div>
        <?php endif; ?>

        <div class="analytics-grid">
            <!-- Overview -->
            <div class="analytics-card">
                <h3 class="card-title">📊 Platform Overview</h3>
                <div class="overview-stats">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo number_format($analytics['overview']['total_movies'] ?? 0); ?></div>
                        <div class="stat-label">Movies</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><?php echo number_format($analytics['overview']['total_tv_shows'] ?? 0); ?></div>
                        <div class="stat-label">TV Shows</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><?php echo number_format($analytics['overview']['total_users'] ?? 0); ?></div>
                        <div class="stat-label">Users</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><?php echo number_format($analytics['overview']['active_servers'] ?? 0); ?></div>
                        <div class="stat-label">Active Servers</div>
                    </div>
                </div>
            </div>

            <!-- Popular Movies -->
            <div class="analytics-card">
                <h3 class="card-title">🎬 Popular Movies</h3>
                <div class="content-list">
                    <?php foreach ($analytics['popular_movies'] ?? [] as $movie): ?>
                        <div class="content-item">
                            <div class="content-title"><?php echo htmlspecialchars($movie['title']); ?></div>
                            <div class="content-meta">⭐ <?php echo number_format($movie['vote_average'], 1); ?></div>
                        </div>
                    <?php endforeach; ?>
                    <?php if (empty($analytics['popular_movies'])): ?>
                        <div class="content-item">
                            <div class="content-title" style="color: #888;">No movies found</div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Popular TV Shows -->
            <div class="analytics-card">
                <h3 class="card-title">📺 Popular TV Shows</h3>
                <div class="content-list">
                    <?php foreach ($analytics['popular_tv_shows'] ?? [] as $show): ?>
                        <div class="content-item">
                            <div class="content-title"><?php echo htmlspecialchars($show['title']); ?></div>
                            <div class="content-meta">⭐ <?php echo number_format($show['vote_average'], 1); ?></div>
                        </div>
                    <?php endforeach; ?>
                    <?php if (empty($analytics['popular_tv_shows'])): ?>
                        <div class="content-item">
                            <div class="content-title" style="color: #888;">No TV shows found</div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Content -->
            <div class="analytics-card">
                <h3 class="card-title">🆕 Recent Additions</h3>
                <div class="content-list">
                    <?php foreach ($analytics['recent_content'] ?? [] as $content): ?>
                        <div class="content-item">
                            <div class="content-title"><?php echo htmlspecialchars($content['title']); ?></div>
                            <div class="content-meta">
                                <?php echo $content['type'] == 'movie' ? '🎬' : '📺'; ?>
                                <?php echo ucfirst(str_replace('_', ' ', $content['type'])); ?> •
                                <?php echo date('M j', strtotime($content['created_at'])); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    <?php if (empty($analytics['recent_content'])): ?>
                        <div class="content-item">
                            <div class="content-title" style="color: #888;">No recent content</div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Content by Genre -->
            <div class="analytics-card">
                <h3 class="card-title">🎭 Content by Genre</h3>
                <div class="content-list">
                    <?php foreach ($analytics['content_by_genre'] ?? [] as $genre): ?>
                        <div class="genre-stats">
                            <div class="genre-name"><?php echo htmlspecialchars($genre['name']); ?></div>
                            <div class="genre-count">
                                <?php echo ($genre['movie_count'] + $genre['tv_count']); ?> items
                            </div>
                        </div>
                    <?php endforeach; ?>
                    <?php if (empty($analytics['content_by_genre'])): ?>
                        <div class="genre-stats">
                            <div class="genre-name" style="color: #888;">No genres found</div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- User Registrations Chart -->
            <div class="analytics-card">
                <h3 class="card-title">👥 User Registrations (Last 30 Days)</h3>
                <div class="chart-container">
                    <?php 
                    $max_registrations = 0;
                    foreach ($analytics['user_registrations'] ?? [] as $day) {
                        $max_registrations = max($max_registrations, $day['count']);
                    }
                    
                    foreach (array_reverse($analytics['user_registrations'] ?? []) as $day): 
                        $height = $max_registrations > 0 ? ($day['count'] / $max_registrations) * 100 : 0;
                    ?>
                        <div class="chart-bar" 
                             style="height: <?php echo $height; ?>%"
                             data-value="<?php echo $day['count']; ?> users on <?php echo date('M j', strtotime($day['date'])); ?>">
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
