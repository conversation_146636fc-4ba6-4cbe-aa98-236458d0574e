<?php
/**
 * StreamFlix API Documentation
 * Main API endpoint with documentation
 */

require_once 'config.php';

// If this is an API request, show documentation
if ($_SERVER['REQUEST_METHOD'] === 'GET' && !isset($_GET['action'])) {
    showAPIDocumentation();
} else {
    APIResponse::notFound('API endpoint not found. Visit /api/ for documentation.');
}

function showAPIDocumentation() {
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>StreamFlix API Documentation</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                background: #f8f9fa;
            }
            
            .container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }
            
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 40px 0;
                text-align: center;
                margin-bottom: 40px;
                border-radius: 10px;
            }
            
            .header h1 {
                font-size: 2.5rem;
                margin-bottom: 10px;
            }
            
            .header p {
                font-size: 1.1rem;
                opacity: 0.9;
            }
            
            .section {
                background: white;
                padding: 30px;
                margin-bottom: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            
            .section h2 {
                color: #667eea;
                margin-bottom: 20px;
                font-size: 1.8rem;
            }
            
            .section h3 {
                color: #555;
                margin: 20px 0 10px 0;
                font-size: 1.3rem;
            }
            
            .endpoint {
                background: #f8f9fa;
                padding: 15px;
                margin: 10px 0;
                border-radius: 5px;
                border-left: 4px solid #667eea;
            }
            
            .method {
                display: inline-block;
                padding: 4px 8px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 0.8rem;
                margin-right: 10px;
            }
            
            .method.get { background: #28a745; color: white; }
            .method.post { background: #007bff; color: white; }
            .method.put { background: #ffc107; color: black; }
            .method.delete { background: #dc3545; color: white; }
            
            .code {
                background: #2d3748;
                color: #e2e8f0;
                padding: 15px;
                border-radius: 5px;
                font-family: 'Courier New', monospace;
                font-size: 0.9rem;
                overflow-x: auto;
                margin: 10px 0;
            }
            
            .auth-required {
                color: #dc3545;
                font-weight: bold;
                font-size: 0.9rem;
            }
            
            .grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-top: 20px;
            }
            
            .card {
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                border-left: 4px solid #667eea;
            }
            
            .card h4 {
                color: #667eea;
                margin-bottom: 10px;
            }
            
            ul {
                margin-left: 20px;
                margin-top: 10px;
            }
            
            li {
                margin-bottom: 5px;
            }
            
            .base-url {
                background: #e3f2fd;
                padding: 10px;
                border-radius: 5px;
                font-family: monospace;
                font-weight: bold;
                text-align: center;
                margin: 20px 0;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>📱 StreamFlix API</h1>
                <p>RESTful API for StreamFlix Mobile Application</p>
                <p>Version: <?php echo API_VERSION; ?></p>
            </div>

            <div class="section">
                <h2>🚀 Getting Started</h2>
                <div class="base-url">
                    Base URL: <?php echo (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']); ?>
                </div>
                
                <h3>Authentication</h3>
                <p>Most endpoints require authentication using JWT tokens. Include the token in the Authorization header:</p>
                <div class="code">Authorization: Bearer YOUR_JWT_TOKEN</div>
                
                <h3>Response Format</h3>
                <p>All responses are in JSON format with the following structure:</p>
                <div class="code">{
    "success": true|false,
    "message": "Response message",
    "data": {...},
    "timestamp": 1234567890,
    "version": "1.0"
}</div>
            </div>

            <div class="section">
                <h2>🔐 Authentication Endpoints</h2>
                
                <div class="endpoint">
                    <span class="method post">POST</span>
                    <strong>/api/auth.php?action=login</strong>
                    <p>User login with username/email and password</p>
                    <div class="code">POST /api/auth.php?action=login
{
    "username": "<EMAIL>",
    "password": "password123"
}</div>
                </div>

                <div class="endpoint">
                    <span class="method post">POST</span>
                    <strong>/api/auth.php?action=register</strong>
                    <p>User registration</p>
                    <div class="code">POST /api/auth.php?action=register
{
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "password123"
}</div>
                </div>

                <div class="endpoint">
                    <span class="method post">POST</span>
                    <strong>/api/auth.php?action=refresh</strong>
                    <span class="auth-required">🔒 Auth Required</span>
                    <p>Refresh JWT token</p>
                </div>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <strong>/api/auth.php?action=profile</strong>
                    <span class="auth-required">🔒 Auth Required</span>
                    <p>Get user profile information</p>
                </div>
            </div>

            <div class="section">
                <h2>🎬 Movies Endpoints</h2>
                
                <div class="grid">
                    <div class="card">
                        <h4>Movie Lists</h4>
                        <ul>
                            <li><span class="method get">GET</span> /api/movies.php?action=list</li>
                            <li><span class="method get">GET</span> /api/movies.php?action=featured</li>
                            <li><span class="method get">GET</span> /api/movies.php?action=trending</li>
                            <li><span class="method get">GET</span> /api/movies.php?action=popular</li>
                            <li><span class="method get">GET</span> /api/movies.php?action=latest</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <h4>Movie Details</h4>
                        <ul>
                            <li><span class="method get">GET</span> /api/movies.php?action=details&id=123</li>
                            <li><span class="method get">GET</span> /api/movies.php?action=servers&id=123</li>
                            <li><span class="method get">GET</span> /api/movies.php?action=search&q=query</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <h4>Movie Genres</h4>
                        <ul>
                            <li><span class="method get">GET</span> /api/movies.php?action=genres</li>
                            <li><span class="method get">GET</span> /api/movies.php?action=by-genre&genre_id=1</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>📺 TV Shows Endpoints</h2>
                
                <div class="grid">
                    <div class="card">
                        <h4>TV Show Lists</h4>
                        <ul>
                            <li><span class="method get">GET</span> /api/tv-shows.php?action=list</li>
                            <li><span class="method get">GET</span> /api/tv-shows.php?action=featured</li>
                            <li><span class="method get">GET</span> /api/tv-shows.php?action=trending</li>
                            <li><span class="method get">GET</span> /api/tv-shows.php?action=popular</li>
                            <li><span class="method get">GET</span> /api/tv-shows.php?action=latest</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <h4>TV Show Details</h4>
                        <ul>
                            <li><span class="method get">GET</span> /api/tv-shows.php?action=details&id=123</li>
                            <li><span class="method get">GET</span> /api/tv-shows.php?action=seasons&id=123</li>
                            <li><span class="method get">GET</span> /api/tv-shows.php?action=episodes&season_id=456</li>
                            <li><span class="method get">GET</span> /api/tv-shows.php?action=search&q=query</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <h4>TV Show Genres</h4>
                        <ul>
                            <li><span class="method get">GET</span> /api/tv-shows.php?action=genres</li>
                            <li><span class="method get">GET</span> /api/tv-shows.php?action=by-genre&genre_id=1</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>👤 User Endpoints</h2>
                <p class="auth-required">🔒 All user endpoints require authentication</p>
                
                <div class="grid">
                    <div class="card">
                        <h4>Watchlist</h4>
                        <ul>
                            <li><span class="method get">GET</span> /api/user.php?action=watchlist</li>
                            <li><span class="method post">POST</span> /api/user.php?action=add-to-watchlist</li>
                            <li><span class="method delete">DELETE</span> /api/user.php?action=remove-from-watchlist</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <h4>Favorites</h4>
                        <ul>
                            <li><span class="method get">GET</span> /api/user.php?action=favorites</li>
                            <li><span class="method post">POST</span> /api/user.php?action=add-to-favorites</li>
                            <li><span class="method delete">DELETE</span> /api/user.php?action=remove-from-favorites</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <h4>Watch History</h4>
                        <ul>
                            <li><span class="method get">GET</span> /api/user.php?action=watch-history</li>
                            <li><span class="method post">POST</span> /api/user.php?action=add-to-history</li>
                            <li><span class="method get">GET</span> /api/user.php?action=continue-watching</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>📱 App Endpoints</h2>
                
                <div class="endpoint">
                    <span class="method get">GET</span>
                    <strong>/api/app.php?action=version</strong>
                    <p>Check app version and update requirements</p>
                </div>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <strong>/api/app.php?action=config</strong>
                    <p>Get app configuration and features</p>
                </div>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <strong>/api/app.php?action=search&q=query</strong>
                    <p>Global search across movies and TV shows</p>
                </div>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <strong>/api/app.php?action=home</strong>
                    <p>Get home page data (featured, trending, latest content)</p>
                </div>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <strong>/api/app.php?action=genres</strong>
                    <p>Get all available genres with content counts</p>
                </div>
            </div>

            <div class="section">
                <h2>📋 Query Parameters</h2>
                
                <div class="grid">
                    <div class="card">
                        <h4>Pagination</h4>
                        <ul>
                            <li><strong>page</strong> - Page number (default: 1)</li>
                            <li><strong>limit</strong> - Items per page (default: 20, max: 50)</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <h4>Search</h4>
                        <ul>
                            <li><strong>q</strong> - Search query</li>
                            <li><strong>genre_id</strong> - Filter by genre ID</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <h4>Content</h4>
                        <ul>
                            <li><strong>id</strong> - Content ID</li>
                            <li><strong>season_id</strong> - Season ID for episodes</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>⚠️ Error Codes</h2>
                
                <div class="grid">
                    <div class="card">
                        <h4>HTTP Status Codes</h4>
                        <ul>
                            <li><strong>200</strong> - Success</li>
                            <li><strong>201</strong> - Created</li>
                            <li><strong>400</strong> - Bad Request</li>
                            <li><strong>401</strong> - Unauthorized</li>
                            <li><strong>403</strong> - Forbidden</li>
                            <li><strong>404</strong> - Not Found</li>
                            <li><strong>405</strong> - Method Not Allowed</li>
                            <li><strong>500</strong> - Internal Server Error</li>
                            <li><strong>503</strong> - Service Unavailable (Maintenance)</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>🔧 Testing</h2>
                <p>You can test the API endpoints using tools like:</p>
                <ul>
                    <li><strong>Postman</strong> - Popular API testing tool</li>
                    <li><strong>curl</strong> - Command line tool</li>
                    <li><strong>Browser</strong> - For GET requests</li>
                </ul>
                
                <h3>Example curl request:</h3>
                <div class="code">curl -X GET "<?php echo (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']); ?>/movies.php?action=featured" \
     -H "Content-Type: application/json"</div>
            </div>
        </div>
    </body>
    </html>
    <?php
}
?>
