-- StreamFlix Complete Database Schema
-- Database: tipsbdxy_bdflix2025
-- Generated for Live Site Deployment

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `users`
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('user','admin') DEFAULT 'user',
  `is_active` tinyint(1) DEFAULT 1,
  `is_premium` tinyint(1) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  <PERSON><PERSON>ARY KEY (`id`),
  <PERSON>I<PERSON><PERSON> KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `genres`
CREATE TABLE `genres` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `tmdb_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `tmdb_id` (`tmdb_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `movies`
CREATE TABLE `movies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tmdb_id` int(11) NOT NULL,
  `content_type` enum('movie','anime','hentai') DEFAULT 'movie',
  `title` varchar(255) NOT NULL,
  `original_title` varchar(255) DEFAULT NULL,
  `overview` text,
  `release_date` date DEFAULT NULL,
  `runtime` int(11) DEFAULT NULL,
  `vote_average` decimal(3,1) DEFAULT 0.0,
  `vote_count` int(11) DEFAULT 0,
  `popularity` decimal(8,3) DEFAULT 0.000,
  `poster_path` varchar(255) DEFAULT NULL,
  `backdrop_path` varchar(255) DEFAULT NULL,
  `adult` tinyint(1) DEFAULT 0,
  `video` tinyint(1) DEFAULT 0,
  `original_language` varchar(10) DEFAULT NULL,
  `budget` bigint(20) DEFAULT 0,
  `revenue` bigint(20) DEFAULT 0,
  `homepage` varchar(500) DEFAULT NULL,
  `imdb_id` varchar(20) DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `tagline` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tmdb_id` (`tmdb_id`),
  KEY `content_type` (`content_type`),
  KEY `release_date` (`release_date`),
  KEY `vote_average` (`vote_average`),
  KEY `popularity` (`popularity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `tv_shows`
CREATE TABLE `tv_shows` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tmdb_id` int(11) NOT NULL,
  `content_type` enum('tv_show','anime','hentai') DEFAULT 'tv_show',
  `title` varchar(255) NOT NULL,
  `original_name` varchar(255) DEFAULT NULL,
  `overview` text,
  `first_air_date` date DEFAULT NULL,
  `last_air_date` date DEFAULT NULL,
  `vote_average` decimal(3,1) DEFAULT 0.0,
  `vote_count` int(11) DEFAULT 0,
  `popularity` decimal(8,3) DEFAULT 0.000,
  `poster_path` varchar(255) DEFAULT NULL,
  `backdrop_path` varchar(255) DEFAULT NULL,
  `adult` tinyint(1) DEFAULT 0,
  `original_language` varchar(10) DEFAULT NULL,
  `homepage` varchar(500) DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `tagline` text,
  `type` varchar(50) DEFAULT NULL,
  `number_of_episodes` int(11) DEFAULT 0,
  `number_of_seasons` int(11) DEFAULT 0,
  `episode_run_time` text,
  `in_production` tinyint(1) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tmdb_id` (`tmdb_id`),
  KEY `content_type` (`content_type`),
  KEY `first_air_date` (`first_air_date`),
  KEY `vote_average` (`vote_average`),
  KEY `popularity` (`popularity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `movie_genres`
CREATE TABLE `movie_genres` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `movie_id` int(11) NOT NULL,
  `genre_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `movie_genre_unique` (`movie_id`, `genre_id`),
  KEY `movie_id` (`movie_id`),
  KEY `genre_id` (`genre_id`),
  CONSTRAINT `movie_genres_movie_fk` FOREIGN KEY (`movie_id`) REFERENCES `movies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `movie_genres_genre_fk` FOREIGN KEY (`genre_id`) REFERENCES `genres` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `tv_show_genres`
CREATE TABLE `tv_show_genres` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tv_show_id` int(11) NOT NULL,
  `genre_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tv_show_genre_unique` (`tv_show_id`, `genre_id`),
  KEY `tv_show_id` (`tv_show_id`),
  KEY `genre_id` (`genre_id`),
  CONSTRAINT `tv_show_genres_tv_show_fk` FOREIGN KEY (`tv_show_id`) REFERENCES `tv_shows` (`id`) ON DELETE CASCADE,
  CONSTRAINT `tv_show_genres_genre_fk` FOREIGN KEY (`genre_id`) REFERENCES `genres` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `seasons`
CREATE TABLE `seasons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tv_show_id` int(11) NOT NULL,
  `season_number` int(11) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `overview` text,
  `air_date` date DEFAULT NULL,
  `episode_count` int(11) DEFAULT 0,
  `poster_path` varchar(255) DEFAULT NULL,
  `tmdb_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tv_show_season_unique` (`tv_show_id`, `season_number`),
  KEY `tv_show_id` (`tv_show_id`),
  KEY `season_number` (`season_number`),
  CONSTRAINT `seasons_tv_show_fk` FOREIGN KEY (`tv_show_id`) REFERENCES `tv_shows` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `episodes`
CREATE TABLE `episodes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tv_show_id` int(11) NOT NULL,
  `season_number` int(11) NOT NULL,
  `episode_number` int(11) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `overview` text,
  `air_date` date DEFAULT NULL,
  `runtime` int(11) DEFAULT NULL,
  `vote_average` decimal(3,1) DEFAULT 0.0,
  `vote_count` int(11) DEFAULT 0,
  `still_path` varchar(255) DEFAULT NULL,
  `tmdb_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tv_show_season_episode_unique` (`tv_show_id`, `season_number`, `episode_number`),
  KEY `tv_show_id` (`tv_show_id`),
  KEY `season_number` (`season_number`),
  KEY `episode_number` (`episode_number`),
  CONSTRAINT `episodes_tv_show_fk` FOREIGN KEY (`tv_show_id`) REFERENCES `tv_shows` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `watchlist`
CREATE TABLE `watchlist` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `content_type` enum('movie','tv_show') NOT NULL,
  `content_id` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_content_unique` (`user_id`, `content_type`, `content_id`),
  KEY `user_id` (`user_id`),
  KEY `content_type` (`content_type`),
  KEY `content_id` (`content_id`),
  CONSTRAINT `watchlist_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `watch_history`
CREATE TABLE `watch_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `content_type` enum('movie','tv_show') NOT NULL,
  `content_id` int(11) NOT NULL,
  `season_number` int(11) DEFAULT NULL,
  `episode_number` int(11) DEFAULT NULL,
  `watch_time` int(11) DEFAULT 0,
  `duration` int(11) DEFAULT 0,
  `completed` tinyint(1) DEFAULT 0,
  `last_watched` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `content_type` (`content_type`),
  KEY `content_id` (`content_id`),
  KEY `last_watched` (`last_watched`),
  CONSTRAINT `watch_history_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `embed_servers`
CREATE TABLE `embed_servers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `url_pattern` varchar(500) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `priority` int(11) DEFAULT 0,
  `server_type` enum('movie','tv','both') DEFAULT 'both',
  `content_types` set('movie','tv_show','anime','hentai') DEFAULT 'movie,tv_show',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `is_active` (`is_active`),
  KEY `priority` (`priority`),
  KEY `server_type` (`server_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `anime_servers`
CREATE TABLE `anime_servers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `url_pattern` varchar(500) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `priority` int(11) DEFAULT 0,
  `server_type` enum('anime','hentai','both') DEFAULT 'anime',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `is_active` (`is_active`),
  KEY `priority` (`priority`),
  KEY `server_type` (`server_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `hentai_servers`
CREATE TABLE `hentai_servers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `url_pattern` varchar(500) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `priority` int(11) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `is_active` (`is_active`),
  KEY `priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `site_settings`
CREATE TABLE `site_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `setting_type` enum('text','number','boolean','json') DEFAULT 'text',
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Insert default admin user
INSERT INTO `users` (`id`, `username`, `email`, `password`, `role`, `is_active`, `is_premium`) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1, 1);

-- --------------------------------------------------------

-- Insert default genres
INSERT INTO `genres` (`id`, `name`, `tmdb_id`) VALUES
(1, 'Action', 28),
(2, 'Adventure', 12),
(3, 'Animation', 16),
(4, 'Comedy', 35),
(5, 'Crime', 80),
(6, 'Documentary', 99),
(7, 'Drama', 18),
(8, 'Family', 10751),
(9, 'Fantasy', 14),
(10, 'History', 36),
(11, 'Horror', 27),
(12, 'Music', 10402),
(13, 'Mystery', 9648),
(14, 'Romance', 10749),
(15, 'Science Fiction', 878),
(16, 'TV Movie', 10770),
(17, 'Thriller', 53),
(18, 'War', 10752),
(19, 'Western', 37),
(20, 'Anime', 16),
(21, 'Hentai', 99999);

-- --------------------------------------------------------

-- Insert default embed servers
INSERT INTO `embed_servers` (`id`, `name`, `url_pattern`, `is_active`, `priority`, `server_type`, `content_types`) VALUES
(1, 'VidSrc', 'https://vidsrc.to/embed/{type}/{tmdb_id}', 1, 1, 'both', 'movie,tv_show'),
(2, 'VidSrc Pro', 'https://vidsrc.pro/embed/{type}/{tmdb_id}', 1, 2, 'both', 'movie,tv_show'),
(3, 'SuperEmbed', 'https://multiembed.mov/?video_id={tmdb_id}&tmdb=1', 1, 3, 'movie', 'movie'),
(4, 'SuperEmbed TV', 'https://multiembed.mov/?video_id={tmdb_id}&tmdb=1&s={season}&e={episode}', 1, 4, 'tv', 'tv_show'),
(5, 'EmbedSu', 'https://embed.su/embed/{type}/{tmdb_id}', 1, 5, 'both', 'movie,tv_show'),
(6, 'SmashyStream', 'https://player.smashy.stream/{type}/{tmdb_id}', 1, 6, 'both', 'movie,tv_show'),
(7, 'VidLink', 'https://vidlink.pro/{type}/{tmdb_id}', 1, 7, 'both', 'movie,tv_show'),
(8, 'MovieAPI', 'https://moviesapi.club/{type}/{tmdb_id}', 1, 8, 'both', 'movie,tv_show');

-- --------------------------------------------------------

-- Insert anime servers
INSERT INTO `anime_servers` (`id`, `name`, `url_pattern`, `is_active`, `priority`, `server_type`) VALUES
(1, 'LetsEmbed Anime', 'https://letsembed.cc/embed/anime/?id={tmdb_id}/{season_number}/{episode_number}', 1, 1, 'anime');

-- --------------------------------------------------------

-- Insert hentai servers
INSERT INTO `hentai_servers` (`id`, `name`, `url_pattern`, `is_active`, `priority`) VALUES
(1, 'LetsEmbed Hentai', 'https://letsembed.cc/embed/hentai/?id={tmdb_id}/{season_number}/{episode_number}', 1, 1);

-- --------------------------------------------------------

-- Insert site settings
INSERT INTO `site_settings` (`setting_key`, `setting_value`, `setting_type`, `description`) VALUES
('site_name', 'StreamFlix', 'text', 'Website name'),
('site_description', 'Watch Movies and TV Shows Online', 'text', 'Website description'),
('tmdb_api_key', '', 'text', 'TMDB API Key for fetching movie/TV data'),
('enable_registration', '1', 'boolean', 'Allow new user registration'),
('enable_adult_content', '1', 'boolean', 'Show adult/18+ content'),
('default_language', 'en', 'text', 'Default language for content'),
('items_per_page', '20', 'number', 'Number of items to show per page'),
('enable_anime', '1', 'boolean', 'Enable anime content'),
('enable_hentai', '1', 'boolean', 'Enable hentai content'),
('maintenance_mode', '0', 'boolean', 'Enable maintenance mode'),
('site_logo', '', 'text', 'Site logo URL'),
('site_favicon', '', 'text', 'Site favicon URL'),
('contact_email', '<EMAIL>', 'text', 'Contact email address'),
('social_facebook', '', 'text', 'Facebook page URL'),
('social_twitter', '', 'text', 'Twitter profile URL'),
('social_instagram', '', 'text', 'Instagram profile URL'),
('google_analytics', '', 'text', 'Google Analytics tracking ID'),
('enable_ads', '0', 'boolean', 'Enable advertisements'),
('ad_code_header', '', 'text', 'Advertisement code for header'),
('ad_code_footer', '', 'text', 'Advertisement code for footer'),
('max_upload_size', '10', 'number', 'Maximum file upload size in MB'),
('session_timeout', '3600', 'number', 'User session timeout in seconds'),
('enable_cache', '1', 'boolean', 'Enable content caching'),
('cache_duration', '3600', 'number', 'Cache duration in seconds'),
('enable_search', '1', 'boolean', 'Enable search functionality'),
('search_results_limit', '50', 'number', 'Maximum search results to show'),
('enable_watchlist', '1', 'boolean', 'Enable user watchlist feature'),
('enable_watch_history', '1', 'boolean', 'Enable watch history tracking'),
('player_autoplay', '1', 'boolean', 'Enable video autoplay'),
('player_volume', '50', 'number', 'Default player volume (0-100)'),
('enable_subtitles', '1', 'boolean', 'Enable subtitle support'),
('subtitle_languages', 'en,es,fr,de,it', 'text', 'Supported subtitle languages'),
('content_rating_system', 'tmdb', 'text', 'Content rating system (tmdb/imdb)'),
('enable_comments', '0', 'boolean', 'Enable user comments'),
('enable_ratings', '1', 'boolean', 'Enable user ratings'),
('backup_frequency', 'daily', 'text', 'Database backup frequency'),
('log_retention_days', '30', 'number', 'Number of days to keep logs'),
('enable_ssl', '1', 'boolean', 'Force SSL/HTTPS'),
('enable_mobile_app', '1', 'boolean', 'Enable mobile app features'),
('api_rate_limit', '100', 'number', 'API requests per minute limit'),
('enable_notifications', '1', 'boolean', 'Enable user notifications'),
('smtp_host', '', 'text', 'SMTP server host'),
('smtp_port', '587', 'number', 'SMTP server port'),
('smtp_username', '', 'text', 'SMTP username'),
('smtp_password', '', 'text', 'SMTP password'),
('smtp_encryption', 'tls', 'text', 'SMTP encryption (tls/ssl)'),
('email_from_address', '<EMAIL>', 'text', 'Email from address'),
('email_from_name', 'StreamFlix', 'text', 'Email from name');

-- --------------------------------------------------------

-- Set AUTO_INCREMENT values
ALTER TABLE `users` AUTO_INCREMENT = 2;
ALTER TABLE `genres` AUTO_INCREMENT = 22;
ALTER TABLE `movies` AUTO_INCREMENT = 1;
ALTER TABLE `tv_shows` AUTO_INCREMENT = 1;
ALTER TABLE `movie_genres` AUTO_INCREMENT = 1;
ALTER TABLE `tv_show_genres` AUTO_INCREMENT = 1;
ALTER TABLE `seasons` AUTO_INCREMENT = 1;
ALTER TABLE `episodes` AUTO_INCREMENT = 1;
ALTER TABLE `watchlist` AUTO_INCREMENT = 1;
ALTER TABLE `watch_history` AUTO_INCREMENT = 1;
ALTER TABLE `embed_servers` AUTO_INCREMENT = 9;
ALTER TABLE `anime_servers` AUTO_INCREMENT = 2;
ALTER TABLE `hentai_servers` AUTO_INCREMENT = 2;
ALTER TABLE `site_settings` AUTO_INCREMENT = 1;

COMMIT;
