package com.streamflix.app.data.model

import com.google.gson.annotations.SerializedName

/**
 * Generic API Response wrapper
 */
data class ApiResponse<T>(
    @SerializedName("success")
    val success: <PERSON><PERSON><PERSON>,
    
    @SerializedName("message")
    val message: String,
    
    @SerializedName("data")
    val data: T? = null,
    
    @SerializedName("errors")
    val errors: Any? = null,
    
    @SerializedName("timestamp")
    val timestamp: Long,
    
    @SerializedName("version")
    val version: String
)

/**
 * Pagination wrapper
 */
data class PaginatedResponse<T>(
    @SerializedName("current_page")
    val currentPage: Int,
    
    @SerializedName("per_page")
    val perPage: Int,
    
    @SerializedName("total")
    val total: Int,
    
    @SerializedName("total_pages")
    val totalPages: Int,
    
    @SerializedName("has_next")
    val hasNext: <PERSON><PERSON><PERSON>,
    
    @SerializedName("has_prev")
    val hasPrev: <PERSON><PERSON><PERSON>,
    
    val data: List<T>
)

/**
 * Movie model
 */
data class Movie(
    @SerializedName("id")
    val id: Int,
    
    @SerializedName("title")
    val title: String,
    
    @SerializedName("overview")
    val overview: String?,
    
    @SerializedName("poster_path")
    val posterPath: String?,
    
    @SerializedName("backdrop_path")
    val backdropPath: String?,
    
    @SerializedName("release_date")
    val releaseDate: String?,
    
    @SerializedName("vote_average")
    val voteAverage: Double,
    
    @SerializedName("vote_count")
    val voteCount: Int,
    
    @SerializedName("popularity")
    val popularity: Double,
    
    @SerializedName("runtime")
    val runtime: Int,
    
    @SerializedName("is_featured")
    val isFeatured: Boolean,
    
    @SerializedName("is_trending")
    val isTrending: Boolean,
    
    @SerializedName("created_at")
    val createdAt: String,
    
    @SerializedName("genres")
    val genres: List<Genre>? = null,
    
    @SerializedName("cast")
    val cast: List<Cast>? = null,
    
    @SerializedName("in_watchlist")
    val inWatchlist: Boolean = false
) {
    fun getFullPosterUrl(): String? {
        return posterPath?.let { "https://image.tmdb.org/t/p/w500$it" }
    }
    
    fun getFullBackdropUrl(): String? {
        return backdropPath?.let { "https://image.tmdb.org/t/p/w1280$it" }
    }
    
    fun getRatingPercentage(): Int {
        return (voteAverage * 10).toInt()
    }
    
    fun getFormattedRuntime(): String {
        val hours = runtime / 60
        val minutes = runtime % 60
        return if (hours > 0) "${hours}h ${minutes}m" else "${minutes}m"
    }
}

/**
 * TV Show model
 */
data class TvShow(
    @SerializedName("id")
    val id: Int,
    
    @SerializedName("name")
    val name: String,
    
    @SerializedName("overview")
    val overview: String?,
    
    @SerializedName("poster_path")
    val posterPath: String?,
    
    @SerializedName("backdrop_path")
    val backdropPath: String?,
    
    @SerializedName("first_air_date")
    val firstAirDate: String?,
    
    @SerializedName("last_air_date")
    val lastAirDate: String?,
    
    @SerializedName("vote_average")
    val voteAverage: Double,
    
    @SerializedName("vote_count")
    val voteCount: Int,
    
    @SerializedName("popularity")
    val popularity: Double,
    
    @SerializedName("number_of_seasons")
    val numberOfSeasons: Int,
    
    @SerializedName("number_of_episodes")
    val numberOfEpisodes: Int,
    
    @SerializedName("is_featured")
    val isFeatured: Boolean,
    
    @SerializedName("is_trending")
    val isTrending: Boolean,
    
    @SerializedName("created_at")
    val createdAt: String,
    
    @SerializedName("genres")
    val genres: List<Genre>? = null,
    
    @SerializedName("seasons")
    val seasons: List<Season>? = null,
    
    @SerializedName("in_watchlist")
    val inWatchlist: Boolean = false
) {
    fun getFullPosterUrl(): String? {
        return posterPath?.let { "https://image.tmdb.org/t/p/w500$it" }
    }
    
    fun getFullBackdropUrl(): String? {
        return backdropPath?.let { "https://image.tmdb.org/t/p/w1280$it" }
    }
    
    fun getRatingPercentage(): Int {
        return (voteAverage * 10).toInt()
    }
}

/**
 * Genre model
 */
data class Genre(
    @SerializedName("id")
    val id: Int,
    
    @SerializedName("name")
    val name: String,
    
    @SerializedName("movie_count")
    val movieCount: Int? = null,
    
    @SerializedName("show_count")
    val showCount: Int? = null,
    
    @SerializedName("total_count")
    val totalCount: Int? = null
)

/**
 * Cast model
 */
data class Cast(
    @SerializedName("id")
    val id: Int,
    
    @SerializedName("name")
    val name: String,
    
    @SerializedName("character")
    val character: String?,
    
    @SerializedName("profile_path")
    val profilePath: String?,
    
    @SerializedName("cast_order")
    val castOrder: Int
) {
    fun getFullProfileUrl(): String? {
        return profilePath?.let { "https://image.tmdb.org/t/p/w185$it" }
    }
}

/**
 * Season model
 */
data class Season(
    @SerializedName("id")
    val id: Int,
    
    @SerializedName("season_number")
    val seasonNumber: Int,
    
    @SerializedName("name")
    val name: String,
    
    @SerializedName("overview")
    val overview: String?,
    
    @SerializedName("poster_path")
    val posterPath: String?,
    
    @SerializedName("air_date")
    val airDate: String?,
    
    @SerializedName("episode_count")
    val episodeCount: Int
) {
    fun getFullPosterUrl(): String? {
        return posterPath?.let { "https://image.tmdb.org/t/p/w300$it" }
    }
}

/**
 * Episode model
 */
data class Episode(
    @SerializedName("id")
    val id: Int,
    
    @SerializedName("episode_number")
    val episodeNumber: Int,
    
    @SerializedName("name")
    val name: String,
    
    @SerializedName("overview")
    val overview: String?,
    
    @SerializedName("still_path")
    val stillPath: String?,
    
    @SerializedName("air_date")
    val airDate: String?,
    
    @SerializedName("vote_average")
    val voteAverage: Double,
    
    @SerializedName("runtime")
    val runtime: Int,
    
    @SerializedName("season_number")
    val seasonNumber: Int,
    
    @SerializedName("tv_show_id")
    val tvShowId: Int
) {
    fun getFullStillUrl(): String? {
        return stillPath?.let { "https://image.tmdb.org/t/p/w300$it" }
    }
    
    fun getFormattedRuntime(): String {
        val hours = runtime / 60
        val minutes = runtime % 60
        return if (hours > 0) "${hours}h ${minutes}m" else "${minutes}m"
    }
}

/**
 * Server model
 */
data class Server(
    @SerializedName("id")
    val id: Int,
    
    @SerializedName("name")
    val name: String,
    
    @SerializedName("embed_url")
    val embedUrl: String,
    
    @SerializedName("quality")
    val quality: String,
    
    @SerializedName("is_premium")
    val isPremium: Boolean
)

/**
 * User model
 */
data class User(
    @SerializedName("id")
    val id: Int,
    
    @SerializedName("username")
    val username: String,
    
    @SerializedName("email")
    val email: String,
    
    @SerializedName("role")
    val role: String,
    
    @SerializedName("is_premium")
    val isPremium: Boolean,
    
    @SerializedName("created_at")
    val createdAt: String,
    
    @SerializedName("watchlist_count")
    val watchlistCount: Int? = null
)

/**
 * Auth response model
 */
data class AuthResponse(
    @SerializedName("token")
    val token: String,
    
    @SerializedName("expires_in")
    val expiresIn: Long,
    
    @SerializedName("user")
    val user: User
)

/**
 * Watchlist item model
 */
data class WatchlistItem(
    @SerializedName("id")
    val id: Int,
    
    @SerializedName("type")
    val type: String, // "movie" or "tv_show"
    
    @SerializedName("content")
    val content: WatchlistContent,
    
    @SerializedName("added_at")
    val addedAt: String
)

data class WatchlistContent(
    @SerializedName("id")
    val id: Int,
    
    @SerializedName("title")
    val title: String,
    
    @SerializedName("poster_path")
    val posterPath: String?,
    
    @SerializedName("vote_average")
    val voteAverage: Double
) {
    fun getFullPosterUrl(): String? {
        return posterPath?.let { "https://image.tmdb.org/t/p/w500$it" }
    }
}



/**
 * App version model
 */
data class AppVersion(
    @SerializedName("current_version")
    val currentVersion: String,
    
    @SerializedName("minimum_version")
    val minimumVersion: String,
    
    @SerializedName("force_update")
    val forceUpdate: Boolean,
    
    @SerializedName("update_url")
    val updateUrl: String,
    
    @SerializedName("maintenance_mode")
    val maintenanceMode: Boolean,
    
    @SerializedName("maintenance_message")
    val maintenanceMessage: String,
    
    @SerializedName("update_required")
    val updateRequired: Boolean,
    
    @SerializedName("update_available")
    val updateAvailable: Boolean
)

/**
 * Home page data model
 */
data class HomeData(
    @SerializedName("featured_movies")
    val featuredMovies: List<Movie>,
    
    @SerializedName("featured_tv_shows")
    val featuredTvShows: List<TvShow>,
    
    @SerializedName("trending_movies")
    val trendingMovies: List<Movie>,
    
    @SerializedName("trending_tv_shows")
    val trendingTvShows: List<TvShow>,
    
    @SerializedName("latest_movies")
    val latestMovies: List<Movie>,
    
    @SerializedName("latest_tv_shows")
    val latestTvShows: List<TvShow>,
    
    @SerializedName("continue_watching")
    val continueWatching: List<WatchHistoryItem>,
    
    @SerializedName("recommended")
    val recommended: List<Any>
)

/**
 * Search result model
 */
data class SearchResult(
    @SerializedName("query")
    val query: String,

    @SerializedName("movies")
    val movies: List<Movie>,

    @SerializedName("tv_shows")
    val tvShows: List<TvShow>,

    @SerializedName("total_results")
    val totalResults: Int
)

/**
 * Movie list response
 */
data class MovieListResponse(
    @SerializedName("success")
    val success: Boolean,

    @SerializedName("message")
    val message: String?,

    @SerializedName("data")
    val data: MovieListData?
)

data class MovieListData(
    @SerializedName("movies")
    val movies: List<Movie>,

    @SerializedName("total")
    val total: Int,

    @SerializedName("page")
    val page: Int,

    @SerializedName("per_page")
    val perPage: Int
)

/**
 * TV Show list response
 */
data class TvShowListResponse(
    @SerializedName("success")
    val success: Boolean,

    @SerializedName("message")
    val message: String?,

    @SerializedName("data")
    val data: TvShowListData?
)

data class TvShowListData(
    @SerializedName("tv_shows")
    val tvShows: List<TvShow>,

    @SerializedName("total")
    val total: Int,

    @SerializedName("page")
    val page: Int,

    @SerializedName("per_page")
    val perPage: Int
)

/**
 * Watch history item model
 */
data class WatchHistoryItem(
    @SerializedName("id")
    val id: Int,

    @SerializedName("content_id")
    val contentId: Int,

    @SerializedName("content_type")
    val contentType: String, // "movie" or "tv_show"

    @SerializedName("title")
    val title: String,

    @SerializedName("poster_url")
    val posterUrl: String?,

    @SerializedName("backdrop_url")
    val backdropUrl: String?,

    @SerializedName("progress")
    val progress: Float, // 0.0 to 1.0

    @SerializedName("duration")
    val duration: Int, // in seconds

    @SerializedName("watched_at")
    val watchedAt: String,

    @SerializedName("genre_ids")
    val genreIds: List<Int> = emptyList(),

    @SerializedName("rating")
    val rating: Float? = null,

    @SerializedName("season_number")
    val seasonNumber: Int? = null,

    @SerializedName("episode_number")
    val episodeNumber: Int? = null
)
