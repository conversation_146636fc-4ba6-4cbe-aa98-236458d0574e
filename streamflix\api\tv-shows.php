<?php
/**
 * TV Shows API Endpoints
 * /api/tv-shows.php
 */

require_once 'config.php';

$method = getRequestMethod();
$data = getRequestData();
$path = $_GET['action'] ?? '';
$id = $_GET['id'] ?? null;

logAPIRequest("tv-shows/{$path}", $method);

try {
    $db = new Database();
    $conn = $db->connect();
    
    switch ($path) {
        case 'list':
            handleTVShowsList($conn);
            break;
            
        case 'featured':
            handleFeaturedTVShows($conn);
            break;
            
        case 'trending':
            handleTrendingTVShows($conn);
            break;
            
        case 'popular':
            handlePopularTVShows($conn);
            break;
            
        case 'latest':
            handleLatestTVShows($conn);
            break;
            
        case 'details':
            handleTVShowDetails($conn, $id);
            break;
            
        case 'seasons':
            handleTVShowSeasons($conn, $id);
            break;
            
        case 'episodes':
            handleTVShowEpisodes($conn);
            break;
            
        case 'search':
            handleTVShowSearch($conn);
            break;
            
        case 'genres':
            handleTVShowGenres($conn);
            break;
            
        case 'by-genre':
            handleTVShowsByGenre($conn);
            break;
            
        default:
            APIResponse::notFound('Endpoint not found');
    }
    
} catch (Exception $e) {
    APIResponse::serverError('Database connection failed');
}

/**
 * Handle TV shows list with pagination
 */
function handleTVShowsList($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $page = max(1, (int)($_GET['page'] ?? 1));
    $limit = min(50, max(1, (int)($_GET['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    // Get total count
    $stmt = $conn->query("SELECT COUNT(*) FROM tv_shows");
    $total = $stmt->fetchColumn();
    
    // Get TV shows
    $stmt = $conn->prepare("
        SELECT * FROM tv_shows 
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$limit, $offset]);
    $shows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $formattedShows = array_map('formatTVShowForAPI', $shows);
    
    APIResponse::success([
        'tv_shows' => $formattedShows,
        'pagination' => [
            'current_page' => $page,
            'per_page' => $limit,
            'total' => (int)$total,
            'total_pages' => ceil($total / $limit),
            'has_next' => $page < ceil($total / $limit),
            'has_prev' => $page > 1
        ]
    ]);
}

/**
 * Handle featured TV shows
 */
function handleFeaturedTVShows($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $limit = min(20, max(1, (int)($_GET['limit'] ?? 10)));
    
    $stmt = $conn->prepare("
        SELECT * FROM tv_shows 
        WHERE is_featured = 1 
        ORDER BY vote_average DESC, popularity DESC 
        LIMIT ?
    ");
    $stmt->execute([$limit]);
    $shows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $formattedShows = array_map('formatTVShowForAPI', $shows);
    
    APIResponse::success(['tv_shows' => $formattedShows]);
}

/**
 * Handle trending TV shows
 */
function handleTrendingTVShows($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $limit = min(20, max(1, (int)($_GET['limit'] ?? 10)));
    
    $stmt = $conn->prepare("
        SELECT * FROM tv_shows 
        WHERE is_trending = 1 
        ORDER BY popularity DESC, vote_average DESC 
        LIMIT ?
    ");
    $stmt->execute([$limit]);
    $shows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $formattedShows = array_map('formatTVShowForAPI', $shows);
    
    APIResponse::success(['tv_shows' => $formattedShows]);
}

/**
 * Handle popular TV shows
 */
function handlePopularTVShows($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $limit = min(50, max(1, (int)($_GET['limit'] ?? 20)));
    
    $stmt = $conn->prepare("
        SELECT * FROM tv_shows 
        ORDER BY popularity DESC, vote_average DESC 
        LIMIT ?
    ");
    $stmt->execute([$limit]);
    $shows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $formattedShows = array_map('formatTVShowForAPI', $shows);
    
    APIResponse::success(['tv_shows' => $formattedShows]);
}

/**
 * Handle latest TV shows
 */
function handleLatestTVShows($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $limit = min(50, max(1, (int)($_GET['limit'] ?? 20)));
    
    $stmt = $conn->prepare("
        SELECT * FROM tv_shows 
        ORDER BY first_air_date DESC, created_at DESC 
        LIMIT ?
    ");
    $stmt->execute([$limit]);
    $shows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $formattedShows = array_map('formatTVShowForAPI', $shows);
    
    APIResponse::success(['tv_shows' => $formattedShows]);
}

/**
 * Handle TV show details
 */
function handleTVShowDetails($conn, $id) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    if (!$id) {
        APIResponse::error('TV Show ID is required');
    }
    
    // Get TV show details
    $stmt = $conn->prepare("SELECT * FROM tv_shows WHERE id = ?");
    $stmt->execute([$id]);
    $show = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$show) {
        APIResponse::notFound('TV Show not found');
    }
    
    $showData = formatTVShowForAPI($show);
    
    // Get genres
    try {
        $stmt = $conn->prepare("
            SELECT g.* FROM genres g
            JOIN tv_show_genres tg ON g.id = tg.genre_id
            WHERE tg.tv_show_id = ?
        ");
        $stmt->execute([$id]);
        $showData['genres'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        $showData['genres'] = [];
    }
    
    // Get seasons
    try {
        $stmt = $conn->prepare("
            SELECT * FROM tv_seasons 
            WHERE tv_show_id = ? 
            ORDER BY season_number ASC
        ");
        $stmt->execute([$id]);
        $showData['seasons'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        $showData['seasons'] = [];
    }
    
    // Check if user has this in watchlist
    $user = APIAuth::optionalAuth();
    if ($user) {
        try {
            $stmt = $conn->prepare("SELECT id FROM watchlist WHERE user_id = ? AND tv_show_id = ?");
            $stmt->execute([$user['id'], $id]);
            $showData['in_watchlist'] = $stmt->rowCount() > 0;
        } catch (Exception $e) {
            $showData['in_watchlist'] = false;
        }
    } else {
        $showData['in_watchlist'] = false;
    }
    
    APIResponse::success(['tv_show' => $showData]);
}

/**
 * Handle TV show seasons
 */
function handleTVShowSeasons($conn, $id) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    if (!$id) {
        APIResponse::error('TV Show ID is required');
    }
    
    // Verify TV show exists
    $stmt = $conn->prepare("SELECT id FROM tv_shows WHERE id = ?");
    $stmt->execute([$id]);
    if (!$stmt->fetch()) {
        APIResponse::notFound('TV Show not found');
    }
    
    try {
        $stmt = $conn->prepare("
            SELECT s.*, COUNT(e.id) as episode_count
            FROM tv_seasons s
            LEFT JOIN tv_episodes e ON s.id = e.season_id
            WHERE s.tv_show_id = ?
            GROUP BY s.id
            ORDER BY s.season_number ASC
        ");
        $stmt->execute([$id]);
        $seasons = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $formattedSeasons = array_map(function($season) {
            return [
                'id' => (int)$season['id'],
                'season_number' => (int)$season['season_number'],
                'name' => $season['name'],
                'overview' => $season['overview'],
                'poster_path' => $season['poster_path'],
                'air_date' => $season['air_date'],
                'episode_count' => (int)$season['episode_count']
            ];
        }, $seasons);
        
        APIResponse::success(['seasons' => $formattedSeasons]);
        
    } catch (Exception $e) {
        APIResponse::success(['seasons' => []]);
    }
}

/**
 * Handle TV show episodes
 */
function handleTVShowEpisodes($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $seasonId = $_GET['season_id'] ?? null;
    if (!$seasonId) {
        APIResponse::error('Season ID is required');
    }
    
    try {
        $stmt = $conn->prepare("
            SELECT e.*, s.season_number, s.tv_show_id
            FROM tv_episodes e
            JOIN tv_seasons s ON e.season_id = s.id
            WHERE e.season_id = ?
            ORDER BY e.episode_number ASC
        ");
        $stmt->execute([$seasonId]);
        $episodes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $formattedEpisodes = array_map(function($episode) {
            return [
                'id' => (int)$episode['id'],
                'episode_number' => (int)$episode['episode_number'],
                'name' => $episode['name'],
                'overview' => $episode['overview'],
                'still_path' => $episode['still_path'],
                'air_date' => $episode['air_date'],
                'vote_average' => (float)$episode['vote_average'],
                'runtime' => (int)$episode['runtime'],
                'season_number' => (int)$episode['season_number'],
                'tv_show_id' => (int)$episode['tv_show_id']
            ];
        }, $episodes);
        
        APIResponse::success(['episodes' => $formattedEpisodes]);
        
    } catch (Exception $e) {
        APIResponse::success(['episodes' => []]);
    }
}

/**
 * Handle TV show search
 */
function handleTVShowSearch($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $query = trim($_GET['q'] ?? '');
    if (empty($query)) {
        APIResponse::error('Search query is required');
    }
    
    $page = max(1, (int)($_GET['page'] ?? 1));
    $limit = min(50, max(1, (int)($_GET['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    $searchTerm = "%{$query}%";
    
    // Get total count
    $stmt = $conn->prepare("
        SELECT COUNT(*) FROM tv_shows 
        WHERE name LIKE ? OR overview LIKE ?
    ");
    $stmt->execute([$searchTerm, $searchTerm]);
    $total = $stmt->fetchColumn();
    
    // Get TV shows
    $stmt = $conn->prepare("
        SELECT * FROM tv_shows 
        WHERE name LIKE ? OR overview LIKE ?
        ORDER BY 
            CASE WHEN name LIKE ? THEN 1 ELSE 2 END,
            vote_average DESC,
            popularity DESC
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$searchTerm, $searchTerm, $searchTerm, $limit, $offset]);
    $shows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $formattedShows = array_map('formatTVShowForAPI', $shows);
    
    APIResponse::success([
        'tv_shows' => $formattedShows,
        'search_query' => $query,
        'pagination' => [
            'current_page' => $page,
            'per_page' => $limit,
            'total' => (int)$total,
            'total_pages' => ceil($total / $limit),
            'has_next' => $page < ceil($total / $limit),
            'has_prev' => $page > 1
        ]
    ]);
}

/**
 * Handle TV show genres
 */
function handleTVShowGenres($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    try {
        $stmt = $conn->query("
            SELECT g.*, COUNT(tg.tv_show_id) as show_count
            FROM genres g
            LEFT JOIN tv_show_genres tg ON g.id = tg.genre_id
            GROUP BY g.id
            HAVING show_count > 0
            ORDER BY g.name ASC
        ");
        $genres = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $formattedGenres = array_map(function($genre) {
            return [
                'id' => (int)$genre['id'],
                'name' => $genre['name'],
                'show_count' => (int)$genre['show_count']
            ];
        }, $genres);
        
        APIResponse::success(['genres' => $formattedGenres]);
        
    } catch (Exception $e) {
        APIResponse::success(['genres' => []]);
    }
}

/**
 * Handle TV shows by genre
 */
function handleTVShowsByGenre($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $genreId = $_GET['genre_id'] ?? null;
    if (!$genreId) {
        APIResponse::error('Genre ID is required');
    }
    
    $page = max(1, (int)($_GET['page'] ?? 1));
    $limit = min(50, max(1, (int)($_GET['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    try {
        // Get total count
        $stmt = $conn->prepare("
            SELECT COUNT(DISTINCT t.id) 
            FROM tv_shows t
            JOIN tv_show_genres tg ON t.id = tg.tv_show_id
            WHERE tg.genre_id = ?
        ");
        $stmt->execute([$genreId]);
        $total = $stmt->fetchColumn();
        
        // Get TV shows
        $stmt = $conn->prepare("
            SELECT DISTINCT t.* 
            FROM tv_shows t
            JOIN tv_show_genres tg ON t.id = tg.tv_show_id
            WHERE tg.genre_id = ?
            ORDER BY t.vote_average DESC, t.popularity DESC
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$genreId, $limit, $offset]);
        $shows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $formattedShows = array_map('formatTVShowForAPI', $shows);
        
        APIResponse::success([
            'tv_shows' => $formattedShows,
            'genre_id' => (int)$genreId,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => (int)$total,
                'total_pages' => ceil($total / $limit),
                'has_next' => $page < ceil($total / $limit),
                'has_prev' => $page > 1
            ]
        ]);
        
    } catch (Exception $e) {
        APIResponse::error('Genre not found or no TV shows available');
    }
}
?>
