<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Alternative theme using AppCompat for maximum compatibility -->
    <style name="Theme.StreamFlix.AppCompat" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/streamflix_red</item>
        <item name="colorPrimaryDark">@color/streamflix_red_dark</item>
        <item name="colorAccent">@color/streamflix_red</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_dark</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        
        <!-- Status bar -->
        <item name="android:statusBarColor">@color/background_dark</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/background_dark</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        <!-- Window flags -->
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    </style>

    <!-- Simple splash theme -->
    <style name="Theme.StreamFlix.Splash.Simple" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="android:windowBackground">@color/background_dark</item>
        <item name="android:statusBarColor">@color/background_dark</item>
        <item name="android:navigationBarColor">@color/background_dark</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style>
</resources>
