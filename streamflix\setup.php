<?php
// Quick Setup for StreamFlix
// This creates a basic config file so you can access the installer

if (!file_exists('config/database.php')) {
    // Create basic config file
    $config_content = '<?php
// Temporary configuration for installer
class Database {
    private $host = "localhost";
    private $db_name = "streamflix";
    private $username = "root";
    private $password = "";
    private $conn;

    public function connect() {
        $this->conn = null;
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
            );
        } catch(PDOException $e) {
            echo "Connection Error: " . $e->getMessage();
        }
        return $this->conn;
    }
}

define("TMDB_API_KEY", "");
define("TMDB_BASE_URL", "https://api.themoviedb.org/3");
define("TMDB_IMAGE_BASE", "https://image.tmdb.org/t/p/");

$embed_servers = [
    "autoembed" => [
        "name" => "AutoEmbed",
        "movie_url" => "https://player.autoembed.cc/embed/movie/{id}",
        "tv_url" => "https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}",
        "priority" => 1
    ],
    "vidjoy" => [
        "name" => "VidJoy", 
        "movie_url" => "https://vidjoy.pro/embed/movie/{id}",
        "tv_url" => "https://vidjoy.pro/embed/tv/{id}/{season}/{episode}",
        "priority" => 2
    ],
    "vidzee" => [
        "name" => "VidZee",
        "movie_url" => "https://player.vidzee.wtf/embed/movie/{id}",
        "tv_url" => "https://player.vidzee.wtf/embed/tv/{id}/{season}/{episode}",
        "priority" => 3
    ]
];

define("SITE_NAME", "StreamFlix");
define("SITE_URL", "http://localhost/streamflix");
define("ADMIN_EMAIL", "<EMAIL>");
define("JWT_SECRET", "temp_secret");
define("ENCRYPTION_KEY", "temp_key");

session_start();
?>';

    if (!is_dir('config')) {
        mkdir('config', 0755, true);
    }
    
    file_put_contents('config/database.php', $config_content);
    
    echo '<!DOCTYPE html>
<html>
<head>
    <title>StreamFlix Setup</title>
    <style>
        body { font-family: Arial, sans-serif; background: #141414; color: white; text-align: center; padding: 50px; }
        .container { max-width: 600px; margin: 0 auto; background: #221f1f; padding: 40px; border-radius: 8px; }
        .success { color: #28a745; font-size: 1.2rem; margin-bottom: 20px; }
        .btn { background: #e50914; color: white; padding: 15px 30px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px; }
        .btn:hover { background: #f40612; }
        h1 { color: #e50914; }
    </style>
</head>
<body>
    <div class="container">
        <h1>StreamFlix Setup Complete!</h1>
        <div class="success">✅ Basic configuration file created successfully!</div>
        <p>Now you can proceed with the full installation.</p>
        <a href="install.php" class="btn">Run Full Installer</a>
        <a href="index.php" class="btn">Go to Homepage</a>
        
        <div style="margin-top: 30px; padding: 20px; background: #333; border-radius: 4px; text-align: left;">
            <h3>Next Steps:</h3>
            <ol>
                <li>Click "Run Full Installer" to complete setup</li>
                <li>Enter your database credentials</li>
                <li>Get TMDB API key from <a href="https://www.themoviedb.org/settings/api" target="_blank" style="color: #e50914;">here</a></li>
                <li>Create admin account</li>
                <li>Start importing movies and TV shows!</li>
            </ol>
        </div>
    </div>
</body>
</html>';
} else {
    header('Location: install.php');
}
?>
