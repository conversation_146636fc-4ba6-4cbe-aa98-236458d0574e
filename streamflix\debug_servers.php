<?php
require_once 'config/database.php';

echo "<h2>🔍 Server Debug Information</h2>";

try {
    $db = new Database();
    $conn = $db->connect();
    
    echo "<h3>📊 Database Tables Status</h3>";
    
    // Check embed_servers table
    try {
        $stmt = $conn->query("SELECT COUNT(*) FROM embed_servers");
        $embed_count = $stmt->fetchColumn();
        echo "<p>✅ embed_servers table exists with {$embed_count} servers</p>";
        
        $stmt = $conn->query("SELECT * FROM embed_servers ORDER BY priority ASC");
        $embed_servers_db = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>📋 embed_servers table content:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Movie URL</th><th>TV URL</th><th>Priority</th><th>Active</th><th>Active Movies</th><th>Active TV</th></tr>";
        foreach ($embed_servers_db as $server) {
            $active_movies = isset($server['is_active_movies']) ? ($server['is_active_movies'] ? 'Yes' : 'No') : 'N/A';
            $active_tv = isset($server['is_active_tv']) ? ($server['is_active_tv'] ? 'Yes' : 'No') : 'N/A';
            echo "<tr>";
            echo "<td>{$server['id']}</td>";
            echo "<td>{$server['name']}</td>";
            echo "<td>" . substr($server['movie_url'], 0, 50) . "...</td>";
            echo "<td>" . substr($server['tv_url'], 0, 50) . "...</td>";
            echo "<td>{$server['priority']}</td>";
            echo "<td>" . ($server['is_active'] ? 'Yes' : 'No') . "</td>";
            echo "<td>{$active_movies}</td>";
            echo "<td>{$active_tv}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } catch (Exception $e) {
        echo "<p>❌ embed_servers table error: " . $e->getMessage() . "</p>";
    }
    
    // Check servers table
    try {
        $stmt = $conn->query("SELECT COUNT(*) FROM servers");
        $servers_count = $stmt->fetchColumn();
        echo "<p>✅ servers table exists with {$servers_count} servers</p>";
        
        $stmt = $conn->query("SELECT * FROM servers ORDER BY name ASC");
        $servers_db = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>📋 servers table content:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>URL</th><th>Type</th><th>Active</th></tr>";
        foreach ($servers_db as $server) {
            echo "<tr>";
            echo "<td>{$server['id']}</td>";
            echo "<td>{$server['name']}</td>";
            echo "<td>" . substr($server['url'], 0, 60) . "...</td>";
            echo "<td>{$server['type']}</td>";
            echo "<td>" . ($server['is_active'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } catch (Exception $e) {
        echo "<p>❌ servers table error: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3>🎬 Config File Servers</h3>";
    global $embed_servers;
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Key</th><th>Name</th><th>Movie URL</th><th>TV URL</th><th>Priority</th></tr>";
    foreach ($embed_servers as $key => $server) {
        echo "<tr>";
        echo "<td>{$key}</td>";
        echo "<td>{$server['name']}</td>";
        echo "<td>" . substr($server['movie_url'], 0, 50) . "...</td>";
        echo "<td>" . substr($server['tv_url'], 0, 50) . "...</td>";
        echo "<td>{$server['priority']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>🧪 Test getEmbedUrls Function</h3>";
    
    // Test with a sample movie
    require_once 'includes/functions.php';
    $streamflix = new StreamFlix();
    
    echo "<h4>Testing Movie (TMDB ID: 550 - Fight Club):</h4>";
    $movie_urls = $streamflix->getEmbedUrls('movie', 550);
    echo "<p>Found " . count($movie_urls) . " servers for movies:</p>";
    echo "<ol>";
    foreach ($movie_urls as $url) {
        echo "<li><strong>{$url['name']}</strong> (Priority: {$url['priority']})<br>";
        echo "URL: " . substr($url['url'], 0, 80) . "...</li>";
    }
    echo "</ol>";
    
    echo "<h4>Testing TV Show (TMDB ID: 1399 - Game of Thrones S1E1):</h4>";
    $tv_urls = $streamflix->getEmbedUrls('tv_show', 1399, 1, 1);
    echo "<p>Found " . count($tv_urls) . " servers for TV shows:</p>";
    echo "<ol>";
    foreach ($tv_urls as $url) {
        echo "<li><strong>{$url['name']}</strong> (Priority: {$url['priority']})<br>";
        echo "URL: " . substr($url['url'], 0, 80) . "...</li>";
    }
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<p>❌ Database connection error: " . $e->getMessage() . "</p>";
}

echo "<h3>💡 Analysis</h3>";
echo "<p>If you see 4 servers in the player but only 3 in the database, it means:</p>";
echo "<ul>";
echo "<li>The system is falling back to config file servers</li>";
echo "<li>Database servers might not be properly configured</li>";
echo "<li>Content-specific active columns might be missing</li>";
echo "</ul>";
?>
