<?php
require_once 'config/database.php';

echo "<h2>🔧 Servers Table Fix</h2>";
echo "<p>Fixing the missing servers table issue.</p>";

try {
    $db = new Database();
    $conn = $db->connect();
    
    echo "<h3>📊 Current Database Status</h3>";
    
    // Check if servers table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'servers'");
    $servers_exists = $stmt->rowCount() > 0;
    
    // Check if embed_servers table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'embed_servers'");
    $embed_servers_exists = $stmt->rowCount() > 0;
    
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🗄️ Table Status:</h4>";
    echo "<ul>";
    echo "<li><strong>servers table:</strong> " . ($servers_exists ? "✅ EXISTS" : "❌ MISSING") . "</li>";
    echo "<li><strong>embed_servers table:</strong> " . ($embed_servers_exists ? "✅ EXISTS" : "❌ MISSING") . "</li>";
    echo "</ul>";
    echo "</div>";
    
    if (!$servers_exists) {
        echo "<h3>🔨 Creating servers table...</h3>";
        
        $create_servers_sql = "
        CREATE TABLE `servers` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `url_pattern` text NOT NULL,
            `type` enum('movie','tv','anime','hentai','all') DEFAULT 'all',
            `is_active` tinyint(1) DEFAULT 1,
            `priority` int(11) DEFAULT 1,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `type` (`type`),
            KEY `is_active` (`is_active`),
            KEY `priority` (`priority`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $conn->exec($create_servers_sql);
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "✅ <strong>servers table created successfully!</strong>";
        echo "</div>";
        
        // Insert default servers
        echo "<h4>📥 Inserting default servers...</h4>";
        
        $default_servers = [
            [
                'name' => 'AutoEmbed',
                'url_pattern' => 'https://player.autoembed.cc/embed/movie/{id}',
                'type' => 'movie',
                'priority' => 1
            ],
            [
                'name' => 'AutoEmbed TV',
                'url_pattern' => 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}',
                'type' => 'tv',
                'priority' => 1
            ],
            [
                'name' => 'VidSrc',
                'url_pattern' => 'https://vidsrc.to/embed/movie/{id}',
                'type' => 'movie',
                'priority' => 2
            ],
            [
                'name' => 'VidSrc TV',
                'url_pattern' => 'https://vidsrc.to/embed/tv/{id}/{season}/{episode}',
                'type' => 'tv',
                'priority' => 2
            ],
            [
                'name' => 'SuperEmbed',
                'url_pattern' => 'https://multiembed.mov/directstream.php?video_id={id}',
                'type' => 'movie',
                'priority' => 3
            ],
            [
                'name' => 'SuperEmbed TV',
                'url_pattern' => 'https://multiembed.mov/directstream.php?video_id={id}&s={season}&e={episode}',
                'type' => 'tv',
                'priority' => 3
            ],
            [
                'name' => 'Anime Server 1',
                'url_pattern' => 'https://2anime.xyz/embed/{id}',
                'type' => 'anime',
                'priority' => 1
            ],
            [
                'name' => 'Anime Server 2',
                'url_pattern' => 'https://aniwatch.to/embed/{id}',
                'type' => 'anime',
                'priority' => 2
            ]
        ];
        
        $insert_stmt = $conn->prepare("
            INSERT INTO servers (name, url_pattern, type, is_active, priority) 
            VALUES (?, ?, ?, 1, ?)
        ");
        
        $inserted = 0;
        foreach ($default_servers as $server) {
            try {
                $insert_stmt->execute([
                    $server['name'],
                    $server['url_pattern'],
                    $server['type'],
                    $server['priority']
                ]);
                $inserted++;
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                echo "❌ Error inserting {$server['name']}: " . $e->getMessage();
                echo "</div>";
            }
        }
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "✅ <strong>Inserted {$inserted} default servers!</strong>";
        echo "</div>";
    }
    
    if (!$embed_servers_exists) {
        echo "<h3>🔨 Creating embed_servers table...</h3>";
        
        $create_embed_servers_sql = "
        CREATE TABLE `embed_servers` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `movie_url` text,
            `tv_url` text,
            `anime_url` text,
            `hentai_url` text,
            `is_active` tinyint(1) DEFAULT 1,
            `priority` int(11) DEFAULT 1,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `is_active` (`is_active`),
            KEY `priority` (`priority`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $conn->exec($create_embed_servers_sql);
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "✅ <strong>embed_servers table created successfully!</strong>";
        echo "</div>";
        
        // Insert default embed servers
        echo "<h4>📥 Inserting default embed servers...</h4>";
        
        $default_embed_servers = [
            [
                'name' => 'AutoEmbed',
                'movie_url' => 'https://player.autoembed.cc/embed/movie/{id}',
                'tv_url' => 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}',
                'anime_url' => 'https://player.autoembed.cc/embed/movie/{id}',
                'hentai_url' => 'https://player.autoembed.cc/embed/movie/{id}',
                'priority' => 1
            ],
            [
                'name' => 'VidSrc',
                'movie_url' => 'https://vidsrc.to/embed/movie/{id}',
                'tv_url' => 'https://vidsrc.to/embed/tv/{id}/{season}/{episode}',
                'anime_url' => 'https://vidsrc.to/embed/movie/{id}',
                'hentai_url' => 'https://vidsrc.to/embed/movie/{id}',
                'priority' => 2
            ],
            [
                'name' => 'SuperEmbed',
                'movie_url' => 'https://multiembed.mov/directstream.php?video_id={id}',
                'tv_url' => 'https://multiembed.mov/directstream.php?video_id={id}&s={season}&e={episode}',
                'anime_url' => 'https://multiembed.mov/directstream.php?video_id={id}',
                'hentai_url' => 'https://multiembed.mov/directstream.php?video_id={id}',
                'priority' => 3
            ]
        ];
        
        $insert_embed_stmt = $conn->prepare("
            INSERT INTO embed_servers (name, movie_url, tv_url, anime_url, hentai_url, is_active, priority) 
            VALUES (?, ?, ?, ?, ?, 1, ?)
        ");
        
        $inserted_embed = 0;
        foreach ($default_embed_servers as $server) {
            try {
                $insert_embed_stmt->execute([
                    $server['name'],
                    $server['movie_url'],
                    $server['tv_url'],
                    $server['anime_url'],
                    $server['hentai_url'],
                    $server['priority']
                ]);
                $inserted_embed++;
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                echo "❌ Error inserting embed server {$server['name']}: " . $e->getMessage();
                echo "</div>";
            }
        }
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "✅ <strong>Inserted {$inserted_embed} default embed servers!</strong>";
        echo "</div>";
    }
    
    // Final status check
    echo "<h3>✅ Final Status</h3>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'servers'");
    $servers_final = $stmt->rowCount() > 0;
    
    $stmt = $conn->query("SHOW TABLES LIKE 'embed_servers'");
    $embed_servers_final = $stmt->rowCount() > 0;
    
    if ($servers_final) {
        $stmt = $conn->query("SELECT COUNT(*) FROM servers");
        $servers_count = $stmt->fetchColumn();
    }
    
    if ($embed_servers_final) {
        $stmt = $conn->query("SELECT COUNT(*) FROM embed_servers");
        $embed_servers_count = $stmt->fetchColumn();
    }
    
    echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🎯 Database Status:</h4>";
    echo "<ul>";
    echo "<li><strong>servers table:</strong> " . ($servers_final ? "✅ EXISTS ({$servers_count} servers)" : "❌ MISSING") . "</li>";
    echo "<li><strong>embed_servers table:</strong> " . ($embed_servers_final ? "✅ EXISTS ({$embed_servers_count} servers)" : "❌ MISSING") . "</li>";
    echo "</ul>";
    echo "</div>";
    
    if ($servers_final && $embed_servers_final) {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h4>🎉 Success!</h4>";
        echo "<p>All server tables have been created and populated successfully. Your admin panel should now work without errors.</p>";
        echo "<p><a href='admin/index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔗 Go to Admin Panel</a></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4 style='color: #721c24;'>❌ Error</h4>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
