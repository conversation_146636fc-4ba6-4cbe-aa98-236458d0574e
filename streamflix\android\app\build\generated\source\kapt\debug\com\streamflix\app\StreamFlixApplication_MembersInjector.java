// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.streamflix.app;

import androidx.hilt.work.HiltWorkerFactory;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class StreamFlixApplication_MembersInjector implements MembersInjector<StreamFlixApplication> {
  private final Provider<HiltWorkerFactory> workerFactoryProvider;

  public StreamFlixApplication_MembersInjector(Provider<HiltWorkerFactory> workerFactoryProvider) {
    this.workerFactoryProvider = workerFactoryProvider;
  }

  public static MembersInjector<StreamFlixApplication> create(
      Provider<HiltWorkerFactory> workerFactoryProvider) {
    return new StreamFlixApplication_MembersInjector(workerFactoryProvider);
  }

  @Override
  public void injectMembers(StreamFlixApplication instance) {
    injectWorkerFactory(instance, workerFactoryProvider.get());
  }

  @InjectedFieldSignature("com.streamflix.app.StreamFlixApplication.workerFactory")
  public static void injectWorkerFactory(StreamFlixApplication instance,
      HiltWorkerFactory workerFactory) {
    instance.workerFactory = workerFactory;
  }
}
