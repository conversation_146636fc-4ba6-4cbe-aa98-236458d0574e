<?php
require_once 'config/database.php';

echo "<h2>🔞 Fix Hentai Server Selection</h2>";
echo "<p>Moving hentai content to use hentai-specific servers instead of TV show servers.</p>";

try {
    $db = new Database();
    $conn = $db->connect();
    
    echo "<h3>📊 Current Hentai Content Status</h3>";
    
    // Check if content_type column exists
    $stmt = $conn->query("SHOW COLUMNS FROM tv_shows LIKE 'content_type'");
    $has_content_type = $stmt->rowCount() > 0;
    
    if (!$has_content_type) {
        echo "<h4>🔨 Adding content_type column to tv_shows table...</h4>";
        $conn->exec("ALTER TABLE tv_shows ADD COLUMN content_type ENUM('tv_show', 'anime', 'hentai', 'documentary') DEFAULT 'tv_show' AFTER tmdb_id");
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "✅ <strong>Added content_type column to tv_shows table</strong>";
        echo "</div>";
    }
    
    // Find hentai content by genre
    $stmt = $conn->query("
        SELECT t.id, t.tmdb_id, t.name, t.content_type, GROUP_CONCAT(g.name) as genres
        FROM tv_shows t
        LEFT JOIN tv_show_genres tg ON t.id = tg.tv_show_id
        LEFT JOIN genres g ON tg.genre_id = g.id
        WHERE g.name LIKE '%hentai%' OR g.name LIKE '%adult%' OR g.name LIKE '%18+%' 
        OR t.name LIKE '%hentai%' OR t.overview LIKE '%hentai%'
        GROUP BY t.id
    ");
    $hentai_content = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🔍 Found Hentai Content:</h4>";
    echo "<p><strong>Total Hentai Items:</strong> " . count($hentai_content) . "</p>";
    
    if (count($hentai_content) > 0) {
        echo "<div style='max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; margin-top: 10px;'>";
        foreach ($hentai_content as $item) {
            echo "<div style='margin-bottom: 5px; padding: 5px; background: white; border-radius: 3px;'>";
            echo "<strong>{$item['name']}</strong> (ID: {$item['tmdb_id']}) - Current Type: {$item['content_type']}";
            echo "</div>";
        }
        echo "</div>";
    }
    echo "</div>";
    
    // Update hentai content type
    if (count($hentai_content) > 0) {
        echo "<h3>🔄 Updating Hentai Content Type...</h3>";
        
        $updated_count = 0;
        foreach ($hentai_content as $item) {
            $stmt = $conn->prepare("UPDATE tv_shows SET content_type = 'hentai' WHERE id = ?");
            $stmt->execute([$item['id']]);
            $updated_count++;
        }
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "✅ <strong>Updated {$updated_count} items to hentai content type</strong>";
        echo "</div>";
    }
    
    // Check and create hentai servers
    echo "<h3>🖥️ Hentai Server Setup</h3>";
    
    // Check if hentai_url column exists in embed_servers
    $stmt = $conn->query("SHOW COLUMNS FROM embed_servers LIKE 'hentai_url'");
    $has_hentai_url = $stmt->rowCount() > 0;
    
    if (!$has_hentai_url) {
        echo "<h4>🔨 Adding hentai_url column to embed_servers...</h4>";
        $conn->exec("ALTER TABLE embed_servers ADD COLUMN hentai_url TEXT AFTER tv_url");
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "✅ <strong>Added hentai_url column to embed_servers table</strong>";
        echo "</div>";
    }
    
    // Get current servers
    $stmt = $conn->query("SELECT * FROM embed_servers ORDER BY priority ASC");
    $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🖥️ Current Servers:</h4>";
    
    $hentai_servers_count = 0;
    foreach ($servers as $server) {
        $has_hentai = !empty($server['hentai_url']);
        if ($has_hentai) $hentai_servers_count++;
        
        echo "<div style='margin-bottom: 10px; padding: 10px; background: " . ($has_hentai ? "#d4edda" : "#f8d7da") . "; border-radius: 5px;'>";
        echo "<strong>{$server['name']}</strong> - Priority: {$server['priority']} - ";
        echo $has_hentai ? "✅ Has Hentai URL" : "❌ No Hentai URL";
        echo "</div>";
    }
    
    echo "<p><strong>Servers with Hentai URLs:</strong> {$hentai_servers_count} / " . count($servers) . "</p>";
    echo "</div>";
    
    // Update servers with hentai URLs if missing
    if ($hentai_servers_count < count($servers)) {
        echo "<h4>🔧 Adding Hentai URLs to Servers...</h4>";
        
        $hentai_urls = [
            'AutoEmbed' => 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}',
            'VidSrc' => 'https://vidsrc.to/embed/tv/{id}/{season}/{episode}',
            'SuperEmbed' => 'https://multiembed.mov/directstream.php?video_id={id}&s={season}&e={episode}',
            'LetsEmbed' => 'https://letsembed.cc/embed/hentai/?id={id}/{season}/{episode}',
            'VidJoy' => 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}',
            'VidZee' => 'https://player.vidzee.wtf/embed/tv/{id}/{season}/{episode}',
            'EmbedSu' => 'https://embed.su/embed/tv/{id}/{season}/{episode}'
        ];
        
        $updated_servers = 0;
        foreach ($servers as $server) {
            if (empty($server['hentai_url'])) {
                $hentai_url = null;
                
                // Find matching URL pattern
                foreach ($hentai_urls as $name => $url) {
                    if (stripos($server['name'], $name) !== false) {
                        $hentai_url = $url;
                        break;
                    }
                }
                
                // Fallback to TV URL if no specific hentai URL found
                if (!$hentai_url && !empty($server['tv_url'])) {
                    $hentai_url = $server['tv_url'];
                }
                
                if ($hentai_url) {
                    $stmt = $conn->prepare("UPDATE embed_servers SET hentai_url = ? WHERE id = ?");
                    $stmt->execute([$hentai_url, $server['id']]);
                    $updated_servers++;
                    
                    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                    echo "✅ <strong>Updated {$server['name']}</strong> with hentai URL: {$hentai_url}";
                    echo "</div>";
                }
            }
        }
        
        if ($updated_servers > 0) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
            echo "✅ <strong>Updated {$updated_servers} servers with hentai URLs</strong>";
            echo "</div>";
        }
    }
    
    // Test hentai server selection
    echo "<h3>🧪 Testing Hentai Server Selection</h3>";
    
    if (count($hentai_content) > 0) {
        $test_item = $hentai_content[0];
        
        // Simulate getEmbedUrls function for hentai
        $stmt = $conn->prepare("SELECT * FROM embed_servers WHERE hentai_url IS NOT NULL AND hentai_url != '' AND is_active = 1 ORDER BY priority ASC");
        $stmt->execute();
        $hentai_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h4>🎯 Test Results for: {$test_item['name']}</h4>";
        echo "<p><strong>Available Hentai Servers:</strong> " . count($hentai_servers) . "</p>";
        
        if (count($hentai_servers) > 0) {
            echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin-top: 10px;'>";
            foreach ($hentai_servers as $server) {
                $test_url = str_replace(['{id}', '{season}', '{episode}'], [$test_item['tmdb_id'], '1', '1'], $server['hentai_url']);
                echo "<div style='margin-bottom: 5px; padding: 5px; background: white; border-radius: 3px;'>";
                echo "<strong>{$server['name']}</strong>: {$test_url}";
                echo "</div>";
            }
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin-top: 10px;'>";
            echo "❌ <strong>No hentai servers found!</strong>";
            echo "</div>";
        }
        echo "</div>";
    }
    
    // Final status
    echo "<h3>✅ Final Status</h3>";
    
    $stmt = $conn->query("SELECT COUNT(*) as count FROM tv_shows WHERE content_type = 'hentai'");
    $hentai_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $stmt = $conn->query("SELECT COUNT(*) as count FROM embed_servers WHERE hentai_url IS NOT NULL AND hentai_url != ''");
    $servers_with_hentai = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>📊 Summary:</h4>";
    echo "<ul>";
    echo "<li><strong>Hentai Content Items:</strong> {$hentai_count}</li>";
    echo "<li><strong>Servers with Hentai URLs:</strong> {$servers_with_hentai}</li>";
    echo "<li><strong>Content Type Fixed:</strong> ✅ Hentai content now uses hentai servers</li>";
    echo "<li><strong>Server Selection:</strong> ✅ Proper server routing implemented</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🔗 Test Links</h3>";
    
    echo "<div style='background: #cff4fc; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🎯 Test Hentai Server Selection:</h4>";
    
    if (count($hentai_content) > 0) {
        $test_item = $hentai_content[0];
        echo "<p><a href='player.php?id={$test_item['tmdb_id']}&type=tv_show' target='_blank' style='background: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin-right: 10px; display: inline-block; margin-bottom: 10px;'>🔞 Test Hentai Player</a></p>";
        echo "<p><small>Testing: {$test_item['name']}</small></p>";
    }
    
    echo "<p><a href='admin/servers.php' target='_blank' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin-right: 10px; display: inline-block; margin-bottom: 10px;'>🖥️ Manage Servers</a></p>";
    
    echo "<p><a href='admin/tv-shows.php' target='_blank' style='background: #17a2b8; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin-right: 10px; display: inline-block; margin-bottom: 10px;'>📺 View TV Shows</a></p>";
    
    echo "<p><a href='admin/index.php' target='_blank' style='background: #6f42c1; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block; margin-bottom: 10px;'>📊 Admin Dashboard</a></p>";
    echo "</div>";
    
    echo "<h3>⚠️ Important Notes</h3>";
    
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>📝 What Changed:</h4>";
    echo "<ul>";
    echo "<li>🔞 <strong>Content Type:</strong> Hentai content marked with content_type = 'hentai'</li>";
    echo "<li>🖥️ <strong>Server URLs:</strong> All servers now have hentai_url configured</li>";
    echo "<li>🎯 <strong>Server Selection:</strong> Hentai content will use hentai-specific servers</li>";
    echo "<li>🔄 <strong>Player Logic:</strong> Player will detect hentai content and use appropriate servers</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<hr>";
    echo "<p><strong>🎉 Hentai Server Selection Fixed!</strong></p>";
    echo "<p>All hentai content will now use hentai-specific servers instead of regular TV show servers.</p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4 style='color: #721c24;'>❌ Error</h4>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
