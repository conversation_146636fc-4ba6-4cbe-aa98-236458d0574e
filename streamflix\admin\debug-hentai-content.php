<?php
session_start();
require_once '../config/database.php';

echo "<h1>🔍 Hentai Content Debug</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
    .info-box { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    table { width: 100%; border-collapse: collapse; margin: 15px 0; }
    th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
    th { background: #f8f9fa; }
    .btn { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
</style>";

try {
    $db = new Database();
    $conn = $db->connect();
    
    echo "<div class='info-box success'>";
    echo "<h3>✅ Database Connection Successful</h3>";
    echo "</div>";
    
    // Check session
    echo "<div class='info-box'>";
    echo "<h3>🔐 Session Information</h3>";
    echo "<pre>" . print_r($_SESSION, true) . "</pre>";
    echo "</div>";
    
    // Check TV shows table structure
    echo "<div class='info-box'>";
    echo "<h3>📋 TV Shows Table Structure</h3>";
    
    $stmt = $conn->query("SHOW COLUMNS FROM tv_shows");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table>";
    echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // Check content_type distribution
    echo "<div class='info-box'>";
    echo "<h3>📊 Content Type Distribution</h3>";
    
    $stmt = $conn->query("
        SELECT 
            content_type,
            COUNT(*) as count
        FROM tv_shows 
        GROUP BY content_type
        ORDER BY count DESC
    ");
    $content_types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table>";
    echo "<tr><th>Content Type</th><th>Count</th></tr>";
    foreach ($content_types as $type) {
        echo "<tr>";
        echo "<td>" . ($type['content_type'] ?? 'NULL') . "</td>";
        echo "<td>{$type['count']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // Check hentai content specifically
    echo "<div class='info-box'>";
    echo "<h3>🔞 Hentai Content Analysis</h3>";
    
    // Current hentai content
    $stmt = $conn->query("SELECT COUNT(*) FROM tv_shows WHERE content_type = 'hentai'");
    $hentai_count = $stmt->fetchColumn();
    
    echo "<p><strong>Current Hentai Content:</strong> {$hentai_count}</p>";
    
    // Potential hentai content
    $stmt = $conn->query("
        SELECT COUNT(*) FROM tv_shows 
        WHERE (
            LOWER(name) LIKE '%hentai%' OR 
            LOWER(name) LIKE '%ecchi%' OR 
            LOWER(name) LIKE '%adult%' OR 
            LOWER(overview) LIKE '%hentai%' OR 
            LOWER(overview) LIKE '%adult%'
        ) AND (content_type != 'hentai' OR content_type IS NULL)
    ");
    $potential_hentai = $stmt->fetchColumn();
    
    echo "<p><strong>Potential Hentai Content:</strong> {$potential_hentai}</p>";
    
    // Show some examples
    if ($hentai_count > 0) {
        echo "<h4>Current Hentai Content (First 10):</h4>";
        $stmt = $conn->query("
            SELECT id, tmdb_id, name, content_type, vote_average 
            FROM tv_shows 
            WHERE content_type = 'hentai' 
            LIMIT 10
        ");
        $hentai_items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>ID</th><th>TMDB ID</th><th>Name</th><th>Type</th><th>Rating</th></tr>";
        foreach ($hentai_items as $item) {
            echo "<tr>";
            echo "<td>{$item['id']}</td>";
            echo "<td>{$item['tmdb_id']}</td>";
            echo "<td>" . htmlspecialchars($item['name']) . "</td>";
            echo "<td>{$item['content_type']}</td>";
            echo "<td>{$item['vote_average']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    if ($potential_hentai > 0) {
        echo "<h4>Potential Hentai Content (First 10):</h4>";
        $stmt = $conn->query("
            SELECT id, tmdb_id, name, content_type, vote_average 
            FROM tv_shows 
            WHERE (
                LOWER(name) LIKE '%hentai%' OR 
                LOWER(name) LIKE '%ecchi%' OR 
                LOWER(name) LIKE '%adult%' OR 
                LOWER(overview) LIKE '%hentai%' OR 
                LOWER(overview) LIKE '%adult%'
            ) AND (content_type != 'hentai' OR content_type IS NULL)
            LIMIT 10
        ");
        $potential_items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>ID</th><th>TMDB ID</th><th>Name</th><th>Type</th><th>Rating</th></tr>";
        foreach ($potential_items as $item) {
            echo "<tr>";
            echo "<td>{$item['id']}</td>";
            echo "<td>{$item['tmdb_id']}</td>";
            echo "<td>" . htmlspecialchars($item['name']) . "</td>";
            echo "<td>" . ($item['content_type'] ?? 'NULL') . "</td>";
            echo "<td>{$item['vote_average']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";
    
    // Test the exact query from hentai-content.php
    echo "<div class='info-box'>";
    echo "<h3>🧪 Test Hentai Content Query</h3>";
    
    $filter = 'hentai';
    $search = '';
    $page = 1;
    $per_page = 20;
    $offset = 0;
    
    $where_conditions = [];
    $params = [];
    
    if ($filter === 'hentai') {
        $where_conditions[] = "content_type = 'hentai'";
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Get total count
    $count_sql = "SELECT COUNT(*) FROM tv_shows {$where_clause}";
    $stmt = $conn->prepare($count_sql);
    $stmt->execute($params);
    $total_items = $stmt->fetchColumn();
    
    echo "<p><strong>Query:</strong> <code>{$count_sql}</code></p>";
    echo "<p><strong>Total Items Found:</strong> {$total_items}</p>";
    
    // Get actual items
    $sql = "
        SELECT id, tmdb_id, name, overview, content_type, vote_average, first_air_date, poster_path, number_of_seasons, number_of_episodes
        FROM tv_shows 
        {$where_clause}
        ORDER BY name ASC 
        LIMIT {$per_page} OFFSET {$offset}
    ";
    
    echo "<p><strong>Content Query:</strong> <code>{$sql}</code></p>";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    $content_items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Items Retrieved:</strong> " . count($content_items) . "</p>";
    
    if (!empty($content_items)) {
        echo "<h4>Retrieved Items:</h4>";
        echo "<table>";
        echo "<tr><th>ID</th><th>Name</th><th>Type</th><th>TMDB ID</th></tr>";
        foreach (array_slice($content_items, 0, 5) as $item) {
            echo "<tr>";
            echo "<td>{$item['id']}</td>";
            echo "<td>" . htmlspecialchars($item['name']) . "</td>";
            echo "<td>{$item['content_type']}</td>";
            echo "<td>{$item['tmdb_id']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";
    
    // Check embed servers
    echo "<div class='info-box'>";
    echo "<h3>🖥️ Hentai Servers Status</h3>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'embed_servers'");
    $embed_servers_exists = $stmt->rowCount() > 0;
    
    if ($embed_servers_exists) {
        $stmt = $conn->query("
            SELECT name, hentai_url, is_active, priority 
            FROM embed_servers 
            WHERE hentai_url IS NOT NULL AND hentai_url != ''
            ORDER BY priority ASC
        ");
        $hentai_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p><strong>Hentai Servers Found:</strong> " . count($hentai_servers) . "</p>";
        
        if (!empty($hentai_servers)) {
            echo "<table>";
            echo "<tr><th>Name</th><th>URL</th><th>Active</th><th>Priority</th></tr>";
            foreach ($hentai_servers as $server) {
                echo "<tr>";
                echo "<td>{$server['name']}</td>";
                echo "<td>" . htmlspecialchars($server['hentai_url']) . "</td>";
                echo "<td>" . ($server['is_active'] ? 'Yes' : 'No') . "</td>";
                echo "<td>{$server['priority']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p class='error'>❌ embed_servers table does not exist</p>";
    }
    echo "</div>";
    
    // Quick actions
    echo "<div class='info-box'>";
    echo "<h3>🔧 Quick Actions</h3>";
    echo "<a href='hentai-content.php' class='btn'>🔞 Go to Hentai Content Page</a>";
    echo "<a href='../fix_hentai_servers_final.php' target='_blank' class='btn'>🔧 Fix Hentai Servers</a>";
    echo "<a href='../convert_hentai_content.php' target='_blank' class='btn'>🔄 Convert Hentai Content</a>";
    echo "<a href='index.php' class='btn'>📊 Admin Dashboard</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='info-box error'>";
    echo "<h3>❌ Error</h3>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
