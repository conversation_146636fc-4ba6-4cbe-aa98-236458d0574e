// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.streamflix.app.data.download;

import android.content.Context;
import androidx.work.WorkManager;
import com.streamflix.app.data.local.UserPreferences;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AdvancedDownloadManager_Factory implements Factory<AdvancedDownloadManager> {
  private final Provider<Context> contextProvider;

  private final Provider<UserPreferences> userPreferencesProvider;

  private final Provider<WorkManager> workManagerProvider;

  public AdvancedDownloadManager_Factory(Provider<Context> contextProvider,
      Provider<UserPreferences> userPreferencesProvider,
      Provider<WorkManager> workManagerProvider) {
    this.contextProvider = contextProvider;
    this.userPreferencesProvider = userPreferencesProvider;
    this.workManagerProvider = workManagerProvider;
  }

  @Override
  public AdvancedDownloadManager get() {
    return newInstance(contextProvider.get(), userPreferencesProvider.get(), workManagerProvider.get());
  }

  public static AdvancedDownloadManager_Factory create(Provider<Context> contextProvider,
      Provider<UserPreferences> userPreferencesProvider,
      Provider<WorkManager> workManagerProvider) {
    return new AdvancedDownloadManager_Factory(contextProvider, userPreferencesProvider, workManagerProvider);
  }

  public static AdvancedDownloadManager newInstance(Context context,
      UserPreferences userPreferences, WorkManager workManager) {
    return new AdvancedDownloadManager(context, userPreferences, workManager);
  }
}
