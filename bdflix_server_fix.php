<?php
require_once 'config/database.php';

echo "<h2>🔧 BDFLiX Server Synchronization Fix</h2>";
echo "<p>Specialized fix for BDFLiX server management issue</p>";

try {
    $db = new Database();
    $conn = $db->connect();
    
    echo "<h3>Step 1: 🔍 Current Situation Analysis</h3>";
    
    // Check current database servers
    $stmt = $conn->query("SELECT * FROM embed_servers ORDER BY priority ASC");
    $current_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Current database servers: <strong>" . count($current_servers) . "</strong></p>";
    
    // Test what player actually loads
    require_once 'includes/functions.php';
    $streamflix = new StreamFlix();
    $player_servers = $streamflix->getEmbedUrls('movie', 950387);
    
    echo "<p>Player loads: <strong>" . count($player_servers) . " servers</strong></p>";
    
    if (count($current_servers) != count($player_servers)) {
        echo "<p style='color: red;'>❌ MISMATCH: Database has " . count($current_servers) . " but player shows " . count($player_servers) . "</p>";
    } else {
        echo "<p style='color: green;'>✅ Counts match, but let's ensure synchronization</p>";
    }
    
    echo "<h3>Step 2: 🧹 Clean Configuration</h3>";
    
    // Check and clean config file
    $config_file = 'config/database.php';
    $config_content = file_get_contents($config_file);
    
    if (strpos($config_content, '$embed_servers') !== false) {
        echo "<p style='color: orange;'>⚠️ Found hardcoded servers in config. Removing...</p>";
        
        // Remove the embed_servers array from config
        $cleaned_config = preg_replace('/\/\/ Embed Servers Configuration.*?\];/s', '// Embed Servers Configuration - REMOVED\n// All servers are now managed through the database only\n// Use admin panel to add/edit/remove servers', $config_content);
        
        if ($cleaned_config !== $config_content) {
            file_put_contents($config_file, $cleaned_config);
            echo "<p style='color: green;'>✅ Removed hardcoded servers from config file</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ Config file is already clean</p>";
    }
    
    echo "<h3>Step 3: 🗃️ Database Standardization</h3>";
    
    // Ensure we have the standard 4 servers with correct URLs
    $standard_servers = [
        'AutoEmbed' => [
            'movie_url' => 'https://player.autoembed.cc/embed/movie/{id}',
            'tv_url' => 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}',
            'priority' => 1
        ],
        'VidJoy' => [
            'movie_url' => 'https://vidjoy.pro/embed/movie/{id}',
            'tv_url' => 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}',
            'priority' => 2
        ],
        'VidZee' => [
            'movie_url' => 'https://player.vidzee.wtf/embed/movie/{id}',
            'tv_url' => 'https://player.vidzee.wtf/embed/tv/{id}/{season}/{episode}',
            'priority' => 3
        ],
        'LetsEmbed' => [
            'movie_url' => 'https://letsembed.cc/embed/movie/?id={id}',
            'tv_url' => 'https://letsembed.cc/embed/tv/?id={id}/{season}/{episode}',
            'priority' => 4
        ]
    ];
    
    foreach ($standard_servers as $name => $config) {
        // Check if server exists
        $stmt = $conn->prepare("SELECT * FROM embed_servers WHERE name = ?");
        $stmt->execute([$name]);
        $existing = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existing) {
            // Update existing server
            $stmt = $conn->prepare("
                UPDATE embed_servers 
                SET movie_url = ?, tv_url = ?, priority = ?, is_active = 1, updated_at = NOW()
                WHERE name = ?
            ");
            $stmt->execute([$config['movie_url'], $config['tv_url'], $config['priority'], $name]);
            echo "<p style='color: blue;'>🔄 Updated server: <strong>{$name}</strong></p>";
        } else {
            // Insert new server
            $stmt = $conn->prepare("
                INSERT INTO embed_servers (name, movie_url, tv_url, priority, is_active, created_at)
                VALUES (?, ?, ?, ?, 1, NOW())
            ");
            $stmt->execute([$name, $config['movie_url'], $config['tv_url'], $config['priority']]);
            echo "<p style='color: green;'>➕ Added server: <strong>{$name}</strong></p>";
        }
    }
    
    // Remove any extra servers not in our standard list
    $standard_names = array_keys($standard_servers);
    $placeholders = str_repeat('?,', count($standard_names) - 1) . '?';
    $stmt = $conn->prepare("SELECT * FROM embed_servers WHERE name NOT IN ($placeholders)");
    $stmt->execute($standard_names);
    $extra_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($extra_servers)) {
        echo "<p style='color: orange;'>⚠️ Found " . count($extra_servers) . " non-standard servers:</p>";
        foreach ($extra_servers as $server) {
            echo "<p>- {$server['name']} (ID: {$server['id']})</p>";
        }
        
        // Optionally disable them instead of deleting
        $stmt = $conn->prepare("UPDATE embed_servers SET is_active = 0 WHERE name NOT IN ($placeholders)");
        $stmt->execute($standard_names);
        echo "<p style='color: orange;'>⚠️ Disabled non-standard servers (not deleted)</p>";
    }
    
    echo "<h3>Step 4: 🧪 Verification Test</h3>";
    
    // Re-test after changes
    $stmt = $conn->query("SELECT * FROM embed_servers WHERE is_active = 1 ORDER BY priority ASC");
    $final_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Database active servers: <strong>" . count($final_servers) . "</strong></p>";
    
    // Test player function again
    $new_player_servers = $streamflix->getEmbedUrls('movie', 950387);
    echo "<p>Player now loads: <strong>" . count($new_player_servers) . " servers</strong></p>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'><th>Priority</th><th>Server Name</th><th>Status</th><th>Movie URL Sample</th></tr>";
    
    foreach ($final_servers as $server) {
        $status = $server['is_active'] ? '✅ Active' : '❌ Inactive';
        $sample_url = str_replace('{id}', '950387', $server['movie_url']);
        $sample_url = substr($sample_url, 0, 60) . '...';
        
        echo "<tr>";
        echo "<td>{$server['priority']}</td>";
        echo "<td><strong>{$server['name']}</strong></td>";
        echo "<td>{$status}</td>";
        echo "<td><small>{$sample_url}</small></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Step 5: 🎯 Final Status</h3>";
    
    if (count($final_servers) == 4 && count($new_player_servers) == 4) {
        echo "<div style='background: #d4edda; padding: 20px; border-left: 5px solid #28a745; margin: 20px 0;'>";
        echo "<h4 style='color: #155724;'>🎉 SUCCESS!</h4>";
        echo "<p><strong>Server synchronization is now fixed!</strong></p>";
        echo "<ul>";
        echo "<li>✅ Database has 4 active servers</li>";
        echo "<li>✅ Player loads 4 servers</li>";
        echo "<li>✅ Config file cleaned</li>";
        echo "<li>✅ Admin panel changes will now reflect in player</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; padding: 20px; border-left: 5px solid #ffc107; margin: 20px 0;'>";
        echo "<h4 style='color: #856404;'>⚠️ Partial Fix</h4>";
        echo "<p>Database: " . count($final_servers) . " servers, Player: " . count($new_player_servers) . " servers</p>";
        echo "<p>Additional troubleshooting may be needed.</p>";
        echo "</div>";
    }
    
    echo "<h3>Step 6: 📋 Important Next Steps</h3>";
    
    echo "<div style='background: #e7f3ff; padding: 20px; border-left: 5px solid #007bff; margin: 20px 0;'>";
    echo "<h4 style='color: #004085;'>CRITICAL: Clear All Caches</h4>";
    echo "<ol>";
    echo "<li><strong>Browser Cache:</strong> Ctrl+Shift+Delete (clear everything)</li>";
    echo "<li><strong>Hard Refresh:</strong> Ctrl+F5 on player pages</li>";
    echo "<li><strong>Server Cache:</strong> If using any caching (Redis, Memcached), clear it</li>";
    echo "<li><strong>CDN Cache:</strong> If using Cloudflare or similar, purge cache</li>";
    echo "</ol>";
    
    echo "<h4 style='color: #004085;'>Test Process:</h4>";
    echo "<ol>";
    echo "<li>Clear browser cache completely</li>";
    echo "<li>Open <a href='player.php?id=950387&type=movie' target='_blank'>test movie player</a></li>";
    echo "<li>Count the servers shown</li>";
    echo "<li>Go to <a href='admin/servers.php' target='_blank'>admin panel</a></li>";
    echo "<li>Disable one server and test player again</li>";
    echo "<li>Enable it back and verify</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 20px 0;'>";
    echo "<h4>🗑️ Cleanup</h4>";
    echo "<p>After confirming the fix works, you can delete these debug files:</p>";
    echo "<ul>";
    echo "<li>bdflix_server_fix.php (this file)</li>";
    echo "<li>live_server_debug.php</li>";
    echo "<li>complete_server_fix.php</li>";
    echo "<li>Any other debug files created</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-left: 5px solid #dc3545; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24;'>❌ Error</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration and try again.</p>";
    echo "</div>";
}
?>
