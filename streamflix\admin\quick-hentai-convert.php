<?php
session_start();
require_once '../config/database.php';

// Debug session
error_log("Session data: " . print_r($_SESSION, true));

// Check if user is admin - more flexible check
$is_admin = false;
if (isset($_SESSION['user_id'])) {
    if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
        $is_admin = true;
    } elseif (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
        $is_admin = true;
    }
}

if (!$is_admin) {
    // Try to check database directly
    try {
        $db = new Database();
        $conn = $db->connect();

        if (isset($_SESSION['user_id'])) {
            $stmt = $conn->prepare("SELECT role FROM users WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($user && $user['role'] === 'admin') {
                $is_admin = true;
                $_SESSION['role'] = 'admin'; // Update session
            }
        }
    } catch (Exception $e) {
        error_log("Database check failed: " . $e->getMessage());
    }
}

if (!$is_admin) {
    header('Location: ../login.php');
    exit;
}

// Database connection (reuse if already created)
if (!isset($conn)) {
    $db = new Database();
    $conn = $db->connect();
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ajax'])) {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    $response = ['success' => false, 'message' => ''];
    
    try {
        if ($action === 'convert_single') {
            $id = (int)($_POST['id'] ?? 0);
            if ($id > 0) {
                $stmt = $conn->prepare("UPDATE tv_shows SET content_type = 'hentai' WHERE id = ?");
                $stmt->execute([$id]);
                
                // Add hentai genre
                $stmt = $conn->prepare("SELECT id FROM genres WHERE LOWER(name) = 'hentai'");
                $stmt->execute();
                $hentai_genre = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$hentai_genre) {
                    $stmt = $conn->prepare("INSERT INTO genres (tmdb_id, name) VALUES (99999, 'Hentai')");
                    $stmt->execute();
                    $hentai_genre_id = $conn->lastInsertId();
                } else {
                    $hentai_genre_id = $hentai_genre['id'];
                }
                
                $stmt = $conn->prepare("INSERT IGNORE INTO tv_show_genres (tv_show_id, genre_id) VALUES (?, ?)");
                $stmt->execute([$id, $hentai_genre_id]);
                
                $response['success'] = true;
                $response['message'] = 'Successfully converted to hentai';
            }
        } elseif ($action === 'revert_single') {
            $id = (int)($_POST['id'] ?? 0);
            if ($id > 0) {
                $stmt = $conn->prepare("UPDATE tv_shows SET content_type = 'tv_show' WHERE id = ?");
                $stmt->execute([$id]);
                
                $response['success'] = true;
                $response['message'] = 'Successfully reverted to TV show';
            }
        } elseif ($action === 'get_potential') {
            $stmt = $conn->query("
                SELECT id, tmdb_id, name, overview, content_type, vote_average
                FROM tv_shows 
                WHERE (
                    LOWER(name) LIKE '%hentai%' OR 
                    LOWER(name) LIKE '%ecchi%' OR 
                    LOWER(name) LIKE '%adult%' OR 
                    LOWER(overview) LIKE '%hentai%' OR 
                    LOWER(overview) LIKE '%adult%'
                ) AND (content_type != 'hentai' OR content_type IS NULL)
                ORDER BY name ASC 
                LIMIT 20
            ");
            $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $response['success'] = true;
            $response['data'] = $items;
        }
    } catch (Exception $e) {
        $response['message'] = $e->getMessage();
    }
    
    echo json_encode($response);
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Hentai Converter - Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        
        .nav-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-links {
            display: flex;
            gap: 15px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: background 0.3s ease;
        }

        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }

        .header {
            background: linear-gradient(135deg, #ff1744, #e91e63);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 30px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .actions {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff1744, #e91e63);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .potential-items {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
        }
        
        .item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .item-info h4 {
            margin-bottom: 5px;
            color: #333;
        }
        
        .item-meta {
            font-size: 0.9rem;
            color: #666;
        }
        
        .item-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn-small {
            padding: 8px 16px;
            font-size: 0.9rem;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .message {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        @media (max-width: 768px) {
            .item {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
            
            .item-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-header">
            <div>
                <h2><i class="fas fa-cog"></i> Admin Panel</h2>
            </div>
            <div class="nav-links">
                <a href="index.php"><i class="fas fa-dashboard"></i> Dashboard</a>
                <a href="hentai-management.php"><i class="fas fa-heart"></i> Hentai Management</a>
                <a href="../index.php"><i class="fas fa-home"></i> Home</a>
                <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </div>

        <div class="header">
            <h1><i class="fas fa-magic"></i> Quick Hentai Converter</h1>
            <p>Quickly identify and convert potential hentai content</p>
        </div>
        
        <div class="content">
            <div class="message" id="message"></div>
            
            <div class="stats" id="stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalTvShows">-</div>
                    <div>Total TV Shows</div>
                </div>
                <div class="stat-card" style="background: linear-gradient(135deg, #ff1744, #e91e63);">
                    <div class="stat-number" id="hentaiContent">-</div>
                    <div>Hentai Content</div>
                </div>
                <div class="stat-card" style="background: linear-gradient(135deg, #4caf50, #45a049);">
                    <div class="stat-number" id="potentialItems">-</div>
                    <div>Potential Items</div>
                </div>
            </div>
            
            <div class="actions">
                <button class="btn btn-primary" onclick="loadPotentialItems()">
                    <i class="fas fa-search"></i> Find Potential Hentai
                </button>
                <button class="btn btn-success" onclick="convertAll()">
                    <i class="fas fa-magic"></i> Convert All Potential
                </button>
                <a href="hentai-management.php" class="btn btn-primary">
                    <i class="fas fa-cog"></i> Advanced Management
                </a>
                <a href="../test_hentai_servers.php" target="_blank" class="btn btn-danger">
                    <i class="fas fa-server"></i> Test Hentai Servers
                </a>
            </div>
            
            <div class="potential-items">
                <h3><i class="fas fa-list"></i> Potential Hentai Content</h3>
                <div id="itemsList">
                    <div class="loading">
                        <div>Click "Find Potential Hentai" to scan for content</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let potentialItems = [];
        
        function showMessage(text, type = 'success') {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = `message ${type}`;
            message.style.display = 'block';
            
            setTimeout(() => {
                message.style.display = 'none';
            }, 5000);
        }
        
        function loadStats() {
            // This would typically load from server, for now we'll update when loading items
        }
        
        function loadPotentialItems() {
            const itemsList = document.getElementById('itemsList');
            itemsList.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <div>Scanning for potential hentai content...</div>
                </div>
            `;
            
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'ajax=1&action=get_potential'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    potentialItems = data.data;
                    displayItems(potentialItems);
                    document.getElementById('potentialItems').textContent = potentialItems.length;
                } else {
                    showMessage('Error loading items: ' + data.message, 'error');
                }
            })
            .catch(error => {
                showMessage('Network error: ' + error.message, 'error');
            });
        }
        
        function displayItems(items) {
            const itemsList = document.getElementById('itemsList');
            
            if (items.length === 0) {
                itemsList.innerHTML = `
                    <div class="loading">
                        <i class="fas fa-check-circle" style="font-size: 3rem; color: #4caf50; margin-bottom: 15px;"></i>
                        <div>No potential hentai content found!</div>
                        <div style="font-size: 0.9rem; color: #666; margin-top: 10px;">All content is properly categorized.</div>
                    </div>
                `;
                return;
            }
            
            itemsList.innerHTML = items.map(item => `
                <div class="item" id="item-${item.id}">
                    <div class="item-info">
                        <h4>${item.name}</h4>
                        <div class="item-meta">
                            <div><strong>TMDB ID:</strong> ${item.tmdb_id}</div>
                            <div><strong>Rating:</strong> ${item.vote_average}/10</div>
                            <div><strong>Current Type:</strong> ${item.content_type || 'Uncategorized'}</div>
                        </div>
                    </div>
                    <div class="item-actions">
                        <button class="btn btn-danger btn-small" onclick="convertSingle(${item.id}, '${item.name.replace(/'/g, "\\'")}')">
                            <i class="fas fa-heart"></i> Convert
                        </button>
                        <a href="../player.php?id=${item.tmdb_id}&type=tv_show" target="_blank" class="btn btn-primary btn-small">
                            <i class="fas fa-play"></i> Preview
                        </a>
                    </div>
                </div>
            `).join('');
        }
        
        function convertSingle(id, name) {
            if (!confirm(`Convert "${name}" to hentai content?`)) return;
            
            const item = document.getElementById(`item-${id}`);
            item.style.opacity = '0.5';
            
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `ajax=1&action=convert_single&id=${id}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    item.remove();
                    potentialItems = potentialItems.filter(item => item.id !== id);
                    document.getElementById('potentialItems').textContent = potentialItems.length;
                    showMessage(`Successfully converted "${name}" to hentai`);
                } else {
                    item.style.opacity = '1';
                    showMessage('Error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                item.style.opacity = '1';
                showMessage('Network error: ' + error.message, 'error');
            });
        }
        
        function convertAll() {
            if (potentialItems.length === 0) {
                showMessage('No items to convert', 'error');
                return;
            }
            
            if (!confirm(`Convert all ${potentialItems.length} items to hentai content?`)) return;
            
            let converted = 0;
            let errors = 0;
            
            const convertNext = (index) => {
                if (index >= potentialItems.length) {
                    showMessage(`Conversion complete! ${converted} converted, ${errors} errors`);
                    loadPotentialItems(); // Refresh the list
                    return;
                }
                
                const item = potentialItems[index];
                fetch('', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `ajax=1&action=convert_single&id=${item.id}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        converted++;
                    } else {
                        errors++;
                    }
                    convertNext(index + 1);
                })
                .catch(error => {
                    errors++;
                    convertNext(index + 1);
                });
            };
            
            convertNext(0);
        }
        
        // Load initial data
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
        });
    </script>
</body>
</html>
