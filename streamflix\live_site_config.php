<?php
/**
 * StreamFlix Live Site Configuration
 * Database: tipsbdxy_bdflix2025
 * 
 * Instructions:
 * 1. Upload streamflix_complete_schema.sql to your database
 * 2. Update the database credentials below
 * 3. Replace your existing config/database.php with these settings
 */

// Database Configuration for Live Site
define('DB_HOST', 'localhost'); // Your hosting provider's database host
define('DB_NAME', 'tipsbdxy_bdflix2025'); // Your database name
define('DB_USER', 'tipsbdxy_bdflix2025'); // Your database username (usually same as DB_NAME)
define('DB_PASS', 'your_database_password'); // Your database password

// Site Configuration
define('SITE_NAME', 'StreamFlix');
define('SITE_URL', 'https://yourdomain.com'); // Replace with your actual domain
define('SITE_DESCRIPTION', 'Watch Movies and TV Shows Online');

// TMDB API Configuration
define('TMDB_API_KEY', 'your_tmdb_api_key_here'); // Get from https://www.themoviedb.org/settings/api
define('TMDB_BASE_URL', 'https://api.themoviedb.org/3');
define('TMDB_IMAGE_BASE_URL', 'https://image.tmdb.org/t/p');

// Security Settings
define('JWT_SECRET', 'your_jwt_secret_key_here'); // Generate a random string
define('ENCRYPTION_KEY', 'your_encryption_key_here'); // Generate a random string
define('SESSION_LIFETIME', 3600); // 1 hour

// File Upload Settings
define('MAX_UPLOAD_SIZE', 10 * 1024 * 1024); // 10MB
define('UPLOAD_PATH', 'uploads/');
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// Content Settings
define('ENABLE_ADULT_CONTENT', true);
define('ENABLE_ANIME', true);
define('ENABLE_HENTAI', true);
define('ITEMS_PER_PAGE', 20);

// Cache Settings
define('ENABLE_CACHE', true);
define('CACHE_DURATION', 3600); // 1 hour
define('CACHE_PATH', 'cache/');

// Email Settings (Optional - for user notifications)
define('SMTP_HOST', 'your_smtp_host');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', 'your_smtp_username');
define('SMTP_PASSWORD', 'your_smtp_password');
define('SMTP_ENCRYPTION', 'tls');
define('EMAIL_FROM', '<EMAIL>');
define('EMAIL_FROM_NAME', 'StreamFlix');

// Error Reporting (Set to false for production)
define('DEBUG_MODE', false);
define('SHOW_ERRORS', false);

// Timezone
date_default_timezone_set('Asia/Dhaka');

// Error Reporting Settings
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

/**
 * Database Connection Class
 */
class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $conn = null;

    public function connect() {
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4",
                $this->username,
                $this->password,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
                ]
            );
        } catch(PDOException $e) {
            if (DEBUG_MODE) {
                die("Connection Error: " . $e->getMessage());
            } else {
                die("Database connection failed. Please try again later.");
            }
        }
        return $this->conn;
    }
}

/**
 * Helper Functions
 */

// Get site setting from database
function getSiteSetting($key, $default = null) {
    try {
        $db = new Database();
        $conn = $db->connect();
        
        $stmt = $conn->prepare("SELECT setting_value FROM site_settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch();
        
        return $result ? $result['setting_value'] : $default;
    } catch (Exception $e) {
        return $default;
    }
}

// Update site setting in database
function updateSiteSetting($key, $value) {
    try {
        $db = new Database();
        $conn = $db->connect();
        
        $stmt = $conn->prepare("
            INSERT INTO site_settings (setting_key, setting_value) 
            VALUES (?, ?) 
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
        ");
        return $stmt->execute([$key, $value]);
    } catch (Exception $e) {
        return false;
    }
}

// Check if user is admin
function isAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

// Check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// Sanitize input
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

// Generate CSRF token
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// Verify CSRF token
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Format runtime (minutes to hours:minutes)
function formatRuntime($minutes) {
    if (!$minutes) return 'N/A';
    $hours = floor($minutes / 60);
    $mins = $minutes % 60;
    return $hours > 0 ? "{$hours}h {$mins}m" : "{$mins}m";
}

// Format file size
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}

// Log activity
function logActivity($message, $level = 'INFO') {
    if (!DEBUG_MODE) return;
    
    $log_file = 'logs/activity_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $user_id = $_SESSION['user_id'] ?? 'guest';
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    
    $log_entry = "[{$timestamp}] [{$level}] [User: {$user_id}] [IP: {$ip}] {$message}" . PHP_EOL;
    
    // Create logs directory if it doesn't exist
    if (!is_dir('logs')) {
        mkdir('logs', 0755, true);
    }
    
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set session timeout
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > SESSION_LIFETIME)) {
    session_unset();
    session_destroy();
    session_start();
}
$_SESSION['last_activity'] = time();

?>

<!-- 
DEPLOYMENT INSTRUCTIONS:

1. Upload streamflix_complete_schema.sql to your database:
   - Go to phpMyAdmin or your database management tool
   - Select database: tipsbdxy_bdflix2025
   - Import the SQL file

2. Update database credentials in this file:
   - Replace DB_HOST, DB_USER, DB_PASS with your hosting provider's details
   - Usually DB_USER is the same as DB_NAME for shared hosting

3. Replace your config/database.php with the Database class from this file

4. Update site settings:
   - Replace SITE_URL with your actual domain
   - Add your TMDB API key
   - Generate random strings for JWT_SECRET and ENCRYPTION_KEY

5. Set proper file permissions:
   - uploads/ folder: 755
   - cache/ folder: 755
   - logs/ folder: 755

6. Default admin login:
   - Email: <EMAIL>
   - Password: password
   - CHANGE THIS IMMEDIATELY AFTER FIRST LOGIN!

7. Test the site:
   - Visit your domain
   - Login as admin
   - Import some content
   - Test player functionality

8. Optional configurations:
   - Setup SMTP for email notifications
   - Configure SSL certificate
   - Setup cron jobs for automated tasks
   - Configure CDN for better performance
-->
