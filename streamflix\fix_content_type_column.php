<?php
require_once 'config/database.php';

echo "<h2>🔧 Fix Content Type Column Error</h2>";
echo "<p>Adding missing content_type columns to movies and tv_shows tables.</p>";

try {
    $db = new Database();
    $conn = $db->connect();
    
    echo "<h3>📊 Current Database Structure</h3>";
    
    // Check movies table structure
    $stmt = $conn->query("SHOW COLUMNS FROM movies LIKE 'content_type'");
    $movies_has_content_type = $stmt->rowCount() > 0;
    
    // Check tv_shows table structure
    $stmt = $conn->query("SHOW COLUMNS FROM tv_shows LIKE 'content_type'");
    $tv_shows_has_content_type = $stmt->rowCount() > 0;
    
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🗄️ Table Status:</h4>";
    echo "<ul>";
    echo "<li><strong>movies.content_type:</strong> " . ($movies_has_content_type ? "✅ EXISTS" : "❌ MISSING") . "</li>";
    echo "<li><strong>tv_shows.content_type:</strong> " . ($tv_shows_has_content_type ? "✅ EXISTS" : "❌ MISSING") . "</li>";
    echo "</ul>";
    echo "</div>";
    
    $changes_made = false;
    
    // Add content_type column to movies table if missing
    if (!$movies_has_content_type) {
        echo "<h3>🔨 Adding content_type column to movies table...</h3>";
        
        $add_movies_content_type = "
        ALTER TABLE movies 
        ADD COLUMN content_type ENUM('movie', 'anime', 'documentary') DEFAULT 'movie' 
        AFTER tmdb_id
        ";
        
        $conn->exec($add_movies_content_type);
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "✅ <strong>Added content_type column to movies table</strong>";
        echo "</div>";
        $changes_made = true;
    }
    
    // Add content_type column to tv_shows table if missing
    if (!$tv_shows_has_content_type) {
        echo "<h3>🔨 Adding content_type column to tv_shows table...</h3>";
        
        $add_tv_content_type = "
        ALTER TABLE tv_shows 
        ADD COLUMN content_type ENUM('tv_show', 'anime', 'hentai', 'documentary') DEFAULT 'tv_show' 
        AFTER tmdb_id
        ";
        
        $conn->exec($add_tv_content_type);
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "✅ <strong>Added content_type column to tv_shows table</strong>";
        echo "</div>";
        $changes_made = true;
    }
    
    if ($changes_made) {
        echo "<h3>🔄 Updating existing content...</h3>";
        
        // Update existing anime content based on genres
        $stmt = $conn->prepare("
            UPDATE movies m
            JOIN movie_genres mg ON m.id = mg.movie_id
            JOIN genres g ON mg.genre_id = g.id
            SET m.content_type = 'anime'
            WHERE g.name IN ('Animation', 'Anime') 
            AND m.content_type = 'movie'
        ");
        $stmt->execute();
        $anime_movies_updated = $stmt->rowCount();
        
        $stmt = $conn->prepare("
            UPDATE tv_shows t
            JOIN tv_show_genres tg ON t.id = tg.tv_show_id
            JOIN genres g ON tg.genre_id = g.id
            SET t.content_type = 'anime'
            WHERE g.name IN ('Animation', 'Anime') 
            AND t.content_type = 'tv_show'
        ");
        $stmt->execute();
        $anime_tv_updated = $stmt->rowCount();
        
        // Update hentai content
        $stmt = $conn->prepare("
            UPDATE tv_shows t
            JOIN tv_show_genres tg ON t.id = tg.tv_show_id
            JOIN genres g ON tg.genre_id = g.id
            SET t.content_type = 'hentai'
            WHERE g.name LIKE '%hentai%' OR g.name LIKE '%adult%' OR g.name LIKE '%18+%'
        ");
        $stmt->execute();
        $hentai_updated = $stmt->rowCount();
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "✅ <strong>Updated existing content:</strong>";
        echo "<ul>";
        echo "<li>Anime Movies: {$anime_movies_updated}</li>";
        echo "<li>Anime TV Shows: {$anime_tv_updated}</li>";
        echo "<li>Hentai Content: {$hentai_updated}</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    // Final status check
    echo "<h3>✅ Final Database Structure</h3>";
    
    $stmt = $conn->query("SHOW COLUMNS FROM movies LIKE 'content_type'");
    $movies_final = $stmt->rowCount() > 0;
    
    $stmt = $conn->query("SHOW COLUMNS FROM tv_shows LIKE 'content_type'");
    $tv_shows_final = $stmt->rowCount() > 0;
    
    // Get content statistics
    if ($movies_final) {
        $stmt = $conn->query("SELECT content_type, COUNT(*) as count FROM movies GROUP BY content_type");
        $movie_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    if ($tv_shows_final) {
        $stmt = $conn->query("SELECT content_type, COUNT(*) as count FROM tv_shows GROUP BY content_type");
        $tv_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🎯 Database Status:</h4>";
    echo "<ul>";
    echo "<li><strong>movies.content_type:</strong> " . ($movies_final ? "✅ EXISTS" : "❌ MISSING") . "</li>";
    echo "<li><strong>tv_shows.content_type:</strong> " . ($tv_shows_final ? "✅ EXISTS" : "❌ MISSING") . "</li>";
    echo "</ul>";
    
    if (isset($movie_stats)) {
        echo "<h4>🎬 Movie Content Types:</h4>";
        echo "<ul>";
        foreach ($movie_stats as $stat) {
            echo "<li><strong>{$stat['content_type']}:</strong> {$stat['count']}</li>";
        }
        echo "</ul>";
    }
    
    if (isset($tv_stats)) {
        echo "<h4>📺 TV Show Content Types:</h4>";
        echo "<ul>";
        foreach ($tv_stats as $stat) {
            echo "<li><strong>{$stat['content_type']}:</strong> {$stat['count']}</li>";
        }
        echo "</ul>";
    }
    echo "</div>";
    
    if ($movies_final && $tv_shows_final) {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h4>🎉 Success!</h4>";
        echo "<p>All content_type columns have been added successfully. The error should now be resolved.</p>";
        echo "<p><a href='admin/index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔗 Go to Admin Panel</a></p>";
        echo "</div>";
    }
    
    echo "<h3>🧪 Test Instructions</h3>";
    
    echo "<div style='background: #e2e3e5; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>📋 Testing Steps:</h4>";
    echo "<ol>";
    echo "<li><strong>Admin Panel:</strong> Try accessing admin panel again</li>";
    echo "<li><strong>Import System:</strong> Test anime and hentai imports</li>";
    echo "<li><strong>Player:</strong> Test content with different types</li>";
    echo "<li><strong>Server Selection:</strong> Check if servers work correctly</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h3>🔗 Test Links</h3>";
    
    echo "<div style='background: #cff4fc; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🎯 Test the Fix:</h4>";
    
    echo "<p><a href='admin/index.php' target='_blank' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin-right: 10px; display: inline-block; margin-bottom: 10px;'>📊 Admin Dashboard</a></p>";
    
    echo "<p><a href='admin/servers.php' target='_blank' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin-right: 10px; display: inline-block; margin-bottom: 10px;'>🖥️ Server Management</a></p>";
    
    echo "<p><a href='admin/import.php' target='_blank' style='background: #17a2b8; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin-right: 10px; display: inline-block; margin-bottom: 10px;'>📥 Import System</a></p>";
    
    echo "<p><a href='index.php' target='_blank' style='background: #6f42c1; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block; margin-bottom: 10px;'>🏠 Homepage</a></p>";
    echo "</div>";
    
    echo "<h3>⚠️ Important Notes</h3>";
    
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>📝 Remember:</h4>";
    echo "<ul>";
    echo "<li>🔄 <strong>Refresh Pages:</strong> Refresh admin pages to see changes</li>";
    echo "<li>📊 <strong>Content Types:</strong> New content will be properly categorized</li>";
    echo "<li>🎌 <strong>Anime Content:</strong> Will use anime-specific servers</li>";
    echo "<li>🔞 <strong>Hentai Content:</strong> Will use hentai-specific servers</li>";
    echo "<li>🗄️ <strong>Database Backup:</strong> Consider backing up database before major changes</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<hr>";
    echo "<p><strong>🎉 Content Type Column Error Fixed!</strong></p>";
    echo "<p>The missing content_type columns have been added and existing content has been categorized.</p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4 style='color: #721c24;'>❌ Error</h4>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
