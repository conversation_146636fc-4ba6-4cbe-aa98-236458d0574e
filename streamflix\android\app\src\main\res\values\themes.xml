<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base application theme -->
    <style name="Theme.StreamFlix" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/streamflix_red</item>
        <item name="colorPrimaryVariant">@color/streamflix_red_dark</item>
        <item name="colorOnPrimary">@color/white</item>

        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/streamflix_gray</item>
        <item name="colorSecondaryVariant">@color/streamflix_gray_light</item>
        <item name="colorOnSecondary">@color/white</item>

        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_dark</item>
        <item name="colorSurface">@color/surface_dark</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="colorOnBackground">@color/text_primary</item>

        <!-- Error colors -->
        <item name="colorError">@color/error_color</item>
        <item name="colorOnError">@color/white</item>

        <!-- Status bar -->
        <item name="android:statusBarColor">@color/background_dark</item>
        <item name="android:windowLightStatusBar">false</item>

        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/background_dark</item>
        <item name="android:windowLightNavigationBar">false</item>

        <!-- Window flags -->
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    </style>

    <!-- Splash screen theme -->
    <style name="Theme.StreamFlix.Splash" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="android:windowBackground">@color/background_dark</item>
        <item name="android:statusBarColor">@color/background_dark</item>
        <item name="android:navigationBarColor">@color/background_dark</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style>

    <!-- Player theme (fullscreen) -->
    <style name="Theme.StreamFlix.Player" parent="Theme.StreamFlix">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowBackground">@color/black</item>
    </style>
</resources>
