# 🎬 StreamFlix Android App - Android Studio Import Guide

## 🚀 Quick Start (Recommended)

### Method 1: Android Studio Import (Easiest)

1. **Open Android Studio**
2. **Select "Open an existing project"**
3. **Navigate to the `android` folder** (not the root folder)
4. **Click "OK"**
5. **Wait for Gradle sync** (Android Studio will automatically download Gradle Wrapper)
6. **Build > Make Project**
7. **Run on device/emulator**

### Method 2: Command Line (If you have Gradle installed)

```bash
# Navigate to android folder
cd android

# Clean and build
gradle clean
gradle assembleDebug

# Or for release
gradle assembleRelease
```

### Method 3: Fix Gradle Wrapper Manually

If you encounter Gradle Wrapper issues:

1. **Delete existing gradle folder**:
   ```bash
   rmdir /s gradle
   ```

2. **Open Android Studio**
3. **Import project** - Android Studio will recreate Gradle Wrapper
4. **Or download Gradle Wrapper manually**:
   - Download from: https://gradle.org/releases/
   - Extract to `gradle/wrapper/` folder

## 🔧 Configuration Before Building

### 1. Update API URLs in `app/build.gradle`:

```gradle
buildConfigField "String", "BASE_URL", "\"https://yourdomain.com/api/\""
buildConfigField "String", "IMAGE_BASE_URL", "\"https://image.tmdb.org/t/p/w500\""
```

### 2. Add your signing config (for release builds):

```gradle
android {
    signingConfigs {
        release {
            storeFile file('your-keystore.jks')
            storePassword 'your-store-password'
            keyAlias 'your-key-alias'
            keyPassword 'your-key-password'
        }
    }
}
```

## 📱 Build Outputs

After successful build:

- **Debug APK**: `app/build/outputs/apk/debug/app-debug.apk`
- **Release APK**: `app/build/outputs/apk/release/app-release.apk`

## 🛠️ System Requirements

- **Android Studio**: Hedgehog (2023.1.1) or later
- **JDK**: 17 or later
- **Android SDK**: 34
- **Minimum Android**: 7.0 (API 24)
- **Target Android**: 14 (API 34)

## 🎯 Features Included

✅ **Netflix-Style UI** - Material 3 design
✅ **Ad-Block Video Player** - Advanced ad blocking
✅ **Smart Downloads** - Background download manager
✅ **AI Recommendations** - Personalized suggestions
✅ **Offline Mode** - Complete offline support
✅ **Modern Architecture** - MVVM + Hilt + Compose

## 🐛 Troubleshooting

### Common Issues:

1. **Gradle Sync Failed**:
   - File > Invalidate Caches and Restart
   - Delete `.gradle` folder and re-sync

2. **Build Failed**:
   - Check Android SDK is installed
   - Update Android Studio to latest version

3. **Dependencies Not Found**:
   - Check internet connection
   - File > Sync Project with Gradle Files

4. **Emulator Issues**:
   - Create new AVD with API 24+
   - Enable hardware acceleration

## 📞 Support

If you encounter any issues:

1. **Check BUILD_STATUS.md** for complete project status
2. **Review PROJECT_SUMMARY.md** for feature details
3. **Open issue on GitHub** with error details

## 🎉 Success!

Once imported successfully, you'll have a **Netflix-level streaming app** with:

- 🚫 **Advanced Ad-Blocking**
- 🎬 **Professional Video Player**
- 🤖 **AI-Powered Recommendations**
- 📱 **Modern Android Architecture**
- 🎨 **Beautiful Material 3 UI**

**Happy Coding! 🚀📱**
