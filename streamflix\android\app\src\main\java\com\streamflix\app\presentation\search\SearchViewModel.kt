package com.streamflix.app.presentation.search

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.streamflix.app.data.model.*
import com.streamflix.app.data.repository.StreamFlixRepository
import com.streamflix.app.utils.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SearchViewModel @Inject constructor(
    private val repository: StreamFlixRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(SearchUiState())
    val uiState: StateFlow<SearchUiState> = _uiState.asStateFlow()

    private val _genresState = MutableStateFlow<Resource<List<Genre>>>(Resource.Loading())
    val genresState: StateFlow<Resource<List<Genre>>> = _genresState.asStateFlow()

    private val _recentSearches = MutableStateFlow<List<String>>(emptyList())
    val recentSearches: StateFlow<List<String>> = _recentSearches.asStateFlow()

    init {
        loadGenres()
        loadRecentSearches()
    }

    fun search(query: String) {
        if (query.isBlank()) {
            clearSearch()
            return
        }

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                searchResults = Resource.Loading()
            )

            repository.globalSearch(query).collect { resource ->
                _uiState.value = _uiState.value.copy(
                    searchResults = resource
                )
            }

            // Add to recent searches
            addToRecentSearches(query)
        }
    }

    fun clearSearch() {
        _uiState.value = _uiState.value.copy(
            searchResults = null
        )
    }

    fun loadGenres() {
        viewModelScope.launch {
            repository.getAllGenres().collect { resource ->
                _genresState.value = resource
            }
        }
    }

    private fun loadRecentSearches() {
        // Load from local storage
        // This would typically load from a local database or preferences
        _recentSearches.value = listOf(
            "Marvel", "Action Movies", "Netflix Series", "Horror", "Comedy"
        )
    }

    private fun addToRecentSearches(query: String) {
        val currentSearches = _recentSearches.value.toMutableList()
        
        // Remove if already exists
        currentSearches.remove(query)
        
        // Add to beginning
        currentSearches.add(0, query)
        
        // Keep only last 10 searches
        if (currentSearches.size > 10) {
            currentSearches.removeAt(currentSearches.size - 1)
        }
        
        _recentSearches.value = currentSearches
    }

    fun clearRecentSearches() {
        _recentSearches.value = emptyList()
    }
}

data class SearchUiState(
    val searchResults: Resource<SearchResult>? = null,
    val isLoading: Boolean = false,
    val error: String? = null
)
