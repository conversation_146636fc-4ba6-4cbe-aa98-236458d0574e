package com.streamflix.app.ui.theme

import androidx.compose.ui.graphics.Color

// Primary Colors - Netflix-inspired red
val StreamFlixRed = Color(0xFFE50914)
val StreamFlixRedDark = Color(0xFFB20710)
val StreamFlixRedLight = Color(0xFFFF4757)

// Secondary Colors
val StreamFlixGray = Color(0xFF221F1F)
val StreamFlixGrayLight = Color(0xFF2F2F2F)
val StreamFlixGrayDark = Color(0xFF141414)

// Background Colors
val BackgroundDark = Color(0xFF000000)
val BackgroundLight = Color(0xFFF5F5F5)
val SurfaceDark = Color(0xFF141414)
val SurfaceLight = Color(0xFFFFFFFF)

// Text Colors
val TextPrimary = Color(0xFFFFFFFF)
val TextSecondary = Color(0xFFB3B3B3)
val TextTertiary = Color(0xFF808080)
val TextPrimaryLight = Color(0xFF000000)
val TextSecondaryLight = Color(0xFF666666)

// Accent Colors
val AccentGreen = Color(0xFF46D369)
val AccentBlue = Color(0xFF0071EB)
val AccentYellow = Color(0xFFF5C518)
val AccentOrange = Color(0xFFFF6B35)

// Status Colors
val SuccessColor = Color(0xFF4CAF50)
val ErrorColor = Color(0xFFF44336)
val WarningColor = Color(0xFFFF9800)
val InfoColor = Color(0xFF2196F3)

// Rating Colors
val RatingExcellent = Color(0xFF4CAF50)  // 8.0+
val RatingGood = Color(0xFF8BC34A)       // 6.0-7.9
val RatingAverage = Color(0xFFFFEB3B)    // 4.0-5.9
val RatingPoor = Color(0xFFFF5722)       // Below 4.0

// Gradient Colors
val GradientStart = Color(0xFF000000)
val GradientEnd = Color(0x00000000)

// Card Colors
val CardDark = Color(0xFF1E1E1E)
val CardLight = Color(0xFFFFFFFF)
val CardBorder = Color(0xFF333333)

// Button Colors
val ButtonPrimary = StreamFlixRed
val ButtonSecondary = Color(0xFF333333)
val ButtonDisabled = Color(0xFF666666)

// Shimmer Colors
val ShimmerColorsDark = listOf(
    Color(0xFF1E1E1E),
    Color(0xFF2A2A2A),
    Color(0xFF1E1E1E)
)

val ShimmerColorsLight = listOf(
    Color(0xFFE0E0E0),
    Color(0xFFF0F0F0),
    Color(0xFFE0E0E0)
)

// Quality Badge Colors
val QualityHD = Color(0xFF4CAF50)
val QualityFHD = Color(0xFF2196F3)
val Quality4K = Color(0xFFFF9800)
val QualityCAM = Color(0xFFF44336)

// Genre Colors (for genre chips)
val GenreColors = listOf(
    Color(0xFFE91E63),
    Color(0xFF9C27B0),
    Color(0xFF673AB7),
    Color(0xFF3F51B5),
    Color(0xFF2196F3),
    Color(0xFF03DAC5),
    Color(0xFF4CAF50),
    Color(0xFF8BC34A),
    Color(0xFFCDDC39),
    Color(0xFFFFEB3B),
    Color(0xFFFFC107),
    Color(0xFFFF9800),
    Color(0xFFFF5722),
    Color(0xFF795548),
    Color(0xFF607D8B)
)

// Progress Colors
val ProgressBackground = Color(0xFF333333)
val ProgressForeground = StreamFlixRed
val ProgressWatched = Color(0xFF666666)

// Overlay Colors
val OverlayDark = Color(0x80000000)
val OverlayLight = Color(0x80FFFFFF)

// Border Colors
val BorderDark = Color(0xFF333333)
val BorderLight = Color(0xFFE0E0E0)

// Icon Colors
val IconPrimary = Color(0xFFFFFFFF)
val IconSecondary = Color(0xFFB3B3B3)
val IconDisabled = Color(0xFF666666)

// Special Colors
val PremiumGold = Color(0xFFFFD700)
val LiveIndicator = Color(0xFFFF0000)
val NewBadge = Color(0xFF00FF00)
val TrendingBadge = Color(0xFFFF6B35)

// Player Colors
val PlayerBackground = Color(0xFF000000)
val PlayerControls = Color(0x80000000)
val PlayerSeekBar = StreamFlixRed
val PlayerSeekBarBackground = Color(0xFF333333)

// Download Colors
val DownloadPending = Color(0xFFFFEB3B)
val DownloadProgress = Color(0xFF2196F3)
val DownloadCompleted = Color(0xFF4CAF50)
val DownloadError = Color(0xFFF44336)
val DownloadPaused = Color(0xFFFF9800)
