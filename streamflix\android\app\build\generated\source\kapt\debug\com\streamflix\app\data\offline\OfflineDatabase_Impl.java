package com.streamflix.app.data.offline;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@SuppressWarnings({"unchecked", "deprecation"})
public final class OfflineDatabase_Impl extends OfflineDatabase {
  private volatile OfflineContentDao _offlineContentDao;

  private volatile OfflineSearchHistoryDao _offlineSearchHistoryDao;

  private volatile OfflineUserPreferencesDao _offlineUserPreferencesDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(1) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `offline_content` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `contentId` INTEGER NOT NULL, `contentType` TEXT NOT NULL, `title` TEXT NOT NULL, `posterPath` TEXT, `backdropPath` TEXT, `overview` TEXT, `rating` REAL NOT NULL, `releaseDate` TEXT, `runtime` INTEGER, `genres` TEXT, `cachedAt` INTEGER NOT NULL, `lastAccessed` INTEGER NOT NULL, `fileSize` INTEGER NOT NULL, `isDownloaded` INTEGER NOT NULL, `numberOfSeasons` INTEGER, `numberOfEpisodes` INTEGER)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `offline_search_history` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `query` TEXT NOT NULL, `timestamp` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `offline_user_preferences` (`id` INTEGER NOT NULL, `preferencesJson` TEXT NOT NULL, `lastUpdated` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '94e2ec0b6a56fc9f9f979611ae461703')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `offline_content`");
        db.execSQL("DROP TABLE IF EXISTS `offline_search_history`");
        db.execSQL("DROP TABLE IF EXISTS `offline_user_preferences`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsOfflineContent = new HashMap<String, TableInfo.Column>(17);
        _columnsOfflineContent.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOfflineContent.put("contentId", new TableInfo.Column("contentId", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOfflineContent.put("contentType", new TableInfo.Column("contentType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOfflineContent.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOfflineContent.put("posterPath", new TableInfo.Column("posterPath", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOfflineContent.put("backdropPath", new TableInfo.Column("backdropPath", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOfflineContent.put("overview", new TableInfo.Column("overview", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOfflineContent.put("rating", new TableInfo.Column("rating", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOfflineContent.put("releaseDate", new TableInfo.Column("releaseDate", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOfflineContent.put("runtime", new TableInfo.Column("runtime", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOfflineContent.put("genres", new TableInfo.Column("genres", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOfflineContent.put("cachedAt", new TableInfo.Column("cachedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOfflineContent.put("lastAccessed", new TableInfo.Column("lastAccessed", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOfflineContent.put("fileSize", new TableInfo.Column("fileSize", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOfflineContent.put("isDownloaded", new TableInfo.Column("isDownloaded", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOfflineContent.put("numberOfSeasons", new TableInfo.Column("numberOfSeasons", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOfflineContent.put("numberOfEpisodes", new TableInfo.Column("numberOfEpisodes", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysOfflineContent = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesOfflineContent = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoOfflineContent = new TableInfo("offline_content", _columnsOfflineContent, _foreignKeysOfflineContent, _indicesOfflineContent);
        final TableInfo _existingOfflineContent = TableInfo.read(db, "offline_content");
        if (!_infoOfflineContent.equals(_existingOfflineContent)) {
          return new RoomOpenHelper.ValidationResult(false, "offline_content(com.streamflix.app.data.offline.OfflineContentItem).\n"
                  + " Expected:\n" + _infoOfflineContent + "\n"
                  + " Found:\n" + _existingOfflineContent);
        }
        final HashMap<String, TableInfo.Column> _columnsOfflineSearchHistory = new HashMap<String, TableInfo.Column>(3);
        _columnsOfflineSearchHistory.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOfflineSearchHistory.put("query", new TableInfo.Column("query", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOfflineSearchHistory.put("timestamp", new TableInfo.Column("timestamp", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysOfflineSearchHistory = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesOfflineSearchHistory = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoOfflineSearchHistory = new TableInfo("offline_search_history", _columnsOfflineSearchHistory, _foreignKeysOfflineSearchHistory, _indicesOfflineSearchHistory);
        final TableInfo _existingOfflineSearchHistory = TableInfo.read(db, "offline_search_history");
        if (!_infoOfflineSearchHistory.equals(_existingOfflineSearchHistory)) {
          return new RoomOpenHelper.ValidationResult(false, "offline_search_history(com.streamflix.app.data.offline.OfflineSearchHistory).\n"
                  + " Expected:\n" + _infoOfflineSearchHistory + "\n"
                  + " Found:\n" + _existingOfflineSearchHistory);
        }
        final HashMap<String, TableInfo.Column> _columnsOfflineUserPreferences = new HashMap<String, TableInfo.Column>(3);
        _columnsOfflineUserPreferences.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOfflineUserPreferences.put("preferencesJson", new TableInfo.Column("preferencesJson", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOfflineUserPreferences.put("lastUpdated", new TableInfo.Column("lastUpdated", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysOfflineUserPreferences = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesOfflineUserPreferences = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoOfflineUserPreferences = new TableInfo("offline_user_preferences", _columnsOfflineUserPreferences, _foreignKeysOfflineUserPreferences, _indicesOfflineUserPreferences);
        final TableInfo _existingOfflineUserPreferences = TableInfo.read(db, "offline_user_preferences");
        if (!_infoOfflineUserPreferences.equals(_existingOfflineUserPreferences)) {
          return new RoomOpenHelper.ValidationResult(false, "offline_user_preferences(com.streamflix.app.data.offline.OfflineUserPreferences).\n"
                  + " Expected:\n" + _infoOfflineUserPreferences + "\n"
                  + " Found:\n" + _existingOfflineUserPreferences);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "94e2ec0b6a56fc9f9f979611ae461703", "7f2ff52644ef4e4b31b2b85c2c2db177");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "offline_content","offline_search_history","offline_user_preferences");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    try {
      super.beginTransaction();
      _db.execSQL("DELETE FROM `offline_content`");
      _db.execSQL("DELETE FROM `offline_search_history`");
      _db.execSQL("DELETE FROM `offline_user_preferences`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(OfflineContentDao.class, OfflineContentDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(OfflineSearchHistoryDao.class, OfflineSearchHistoryDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(OfflineUserPreferencesDao.class, OfflineUserPreferencesDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public OfflineContentDao offlineContentDao() {
    if (_offlineContentDao != null) {
      return _offlineContentDao;
    } else {
      synchronized(this) {
        if(_offlineContentDao == null) {
          _offlineContentDao = new OfflineContentDao_Impl(this);
        }
        return _offlineContentDao;
      }
    }
  }

  @Override
  public OfflineSearchHistoryDao searchHistoryDao() {
    if (_offlineSearchHistoryDao != null) {
      return _offlineSearchHistoryDao;
    } else {
      synchronized(this) {
        if(_offlineSearchHistoryDao == null) {
          _offlineSearchHistoryDao = new OfflineSearchHistoryDao_Impl(this);
        }
        return _offlineSearchHistoryDao;
      }
    }
  }

  @Override
  public OfflineUserPreferencesDao userPreferencesDao() {
    if (_offlineUserPreferencesDao != null) {
      return _offlineUserPreferencesDao;
    } else {
      synchronized(this) {
        if(_offlineUserPreferencesDao == null) {
          _offlineUserPreferencesDao = new OfflineUserPreferencesDao_Impl(this);
        }
        return _offlineUserPreferencesDao;
      }
    }
  }
}
