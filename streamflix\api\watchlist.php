<?php
require_once '../includes/functions.php';

header('Content-Type: application/json');

if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'Please login first']);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);
$content_id = isset($input['content_id']) ? (int)$input['content_id'] : 0;
$content_type = isset($input['content_type']) ? sanitizeInput($input['content_type']) : '';
$action = isset($input['action']) ? sanitizeInput($input['action']) : '';

if (!$content_id || !$content_type || !$action) {
    echo json_encode(['success' => false, 'message' => 'Invalid parameters']);
    exit();
}

try {
    $db = new Database();
    $conn = $db->connect();
    $user_id = $_SESSION['user_id'];
    
    if ($action === 'toggle') {
        // Check if already in watchlist
        $stmt = $conn->prepare("
            SELECT id FROM watchlist 
            WHERE user_id = :user_id AND content_id = :content_id AND content_type = :content_type
        ");
        $stmt->execute([
            ':user_id' => $user_id,
            ':content_id' => $content_id,
            ':content_type' => $content_type
        ]);
        
        $exists = $stmt->fetch();
        
        if ($exists) {
            // Remove from watchlist
            $stmt = $conn->prepare("
                DELETE FROM watchlist 
                WHERE user_id = :user_id AND content_id = :content_id AND content_type = :content_type
            ");
            $stmt->execute([
                ':user_id' => $user_id,
                ':content_id' => $content_id,
                ':content_type' => $content_type
            ]);
            
            echo json_encode([
                'success' => true,
                'added' => false,
                'message' => 'Removed from watchlist'
            ]);
        } else {
            // Add to watchlist
            $stmt = $conn->prepare("
                INSERT INTO watchlist (user_id, content_id, content_type, added_at)
                VALUES (:user_id, :content_id, :content_type, NOW())
            ");
            $stmt->execute([
                ':user_id' => $user_id,
                ':content_id' => $content_id,
                ':content_type' => $content_type
            ]);
            
            echo json_encode([
                'success' => true,
                'added' => true,
                'message' => 'Added to watchlist'
            ]);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
