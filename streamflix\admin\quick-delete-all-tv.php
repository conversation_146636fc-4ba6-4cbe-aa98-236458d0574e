<?php
session_start();
require_once '../config/database.php';

// Simple admin check
$is_admin = false;
if (isset($_SESSION['user_id'])) {
    if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
        $is_admin = true;
    } elseif (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
        $is_admin = true;
    }
}

if (!$is_admin) {
    try {
        $db = new Database();
        $conn = $db->connect();
        
        if (isset($_SESSION['user_id'])) {
            $stmt = $conn->prepare("SELECT role FROM users WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && $user['role'] === 'admin') {
                $is_admin = true;
                $_SESSION['role'] = 'admin';
            }
        }
    } catch (Exception $e) {
        // Database check failed
    }
}

if (!$is_admin) {
    header('Location: ../login.php');
    exit;
}

// Database connection
if (!isset($conn)) {
    $db = new Database();
    $conn = $db->connect();
}

echo "<h1>🗑️ Quick Delete All TV Series</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
    .container { max-width: 800px; margin: 0 auto; }
    .section { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    .danger { background: #f8d7da; color: #721c24; border: 3px solid #dc3545; }
    .btn { padding: 15px 30px; background: #007bff; color: white; text-decoration: none; border-radius: 6px; display: inline-block; margin: 10px; border: none; cursor: pointer; font-size: 18px; font-weight: bold; }
    .btn-danger { background: #dc3545; }
    .btn-success { background: #28a745; }
    .btn-secondary { background: #6c757d; }
    .stat-number { font-size: 3rem; font-weight: bold; color: #dc3545; text-align: center; margin: 20px 0; }
    .countdown { font-size: 2rem; color: #dc3545; text-align: center; margin: 20px 0; }
</style>";

echo "<div class='container'>";

try {
    // Get current count
    $stmt = $conn->query("SELECT COUNT(*) FROM tv_shows");
    $total_tv_shows = $stmt->fetchColumn();
    
    // Handle deletion
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';
        $confirm_code = $_POST['confirm_code'] ?? '';
        
        if ($action === 'delete_all' && $confirm_code === 'DELETE-ALL-TV-SERIES') {
            echo "<div class='section'>";
            echo "<h2>🗑️ Deleting All TV Series...</h2>";
            
            try {
                // Start transaction
                $conn->beginTransaction();
                
                // Delete genre relations first
                $stmt = $conn->query("DELETE FROM tv_show_genres");
                $deleted_genres = $stmt->rowCount();
                
                // Delete all TV series
                $stmt = $conn->query("DELETE FROM tv_shows");
                $deleted_count = $stmt->rowCount();
                
                // Commit transaction
                $conn->commit();
                
                echo "<div class='success'>";
                echo "<h3>✅ Deletion Complete!</h3>";
                echo "<p><strong>TV Series Deleted:</strong> {$deleted_count}</p>";
                echo "<p><strong>Genre Relations Deleted:</strong> {$deleted_genres}</p>";
                echo "<p><strong>Database Status:</strong> All TV series have been permanently removed</p>";
                echo "</div>";
                
                // Verify deletion
                $stmt = $conn->query("SELECT COUNT(*) FROM tv_shows");
                $remaining = $stmt->fetchColumn();
                
                if ($remaining == 0) {
                    echo "<div class='success'>";
                    echo "<h3>✅ Verification Successful</h3>";
                    echo "<p>Database confirmed: 0 TV series remaining</p>";
                    echo "</div>";
                } else {
                    echo "<div class='warning'>";
                    echo "<h3>⚠️ Warning</h3>";
                    echo "<p>{$remaining} TV series still remain in database</p>";
                    echo "</div>";
                }
                
            } catch (Exception $e) {
                $conn->rollback();
                echo "<div class='error'>";
                echo "<h3>❌ Deletion Failed!</h3>";
                echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
                echo "</div>";
            }
            echo "</div>";
            
            // Update count
            $stmt = $conn->query("SELECT COUNT(*) FROM tv_shows");
            $total_tv_shows = $stmt->fetchColumn();
        }
    }
    
    if ($total_tv_shows > 0) {
        echo "<div class='section danger'>";
        echo "<h2>🚨 DANGER ZONE 🚨</h2>";
        echo "<div class='stat-number'>{$total_tv_shows}</div>";
        echo "<p style='text-align: center; font-size: 1.2rem;'><strong>TV Series will be PERMANENTLY DELETED</strong></p>";
        echo "</div>";
        
        echo "<div class='section warning'>";
        echo "<h3>⚠️ What This Will Do:</h3>";
        echo "<ul>";
        echo "<li>🗑️ Delete ALL {$total_tv_shows} TV series from database</li>";
        echo "<li>🏷️ Delete ALL genre relations for TV series</li>";
        echo "<li>💾 Free up database space</li>";
        echo "<li>🚫 This action is IRREVERSIBLE</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div class='section'>";
        echo "<h3>🔐 Confirmation Required</h3>";
        echo "<p>To proceed with deletion, you must type the exact confirmation code:</p>";
        
        echo "<form method='POST' id='deleteForm'>";
        echo "<input type='hidden' name='action' value='delete_all'>";
        
        echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;'>";
        echo "<h4>Type this code exactly:</h4>";
        echo "<div style='font-size: 1.5rem; font-weight: bold; color: #dc3545; margin: 10px 0;'>DELETE-ALL-TV-SERIES</div>";
        echo "<input type='text' name='confirm_code' placeholder='Enter confirmation code' style='padding: 10px; font-size: 16px; width: 300px; text-align: center;' required>";
        echo "</div>";
        
        echo "<div style='text-align: center; margin: 30px 0;'>";
        echo "<button type='submit' class='btn btn-danger' onclick='return finalConfirm()'>🗑️ DELETE ALL {$total_tv_shows} TV SERIES</button>";
        echo "</div>";
        
        echo "</form>";
        echo "</div>";
        
        // Show some examples
        echo "<div class='section'>";
        echo "<h3>📋 Examples of TV Series to be Deleted</h3>";
        
        $stmt = $conn->query("
            SELECT name, content_type, vote_average, first_air_date 
            FROM tv_shows 
            ORDER BY RAND() 
            LIMIT 10
        ");
        $examples = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($examples)) {
            echo "<ul>";
            foreach ($examples as $example) {
                $type = $example['content_type'] ?? 'Regular';
                $year = $example['first_air_date'] ? date('Y', strtotime($example['first_air_date'])) : 'N/A';
                echo "<li><strong>" . htmlspecialchars($example['name']) . "</strong> ({$type}, {$year}) - Rating: {$example['vote_average']}/10</li>";
            }
            echo "</ul>";
            echo "<p><em>... and " . ($total_tv_shows - 10) . " more TV series</em></p>";
        }
        echo "</div>";
        
    } else {
        echo "<div class='section success'>";
        echo "<h2>✅ No TV Series Found</h2>";
        echo "<div class='stat-number' style='color: #28a745;'>0</div>";
        echo "<p style='text-align: center;'>There are no TV series in the database.</p>";
        echo "</div>";
    }
    
    // Quick actions
    echo "<div class='section'>";
    echo "<h3>🔧 Quick Actions</h3>";
    echo "<div style='text-align: center;'>";
    echo "<a href='index.php' class='btn btn-secondary'>📊 Admin Dashboard</a>";
    echo "<a href='bulk-delete-tv-series.php' class='btn btn-secondary'>🗑️ Advanced Delete Options</a>";
    echo "<a href='import.php' class='btn btn-success'>📥 Import New Content</a>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section error'>";
    echo "<h3>❌ Error</h3>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div>";

echo "<script>
function finalConfirm() {
    const confirmCode = document.querySelector('input[name=\"confirm_code\"]').value;
    
    if (confirmCode !== 'DELETE-ALL-TV-SERIES') {
        alert('Incorrect confirmation code. Please type exactly: DELETE-ALL-TV-SERIES');
        return false;
    }
    
    return confirm('FINAL WARNING: This will permanently delete ALL TV series!\\n\\nAre you absolutely sure?\\n\\nClick OK to proceed with deletion.');
}

// Auto-focus on confirmation input
document.addEventListener('DOMContentLoaded', function() {
    const input = document.querySelector('input[name=\"confirm_code\"]');
    if (input) {
        input.focus();
    }
});
</script>";
?>
