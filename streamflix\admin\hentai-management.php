<?php
session_start();
require_once '../config/database.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$db = new Database();
$conn = $db->connect();

// Handle actions
$message = '';
$messageType = '';

if ($_POST['action'] ?? '' === 'convert_to_hentai') {
    $selected_ids = $_POST['selected_items'] ?? [];
    $update_genres = isset($_POST['update_genres']);
    
    if (!empty($selected_ids)) {
        $success_count = 0;
        $error_count = 0;
        
        foreach ($selected_ids as $id) {
            try {
                // Update content type
                $stmt = $conn->prepare("UPDATE tv_shows SET content_type = 'hentai' WHERE id = ?");
                $stmt->execute([$id]);
                
                // Add hentai genre if requested
                if ($update_genres) {
                    // Get or create hentai genre
                    $stmt = $conn->prepare("SELECT id FROM genres WHERE LOWER(name) = 'hentai'");
                    $stmt->execute();
                    $hentai_genre = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if (!$hentai_genre) {
                        $stmt = $conn->prepare("INSERT INTO genres (tmdb_id, name) VALUES (99999, 'Hentai')");
                        $stmt->execute();
                        $hentai_genre_id = $conn->lastInsertId();
                    } else {
                        $hentai_genre_id = $hentai_genre['id'];
                    }
                    
                    // Add genre relation if not exists
                    $stmt = $conn->prepare("INSERT IGNORE INTO tv_show_genres (tv_show_id, genre_id) VALUES (?, ?)");
                    $stmt->execute([$id, $hentai_genre_id]);
                }
                
                $success_count++;
            } catch (Exception $e) {
                $error_count++;
            }
        }
        
        $message = "Successfully converted {$success_count} items to hentai. {$error_count} errors.";
        $messageType = $success_count > 0 ? 'success' : 'error';
    }
}

if ($_POST['action'] ?? '' === 'revert_from_hentai') {
    $selected_ids = $_POST['selected_items'] ?? [];
    
    if (!empty($selected_ids)) {
        $success_count = 0;
        foreach ($selected_ids as $id) {
            try {
                $stmt = $conn->prepare("UPDATE tv_shows SET content_type = 'tv_show' WHERE id = ?");
                $stmt->execute([$id]);
                $success_count++;
            } catch (Exception $e) {
                // Handle error
            }
        }
        
        $message = "Successfully reverted {$success_count} items from hentai to TV show.";
        $messageType = 'success';
    }
}

// Get statistics
$stats = [];
$stmt = $conn->query("SELECT content_type, COUNT(*) as count FROM tv_shows GROUP BY content_type");
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $stats[$row['content_type'] ?? 'null'] = $row['count'];
}

// Get hentai servers count
$stmt = $conn->query("SELECT COUNT(*) FROM embed_servers WHERE hentai_url IS NOT NULL AND hentai_url != ''");
$hentai_servers_count = $stmt->fetchColumn();

// Get potential hentai content
$search_term = $_GET['search'] ?? '';
$filter_type = $_GET['filter'] ?? 'potential';

$where_conditions = [];
$params = [];

if ($filter_type === 'potential') {
    $where_conditions[] = "(
        LOWER(name) LIKE '%hentai%' OR 
        LOWER(name) LIKE '%ecchi%' OR 
        LOWER(name) LIKE '%adult%' OR 
        LOWER(overview) LIKE '%hentai%' OR 
        LOWER(overview) LIKE '%adult%'
    ) AND (content_type != 'hentai' OR content_type IS NULL)";
} elseif ($filter_type === 'hentai') {
    $where_conditions[] = "content_type = 'hentai'";
} elseif ($filter_type === 'tv_show') {
    $where_conditions[] = "(content_type = 'tv_show' OR content_type IS NULL)";
}

if (!empty($search_term)) {
    $where_conditions[] = "(name LIKE ? OR overview LIKE ?)";
    $params[] = "%{$search_term}%";
    $params[] = "%{$search_term}%";
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

$stmt = $conn->prepare("
    SELECT id, tmdb_id, name, overview, content_type, vote_average, first_air_date
    FROM tv_shows 
    {$where_clause}
    ORDER BY name ASC 
    LIMIT 50
");
$stmt->execute($params);
$content_items = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hentai Content Management - StreamFlix Admin</title>
    <link rel="stylesheet" href="assets/admin-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .hentai-management {
            padding: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .stat-card.hentai {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        
        .stat-card.servers {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .filters {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .filter-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .content-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .table-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .bulk-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .content-item {
            display: grid;
            grid-template-columns: 40px 80px 1fr 150px 120px 100px;
            gap: 15px;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            align-items: center;
        }
        
        .content-item:hover {
            background: #f8f9fa;
        }
        
        .content-poster {
            width: 60px;
            height: 90px;
            border-radius: 8px;
            object-fit: cover;
        }
        
        .content-info h4 {
            margin: 0 0 5px 0;
            color: #333;
        }
        
        .content-meta {
            font-size: 0.85rem;
            color: #666;
        }
        
        .content-type {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .type-hentai {
            background: #ff6b6b;
            color: white;
        }
        
        .type-tv_show {
            background: #4ecdc4;
            color: white;
        }
        
        .type-null {
            background: #95a5a6;
            color: white;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-small {
            padding: 5px 10px;
            font-size: 0.8rem;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        
        .btn-hentai {
            background: #ff6b6b;
            color: white;
        }
        
        .btn-revert {
            background: #4ecdc4;
            color: white;
        }
        
        .btn-view {
            background: #667eea;
            color: white;
        }
        
        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        @media (max-width: 768px) {
            .content-item {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <nav class="admin-sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-cog"></i> Admin Panel</h2>
            </div>
            <ul class="sidebar-menu">
                <li><a href="index.php"><i class="fas fa-dashboard"></i> Dashboard</a></li>
                <li><a href="movies.php"><i class="fas fa-film"></i> Movies</a></li>
                <li><a href="tv-shows.php"><i class="fas fa-tv"></i> TV Shows</a></li>
                <li><a href="hentai-management.php" class="active"><i class="fas fa-heart"></i> Hentai Management</a></li>
                <li><a href="servers.php"><i class="fas fa-server"></i> Servers</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> Users</a></li>
                <li><a href="import.php"><i class="fas fa-upload"></i> Import</a></li>
                <li><a href="settings.php"><i class="fas fa-cog"></i> Settings</a></li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="admin-main">
            <div class="hentai-management">
                <div class="page-header">
                    <h1><i class="fas fa-heart"></i> Hentai Content Management</h1>
                    <p>Manage and convert content between TV shows and hentai categories</p>
                </div>

                <?php if ($message): ?>
                <div class="message <?= $messageType ?>">
                    <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-circle' ?>"></i>
                    <?= htmlspecialchars($message) ?>
                </div>
                <?php endif; ?>

                <!-- Statistics -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?= $stats['tv_show'] ?? 0 ?></div>
                        <div>TV Shows</div>
                    </div>
                    <div class="stat-card hentai">
                        <div class="stat-number"><?= $stats['hentai'] ?? 0 ?></div>
                        <div>Hentai Content</div>
                    </div>
                    <div class="stat-card servers">
                        <div class="stat-number"><?= $hentai_servers_count ?></div>
                        <div>Hentai Servers</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?= $stats['null'] ?? 0 ?></div>
                        <div>Uncategorized</div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="filters">
                    <form method="GET" class="filter-row">
                        <div>
                            <label for="filter">Filter:</label>
                            <select name="filter" id="filter" onchange="this.form.submit()">
                                <option value="potential" <?= $filter_type === 'potential' ? 'selected' : '' ?>>Potential Hentai</option>
                                <option value="hentai" <?= $filter_type === 'hentai' ? 'selected' : '' ?>>Current Hentai</option>
                                <option value="tv_show" <?= $filter_type === 'tv_show' ? 'selected' : '' ?>>TV Shows</option>
                            </select>
                        </div>
                        <div>
                            <label for="search">Search:</label>
                            <input type="text" name="search" id="search" value="<?= htmlspecialchars($search_term) ?>" placeholder="Search by name or description">
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Search
                        </button>
                    </form>
                </div>

                <!-- Content Table -->
                <div class="content-table">
                    <div class="table-header">
                        <h3><i class="fas fa-list"></i> Content Items (<?= count($content_items) ?>)</h3>
                        <div class="bulk-actions">
                            <button onclick="selectAll()" class="btn btn-secondary">
                                <i class="fas fa-check-square"></i> Select All
                            </button>
                            <button onclick="selectNone()" class="btn btn-secondary">
                                <i class="fas fa-square"></i> Select None
                            </button>
                        </div>
                    </div>

                    <form method="POST" id="bulkForm">
                        <div class="bulk-actions" style="padding: 15px 20px; background: #f8f9fa; border-bottom: 1px solid #eee;">
                            <label>
                                <input type="checkbox" name="update_genres" checked>
                                Update genres when converting
                            </label>
                            <div style="margin-left: auto; display: flex; gap: 10px;">
                                <button type="submit" name="action" value="convert_to_hentai" class="btn btn-danger" onclick="return confirm('Convert selected items to hentai?')">
                                    <i class="fas fa-heart"></i> Convert to Hentai
                                </button>
                                <button type="submit" name="action" value="revert_from_hentai" class="btn btn-info" onclick="return confirm('Revert selected items to TV show?')">
                                    <i class="fas fa-tv"></i> Revert to TV Show
                                </button>
                            </div>
                        </div>

                        <?php if (empty($content_items)): ?>
                        <div style="padding: 40px; text-align: center; color: #666;">
                            <i class="fas fa-search" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.3;"></i>
                            <h3>No content found</h3>
                            <p>Try adjusting your search criteria or filters.</p>
                        </div>
                        <?php else: ?>

                        <!-- Table Headers -->
                        <div class="content-item" style="background: #f8f9fa; font-weight: bold; border-bottom: 2px solid #dee2e6;">
                            <div>Select</div>
                            <div>Poster</div>
                            <div>Title & Info</div>
                            <div>Type</div>
                            <div>Rating</div>
                            <div>Actions</div>
                        </div>

                        <?php foreach ($content_items as $item): ?>
                        <div class="content-item">
                            <div>
                                <input type="checkbox" name="selected_items[]" value="<?= $item['id'] ?>" class="item-checkbox">
                            </div>
                            <div>
                                <img src="https://image.tmdb.org/t/p/w92<?= htmlspecialchars($item['poster_path'] ?? '') ?>"
                                     alt="<?= htmlspecialchars($item['name']) ?>"
                                     class="content-poster"
                                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iOTAiIHZpZXdCb3g9IjAgMCA2MCA5MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjkwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAzMEgzMFYzNUgyMFYzMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTIwIDQwSDQwVjQ1SDIwVjQwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMjAgNTBIMzVWNTVIMjBWNTBaIiBmaWxsPSIjOUNBM0FGIi8+Cjwvc3ZnPgo='">
                            </div>
                            <div class="content-info">
                                <h4><?= htmlspecialchars($item['name']) ?></h4>
                                <div class="content-meta">
                                    <div><strong>TMDB ID:</strong> <?= $item['tmdb_id'] ?></div>
                                    <div><strong>Year:</strong> <?= $item['first_air_date'] ? date('Y', strtotime($item['first_air_date'])) : 'N/A' ?></div>
                                    <?php if (!empty($item['overview'])): ?>
                                    <div style="margin-top: 5px;">
                                        <strong>Overview:</strong>
                                        <?= htmlspecialchars(strlen($item['overview']) > 100 ? substr($item['overview'], 0, 100) . '...' : $item['overview']) ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div>
                                <span class="content-type type-<?= $item['content_type'] ?? 'null' ?>">
                                    <?= $item['content_type'] ?? 'Uncategorized' ?>
                                </span>
                            </div>
                            <div>
                                <div style="display: flex; align-items: center; gap: 5px;">
                                    <i class="fas fa-star" style="color: #ffc107;"></i>
                                    <?= number_format($item['vote_average'], 1) ?>
                                </div>
                            </div>
                            <div class="action-buttons">
                                <a href="../player.php?id=<?= $item['tmdb_id'] ?>&type=tv_show"
                                   target="_blank"
                                   class="btn-small btn-view"
                                   title="View Player">
                                    <i class="fas fa-play"></i>
                                </a>
                                <a href="edit-tv-show.php?id=<?= $item['id'] ?>"
                                   class="btn-small btn-primary"
                                   title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <?php if ($item['content_type'] !== 'hentai'): ?>
                                <button type="button"
                                        onclick="convertSingle(<?= $item['id'] ?>, '<?= htmlspecialchars($item['name']) ?>')"
                                        class="btn-small btn-hentai"
                                        title="Convert to Hentai">
                                    <i class="fas fa-heart"></i>
                                </button>
                                <?php else: ?>
                                <button type="button"
                                        onclick="revertSingle(<?= $item['id'] ?>, '<?= htmlspecialchars($item['name']) ?>')"
                                        class="btn-small btn-revert"
                                        title="Revert to TV Show">
                                    <i class="fas fa-tv"></i>
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </form>
                </div>

                <!-- Quick Actions -->
                <div style="margin-top: 30px; display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <h3><i class="fas fa-magic"></i> Quick Actions</h3>
                        <div style="display: flex; flex-direction: column; gap: 10px; margin-top: 15px;">
                            <a href="../convert_hentai_content.php" target="_blank" class="btn btn-warning">
                                <i class="fas fa-search"></i> Auto-Detect Hentai Content
                            </a>
                            <a href="../test_hentai_servers.php" target="_blank" class="btn btn-info">
                                <i class="fas fa-server"></i> Test Hentai Servers
                            </a>
                            <a href="servers.php" class="btn btn-primary">
                                <i class="fas fa-cog"></i> Manage Servers
                            </a>
                        </div>
                    </div>

                    <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <h3><i class="fas fa-info-circle"></i> Help & Tips</h3>
                        <ul style="margin-top: 15px; padding-left: 20px; color: #666;">
                            <li>Use "Potential Hentai" filter to find content that might be hentai</li>
                            <li>Always test servers after converting content</li>
                            <li>Check content manually before bulk conversion</li>
                            <li>Hentai content uses different server URLs</li>
                        </ul>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function selectAll() {
            document.querySelectorAll('.item-checkbox').forEach(cb => cb.checked = true);
        }

        function selectNone() {
            document.querySelectorAll('.item-checkbox').forEach(cb => cb.checked = false);
        }

        function convertSingle(id, name) {
            if (confirm(`Convert "${name}" to hentai content?`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="convert_to_hentai">
                    <input type="hidden" name="selected_items[]" value="${id}">
                    <input type="hidden" name="update_genres" value="1">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function revertSingle(id, name) {
            if (confirm(`Revert "${name}" back to TV show?`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="revert_from_hentai">
                    <input type="hidden" name="selected_items[]" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Auto-submit search form on Enter
        document.getElementById('search').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                this.form.submit();
            }
        });
    </script>
</body>
</html>
