<?php
require_once 'includes/functions.php';

// Get parameters
$tmdb_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$type = isset($_GET['type']) ? sanitizeInput($_GET['type']) : 'movie';
$season = isset($_GET['season']) ? (int)$_GET['season'] : 1;
$episode = isset($_GET['episode']) ? (int)$_GET['episode'] : 1;

if (!$tmdb_id) {
    header('Location: index.php');
    exit();
}

$streamflix = new StreamFlix();

// Get content details
$content = null;
try {
    $db = new Database();
    $conn = $db->connect();

    if ($type === 'movie') {
        $stmt = $conn->prepare("SELECT * FROM movies WHERE tmdb_id = :tmdb_id");
        $stmt->execute([':tmdb_id' => $tmdb_id]);
        $content = $stmt->fetch(PDO::FETCH_ASSOC);
    } else {
        $stmt = $conn->prepare("SELECT * FROM tv_shows WHERE tmdb_id = :tmdb_id");
        $stmt->execute([':tmdb_id' => $tmdb_id]);
        $content = $stmt->fetch(PDO::FETCH_ASSOC);

        // Get episode details for TV shows
        if ($content) {
            $stmt = $conn->prepare("
                SELECT e.*, s.season_number
                FROM episodes e
                JOIN seasons s ON e.season_id = s.id
                WHERE s.tv_show_id = :tv_show_id AND s.season_number = :season AND e.episode_number = :episode
            ");
            $stmt->execute([
                ':tv_show_id' => $content['id'],
                ':season' => $season,
                ':episode' => $episode
            ]);
            $episode_data = $stmt->fetch(PDO::FETCH_ASSOC);
        }
    }
} catch (Exception $e) {
    error_log("Content fetch error: " . $e->getMessage());
}

if (!$content) {
    header('Location: index.php');
    exit();
}

// Check content type
$is_hentai = false;
$is_anime = false;

// Generate embed URLs with fallback
$embed_urls = $streamflix->getEmbedUrls($type, $tmdb_id, $season, $episode, $is_hentai, $is_anime);

// Fallback: If no URLs from function, get directly from database
if (empty($embed_urls)) {
    try {
        $db = new Database();
        $conn = $db->connect();

        $stmt = $conn->prepare("SELECT * FROM embed_servers WHERE is_active = 1 ORDER BY priority ASC");
        $stmt->execute();
        $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($servers as $server) {
            $url = '';
            if ($type === 'movie' && isset($server['movie_url'])) {
                $url = str_replace('{id}', $tmdb_id, $server['movie_url']);
            } elseif ($type === 'tv_show' && isset($server['tv_url'])) {
                $url = str_replace(['{id}', '{season}', '{episode}'], [$tmdb_id, $season, $episode], $server['tv_url']);
            }

            if (!empty($url)) {
                $embed_urls[] = [
                    'name' => $server['name'],
                    'url' => $url,
                    'priority' => $server['priority'] ?? 1
                ];
            }
        }

        // Sort by priority
        usort($embed_urls, function($a, $b) {
            return $a['priority'] - $b['priority'];
        });

    } catch (Exception $e) {
        error_log("Player fallback error: " . $e->getMessage());
        $embed_urls = [];
    }
}

$title = $content['title'] ?? $content['name'];
if ($type === 'tv_show') {
    $title .= " - S{$season}E{$episode}";
    if (isset($episode_data) && $episode_data['name']) {
        $title .= ": " . $episode_data['name'];
    }
}

// Get related content for "You Also Like" section
$related_content = [];
try {
    if ($type === 'movie') {
        $stmt = $conn->prepare("
            SELECT m.*, GROUP_CONCAT(g.name) as genres
            FROM movies m
            LEFT JOIN movie_genres mg ON m.id = mg.movie_id
            LEFT JOIN genres g ON mg.genre_id = g.id
            WHERE m.tmdb_id != ? AND m.is_featured = 1
            GROUP BY m.id
            ORDER BY m.vote_average DESC
            LIMIT 8
        ");
        $stmt->execute([$tmdb_id]);
        $related_content = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } else {
        $stmt = $conn->prepare("
            SELECT t.*, GROUP_CONCAT(g.name) as genres
            FROM tv_shows t
            LEFT JOIN tv_show_genres tg ON t.id = tg.tv_show_id
            LEFT JOIN genres g ON tg.genre_id = g.id
            WHERE t.tmdb_id != ? AND t.is_featured = 1
            GROUP BY t.id
            ORDER BY t.vote_average DESC
            LIMIT 8
        ");
        $stmt->execute([$tmdb_id]);
        $related_content = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (Exception $e) {
    error_log("Related content error: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($title); ?> - <?php echo SITE_NAME; ?></title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        :root {
            --primary-color: #e50914;
            --secondary-color: #221f1f;
            --dark-bg: #0a0a0a;
            --text-primary: #ffffff;
            --text-secondary: #b3b3b3;
            --border-color: #333333;
            --accent-color: #00d4ff;
            --success-color: #00ff88;
            --warning-color: #ffb800;
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #0f0f0f 50%, #1a1a1a 75%, #0a0a0a 100%);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
            line-height: 1.6;
        }

        /* Modern Container Layout */
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 70px 15px 30px;
        }

        /* Player Section */
        .player-section {
            background: linear-gradient(145deg, rgba(30, 30, 30, 0.95), rgba(20, 20, 20, 0.98));
            border-radius: 16px;
            overflow: hidden;
            box-shadow:
                0 15px 40px rgba(0, 0, 0, 0.5),
                0 0 0 1px rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            position: relative;
            margin-bottom: 25px;
        }

        .player-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--primary-color));
            opacity: 0.8;
        }

        .player-wrapper {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.4), inset 0 1px 0 rgba(255,255,255,0.1);
            border: 1px solid rgba(229, 9, 20, 0.2);
        }

        .player-wrapper::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at center, transparent 0%, rgba(0,0,0,0.2) 100%);
            pointer-events: none;
            z-index: 1;
        }

        .player-iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            border-radius: 12px;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .player-iframe:hover {
            box-shadow: 0 0 20px rgba(229, 9, 20, 0.3);
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(10, 10, 10, 0.95), rgba(30, 30, 30, 0.95));
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Server Selector */
        .server-selector {
            background: linear-gradient(135deg, rgba(40, 40, 40, 0.9), rgba(25, 25, 25, 0.95));
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
        }

        .server-label {
            color: var(--text-primary);
            margin-bottom: 15px;
            font-weight: 600;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .server-label i {
            color: var(--accent-color);
        }

        .server-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        /* Episode Selector Styles */
        .episode-selector {
            background: linear-gradient(145deg, rgba(25, 25, 25, 0.95), rgba(15, 15, 15, 0.98));
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
        }

        .episode-controls {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .season-episode-row {
            display: flex;
            gap: 20px;
            align-items: end;
            flex-wrap: wrap;
        }

        .season-selector, .episode-selector-dropdown {
            display: flex;
            flex-direction: column;
            gap: 8px;
            min-width: 200px;
        }

        .season-selector label, .episode-selector-dropdown label {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .season-selector label i, .episode-selector-dropdown label i {
            color: var(--accent-color);
        }

        .season-selector select, .episode-selector-dropdown select {
            background: linear-gradient(145deg, rgba(30, 30, 30, 0.95), rgba(20, 20, 20, 0.98));
            color: var(--text-primary);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 12px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
            backdrop-filter: blur(10px);
        }

        .season-selector select:hover, .episode-selector-dropdown select:hover {
            border-color: var(--accent-color);
            background: linear-gradient(145deg, rgba(40, 40, 40, 0.98), rgba(30, 30, 30, 0.99));
        }

        .season-selector select:focus, .episode-selector-dropdown select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(229, 9, 20, 0.2);
        }

        .episode-navigation {
            display: flex;
            gap: 10px;
            align-items: end;
        }

        .nav-btn {
            background: linear-gradient(145deg, var(--accent-color), #2980b9);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 50px;
            height: 48px;
        }

        .nav-btn:hover {
            background: linear-gradient(145deg, #2980b9, var(--accent-color));
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.4);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .server-btn {
            padding: 12px 20px;
            background: linear-gradient(145deg, rgba(60, 60, 60, 0.8), rgba(40, 40, 40, 0.9));
            color: var(--text-primary);
            border: 2px solid transparent;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            font-size: 0.95rem;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .server-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .server-btn:hover::before {
            left: 100%;
        }

        .server-btn:hover {
            background: linear-gradient(145deg, var(--primary-color), #c40812);
            border-color: var(--primary-color);
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 15px 35px rgba(229, 9, 20, 0.4);
        }

        .server-btn.active {
            background: linear-gradient(145deg, var(--primary-color), #c40812);
            border-color: var(--accent-color);
            box-shadow: 0 10px 25px rgba(229, 9, 20, 0.5);
            transform: translateY(-2px);
            position: relative;
        }

        .server-btn.active::after {
            content: '●';
            position: absolute;
            top: -8px;
            right: -8px;
            background: #00ff00;
            color: #000;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(0, 255, 0, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(0, 255, 0, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(0, 255, 0, 0);
            }
        }

        .no-servers-message {
            text-align: center;
            padding: 60px 20px;
            background: linear-gradient(135deg, rgba(229, 9, 20, 0.1), rgba(196, 8, 18, 0.05));
            border: 2px dashed rgba(229, 9, 20, 0.3);
            border-radius: 20px;
            margin: 20px;
        }

        .no-servers-message h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.5rem;
        }

        .no-servers-message p {
            color: var(--text-secondary);
            margin-bottom: 20px;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(145deg, var(--primary-color), #c40812);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(229, 9, 20, 0.4);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header scrolled">
        <nav class="navbar">
            <a href="index.php" class="logo"><?php echo SITE_NAME; ?></a>

            <!-- Desktop Navigation -->
            <div class="nav-links">
                <a href="index.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>Home</span>
                </a>
                <a href="movies.php" class="nav-link">
                    <i class="fas fa-film"></i>
                    <span>Movies</span>
                </a>
                <a href="tv-shows.php" class="nav-link">
                    <i class="fas fa-tv"></i>
                    <span>TV Shows</span>
                </a>
                <a href="genres.php" class="nav-link">
                    <i class="fas fa-list"></i>
                    <span>Genres</span>
                </a>
            </div>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" id="mobileMenuToggle">
                <span></span>
                <span></span>
                <span></span>
            </button>

            <!-- User Menu -->
            <div class="user-menu">
                <a href="javascript:history.back()" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Back</span>
                </a>
            </div>
        </nav>

        <!-- Mobile Navigation -->
        <div class="mobile-nav" id="mobileNav">
            <a href="index.php" class="mobile-nav-link">
                <i class="fas fa-home"></i>
                <span>Home</span>
            </a>
            <a href="movies.php" class="mobile-nav-link">
                <i class="fas fa-film"></i>
                <span>Movies</span>
            </a>
            <a href="tv-shows.php" class="mobile-nav-link">
                <i class="fas fa-tv"></i>
                <span>TV Shows</span>
            </a>
            <a href="genres.php" class="mobile-nav-link">
                <i class="fas fa-list"></i>
                <span>Genres</span>
            </a>
        </div>
    </header>

    <div class="main-container">
        <!-- Player Section -->
        <div class="player-section">
            <div class="player-wrapper" id="playerWrapper">
                <div class="loading-overlay" id="loadingOverlay">
                    <div class="loading-spinner"></div>
                </div>



                <?php if (!empty($embed_urls)): ?>
                <iframe
                    id="playerIframe"
                    class="player-iframe"
                    src="<?php echo $embed_urls[0]['url']; ?>"
                    allowfullscreen
                    webkitallowfullscreen
                    mozallowfullscreen
                    allow="autoplay; fullscreen; picture-in-picture"
                    sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-presentation"
                    referrerpolicy="no-referrer">
                </iframe>
                <?php else: ?>
                <div class="no-servers-message">
                    <h3><i class="fas fa-exclamation-triangle"></i> No Servers Available</h3>
                    <p>Please contact administrator to configure servers.</p>
                    <a href="admin/servers.php" class="btn">
                        <i class="fas fa-cog"></i> Configure Servers
                    </a>
                </div>
                <?php endif; ?>
            </div>

            <!-- Server Selector -->
            <div class="server-selector">
                <div class="server-label">
                    <i class="fas fa-server"></i>
                    Select Server:
                </div>
                <div class="server-buttons">
                    <?php if (!empty($embed_urls)): ?>
                        <?php foreach ($embed_urls as $index => $server): ?>
                            <button
                                class="server-btn <?php echo $index === 0 ? 'active' : ''; ?>"
                                data-url="<?php echo htmlspecialchars($server['url']); ?>"
                                data-server="<?php echo htmlspecialchars($server['name']); ?>">
                                <i class="fas fa-play-circle"></i>
                                <?php echo htmlspecialchars($server['name']); ?>
                            </button>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div style="text-align: center; padding: 20px; color: #ccc;">
                            <p><i class="fas fa-exclamation-circle"></i> No servers configured</p>
                            <p><a href="admin/servers.php" style="color: #e50914;">Configure servers in admin panel</a></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <?php if ($type === 'tv_show'): ?>
            <!-- Season & Episode Selector for TV Shows -->
            <div class="episode-selector">
                <div class="episode-controls">
                    <div class="season-episode-row">
                        <div class="season-selector">
                            <label for="seasonSelect">
                                <i class="fas fa-list"></i>
                                Season:
                            </label>
                            <select id="seasonSelect" onchange="updateEpisodes()">
                                <?php
                                // Get all seasons from database
                                try {
                                    $stmt = $conn->prepare("
                                        SELECT DISTINCT s.season_number
                                        FROM seasons s
                                        WHERE s.tv_show_id = :tv_show_id
                                        ORDER BY s.season_number ASC
                                    ");
                                    $stmt->execute([':tv_show_id' => $content['id']]);
                                    $seasons = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                    if (!empty($seasons)):
                                        foreach ($seasons as $s):
                                ?>
                                    <option value="<?= $s['season_number'] ?>" <?= $s['season_number'] == $season ? 'selected' : '' ?>>
                                        Season <?= $s['season_number'] ?>
                                    </option>
                                <?php
                                        endforeach;
                                    else:
                                        // Fallback to number_of_seasons
                                        for ($s = 1; $s <= ($content['number_of_seasons'] ?? 1); $s++):
                                ?>
                                    <option value="<?= $s ?>" <?= $s == $season ? 'selected' : '' ?>>Season <?= $s ?></option>
                                <?php
                                        endfor;
                                    endif;
                                } catch (Exception $e) {
                                    // Fallback
                                    for ($s = 1; $s <= ($content['number_of_seasons'] ?? 1); $s++):
                                ?>
                                    <option value="<?= $s ?>" <?= $s == $season ? 'selected' : '' ?>>Season <?= $s ?></option>
                                <?php
                                    endfor;
                                }
                                ?>
                            </select>
                        </div>

                        <div class="episode-selector-dropdown">
                            <label for="episodeSelect">
                                <i class="fas fa-play"></i>
                                Episode:
                            </label>
                            <select id="episodeSelect" onchange="updatePlayer()">
                                <?php
                                // Get episodes for current season
                                try {
                                    $stmt = $conn->prepare("
                                        SELECT e.episode_number, e.name
                                        FROM episodes e
                                        JOIN seasons s ON e.season_id = s.id
                                        WHERE s.tv_show_id = :tv_show_id AND s.season_number = :season
                                        ORDER BY e.episode_number ASC
                                    ");
                                    $stmt->execute([
                                        ':tv_show_id' => $content['id'],
                                        ':season' => $season
                                    ]);
                                    $current_season_episodes = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                    if (!empty($current_season_episodes)):
                                        foreach ($current_season_episodes as $ep):
                                ?>
                                    <option value="<?= $ep['episode_number'] ?>" <?= $ep['episode_number'] == $episode ? 'selected' : '' ?>>
                                        Episode <?= $ep['episode_number'] ?><?= !empty($ep['name']) ? ' - ' . htmlspecialchars($ep['name']) : '' ?>
                                    </option>
                                <?php
                                        endforeach;
                                    else:
                                        // Fallback
                                        for ($e = 1; $e <= 20; $e++):
                                ?>
                                    <option value="<?= $e ?>" <?= $e == $episode ? 'selected' : '' ?>>Episode <?= $e ?></option>
                                <?php
                                        endfor;
                                    endif;
                                } catch (Exception $e) {
                                    // Fallback
                                    for ($e = 1; $e <= 20; $e++):
                                ?>
                                    <option value="<?= $e ?>" <?= $e == $episode ? 'selected' : '' ?>>Episode <?= $e ?></option>
                                <?php
                                    endfor;
                                }
                                ?>
                            </select>
                        </div>

                        <div class="episode-navigation">
                            <button onclick="previousEpisode()" id="prevBtn" class="nav-btn" title="Previous Episode">
                                <i class="fas fa-step-backward"></i>
                            </button>
                            <button onclick="nextEpisode()" id="nextBtn" class="nav-btn" title="Next Episode">
                                <i class="fas fa-step-forward"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Content Information -->
        <div class="content-info-section">
            <div class="content-header">
                <div class="content-poster">
                    <img src="<?php echo getImageUrl($content['poster_path'], 'w300'); ?>"
                         alt="<?php echo htmlspecialchars($title); ?>"
                         class="poster-image">
                </div>
                <div class="content-details">
                    <h1 class="content-title"><?php echo htmlspecialchars($title); ?></h1>

                    <div class="content-meta">
                        <span class="year">
                            <i class="fas fa-calendar"></i>
                            <?php echo date('Y', strtotime($content['release_date'] ?? $content['first_air_date'])); ?>
                        </span>
                        <span class="rating">
                            <i class="fas fa-star"></i>
                            <?php echo number_format($content['vote_average'], 1); ?>/10
                        </span>
                        <?php if ($content['runtime'] ?? false): ?>
                        <span class="runtime">
                            <i class="fas fa-clock"></i>
                            <?php echo formatRuntime($content['runtime']); ?>
                        </span>
                        <?php endif; ?>
                        <span class="type">
                            <i class="fas fa-<?php echo $type === 'movie' ? 'film' : 'tv'; ?>"></i>
                            <?php echo ucfirst(str_replace('_', ' ', $type)); ?>
                        </span>
                    </div>

                    <div class="content-description">
                        <?php if ($type === 'tv_show'): ?>
                            <div class="episode-info-section">
                                <h3><i class="fas fa-tv"></i> Current Episode</h3>
                                <div class="current-episode-details">
                                    <div class="episode-badge-container">
                                        <span class="episode-badge">S<?php echo str_pad($season, 2, '0', STR_PAD_LEFT); ?>E<?php echo str_pad($episode, 2, '0', STR_PAD_LEFT); ?></span>
                                        <span class="episode-title"><?php echo htmlspecialchars($episode_data['name'] ?? 'Episode ' . $episode); ?></span>
                                    </div>
                                    <?php if (!empty($episode_data['overview'])): ?>
                                        <p class="episode-overview"><?php echo htmlspecialchars($episode_data['overview']); ?></p>
                                    <?php endif; ?>
                                    <?php if (!empty($episode_data['air_date']) || !empty($episode_data['runtime']) || !empty($episode_data['vote_average'])): ?>
                                        <div class="episode-meta-info">
                                            <?php if (!empty($episode_data['air_date'])): ?>
                                                <span><i class="fas fa-calendar"></i> <?php echo date('M d, Y', strtotime($episode_data['air_date'])); ?></span>
                                            <?php endif; ?>
                                            <?php if (!empty($episode_data['runtime'])): ?>
                                                <span><i class="fas fa-clock"></i> <?php echo $episode_data['runtime']; ?> min</span>
                                            <?php endif; ?>
                                            <?php if (!empty($episode_data['vote_average'])): ?>
                                                <span><i class="fas fa-star"></i> <?php echo number_format($episode_data['vote_average'], 1); ?>/10</span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <h3><i class="fas fa-info-circle"></i> <?php echo $type === 'tv_show' ? 'Show Overview' : 'Overview'; ?></h3>
                        <p><?php echo htmlspecialchars($content['overview']); ?></p>
                    </div>

                    <div class="content-actions">
                        <button class="action-btn favorite" onclick="toggleFavorite()">
                            <i class="fas fa-heart"></i>
                            Add to Favorites
                        </button>
                        <button class="action-btn watchlist" onclick="toggleWatchlist()">
                            <i class="fas fa-bookmark"></i>
                            Watchlist
                        </button>
                        <button class="action-btn share" onclick="shareContent()">
                            <i class="fas fa-share"></i>
                            Share
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- You Also Like Section -->
        <?php if (!empty($related_content)): ?>
        <div class="related-section">
            <h2 class="section-title">
                <i class="fas fa-thumbs-up"></i>
                You Also Like
            </h2>
            <div class="related-grid">
                <?php foreach ($related_content as $item): ?>
                <div class="related-item" onclick="playContent(<?php echo $item['tmdb_id']; ?>, '<?php echo $type; ?>')">
                    <div class="related-poster">
                        <img src="<?php echo getImageUrl($item['poster_path'], 'w300'); ?>"
                             alt="<?php echo htmlspecialchars($item['title'] ?? $item['name']); ?>"
                             loading="lazy">
                        <div class="play-overlay">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <div class="related-info">
                        <h4><?php echo htmlspecialchars($item['title'] ?? $item['name']); ?></h4>
                        <div class="related-meta">
                            <span class="rating">
                                <i class="fas fa-star"></i>
                                <?php echo number_format($item['vote_average'], 1); ?>
                            </span>
                            <span class="year">
                                <?php echo date('Y', strtotime($item['release_date'] ?? $item['first_air_date'])); ?>
                            </span>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($type === 'tv_show'): ?>
        <!-- Season & Episode List Section -->
        <div class="episode-list-section">
            <div class="season-tabs-container">
                <h2 class="section-title">
                    <i class="fas fa-tv"></i>
                    Seasons & Episodes
                </h2>
                <div class="season-tabs" id="seasonTabs">
                    <?php
                    // Get all seasons from database
                    try {
                        $stmt = $conn->prepare("
                            SELECT DISTINCT s.season_number, COUNT(e.id) as episode_count
                            FROM seasons s
                            LEFT JOIN episodes e ON s.id = e.season_id
                            WHERE s.tv_show_id = :tv_show_id
                            GROUP BY s.season_number
                            ORDER BY s.season_number ASC
                        ");
                        $stmt->execute([':tv_show_id' => $content['id']]);
                        $all_seasons = $stmt->fetchAll(PDO::FETCH_ASSOC);

                        if (!empty($all_seasons)):
                            foreach ($all_seasons as $s):
                    ?>
                        <button class="season-tab <?php echo $s['season_number'] == $season ? 'active' : ''; ?>"
                                onclick="switchSeason(<?php echo $s['season_number']; ?>)"
                                data-season="<?php echo $s['season_number']; ?>">
                            <span class="season-number">Season <?php echo $s['season_number']; ?></span>
                            <span class="episode-count"><?php echo $s['episode_count']; ?> Episodes</span>
                        </button>
                    <?php
                            endforeach;
                        else:
                            // Fallback to number_of_seasons
                            for ($s = 1; $s <= ($content['number_of_seasons'] ?? 1); $s++):
                    ?>
                        <button class="season-tab <?php echo $s == $season ? 'active' : ''; ?>"
                                onclick="switchSeason(<?php echo $s; ?>)"
                                data-season="<?php echo $s; ?>">
                            <span class="season-number">Season <?php echo $s; ?></span>
                            <span class="episode-count">Episodes</span>
                        </button>
                    <?php
                            endfor;
                        endif;
                    } catch (Exception $e) {
                        // Fallback
                        for ($s = 1; $s <= ($content['number_of_seasons'] ?? 1); $s++):
                    ?>
                        <button class="season-tab <?php echo $s == $season ? 'active' : ''; ?>"
                                onclick="switchSeason(<?php echo $s; ?>)"
                                data-season="<?php echo $s; ?>">
                            <span class="season-number">Season <?php echo $s; ?></span>
                            <span class="episode-count">Episodes</span>
                        </button>
                    <?php
                        endfor;
                    }
                    ?>
                </div>
            </div>

            <div class="current-season-info">
                <h3>Season <?php echo $season; ?> Episodes</h3>
                <p>Currently watching: S<?php echo str_pad($season, 2, '0', STR_PAD_LEFT); ?>E<?php echo str_pad($episode, 2, '0', STR_PAD_LEFT); ?></p>
            </div>

            <div class="episode-grid" id="episodeGrid">
                <?php
                // Get all episodes for current season
                try {
                    $stmt = $conn->prepare("
                        SELECT e.*, s.season_number
                        FROM episodes e
                        JOIN seasons s ON e.season_id = s.id
                        WHERE s.tv_show_id = :tv_show_id AND s.season_number = :season
                        ORDER BY e.episode_number ASC
                    ");
                    $stmt->execute([
                        ':tv_show_id' => $content['id'],
                        ':season' => $season
                    ]);
                    $all_episodes = $stmt->fetchAll(PDO::FETCH_ASSOC);

                    if (!empty($all_episodes)):
                        foreach ($all_episodes as $ep):
                ?>
                    <div class="episode-card <?php echo $ep['episode_number'] == $episode ? 'active' : ''; ?>"
                         onclick="selectEpisode(<?php echo $ep['episode_number']; ?>)"
                         data-episode="<?php echo $ep['episode_number']; ?>">
                        <div class="episode-number">E<?php echo str_pad($ep['episode_number'], 2, '0', STR_PAD_LEFT); ?></div>
                        <div class="episode-details">
                            <h4><?php echo htmlspecialchars($ep['name'] ?? 'Episode ' . $ep['episode_number']); ?></h4>
                            <?php if (!empty($ep['overview'])): ?>
                                <p><?php echo htmlspecialchars(substr($ep['overview'], 0, 100)) . (strlen($ep['overview']) > 100 ? '...' : ''); ?></p>
                            <?php endif; ?>
                            <div class="episode-meta">
                                <?php if (!empty($ep['air_date'])): ?>
                                    <span><i class="fas fa-calendar"></i> <?php echo date('M d, Y', strtotime($ep['air_date'])); ?></span>
                                <?php endif; ?>
                                <?php if (!empty($ep['runtime'])): ?>
                                    <span><i class="fas fa-clock"></i> <?php echo $ep['runtime']; ?>m</span>
                                <?php endif; ?>
                                <?php if (!empty($ep['vote_average'])): ?>
                                    <span><i class="fas fa-star"></i> <?php echo number_format($ep['vote_average'], 1); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php if ($ep['episode_number'] == $episode): ?>
                            <div class="playing-indicator">
                                <i class="fas fa-play"></i>
                                <span>Now Playing</span>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php
                        endforeach;
                    else:
                        // Fallback: Generate episodes if not in database
                        for ($e = 1; $e <= ($content['number_of_episodes'] ?? 20); $e++):
                ?>
                    <div class="episode-card <?php echo $e == $episode ? 'active' : ''; ?>"
                         onclick="selectEpisode(<?php echo $e; ?>)"
                         data-episode="<?php echo $e; ?>">
                        <div class="episode-number">E<?php echo str_pad($e, 2, '0', STR_PAD_LEFT); ?></div>
                        <div class="episode-details">
                            <h4>Episode <?php echo $e; ?></h4>
                            <p>Episode description not available.</p>
                        </div>
                        <?php if ($e == $episode): ?>
                            <div class="playing-indicator">
                                <i class="fas fa-play"></i>
                                <span>Now Playing</span>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php
                        endfor;
                    endif;
                } catch (Exception $e) {
                    // Fallback if database error
                    for ($ep = 1; $ep <= 20; $ep++):
                ?>
                    <div class="episode-card <?php echo $ep == $episode ? 'active' : ''; ?>"
                         onclick="selectEpisode(<?php echo $ep; ?>)"
                         data-episode="<?php echo $ep; ?>">
                        <div class="episode-number">E<?php echo str_pad($ep, 2, '0', STR_PAD_LEFT); ?></div>
                        <div class="episode-details">
                            <h4>Episode <?php echo $ep; ?></h4>
                            <p>Episode description not available.</p>
                        </div>
                        <?php if ($ep == $episode): ?>
                            <div class="playing-indicator">
                                <i class="fas fa-play"></i>
                                <span>Now Playing</span>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php
                    endfor;
                }
                ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Comments Section -->
        <div class="comments-section">
            <h2 class="section-title">
                <i class="fas fa-comments"></i>
                Comments & Reviews
                <span class="comment-count">(2)</span>
            </h2>

            <div class="comment-form">
                <div class="user-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="comment-input-group">
                    <textarea id="commentText" placeholder="Share your thoughts about this <?php echo $type === 'movie' ? 'movie' : 'show'; ?>..." rows="3"></textarea>
                    <div class="comment-actions">
                        <div class="rating-input">
                            <span>Rate:</span>
                            <div class="stars">
                                <i class="fas fa-star" data-rating="1"></i>
                                <i class="fas fa-star" data-rating="2"></i>
                                <i class="fas fa-star" data-rating="3"></i>
                                <i class="fas fa-star" data-rating="4"></i>
                                <i class="fas fa-star" data-rating="5"></i>
                            </div>
                        </div>
                        <button class="submit-comment" onclick="submitComment()">
                            <i class="fas fa-paper-plane"></i>
                            Post Comment
                        </button>
                    </div>
                </div>
            </div>

            <div class="comments-list" id="commentsList">
                <!-- Sample comments -->
                <div class="comment-item">
                    <div class="comment-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="comment-content">
                        <div class="comment-header">
                            <span class="comment-author">MovieLover123</span>
                            <div class="comment-rating">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                            </div>
                            <span class="comment-time">2 hours ago</span>
                        </div>
                        <p class="comment-text">Amazing movie! The storyline was captivating and the acting was superb. Highly recommend watching this.</p>
                        <div class="comment-actions">
                            <button class="comment-action" onclick="likeComment(this)">
                                <i class="fas fa-thumbs-up"></i>
                                <span>12</span>
                            </button>
                            <button class="comment-action" onclick="dislikeComment(this)">
                                <i class="fas fa-thumbs-down"></i>
                                <span>1</span>
                            </button>
                            <button class="comment-action">
                                <i class="fas fa-reply"></i>
                                Reply
                            </button>
                        </div>
                    </div>
                </div>

                <div class="comment-item">
                    <div class="comment-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="comment-content">
                        <div class="comment-header">
                            <span class="comment-author">CinemaFan</span>
                            <div class="comment-rating">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="comment-time">5 hours ago</span>
                        </div>
                        <p class="comment-text">Perfect! This is exactly what I was looking for. Great quality and excellent servers.</p>
                        <div class="comment-actions">
                            <button class="comment-action" onclick="likeComment(this)">
                                <i class="fas fa-thumbs-up"></i>
                                <span>8</span>
                            </button>
                            <button class="comment-action" onclick="dislikeComment(this)">
                                <i class="fas fa-thumbs-down"></i>
                                <span>0</span>
                            </button>
                            <button class="comment-action">
                                <i class="fas fa-reply"></i>
                                Reply
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Additional Styles */
        .content-info-section {
            background: linear-gradient(145deg, rgba(25, 25, 25, 0.95), rgba(15, 15, 15, 0.98));
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
        }

        .content-header {
            display: grid;
            grid-template-columns: 150px 1fr;
            gap: 25px;
            align-items: start;
        }

        .poster-image {
            width: 100%;
            border-radius: 16px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6);
            transition: transform 0.3s ease;
        }

        .content-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 15px;
            background: linear-gradient(135deg, var(--text-primary), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .content-meta {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .content-meta span {
            background: linear-gradient(145deg, rgba(60, 60, 60, 0.8), rgba(40, 40, 40, 0.9));
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .content-meta i {
            color: var(--accent-color);
        }

        .content-description {
            margin-bottom: 20px;
        }

        .content-description h3 {
            font-size: 1.2rem;
            margin-bottom: 10px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .content-description p {
            color: var(--text-secondary);
            line-height: 1.6;
            font-size: 1rem;
        }

        /* Episode Information Styles */
        .episode-info-section {
            background: linear-gradient(135deg, rgba(229, 9, 20, 0.1), rgba(52, 152, 219, 0.1));
            border: 1px solid rgba(229, 9, 20, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .current-episode-details {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .episode-badge-container {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .episode-badge {
            background: linear-gradient(135deg, var(--primary-color), #c40812);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 700;
            font-size: 0.9rem;
            letter-spacing: 1px;
            text-transform: uppercase;
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
        }

        .episode-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            flex: 1;
        }

        .episode-overview {
            color: var(--text-secondary);
            line-height: 1.6;
            font-style: italic;
            margin: 0;
        }

        .episode-meta-info {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            font-size: 0.9rem;
        }

        .episode-meta-info span {
            display: flex;
            align-items: center;
            gap: 6px;
            color: var(--text-secondary);
            background: rgba(255, 255, 255, 0.05);
            padding: 6px 12px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .episode-meta-info i {
            color: var(--accent-color);
        }

        /* Episode List Section */
        .episode-list-section {
            background: linear-gradient(145deg, rgba(25, 25, 25, 0.95), rgba(15, 15, 15, 0.98));
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
        }

        /* Season Tabs */
        .season-tabs-container {
            margin-bottom: 30px;
        }

        .season-tabs {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .season-tab {
            background: linear-gradient(145deg, rgba(30, 30, 30, 0.95), rgba(20, 20, 20, 0.98));
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            min-width: 120px;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .season-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .season-tab:hover::before {
            left: 100%;
        }

        .season-tab:hover {
            border-color: var(--accent-color);
            background: linear-gradient(145deg, rgba(40, 40, 40, 0.98), rgba(30, 30, 30, 0.99));
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
        }

        .season-tab.active {
            border-color: var(--primary-color);
            background: linear-gradient(145deg, rgba(229, 9, 20, 0.15), rgba(20, 20, 20, 0.95));
            box-shadow: 0 8px 25px rgba(229, 9, 20, 0.4);
        }

        .season-number {
            font-weight: 700;
            font-size: 1rem;
            color: var(--text-primary);
        }

        .episode-count {
            font-size: 0.8rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .season-tab.active .season-number {
            color: var(--primary-color);
        }

        .season-tab.active .episode-count {
            color: rgba(229, 9, 20, 0.8);
        }

        /* Current Season Info */
        .current-season-info {
            background: linear-gradient(135deg, rgba(229, 9, 20, 0.08), rgba(20, 20, 20, 0.95));
            border: 1px solid rgba(229, 9, 20, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .current-season-info h3 {
            color: var(--text-primary);
            margin-bottom: 10px;
            font-size: 1.3rem;
        }

        .current-season-info p {
            color: var(--text-secondary);
            font-size: 1rem;
            margin: 0;
        }

        .episode-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .episode-card {
            background: linear-gradient(145deg, rgba(30, 30, 30, 0.95), rgba(20, 20, 20, 0.98));
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .episode-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(229, 9, 20, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .episode-card:hover::before {
            left: 100%;
        }

        .episode-card:hover {
            border-color: var(--accent-color);
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(52, 152, 219, 0.3);
            background: linear-gradient(145deg, rgba(40, 40, 40, 0.98), rgba(30, 30, 30, 0.99));
        }

        .episode-card.active {
            border-color: var(--primary-color);
            background: linear-gradient(145deg, rgba(229, 9, 20, 0.15), rgba(20, 20, 20, 0.95));
            box-shadow: 0 8px 25px rgba(229, 9, 20, 0.4);
        }

        .episode-number {
            background: linear-gradient(135deg, var(--accent-color), #2980b9);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-weight: 700;
            font-size: 0.9rem;
            display: inline-block;
            margin-bottom: 15px;
            letter-spacing: 1px;
        }

        .episode-card.active .episode-number {
            background: linear-gradient(135deg, var(--primary-color), #c40812);
        }

        .episode-details h4 {
            color: var(--text-primary);
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 10px;
            line-height: 1.3;
        }

        .episode-details p {
            color: var(--text-secondary);
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .episode-meta {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            font-size: 0.8rem;
        }

        .episode-meta span {
            display: flex;
            align-items: center;
            gap: 5px;
            color: var(--text-secondary);
            background: rgba(255, 255, 255, 0.05);
            padding: 4px 8px;
            border-radius: 10px;
        }

        .episode-meta i {
            color: var(--accent-color);
        }

        .playing-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, var(--primary-color), #c40812);
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 5px;
            animation: pulse 2s infinite;
        }

        @media (max-width: 768px) {
            .episode-selector {
                margin: 0 10px 20px 10px;
                padding: 20px 15px;
            }

            .season-episode-row {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .season-selector, .episode-selector-dropdown {
                min-width: auto;
                width: 100%;
            }

            .episode-navigation {
                justify-content: center;
                margin-top: 10px;
            }

            .season-tabs {
                gap: 10px;
                justify-content: center;
            }

            .season-tab {
                min-width: 100px;
                padding: 12px 15px;
            }

            .episode-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .episode-card {
                padding: 15px;
            }

            .episode-list-section {
                margin: 0 10px 20px 10px;
                padding: 20px 15px;
            }

            .current-season-info {
                padding: 15px;
                margin-bottom: 20px;
            }

            .current-season-info h3 {
                font-size: 1.1rem;
            }

            .auto-play-container {
                margin-left: 0;
                margin-top: 15px;
                justify-content: center;
            }

            .auto-play-btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* Auto-play Button */
        .auto-play-container {
            display: flex;
            align-items: center;
            margin-left: 20px;
        }

        .auto-play-btn {
            background: linear-gradient(145deg, rgba(30, 30, 30, 0.95), rgba(20, 20, 20, 0.98));
            color: var(--text-primary);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 8px;
            backdrop-filter: blur(10px);
        }

        .auto-play-btn:hover {
            border-color: var(--accent-color);
            background: linear-gradient(145deg, rgba(40, 40, 40, 0.98), rgba(30, 30, 30, 0.99));
            transform: translateY(-2px);
        }

        .auto-play-btn.active {
            background: linear-gradient(145deg, var(--primary-color), #c40812);
            border-color: var(--primary-color);
            box-shadow: 0 8px 25px rgba(229, 9, 20, 0.4);
        }

        .auto-play-btn i {
            font-size: 1.1rem;
        }

        /* Auto-play Countdown */
        .autoplay-countdown {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, rgba(20, 20, 20, 0.98), rgba(40, 40, 40, 0.95));
            border: 2px solid var(--primary-color);
            border-radius: 16px;
            padding: 20px;
            z-index: 10000;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(20px);
            min-width: 300px;
            animation: slideInUp 0.4s ease-out;
        }

        .countdown-content h4 {
            color: var(--text-primary);
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .countdown-content p {
            color: var(--text-secondary);
            margin-bottom: 20px;
        }

        .countdown-buttons {
            display: flex;
            gap: 10px;
        }

        .btn-cancel, .btn-play-now {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-cancel {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        .btn-cancel:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .btn-play-now {
            background: linear-gradient(135deg, var(--primary-color), #c40812);
            color: white;
        }

        .btn-play-now:hover {
            background: linear-gradient(135deg, #c40812, var(--primary-color));
            transform: translateY(-2px);
        }

        @keyframes slideInUp {
            from {
                transform: translateY(100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .content-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 12px 24px;
            background: linear-gradient(145deg, rgba(60, 60, 60, 0.8), rgba(40, 40, 40, 0.9));
            color: var(--text-primary);
            border: 2px solid transparent;
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .action-btn:hover {
            background: linear-gradient(145deg, var(--primary-color), #c40812);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(229, 9, 20, 0.4);
        }

        .section-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            gap: 15px;
            color: var(--text-primary);
        }

        .section-title i {
            color: var(--accent-color);
        }

        .related-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 25px;
        }

        .related-item {
            background: linear-gradient(145deg, rgba(30, 30, 30, 0.9), rgba(20, 20, 20, 0.95));
            border-radius: 16px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.4s ease;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .related-item:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.6);
            border-color: var(--accent-color);
        }

        .related-poster {
            position: relative;
            overflow: hidden;
        }

        .related-poster img {
            width: 100%;
            height: 280px;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .play-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(229, 9, 20, 0.9);
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .related-item:hover .play-overlay {
            opacity: 1;
        }

        .related-info {
            padding: 20px;
        }

        .related-info h4 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--text-primary);
        }

        .related-meta {
            display: flex;
            justify-content: space-between;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .comments-section {
            background: linear-gradient(145deg, rgba(25, 25, 25, 0.95), rgba(15, 15, 15, 0.98));
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .comment-form {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
            padding: 20px;
            background: linear-gradient(145deg, rgba(40, 40, 40, 0.6), rgba(30, 30, 30, 0.8));
            border-radius: 16px;
        }

        .user-avatar {
            font-size: 3rem;
            color: var(--accent-color);
        }

        .comment-input-group {
            flex: 1;
        }

        .comment-input-group textarea {
            width: 100%;
            background: rgba(60, 60, 60, 0.8);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 20px;
            color: var(--text-primary);
            font-size: 1rem;
            resize: vertical;
            min-height: 100px;
            font-family: inherit;
        }

        .comment-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
        }

        .rating-input {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .stars {
            display: flex;
            gap: 5px;
        }

        .stars i {
            color: #666;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .stars i:hover,
        .stars i.active {
            color: var(--warning-color);
        }

        .submit-comment {
            background: linear-gradient(145deg, var(--primary-color), #c40812);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .comment-item {
            display: flex;
            gap: 20px;
            padding: 25px;
            margin-bottom: 20px;
            background: linear-gradient(145deg, rgba(40, 40, 40, 0.6), rgba(30, 30, 30, 0.8));
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .comment-avatar {
            font-size: 2.5rem;
            color: var(--accent-color);
        }

        .comment-content {
            flex: 1;
        }

        .comment-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }

        .comment-author {
            font-weight: 600;
            color: var(--text-primary);
        }

        .comment-rating {
            display: flex;
            gap: 2px;
        }

        .comment-rating i {
            color: var(--warning-color);
            font-size: 0.9rem;
        }

        .comment-time {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .comment-text {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .comment-actions {
            display: flex;
            gap: 20px;
        }

        .comment-action {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 5px 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .comment-action:hover {
            color: var(--accent-color);
            background: rgba(0, 212, 255, 0.1);
        }

        .comment-action.liked {
            color: var(--success-color);
            background: rgba(0, 255, 136, 0.1);
        }

        .comment-action.disliked {
            color: var(--primary-color);
            background: rgba(229, 9, 20, 0.1);
        }

        /* Mobile Optimizations */
        @media (max-width: 768px) {
            .main-container {
                padding: 60px 5px 20px;
                max-width: 100%;
            }

            .player-section {
                margin: 0 0 20px 0;
                border-radius: 0;
            }

            .server-selector {
                padding: 15px;
            }

            .server-buttons {
                justify-content: center;
                gap: 10px;
            }

            .server-btn {
                padding: 10px 15px;
                font-size: 0.9rem;
                flex: 1;
                min-width: 80px;
            }

            .content-header {
                grid-template-columns: 1fr;
                gap: 15px;
                text-align: center;
            }

            .content-title {
                font-size: 1.8rem;
            }

            .content-meta {
                justify-content: center;
                gap: 10px;
            }

            .content-meta span {
                padding: 6px 12px;
                font-size: 0.85rem;
            }

            .related-grid {
                grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
                gap: 15px;
            }

            .comment-form {
                flex-direction: column;
                gap: 15px;
                padding: 20px 15px;
            }

            .comment-actions {
                flex-direction: column;
                gap: 15px;
            }
        }

        /* Extra small screens */
        @media (max-width: 480px) {
            .main-container {
                padding: 60px 0 20px 0;
            }

            .player-section {
                border-radius: 0;
                margin: 0 0 15px 0;
            }

            .content-info-section,
            .related-section,
            .comments-section {
                margin: 0 10px 20px 10px;
                border-radius: 12px;
                padding: 20px 15px;
            }

            .server-btn {
                font-size: 0.85rem;
                padding: 8px 12px;
            }

            .related-grid {
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
                gap: 10px;
            }
        }



        /* Landscape orientation for mobile */
        @media screen and (orientation: landscape) and (max-height: 500px) {
            .main-container {
                padding: 10px 0;
            }

            .player-section {
                margin: 0;
                border-radius: 0;
            }

            .player-wrapper {
                padding-bottom: 100vh;
                height: 100vh;
            }

            .content-info-section,
            .related-section,
            .comments-section {
                display: none;
            }

            .server-selector {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                z-index: 1000;
                background: rgba(0, 0, 0, 0.9);
                padding: 10px;
                border-radius: 0;
            }

            .server-buttons {
                justify-content: center;
                gap: 8px;
            }

            .server-btn {
                padding: 6px 10px;
                font-size: 0.8rem;
            }
        }

        /* Header Navigation Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, rgba(20, 20, 20, 0.95), rgba(10, 10, 10, 0.98));
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .navbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--text-secondary);
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            color: var(--text-primary);
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .nav-link.active {
            color: var(--primary-color);
            background: rgba(229, 9, 20, 0.1);
        }

        .mobile-menu-toggle {
            display: none;
            flex-direction: column;
            background: none;
            border: none;
            cursor: pointer;
            padding: 5px;
            gap: 4px;
        }

        .mobile-menu-toggle span {
            width: 25px;
            height: 3px;
            background: var(--text-primary);
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .mobile-menu-toggle.active span:nth-child(1) {
            transform: rotate(45deg) translate(6px, 6px);
        }

        .mobile-menu-toggle.active span:nth-child(2) {
            opacity: 0;
        }

        .mobile-menu-toggle.active span:nth-child(3) {
            transform: rotate(-45deg) translate(6px, -6px);
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .back-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--text-secondary);
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .back-btn:hover {
            color: var(--text-primary);
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
        }

        /* Mobile Navigation */
        .mobile-nav {
            display: none;
            background: linear-gradient(135deg, rgba(30, 30, 30, 0.98), rgba(20, 20, 20, 0.99));
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px;
            gap: 10px;
            flex-direction: column;
        }

        .mobile-nav.active {
            display: flex;
        }

        .mobile-nav-link {
            display: flex;
            align-items: center;
            gap: 12px;
            color: var(--text-secondary);
            text-decoration: none;
            padding: 15px 20px;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .mobile-nav-link:hover {
            color: var(--text-primary);
            background: rgba(255, 255, 255, 0.1);
        }

        .mobile-nav-link.active {
            color: var(--primary-color);
            background: rgba(229, 9, 20, 0.1);
        }

        /* Mobile Responsive Header */
        @media (max-width: 768px) {
            .navbar {
                padding: 12px 15px;
            }

            .logo {
                font-size: 1.5rem;
            }

            .nav-links {
                display: none;
            }

            .mobile-menu-toggle {
                display: flex;
            }

            .user-menu .back-btn span {
                display: none;
            }

            .user-menu .back-btn {
                padding: 8px 12px;
            }
        }

        @media (max-width: 480px) {
            .navbar {
                padding: 10px 10px;
            }

            .logo {
                font-size: 1.3rem;
            }

            .mobile-nav {
                padding: 15px 10px;
            }

            .mobile-nav-link {
                padding: 12px 15px;
            }
        }
    </style>

    <script>
        // Mobile menu toggle
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const mobileNav = document.getElementById('mobileNav');

            if (mobileMenuToggle && mobileNav) {
                mobileMenuToggle.addEventListener('click', function() {
                    mobileMenuToggle.classList.toggle('active');
                    mobileNav.classList.toggle('active');
                });

                // Close mobile menu when clicking on a link
                const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');
                mobileNavLinks.forEach(link => {
                    link.addEventListener('click', function() {
                        mobileMenuToggle.classList.remove('active');
                        mobileNav.classList.remove('active');
                    });
                });

                // Close mobile menu when clicking outside
                document.addEventListener('click', function(e) {
                    if (!mobileMenuToggle.contains(e.target) && !mobileNav.contains(e.target)) {
                        mobileMenuToggle.classList.remove('active');
                        mobileNav.classList.remove('active');
                    }
                });
            }
        });
        // Advanced Ad Blocker System
        (function() {
            // Anti-detection methods
            const originalConsoleLog = console.log;
            const originalConsoleWarn = console.warn;
            const originalConsoleError = console.error;

            // Override console methods to hide ad blocker detection
            console.log = function(...args) {
                const message = args.join(' ');
                if (!message.includes('adblock') && !message.includes('ublock') && !message.includes('blocker')) {
                    originalConsoleLog.apply(console, args);
                }
            };

            console.warn = function(...args) {
                const message = args.join(' ');
                if (!message.includes('adblock') && !message.includes('ublock') && !message.includes('blocker')) {
                    originalConsoleWarn.apply(console, args);
                }
            };

            console.error = function(...args) {
                const message = args.join(' ');
                if (!message.includes('adblock') && !message.includes('ublock') && !message.includes('blocker')) {
                    originalConsoleError.apply(console, args);
                }
            };

            // Block common ad domains
            const adDomains = [
                'googleads.g.doubleclick.net',
                'googlesyndication.com',
                'google-analytics.com',
                'googletagmanager.com',
                'facebook.com/tr',
                'connect.facebook.net',
                'ads.yahoo.com',
                'adsystem.amazon.com',
                'amazon-adsystem.com',
                'adsystem.amazon.co.uk',
                'outbrain.com',
                'taboola.com',
                'adskeeper.co.uk',
                'mgid.com',
                'popads.net',
                'popcash.net',
                'propellerads.com',
                'revcontent.com',
                'contentad.net',
                'bidvertiser.com',
                'chitika.com',
                'infolinks.com',
                'media.net',
                'adnxs.com',
                'adsrvr.org',
                'doubleclick.net',
                'adsystem.amazon.com'
            ];

            // Override fetch to block ad requests
            const originalFetch = window.fetch;
            window.fetch = function(url, options) {
                if (typeof url === 'string') {
                    for (let domain of adDomains) {
                        if (url.includes(domain)) {
                            return Promise.reject(new Error('Blocked by ad blocker'));
                        }
                    }
                }
                return originalFetch.apply(this, arguments);
            };

            // Override XMLHttpRequest to block ad requests
            const originalXHROpen = XMLHttpRequest.prototype.open;
            XMLHttpRequest.prototype.open = function(method, url) {
                if (typeof url === 'string') {
                    for (let domain of adDomains) {
                        if (url.includes(domain)) {
                            this.abort();
                            return;
                        }
                    }
                }
                return originalXHROpen.apply(this, arguments);
            };

            // Block ad-related scripts
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            if (node.tagName === 'SCRIPT' || node.tagName === 'IFRAME') {
                                const src = node.src || node.getAttribute('src') || '';
                                for (let domain of adDomains) {
                                    if (src.includes(domain)) {
                                        node.remove();
                                        return;
                                    }
                                }
                            }

                            // Remove elements with ad-related classes or IDs
                            const adSelectors = [
                                '[class*="ad"]', '[id*="ad"]', '[class*="banner"]',
                                '[id*="banner"]', '[class*="popup"]', '[id*="popup"]',
                                '[class*="overlay"]', '[id*="overlay"]'
                            ];

                            adSelectors.forEach(selector => {
                                try {
                                    const adElements = node.querySelectorAll ? node.querySelectorAll(selector) : [];
                                    adElements.forEach(el => {
                                        if (el.offsetWidth > 0 && el.offsetHeight > 0) {
                                            el.style.display = 'none !important';
                                        }
                                    });
                                } catch(e) {}
                            });
                        }
                    });
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            // Anti-adblock detection bypass
            Object.defineProperty(window, 'adblockDetected', {
                get: function() { return false; },
                set: function() { return false; }
            });

            Object.defineProperty(window, 'canRunAds', {
                get: function() { return true; },
                set: function() { return true; }
            });

            // Fake ad elements to fool detection
            const fakeAd = document.createElement('div');
            fakeAd.className = 'adsbox';
            fakeAd.style.display = 'none';
            document.body.appendChild(fakeAd);

        })();

        // Player functionality
        document.addEventListener('DOMContentLoaded', function() {
            const iframe = document.getElementById('playerIframe');
            const loadingOverlay = document.getElementById('loadingOverlay');
            const serverButtons = document.querySelectorAll('.server-btn');
            const playerWrapper = document.getElementById('playerWrapper');

            // Enhanced iframe loading with ad blocking
            if (iframe) {
                // Add additional security attributes
                iframe.setAttribute('sandbox', 'allow-same-origin allow-scripts allow-forms allow-popups allow-presentation allow-popups-to-escape-sandbox');
                iframe.setAttribute('referrerpolicy', 'no-referrer');

                iframe.addEventListener('load', function() {
                    if (loadingOverlay) {
                        loadingOverlay.style.display = 'none';
                    }

                    // Try to inject ad blocker into iframe (if same origin)
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        if (iframeDoc) {
                            const script = iframeDoc.createElement('script');
                            script.textContent = `
                                // Block ads in iframe
                                const adSelectors = ['[class*="ad"]', '[id*="ad"]', '[class*="banner"]', '[id*="banner"]'];
                                setInterval(() => {
                                    adSelectors.forEach(selector => {
                                        try {
                                            const ads = document.querySelectorAll(selector);
                                            ads.forEach(ad => ad.style.display = 'none');
                                        } catch(e) {}
                                    });
                                }, 1000);
                            `;
                            iframeDoc.head.appendChild(script);
                        }
                    } catch(e) {
                        // Cross-origin restriction, ignore
                    }
                });
            }



            // Server switching functionality
            serverButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const url = this.getAttribute('data-url');
                    const serverName = this.getAttribute('data-server');

                    if (iframe && url) {
                        // Show loading overlay
                        if (loadingOverlay) {
                            loadingOverlay.style.display = 'flex';
                        }

                        // Update iframe source
                        iframe.src = url;

                        // Update active button
                        serverButtons.forEach(btn => btn.classList.remove('active'));
                        this.classList.add('active');

                        console.log('Switched to server:', serverName);
                    }
                });
            });

            // Star rating functionality
            const stars = document.querySelectorAll('.stars i');
            let currentRating = 0;

            stars.forEach((star, index) => {
                star.addEventListener('click', function() {
                    currentRating = index + 1;
                    updateStars();
                });

                star.addEventListener('mouseover', function() {
                    highlightStars(index + 1);
                });
            });

            document.querySelector('.stars').addEventListener('mouseleave', function() {
                updateStars();
            });

            function highlightStars(rating) {
                stars.forEach((star, index) => {
                    if (index < rating) {
                        star.classList.add('active');
                    } else {
                        star.classList.remove('active');
                    }
                });
            }

            function updateStars() {
                highlightStars(currentRating);
            }
        });

        // Action button functions
        function toggleFavorite() {
            const btn = event.target.closest('.favorite');
            const icon = btn.querySelector('i');

            if (btn.classList.contains('favorited')) {
                btn.classList.remove('favorited');
                icon.className = 'fas fa-heart';
                btn.innerHTML = '<i class="fas fa-heart"></i> Add to Favorites';
                showNotification('Removed from favorites', 'info');
            } else {
                btn.classList.add('favorited');
                icon.className = 'fas fa-heart';
                btn.innerHTML = '<i class="fas fa-heart"></i> Added to Favorites';
                showNotification('Added to favorites!', 'success');
            }
        }

        function toggleWatchlist() {
            const btn = event.target.closest('.watchlist');
            const icon = btn.querySelector('i');

            if (btn.classList.contains('watchlisted')) {
                btn.classList.remove('watchlisted');
                btn.innerHTML = '<i class="fas fa-bookmark"></i> Watchlist';
                showNotification('Removed from watchlist', 'info');
            } else {
                btn.classList.add('watchlisted');
                btn.innerHTML = '<i class="fas fa-bookmark"></i> In Watchlist';
                showNotification('Added to watchlist!', 'success');
            }
        }

        function shareContent() {
            if (navigator.share) {
                navigator.share({
                    title: document.title,
                    url: window.location.href
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(window.location.href).then(() => {
                    showNotification('Link copied to clipboard!', 'success');
                });
            }
        }

        function playContent(tmdbId, type) {
            window.location.href = `player.php?id=${tmdbId}&type=${type}`;
        }



        function submitComment() {
            const textarea = document.getElementById('commentText');
            const text = textarea.value.trim();
            const stars = document.querySelectorAll('.stars i.active');
            const rating = stars.length;

            if (!text) {
                showNotification('Please write a comment', 'warning');
                return;
            }

            // Create new comment element
            const commentsList = document.getElementById('commentsList');
            const newComment = document.createElement('div');
            newComment.className = 'comment-item';

            // Generate star rating HTML
            let starsHtml = '';
            for (let i = 1; i <= 5; i++) {
                starsHtml += `<i class="fas fa-star${i <= rating ? '' : ' far'}"></i>`;
            }

            newComment.innerHTML = `
                <div class="comment-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="comment-content">
                    <div class="comment-header">
                        <span class="comment-author">You</span>
                        <div class="comment-rating">
                            ${starsHtml}
                        </div>
                        <span class="comment-time">Just now</span>
                    </div>
                    <p class="comment-text">${text}</p>
                    <div class="comment-actions">
                        <button class="comment-action" onclick="likeComment(this)">
                            <i class="fas fa-thumbs-up"></i>
                            <span>0</span>
                        </button>
                        <button class="comment-action" onclick="dislikeComment(this)">
                            <i class="fas fa-thumbs-down"></i>
                            <span>0</span>
                        </button>
                        <button class="comment-action">
                            <i class="fas fa-reply"></i>
                            Reply
                        </button>
                    </div>
                </div>
            `;

            // Add to top of comments list
            commentsList.insertBefore(newComment, commentsList.firstChild);

            // Update comment count
            const commentCount = document.querySelector('.comment-count');
            const currentCount = parseInt(commentCount.textContent.match(/\d+/)[0]);
            commentCount.textContent = `(${currentCount + 1})`;

            showNotification('Comment posted successfully!', 'success');
            textarea.value = '';

            // Reset rating
            document.querySelectorAll('.stars i').forEach(star => {
                star.classList.remove('active');
            });
        }

        // Like/Dislike functionality
        function likeComment(button) {
            const likeCount = button.querySelector('span');
            const dislikeButton = button.parentElement.querySelector('.comment-action:nth-child(2)');
            const dislikeCount = dislikeButton.querySelector('span');

            if (button.classList.contains('liked')) {
                // Unlike
                button.classList.remove('liked');
                likeCount.textContent = parseInt(likeCount.textContent) - 1;
                showNotification('Like removed', 'info');
            } else {
                // Like
                button.classList.add('liked');
                likeCount.textContent = parseInt(likeCount.textContent) + 1;

                // Remove dislike if exists
                if (dislikeButton.classList.contains('disliked')) {
                    dislikeButton.classList.remove('disliked');
                    dislikeCount.textContent = parseInt(dislikeCount.textContent) - 1;
                }

                showNotification('Comment liked!', 'success');
            }
        }

        function dislikeComment(button) {
            const dislikeCount = button.querySelector('span');
            const likeButton = button.parentElement.querySelector('.comment-action:nth-child(1)');
            const likeCount = likeButton.querySelector('span');

            if (button.classList.contains('disliked')) {
                // Remove dislike
                button.classList.remove('disliked');
                dislikeCount.textContent = parseInt(dislikeCount.textContent) - 1;
                showNotification('Dislike removed', 'info');
            } else {
                // Dislike
                button.classList.add('disliked');
                dislikeCount.textContent = parseInt(dislikeCount.textContent) + 1;

                // Remove like if exists
                if (likeButton.classList.contains('liked')) {
                    likeButton.classList.remove('liked');
                    likeCount.textContent = parseInt(likeCount.textContent) - 1;
                }

                showNotification('Comment disliked', 'info');
            }
        }

        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                <span>${message}</span>
            `;

            // Add styles
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'var(--success-color)' : type === 'warning' ? 'var(--warning-color)' : 'var(--accent-color)'};
                color: white;
                padding: 15px 20px;
                border-radius: 12px;
                display: flex;
                align-items: center;
                gap: 10px;
                z-index: 10000;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // TV Show Episode Navigation Functions
        function updateEpisodes() {
            const seasonSelect = document.getElementById('seasonSelect');
            const episodeSelect = document.getElementById('episodeSelect');
            const selectedSeason = seasonSelect.value;

            // Clear current episode options
            episodeSelect.innerHTML = '';

            // Fetch episodes for selected season via AJAX
            fetch(`get_episodes.php?tv_show_id=<?php echo $content['id']; ?>&season=${selectedSeason}`)
                .then(response => response.json())
                .then(episodes => {
                    if (episodes && episodes.length > 0) {
                        episodes.forEach(ep => {
                            const option = document.createElement('option');
                            option.value = ep.episode_number;
                            option.textContent = `Episode ${ep.episode_number}${ep.name ? ' - ' + ep.name : ''}`;
                            episodeSelect.appendChild(option);
                        });
                    } else {
                        // Fallback: create default episodes
                        for (let i = 1; i <= 20; i++) {
                            const option = document.createElement('option');
                            option.value = i;
                            option.textContent = `Episode ${i}`;
                            episodeSelect.appendChild(option);
                        }
                    }

                    // Select first episode by default
                    episodeSelect.value = 1;
                    updatePlayer();
                })
                .catch(error => {
                    console.error('Error fetching episodes:', error);
                    // Fallback: create default episodes
                    for (let i = 1; i <= 20; i++) {
                        const option = document.createElement('option');
                        option.value = i;
                        option.textContent = `Episode ${i}`;
                        episodeSelect.appendChild(option);
                    }
                    episodeSelect.value = 1;
                    updatePlayer();
                });
        }

        function updatePlayer() {
            const seasonSelect = document.getElementById('seasonSelect');
            const episodeSelect = document.getElementById('episodeSelect');
            const selectedSeason = seasonSelect.value;
            const selectedEpisode = episodeSelect.value;

            // Update URL and reload player
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('season', selectedSeason);
            currentUrl.searchParams.set('episode', selectedEpisode);

            // Update browser history and reload
            window.history.pushState({}, '', currentUrl);
            window.location.reload();
        }

        function switchSeason(seasonNumber) {
            const seasonSelect = document.getElementById('seasonSelect');
            if (seasonSelect) {
                seasonSelect.value = seasonNumber;
                updateEpisodes();
            } else {
                // Direct navigation if no season selector
                const currentUrl = new URL(window.location);
                currentUrl.searchParams.set('season', seasonNumber);
                currentUrl.searchParams.set('episode', 1);
                window.history.pushState({}, '', currentUrl);
                window.location.reload();
            }
        }

        function selectEpisode(episodeNumber) {
            const episodeSelect = document.getElementById('episodeSelect');
            if (episodeSelect) {
                episodeSelect.value = episodeNumber;
            }

            // Update active episode button
            document.querySelectorAll('.episode-card').forEach(card => {
                card.classList.remove('active');
            });
            const selectedCard = document.querySelector(`[data-episode="${episodeNumber}"]`);
            if (selectedCard) {
                selectedCard.classList.add('active');
            }

            updatePlayer();
        }

        function previousEpisode() {
            const episodeSelect = document.getElementById('episodeSelect');
            if (!episodeSelect) return;

            const currentEpisode = parseInt(episodeSelect.value);

            if (currentEpisode > 1) {
                selectEpisode(currentEpisode - 1);
            } else {
                showNotification('This is the first episode!', 'info');
            }
        }

        function nextEpisode() {
            const episodeSelect = document.getElementById('episodeSelect');
            const seasonSelect = document.getElementById('seasonSelect');
            if (!episodeSelect || !seasonSelect) return;

            const currentEpisode = parseInt(episodeSelect.value);
            const currentSeason = parseInt(seasonSelect.value);
            const maxEpisodes = episodeSelect.options.length;
            const maxSeasons = seasonSelect.options.length;

            if (currentEpisode < maxEpisodes) {
                selectEpisode(currentEpisode + 1);
            } else if (currentSeason < maxSeasons) {
                // Move to next season, episode 1
                showNotification(`Moving to Season ${currentSeason + 1}`, 'info');
                seasonSelect.value = currentSeason + 1;

                // Update episodes for new season and then select episode 1
                updateEpisodesAndSelect(1);
            } else {
                showNotification('You have reached the last episode of the series!', 'info');
            }
        }

        function previousEpisode() {
            const episodeSelect = document.getElementById('episodeSelect');
            const seasonSelect = document.getElementById('seasonSelect');
            if (!episodeSelect || !seasonSelect) return;

            const currentEpisode = parseInt(episodeSelect.value);
            const currentSeason = parseInt(seasonSelect.value);

            if (currentEpisode > 1) {
                selectEpisode(currentEpisode - 1);
            } else if (currentSeason > 1) {
                // Move to previous season, last episode
                showNotification(`Moving to Season ${currentSeason - 1}`, 'info');
                seasonSelect.value = currentSeason - 1;

                // Update episodes for previous season and select last episode
                updateEpisodesAndSelectLast();
            } else {
                showNotification('This is the first episode of the series!', 'info');
            }
        }

        function updateEpisodesAndSelect(episodeNumber) {
            const seasonSelect = document.getElementById('seasonSelect');
            const episodeSelect = document.getElementById('episodeSelect');
            const selectedSeason = seasonSelect.value;

            // Fetch episodes for selected season
            fetch(`get_episodes.php?tv_show_id=<?php echo $content['id']; ?>&season=${selectedSeason}`)
                .then(response => response.json())
                .then(episodes => {
                    episodeSelect.innerHTML = '';

                    if (episodes && episodes.length > 0) {
                        episodes.forEach(ep => {
                            const option = document.createElement('option');
                            option.value = ep.episode_number;
                            option.textContent = `Episode ${ep.episode_number}${ep.name ? ' - ' + ep.name : ''}`;
                            episodeSelect.appendChild(option);
                        });
                    } else {
                        for (let i = 1; i <= 20; i++) {
                            const option = document.createElement('option');
                            option.value = i;
                            option.textContent = `Episode ${i}`;
                            episodeSelect.appendChild(option);
                        }
                    }

                    episodeSelect.value = episodeNumber;
                    updatePlayer();
                })
                .catch(error => {
                    console.error('Error fetching episodes:', error);
                    selectEpisode(episodeNumber);
                });
        }

        function updateEpisodesAndSelectLast() {
            const seasonSelect = document.getElementById('seasonSelect');
            const episodeSelect = document.getElementById('episodeSelect');
            const selectedSeason = seasonSelect.value;

            // Fetch episodes for selected season
            fetch(`get_episodes.php?tv_show_id=<?php echo $content['id']; ?>&season=${selectedSeason}`)
                .then(response => response.json())
                .then(episodes => {
                    episodeSelect.innerHTML = '';
                    let lastEpisode = 20; // default

                    if (episodes && episodes.length > 0) {
                        episodes.forEach(ep => {
                            const option = document.createElement('option');
                            option.value = ep.episode_number;
                            option.textContent = `Episode ${ep.episode_number}${ep.name ? ' - ' + ep.name : ''}`;
                            episodeSelect.appendChild(option);
                        });
                        lastEpisode = episodes[episodes.length - 1].episode_number;
                    } else {
                        for (let i = 1; i <= 20; i++) {
                            const option = document.createElement('option');
                            option.value = i;
                            option.textContent = `Episode ${i}`;
                            episodeSelect.appendChild(option);
                        }
                    }

                    episodeSelect.value = lastEpisode;
                    updatePlayer();
                })
                .catch(error => {
                    console.error('Error fetching episodes:', error);
                    selectEpisode(20); // fallback to episode 20
                });
        }

        // Auto-play next episode feature
        let autoPlayTimer;
        let autoPlayEnabled = localStorage.getItem('autoPlayEnabled') === 'true';

        function toggleAutoPlay() {
            autoPlayEnabled = !autoPlayEnabled;
            localStorage.setItem('autoPlayEnabled', autoPlayEnabled);

            const autoPlayBtn = document.getElementById('autoPlayBtn');
            if (autoPlayBtn) {
                autoPlayBtn.classList.toggle('active', autoPlayEnabled);
                autoPlayBtn.innerHTML = autoPlayEnabled ?
                    '<i class="fas fa-toggle-on"></i> Auto-play: ON' :
                    '<i class="fas fa-toggle-off"></i> Auto-play: OFF';
            }

            showNotification(autoPlayEnabled ? 'Auto-play enabled' : 'Auto-play disabled', 'info');
        }

        // Setup auto-play toggle button
        function setupAutoPlay() {
            if ('<?php echo $type; ?>' !== 'tv_show') return;

            const episodeControls = document.querySelector('.episode-controls');
            if (episodeControls && !document.getElementById('autoPlayBtn')) {
                const autoPlayContainer = document.createElement('div');
                autoPlayContainer.className = 'auto-play-container';

                const autoPlayBtn = document.createElement('button');
                autoPlayBtn.id = 'autoPlayBtn';
                autoPlayBtn.className = `auto-play-btn ${autoPlayEnabled ? 'active' : ''}`;
                autoPlayBtn.innerHTML = autoPlayEnabled ?
                    '<i class="fas fa-toggle-on"></i> Auto-play: ON' :
                    '<i class="fas fa-toggle-off"></i> Auto-play: OFF';
                autoPlayBtn.onclick = toggleAutoPlay;

                autoPlayContainer.appendChild(autoPlayBtn);
                episodeControls.appendChild(autoPlayContainer);
            }
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Space bar to play/pause (if supported by iframe)
            if (e.code === 'Space' && e.target.tagName !== 'TEXTAREA') {
                e.preventDefault();
                // This would require iframe communication
            }

            // Arrow keys for episode navigation (TV shows only)
            if ('<?php echo $type; ?>' === 'tv_show') {
                if (e.key === 'ArrowLeft') {
                    e.preventDefault();
                    previousEpisode();
                } else if (e.key === 'ArrowRight') {
                    e.preventDefault();
                    nextEpisode();
                }
            }

            // Number keys to switch servers
            if (e.key >= '1' && e.key <= '9') {
                const serverIndex = parseInt(e.key) - 1;
                const serverButtons = document.querySelectorAll('.server-btn');
                if (serverButtons[serverIndex]) {
                    serverButtons[serverIndex].click();
                }
            }
        });

        // Initialize TV show features
        document.addEventListener('DOMContentLoaded', function() {
            setupAutoPlay();
        });
    </script>
</body>
</html>