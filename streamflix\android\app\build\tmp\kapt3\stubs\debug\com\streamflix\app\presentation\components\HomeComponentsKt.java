package com.streamflix.app.presentation.components;

import androidx.compose.foundation.layout.*;
import androidx.compose.material3.*;
import androidx.compose.runtime.Composable;
import androidx.compose.ui.Alignment;
import androidx.compose.ui.Modifier;
import androidx.compose.ui.text.font.FontWeight;
import com.streamflix.app.data.model.*;
import com.streamflix.app.ui.theme.*;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000 \n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010\u0000\n\u0002\b\u0002\u001a\u001c\u0010\u0000\u001a\u00020\u00012\u0012\u0010\u0002\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a*\u0010\u0005\u001a\u00020\u00012\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u00a8\u0006\n"}, d2 = {"GenresSection", "", "onGenreClick", "Lkotlin/Function1;", "Lcom/streamflix/app/data/model/Genre;", "TopRatedSection", "items", "", "", "onItemClick", "app_debug"})
public final class HomeComponentsKt {
    
    @androidx.compose.runtime.Composable()
    public static final void TopRatedSection(@org.jetbrains.annotations.NotNull()
    java.util.List<? extends java.lang.Object> items, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<java.lang.Object, kotlin.Unit> onItemClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void GenresSection(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.streamflix.app.data.model.Genre, kotlin.Unit> onGenreClick) {
    }
}