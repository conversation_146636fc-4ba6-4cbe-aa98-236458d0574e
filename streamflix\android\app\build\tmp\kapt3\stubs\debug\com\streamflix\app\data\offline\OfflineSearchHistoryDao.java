package com.streamflix.app.data.offline;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import androidx.room.*;
import com.google.gson.Gson;
import com.streamflix.app.data.local.UserPreferences;
import com.streamflix.app.data.model.*;
import dagger.hilt.android.qualifiers.ApplicationContext;
import kotlinx.coroutines.flow.*;
import java.io.File;
import javax.inject.Inject;
import javax.inject.Singleton;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0005\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u001c\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u000b\u001a\u00020\u00032\u0006\u0010\f\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\r\u00a8\u0006\u000e"}, d2 = {"Lcom/streamflix/app/data/offline/OfflineSearchHistoryDao;", "", "deleteAllSearches", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getRecentSearches", "", "Lcom/streamflix/app/data/offline/OfflineSearchHistory;", "limit", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertSearch", "search", "(Lcom/streamflix/app/data/offline/OfflineSearchHistory;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao()
public abstract interface OfflineSearchHistoryDao {
    
    @androidx.room.Query(value = "SELECT * FROM offline_search_history ORDER BY timestamp DESC LIMIT :limit")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRecentSearches(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.streamflix.app.data.offline.OfflineSearchHistory>> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertSearch(@org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.offline.OfflineSearchHistory search, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM offline_search_history")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAllSearches(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}