<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}



// Handle import actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'search_content') {
        $search_query = sanitizeInput($_POST['search_query'] ?? '');
        $search_type = sanitizeInput($_POST['search_type'] ?? 'multi');
        $search_year = sanitizeInput($_POST['search_year'] ?? '');

        if (!empty($search_query)) {
            try {
                $streamflix = new StreamFlix();
                $search_results = $streamflix->searchTMDB($search_query, $search_type, $search_year);

                if ($search_results && isset($search_results['results'])) {
                    $search_data = $search_results['results'];
                } else {
                    $search_error = "No results found for '{$search_query}'";
                }
            } catch (Exception $e) {
                $search_error = 'Search failed: ' . $e->getMessage();
            }
        } else {
            $search_error = 'Please enter a search query.';
        }
    }

    if ($action === 'bulk_import') {
        $selected_items = $_POST['selected_items'] ?? [];
        $import_type = sanitizeInput($_POST['import_type'] ?? '');

        if (!empty($selected_items) && !empty($import_type)) {
            try {
                $streamflix = new StreamFlix();
                $imported = 0;
                $errors = [];

                foreach ($selected_items as $item_data) {
                    $item = json_decode($item_data, true);
                    if (!$item || !isset($item['id'])) continue;

                    try {
                        $result = null;

                        // Determine content type
                        $content_type = $item['media_type'] ?? $import_type;

                        if ($content_type === 'movie' || $import_type === 'movie') {
                            $result = $streamflix->importMovie($item['id']);
                            if ($result && $import_type === 'anime') {
                                markAsAnime($result, 'movie');
                            }
                        } else {
                            $result = $streamflix->importTVShow($item['id']);
                            if ($result && $import_type === 'anime') {
                                markAsAnime($result, 'tv_show');
                            } elseif ($result && $import_type === 'hentai') {
                                markAsHentai($result);
                            }
                        }

                        if ($result) {
                            $imported++;
                        }

                        usleep(300000); // 0.3 seconds delay

                    } catch (Exception $e) {
                        $item_name = isset($item['title']) ? $item['title'] : (isset($item['name']) ? $item['name'] : 'Unknown');
                        $errors[] = "Error importing {$item_name}: " . $e->getMessage();
                        continue;
                    }
                }

                $message = "Successfully imported {$imported} items!";
                if (!empty($errors)) {
                    $message .= " Errors: " . implode(", ", array_slice($errors, 0, 3));
                    if (count($errors) > 3) {
                        $message .= " and " . (count($errors) - 3) . " more...";
                    }
                }

            } catch (Exception $e) {
                $error = 'Bulk import failed: ' . $e->getMessage();
            }
        } else {
            $error = 'Please select items to import and specify import type.';
        }
    }

    if ($action === 'import_letsembed') {
        $type = sanitizeInput($_POST['type'] ?? '');
        $limit = (int)($_POST['limit'] ?? 50);
        
        if (in_array($type, ['movie', 'tv', 'anime', 'hentai'])) {
            try {
                $imported = importFromLetsEmbed($type, $limit);
                $message = "Successfully imported {$imported} {$type}s from TMDB!";

                // Add debug information
                if ($imported == 0) {
                    $error = "No content was imported. This might be due to: 1) All content already exists, 2) TMDB API issues, 3) Database connection problems. Check your TMDB API key in settings.";
                }
            } catch (Exception $e) {
                $error = 'Import failed: ' . $e->getMessage();
            }
        } else {
            $error = 'Invalid content type selected.';
        }
    }
    
    if ($action === 'import_custom_json') {
        $json_url = sanitizeInput($_POST['json_url'] ?? '');
        $content_type = sanitizeInput($_POST['content_type'] ?? '');
        $batch_size = (int)($_POST['batch_size'] ?? 50);
        $auto_detect = isset($_POST['auto_detect']) ? true : false;

        if (!empty($json_url) && in_array($content_type, ['movie', 'tv_show', 'anime_movie', 'anime_tv', 'hentai', 'auto'])) {
            try {
                // Validate URL format
                if (!filter_var($json_url, FILTER_VALIDATE_URL)) {
                    throw new Exception('Invalid URL format');
                }

                $imported = importFromCustomJSON($json_url, $content_type, $batch_size);

                // Add type-specific message
                $type_name = '';
                switch ($content_type) {
                    case 'anime_movie': $type_name = 'anime movies'; break;
                    case 'anime_tv': $type_name = 'anime TV shows'; break;
                    case 'hentai': $type_name = 'hentai content'; break;
                    case 'movie': $type_name = 'movies'; break;
                    case 'tv_show': $type_name = 'TV shows'; break;
                    case 'auto': $type_name = 'items (auto-detected)'; break;
                    default: $type_name = 'items'; break;
                }

                $message = "Successfully imported {$imported} {$type_name} from JSON!";

                if ($imported == 0) {
                    $error = "No content was imported. This might be due to: 1) All content already exists, 2) Invalid TMDB IDs in JSON, 3) TMDB API issues, 4) Network connectivity problems.";
                }
            } catch (Exception $e) {
                $error = 'Import failed: ' . $e->getMessage();
            }
        } else {
            $error = 'Please provide valid JSON URL and content type.';
        }
    }

    if ($action === 'import_letsembed_json') {
        $letsembed_type = sanitizeInput($_POST['letsembed_type'] ?? '');
        $batch_size = (int)($_POST['batch_size'] ?? 100);

        if (in_array($letsembed_type, ['movie', 'tv', 'anime', 'hentai'])) {
            try {
                $imported = importFromLetsEmbedJSON($letsembed_type, $batch_size);
                $message = "Successfully imported {$imported} {$letsembed_type}s from LetsEmbed JSON!";

                if ($imported == 0) {
                    $error = "No content was imported. The LetsEmbed JSON might be empty or all content already exists.";
                }
            } catch (Exception $e) {
                $error = 'LetsEmbed import failed: ' . $e->getMessage();
            }
        } else {
            $error = 'Invalid LetsEmbed content type selected.';
        }
    }
}

function importFromLetsEmbed($type, $limit = 50) {
    // For now, we'll use TMDB popular/trending lists since LetsEmbed API might not be accessible
    // You can replace this with actual LetsEmbed API when available

    $streamflix = new StreamFlix();
    $imported = 0;
    $errors = [];

    try {
        if (in_array($type, ['movie', 'anime'])) {
            // Import popular movies
            $endpoint = '/movie/popular';
            $data = $streamflix->fetchFromTMDB($endpoint);

            if ($data && isset($data['results'])) {
                $items = array_slice($data['results'], 0, $limit);

                foreach ($items as $item) {
                    try {
                        // Check if movie already exists
                        $db = new Database();
                        $conn = $db->connect();
                        $stmt = $conn->prepare("SELECT id FROM movies WHERE tmdb_id = ?");
                        $stmt->execute([$item['id']]);

                        if ($stmt->rowCount() > 0) {
                            // Movie already exists, skip
                            continue;
                        }

                        $result = $streamflix->importMovie($item['id']);
                        if ($result) {
                            $imported++;

                            // Mark anime content
                            if ($type === 'anime') {
                                markAsAnime($result, 'movie');
                            }
                        }

                        // Small delay to avoid overwhelming TMDB API
                        usleep(500000); // 0.5 seconds

                    } catch (Exception $e) {
                        $errors[] = "Error importing movie {$item['id']}: " . $e->getMessage();
                        continue;
                    }
                }
            } else {
                throw new Exception('No data received from TMDB movies API');
            }
        } else {
            // Import popular TV shows
            $endpoint = '/tv/popular';
            $data = $streamflix->fetchFromTMDB($endpoint);

            if ($data && isset($data['results'])) {
                $items = array_slice($data['results'], 0, $limit);

                foreach ($items as $item) {
                    try {
                        // Check if TV show already exists
                        $db = new Database();
                        $conn = $db->connect();
                        $stmt = $conn->prepare("SELECT id FROM tv_shows WHERE tmdb_id = ?");
                        $stmt->execute([$item['id']]);

                        if ($stmt->rowCount() > 0) {
                            // TV show already exists, skip
                            continue;
                        }

                        $result = $streamflix->importTVShow($item['id']);
                        if ($result) {
                            $imported++;

                            // Mark anime content
                            if ($type === 'anime') {
                                markAsAnime($result, 'tv_show');
                            }
                        }

                        // Small delay to avoid overwhelming TMDB API
                        usleep(500000); // 0.5 seconds

                    } catch (Exception $e) {
                        $errors[] = "Error importing TV show {$item['id']}: " . $e->getMessage();
                        continue;
                    }
                }
            } else {
                throw new Exception('No data received from TMDB TV API');
            }
        }

        // Log errors if any
        if (!empty($errors)) {
            error_log("Import errors: " . implode(", ", $errors));
        }

    } catch (Exception $e) {
        throw new Exception('Failed to import from TMDB: ' . $e->getMessage());
    }

    return $imported;
}

function importFromCustomJSON($json_url, $content_type, $batch_size = 50) {
    // Enhanced JSON import with better error handling and batch processing

    // Set up context with longer timeout and proper headers
    $context = stream_context_create([
        'http' => [
            'timeout' => 120, // Increased timeout for large files
            'user_agent' => 'Mozilla/5.0 (compatible; StreamFlix/1.0)',
            'method' => 'GET',
            'header' => [
                'Accept: application/json, text/plain, */*',
                'Accept-Encoding: gzip, deflate',
                'Connection: keep-alive'
            ]
        ]
    ]);

    // Try to fetch JSON data with better error handling
    $json_data = @file_get_contents($json_url, false, $context);
    $http_code = 0;

    if ($json_data === false) {
        // Get HTTP response code from headers
        if (isset($http_response_header)) {
            foreach ($http_response_header as $header) {
                if (preg_match('/HTTP\/\d\.\d\s+(\d+)/', $header, $matches)) {
                    $http_code = (int)$matches[1];
                    break;
                }
            }
        }

        // Try with cURL as fallback
        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $json_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 120);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json, text/plain, */*',
                'Accept-Language: en-US,en;q=0.9',
                'Cache-Control: no-cache',
                'Pragma: no-cache'
            ]);

            $json_data = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($json_data === false) {
                throw new Exception("Failed to fetch JSON from URL. cURL Error: $curl_error");
            }

            if ($http_code !== 200) {
                throw new Exception("Failed to fetch JSON from URL. HTTP Code: $http_code. The URL might be temporarily unavailable or the JSON file might have been moved.");
            }
        } else {
            $error_msg = "Failed to fetch JSON from provided URL";
            if ($http_code > 0) {
                $error_msg .= ". HTTP Code: $http_code";
            }
            $error_msg .= ". cURL is not available. Please check the URL or contact your hosting provider.";
            throw new Exception($error_msg);
        }
    }

    // Handle compressed data
    if (function_exists('gzdecode') && substr($json_data, 0, 2) === "\x1f\x8b") {
        $json_data = gzdecode($json_data);
    }

    // Parse JSON with better error handling
    $data = json_decode($json_data, true);
    $json_error = json_last_error();

    if ($json_error !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON format: ' . json_last_error_msg());
    }

    if (!$data || !is_array($data)) {
        throw new Exception('JSON must contain an array of items');
    }

    $streamflix = new StreamFlix();
    $imported = 0;
    $errors = [];
    $total_items = count($data);

    // Process in batches to handle large datasets
    $batches = array_chunk($data, $batch_size);

    foreach ($batches as $batch_index => $batch) {
        foreach ($batch as $item_index => $item) {
            // Support multiple ID field names
            $tmdb_id = null;
            $possible_id_fields = ['tmdb_id', 'id', 'tmdb', 'movie_id', 'tv_id'];

            foreach ($possible_id_fields as $field) {
                if (isset($item[$field]) && !empty($item[$field])) {
                    $tmdb_id = (int)$item[$field];
                    break;
                }
            }

            if (!$tmdb_id) {
                $errors[] = "Item at index " . ($batch_index * $batch_size + $item_index) . " has no valid TMDB ID";
                continue;
            }

            try {
                // Check if content already exists
                $db = new Database();
                $conn = $db->connect();

                $table = (in_array($content_type, ['movie', 'anime_movie'])) ? 'movies' : 'tv_shows';
                $stmt = $conn->prepare("SELECT id FROM {$table} WHERE tmdb_id = ?");
                $stmt->execute([$tmdb_id]);

                if ($stmt->rowCount() > 0) {
                    // Content already exists, skip
                    continue;
                }

                $result = null;

                // Handle different content types
                switch ($content_type) {
                    case 'movie':
                    case 'anime_movie':
                        $result = $streamflix->importMovie($tmdb_id);
                        if ($result && $content_type === 'anime_movie') {
                            markAsAnime($result, 'movie');
                        }
                        break;

                    case 'tv_show':
                    case 'anime_tv':
                        $result = $streamflix->importTVShow($tmdb_id);
                        if ($result && $content_type === 'anime_tv') {
                            markAsAnime($result, 'tv_show');
                        }
                        break;

                    case 'hentai':
                        $result = $streamflix->importTVShow($tmdb_id);
                        if ($result) {
                            markAsHentai($result);
                        }
                        break;

                    default:
                        // Auto-detect based on item data
                        if (isset($item['media_type'])) {
                            if ($item['media_type'] === 'movie') {
                                $result = $streamflix->importMovie($tmdb_id);
                            } else {
                                $result = $streamflix->importTVShow($tmdb_id);
                            }
                        } else {
                            // Default to movie if no type specified
                            $result = $streamflix->importMovie($tmdb_id);
                        }
                        break;
                }

                if ($result) {
                    $imported++;

                    // Mark as featured if specified in JSON
                    if (isset($item['featured']) && $item['featured']) {
                        markAsFeatured($result, $table);
                    }
                }

                // Shorter delay for better performance
                usleep(200000); // 0.2 seconds delay

            } catch (Exception $e) {
                $item_name = isset($item['title']) ? $item['title'] : (isset($item['name']) ? $item['name'] : "TMDB ID: $tmdb_id");
                $errors[] = "Error importing {$item_name}: " . $e->getMessage();
                continue;
            }
        }

        // Small delay between batches
        usleep(500000); // 0.5 seconds between batches
    }

    // Log errors if any
    if (!empty($errors)) {
        error_log("JSON Import errors: " . implode(", ", array_slice($errors, 0, 10)));
    }

    return $imported;
}

function markAsFeatured($content_id, $table) {
    try {
        $db = new Database();
        $conn = $db->connect();

        $stmt = $conn->prepare("UPDATE {$table} SET is_featured = 1 WHERE id = ?");
        $stmt->execute([$content_id]);
    } catch (Exception $e) {
        error_log("Error marking as featured: " . $e->getMessage());
    }
}

function importFromLetsEmbedJSON($type, $batch_size = 100) {
    // Updated LetsEmbed JSON URLs with fallbacks
    $letsembed_urls = [
        'movie' => [
            'https://letsembed.cc/list/movie.json',
            'https://api.letsembed.cc/movie.json',
            'https://raw.githubusercontent.com/letsembed/database/main/movie.json'
        ],
        'tv' => [
            'https://letsembed.cc/list/tv.json',
            'https://api.letsembed.cc/tv.json',
            'https://raw.githubusercontent.com/letsembed/database/main/tv.json'
        ],
        'anime' => [
            'https://letsembed.cc/list/anime.json',
            'https://api.letsembed.cc/anime.json',
            'https://raw.githubusercontent.com/letsembed/database/main/anime.json'
        ],
        'hentai' => [
            'https://letsembed.cc/list/hentai.json',
            'https://api.letsembed.cc/hentai.json',
            'https://raw.githubusercontent.com/letsembed/database/main/hentai.json'
        ]
    ];

    if (!isset($letsembed_urls[$type])) {
        throw new Exception('Invalid LetsEmbed type');
    }

    $urls = $letsembed_urls[$type];
    $json_url = null;
    $last_error = '';

    // Try each URL until one works
    foreach ($urls as $url) {
        try {
            // Test URL accessibility
            $validation = validateJSONStructure($url);
            if ($validation['valid']) {
                $json_url = $url;
                break;
            } else {
                $last_error = $validation['error'];
            }
        } catch (Exception $e) {
            $last_error = $e->getMessage();
            continue;
        }
    }

    if (!$json_url) {
        throw new Exception("All LetsEmbed URLs failed. Last error: $last_error. Please try again later or use custom JSON import.");
    }

    // Determine content type for import
    $content_type = $type;
    if ($type === 'tv') {
        $content_type = 'tv_show';
    } elseif ($type === 'anime') {
        $content_type = 'anime_tv'; // Import anime as TV shows with anime content type
    }

    return importFromCustomJSON($json_url, $content_type, $batch_size);
}

function validateJSONStructure($json_url) {
    // Quick validation to check JSON structure without full download

    // First try with cURL if available
    if (function_exists('curl_init')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $json_url);
        curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

        curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($curl_error) {
            return ['valid' => false, 'error' => "Connection error: $curl_error"];
        }

        if ($http_code !== 200) {
            return ['valid' => false, 'error' => "HTTP Error: $http_code"];
        }

        // Check content type (be more lenient)
        if ($content_type &&
            strpos($content_type, 'application/json') === false &&
            strpos($content_type, 'text/plain') === false &&
            strpos($content_type, 'text/html') === false) {
            return ['valid' => false, 'error' => 'URL does not return valid content type'];
        }

        return ['valid' => true, 'content_type' => $content_type];
    }

    // Fallback to get_headers
    $context = stream_context_create([
        'http' => [
            'timeout' => 30,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'method' => 'HEAD'
        ]
    ]);

    $headers = @get_headers($json_url, 1, $context);

    if (!$headers) {
        return ['valid' => false, 'error' => 'Cannot access URL - connection failed'];
    }

    $status_code = null;
    if (isset($headers[0])) {
        preg_match('/HTTP\/\d\.\d\s+(\d+)/', $headers[0], $matches);
        $status_code = isset($matches[1]) ? (int)$matches[1] : null;
    }

    if ($status_code !== 200) {
        return ['valid' => false, 'error' => "HTTP Error: $status_code"];
    }

    return ['valid' => true, 'content_type' => 'unknown'];
}

function markAsAnime($content_id, $content_type) {
    try {
        $db = new Database();
        $conn = $db->connect();

        // Update content_type column to 'anime'
        if ($content_type === 'movie') {
            $stmt = $conn->prepare("UPDATE movies SET content_type = 'anime' WHERE id = ?");
            $stmt->execute([$content_id]);
        } else {
            $stmt = $conn->prepare("UPDATE tv_shows SET content_type = 'anime' WHERE id = ?");
            $stmt->execute([$content_id]);
        }

        // Also add anime genre for backward compatibility
        $stmt = $conn->prepare("INSERT IGNORE INTO genres (name, tmdb_id) VALUES (?, ?)");
        $stmt->execute(['Anime', 16]); // 16 is TMDB's Animation genre ID

        $stmt = $conn->prepare("SELECT id FROM genres WHERE name = 'Anime'");
        $stmt->execute();
        $genre = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($genre) {
            if ($content_type === 'movie') {
                // Add to movie_genres
                $stmt = $conn->prepare("INSERT IGNORE INTO movie_genres (movie_id, genre_id) VALUES (?, ?)");
                $stmt->execute([$content_id, $genre['id']]);
            } else {
                // Add to tv_show_genres
                $stmt = $conn->prepare("INSERT IGNORE INTO tv_show_genres (tv_show_id, genre_id) VALUES (?, ?)");
                $stmt->execute([$content_id, $genre['id']]);
            }
        }
    } catch (Exception $e) {
        // Ignore errors in genre assignment
        error_log("Error marking as anime: " . $e->getMessage());
    }
}

function markAsHentai($content_id) {
    try {
        $db = new Database();
        $conn = $db->connect();

        // Update content_type column to 'hentai' (hentai is typically TV show format)
        $stmt = $conn->prepare("UPDATE tv_shows SET content_type = 'hentai' WHERE id = ?");
        $stmt->execute([$content_id]);

        // Also add hentai genre for backward compatibility
        $stmt = $conn->prepare("INSERT IGNORE INTO genres (name, tmdb_id) VALUES (?, ?)");
        $stmt->execute(['Hentai', 99999]); // Using a high number for custom genre

        $stmt = $conn->prepare("SELECT id FROM genres WHERE name = 'Hentai'");
        $stmt->execute();
        $genre = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($genre) {
            // Add to tv_show_genres
            $stmt = $conn->prepare("INSERT IGNORE INTO tv_show_genres (tv_show_id, genre_id) VALUES (?, ?)");
            $stmt->execute([$content_id, $genre['id']]);
        }
    } catch (Exception $e) {
        // Ignore errors in genre assignment
        error_log("Error marking as hentai: " . $e->getMessage());
    }
}

// Get import statistics
try {
    $db = new Database();
    $conn = $db->connect();
    
    $stats = [];
    $stmt = $conn->query("SELECT COUNT(*) as count FROM movies WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $stats['movies_today'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $stmt = $conn->query("SELECT COUNT(*) as count FROM tv_shows WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $stats['tv_shows_today'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
} catch (Exception $e) {
    $stats = ['movies_today' => 0, 'tv_shows_today' => 0];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Content Import - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="assets/admin-style.css">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .import-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        
        .import-section {
            background: var(--secondary-color);
            padding: 30px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .letsembed-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .option-card {
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 123, 255, 0.05));
            border: 2px solid rgba(0, 123, 255, 0.2);
            border-radius: 12px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .option-card:hover {
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.2), rgba(0, 123, 255, 0.1));
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
        }

        .option-card.selected {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border-color: #007bff;
            color: white;
        }

        .option-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .option-description {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            margin: 15px 0;
            overflow: hidden;
            display: none;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #00d4ff);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        .validation-success {
            color: #28a745;
            font-weight: 500;
        }

        .validation-error {
            color: #dc3545;
            font-weight: 500;
        }

        .validation-loading {
            color: #ffc107;
            font-weight: 500;
        }

        .btn-secondary {
            background: rgba(108, 117, 125, 0.8);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: rgba(108, 117, 125, 1);
            transform: translateY(-1px);
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--text-primary);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
        }
        
        .section-description {
            color: var(--text-secondary);
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 12px;
            background: var(--dark-bg);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-primary);
            font-size: 1rem;
        }
        
        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .import-stats {
            background: var(--dark-bg);
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }
        
        .letsembed-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .option-card {
            background: var(--dark-bg);
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .option-card:hover,
        .option-card.selected {
            border-color: var(--primary-color);
            background: rgba(229, 9, 20, 0.1);
        }
        
        .option-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 5px;
        }
        
        .option-description {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: var(--border-color);
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
            display: none;
        }
        
        .progress-fill {
            height: 100%;
            background: var(--primary-color);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .warning-box {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid #ffc107;
            color: #ffc107;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .warning-box h4 {
            margin-bottom: 10px;
        }

        .search-result-item {
            transition: all 0.3s ease;
        }

        .search-result-item:hover {
            background: rgba(229, 9, 20, 0.1) !important;
            border-color: var(--primary-color) !important;
        }

        .search-result-item input[type="checkbox"]:checked + div {
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .import-grid {
                grid-template-columns: 1fr;
            }
            
            .letsembed-options {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>📥 Content Import</h1>
            <p>Import movies, TV shows, anime, and other content from various sources</p>
            <div style="margin-top: 10px;">
                <a href="test-import.php" class="btn btn-secondary" style="background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;">🧪 Test Import System</a>
                <a href="test-json-urls.php" class="btn btn-warning" style="background: #ffc107; color: #212529; padding: 8px 16px; text-decoration: none; border-radius: 4px;">🔍 Test JSON URLs</a>
            </div>
        </div>

        <nav class="admin-nav">
            <a href="index.php">📊 Dashboard</a>
            <a href="movies.php">🎬 Movies</a>
            <a href="tv-shows.php">📺 TV Shows</a>
            <a href="users.php">👥 Users</a>
            <a href="servers.php">🖥️ Servers</a>
            <a href="analytics.php">📈 Analytics</a>
            <a href="import.php" class="active">📥 Import</a>
            <a href="maintenance.php">🔧 Maintenance</a>
            <a href="database-updater.php">🗄️ DB Updater</a>
            <a href="quick-setup.php">🚀 Quick Setup</a>
            <a href="settings.php">⚙️ Settings</a>
        </nav>

        <?php if (isset($message)): ?>
            <div class="success-message"><?php echo $message; ?></div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="error-message"><?php echo $error; ?></div>
        <?php endif; ?>

        <!-- Import Statistics -->
        <div class="import-stats">
            <h3 style="color: var(--text-primary); margin-bottom: 15px;">Today's Imports</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number"><?php echo $stats['movies_today']; ?></div>
                    <div class="stat-label">Movies Added</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo $stats['tv_shows_today']; ?></div>
                    <div class="stat-label">TV Shows Added</div>
                </div>
            </div>
        </div>

        <div class="import-grid">
            <!-- TMDB Search & Bulk Import -->
            <div class="import-section" style="grid-column: 1 / -1;">
                <h2 class="section-title">🔍 TMDB Search & Bulk Import</h2>
                <p class="section-description">
                    Search TMDB by name, year, or TMDB ID and bulk import selected content.
                </p>

                <form method="POST" id="searchForm" style="margin-bottom: 20px;">
                    <input type="hidden" name="action" value="search_content">

                    <div style="display: grid; grid-template-columns: 1fr auto auto auto; gap: 15px; align-items: end;">
                        <div class="form-group" style="margin-bottom: 0;">
                            <label for="search_query">Search Query:</label>
                            <input type="text" name="search_query" id="search_query"
                                   placeholder="Movie/TV show name or TMDB ID"
                                   value="<?php echo htmlspecialchars($search_query ?? ''); ?>" required>
                        </div>

                        <div class="form-group" style="margin-bottom: 0;">
                            <label for="search_type">Type:</label>
                            <select name="search_type" id="search_type">
                                <option value="multi" <?php echo ($search_type ?? '') === 'multi' ? 'selected' : ''; ?>>All</option>
                                <option value="movie" <?php echo ($search_type ?? '') === 'movie' ? 'selected' : ''; ?>>Movies</option>
                                <option value="tv" <?php echo ($search_type ?? '') === 'tv' ? 'selected' : ''; ?>>TV Shows</option>
                            </select>
                        </div>

                        <div class="form-group" style="margin-bottom: 0;">
                            <label for="search_year">Year (optional):</label>
                            <input type="number" name="search_year" id="search_year"
                                   placeholder="2023" min="1900" max="2030"
                                   value="<?php echo htmlspecialchars($search_year ?? ''); ?>">
                        </div>

                        <button type="submit" class="btn btn-primary">Search</button>
                    </div>
                </form>

                <?php if (isset($search_error)): ?>
                    <div class="error-message"><?php echo $search_error; ?></div>
                <?php endif; ?>

                <?php if (isset($search_data) && !empty($search_data)): ?>
                    <form method="POST" id="bulkImportForm">
                        <input type="hidden" name="action" value="bulk_import">

                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <h3 style="color: var(--text-primary); margin: 0;">Search Results (<?php echo count($search_data); ?> found)</h3>
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <select name="import_type" id="import_type" required style="padding: 8px; background: var(--dark-bg); border: 1px solid var(--border-color); color: var(--text-primary); border-radius: 4px;">
                                    <option value="">Import as...</option>
                                    <option value="movie">Movies</option>
                                    <option value="tv">TV Shows</option>
                                    <option value="anime">Anime</option>
                                    <option value="hentai">Hentai</option>
                                </select>
                                <button type="button" onclick="selectAll()" class="btn btn-secondary">Select All</button>
                                <button type="button" onclick="selectNone()" class="btn btn-secondary">Select None</button>
                                <button type="submit" class="btn btn-primary">Import Selected</button>
                            </div>
                        </div>

                        <div style="max-height: 400px; overflow-y: auto; border: 1px solid var(--border-color); border-radius: 6px;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px; padding: 15px;">
                                <?php foreach ($search_data as $item): ?>
                                    <div class="search-result-item" style="background: var(--dark-bg); border-radius: 6px; padding: 15px; border: 1px solid var(--border-color);">
                                        <label style="display: flex; align-items: flex-start; gap: 10px; cursor: pointer;">
                                            <input type="checkbox" name="selected_items[]"
                                                   value="<?php echo htmlspecialchars(json_encode($item)); ?>"
                                                   style="margin-top: 5px;">

                                            <div style="flex: 1;">
                                                <div style="display: flex; gap: 10px;">
                                                    <?php if (!empty($item['poster_path'])): ?>
                                                        <img src="https://image.tmdb.org/t/p/w92<?php echo $item['poster_path']; ?>"
                                                             alt="Poster" style="width: 60px; height: 90px; object-fit: cover; border-radius: 4px;">
                                                    <?php else: ?>
                                                        <div style="width: 60px; height: 90px; background: var(--border-color); border-radius: 4px; display: flex; align-items: center; justify-content: center; color: var(--text-muted); font-size: 0.8rem;">No Image</div>
                                                    <?php endif; ?>

                                                    <div style="flex: 1;">
                                                        <h4 style="color: var(--text-primary); margin: 0 0 5px 0; font-size: 1rem;">
                                                            <?php echo htmlspecialchars($item['title'] ?? $item['name'] ?? 'Unknown'); ?>
                                                        </h4>

                                                        <div style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 5px;">
                                                            <span style="background: var(--primary-color); color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8rem; margin-right: 5px;">
                                                                <?php echo strtoupper($item['media_type'] ?? 'unknown'); ?>
                                                            </span>
                                                            TMDB ID: <?php echo $item['id']; ?>
                                                        </div>

                                                        <?php if (!empty($item['release_date']) || !empty($item['first_air_date'])): ?>
                                                            <div style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 5px;">
                                                                📅 <?php echo $item['release_date'] ?? $item['first_air_date']; ?>
                                                            </div>
                                                        <?php endif; ?>

                                                        <?php if (!empty($item['vote_average'])): ?>
                                                            <div style="color: var(--text-secondary); font-size: 0.9rem;">
                                                                ⭐ <?php echo number_format($item['vote_average'], 1); ?>/10
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>

                                                <?php if (!empty($item['overview'])): ?>
                                                    <div style="color: var(--text-muted); font-size: 0.85rem; margin-top: 10px; line-height: 1.4;">
                                                        <?php echo htmlspecialchars(substr($item['overview'], 0, 150)) . (strlen($item['overview']) > 150 ? '...' : ''); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </form>
                <?php endif; ?>
            </div>

            <!-- LetsEmbed Import -->
            <div class="import-section">
                <h2 class="section-title">LetsEmbed Import</h2>
                <p class="section-description">
                    Import content from TMDB's popular and trending lists. Content will be automatically categorized and marked as featured/trending.
                </p>

                <div style="background: rgba(23, 162, 184, 0.1); border: 1px solid #17a2b8; color: #17a2b8; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
                    <strong>ℹ️ Note:</strong> Currently importing from TMDB popular/trending lists. LetsEmbed API integration can be added when their API is accessible.
                </div>
                
                <div class="warning-box">
                    <h4>⚠️ Important Notes:</h4>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>Large imports may take several minutes</li>
                        <li>Duplicate content will be skipped automatically</li>
                        <li>TMDB API rate limits apply</li>
                    </ul>
                </div>
                
                <form method="POST" id="letsembedForm">
                    <input type="hidden" name="action" value="import_letsembed">
                    
                    <div class="form-group">
                        <label>Select Content Type:</label>
                        <div class="letsembed-options">
                            <div class="option-card" onclick="selectOption('movie')">
                                <div class="option-title">Movies</div>
                                <div class="option-description">Latest movies</div>
                            </div>
                            <div class="option-card" onclick="selectOption('tv')">
                                <div class="option-title">TV Shows</div>
                                <div class="option-description">Popular series</div>
                            </div>
                            <div class="option-card" onclick="selectOption('anime')">
                                <div class="option-title">Anime</div>
                                <div class="option-description">Anime movies & series</div>
                            </div>
                            <div class="option-card" onclick="selectOption('hentai')">
                                <div class="option-title">Hentai</div>
                                <div class="option-description">Adult anime content</div>
                            </div>
                        </div>
                        <input type="hidden" name="type" id="selectedType" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="limit">Import Limit:</label>
                        <select name="limit" id="limit">
                            <option value="25">25 items</option>
                            <option value="50" selected>50 items</option>
                            <option value="100">100 items</option>
                            <option value="200">200 items</option>
                        </select>
                    </div>
                    
                    <div class="progress-bar" id="progressBar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary" id="importBtn">Start Import</button>
                </form>
            </div>

            <!-- Enhanced JSON Import -->
            <div class="import-section">
                <h2 class="section-title">🔗 Enhanced JSON Import</h2>
                <p class="section-description">
                    Import content from JSON files with advanced options. Supports large files, batch processing, and auto-detection.
                </p>

                <form method="POST" id="jsonImportForm">
                    <input type="hidden" name="action" value="import_custom_json">

                    <div class="form-group">
                        <label for="json_url">JSON URL:</label>
                        <input type="url" name="json_url" id="json_url"
                               placeholder="https://letsembed.cc/list/movie.json" required>
                        <small style="color: var(--text-secondary); margin-top: 5px; display: block;">
                            <button type="button" onclick="validateURL()" class="btn btn-secondary" style="padding: 4px 8px; font-size: 0.8rem;">🔍 Validate URL</button>
                            <button type="button" onclick="useSampleJSON()" class="btn btn-success" style="padding: 4px 8px; font-size: 0.8rem; margin-left: 5px;">📝 Sample JSON</button>
                            <span id="urlValidation" style="margin-left: 10px;"></span>
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="content_type">Content Type:</label>
                        <select name="content_type" id="content_type" required>
                            <option value="">Select type...</option>
                            <option value="auto">🤖 Auto-detect from JSON</option>
                            <option value="movie">🎬 Movies</option>
                            <option value="tv_show">📺 TV Shows</option>
                            <option value="anime_movie">🎌 Anime Movies</option>
                            <option value="anime_tv">🎌 Anime TV Shows</option>
                            <option value="hentai">🔞 Hentai</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="batch_size">Batch Size:</label>
                        <select name="batch_size" id="batch_size">
                            <option value="25">25 items per batch</option>
                            <option value="50" selected>50 items per batch</option>
                            <option value="100">100 items per batch</option>
                            <option value="200">200 items per batch</option>
                        </select>
                        <small style="color: var(--text-secondary); margin-top: 5px; display: block;">
                            Smaller batches are safer for large files but slower.
                        </small>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="auto_detect" value="1" style="margin-right: 8px;">
                            Auto-detect content type from JSON data
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary">Import from JSON</button>
                </form>

                <div style="margin-top: 20px; padding: 15px; background: var(--dark-bg); border-radius: 6px;">
                    <h4 style="color: var(--text-primary); margin-bottom: 10px;">📋 Supported JSON Formats:</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <h5 style="color: var(--primary-color); margin-bottom: 5px;">Basic Format:</h5>
                            <pre style="color: var(--text-secondary); font-size: 0.8rem; overflow-x: auto;">
[
  {"tmdb_id": 550},
  {"id": 13},
  {"tmdb": 680}
]</pre>
                        </div>
                        <div>
                            <h5 style="color: var(--primary-color); margin-bottom: 5px;">Extended Format:</h5>
                            <pre style="color: var(--text-secondary); font-size: 0.8rem; overflow-x: auto;">
[
  {
    "tmdb_id": 550,
    "title": "Fight Club",
    "media_type": "movie",
    "featured": true
  }
]</pre>
                        </div>
                    </div>
                </div>
            </div>

            <!-- LetsEmbed Direct Import -->
            <div class="import-section">
                <h2 class="section-title">🚀 LetsEmbed Direct Import</h2>
                <p class="section-description">
                    Import directly from LetsEmbed's JSON lists. These are curated lists with thousands of movies and shows.
                </p>

                <div style="background: rgba(0, 123, 255, 0.1); border: 1px solid #007bff; color: #007bff; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
                    <strong>🔥 Hot Feature:</strong> Direct access to LetsEmbed's massive content database!
                </div>

                <form method="POST" id="letsembedForm">
                    <input type="hidden" name="action" value="import_letsembed_json">

                    <div class="form-group">
                        <label>Select LetsEmbed List:</label>
                        <div class="letsembed-options">
                            <div class="option-card" onclick="selectLetsEmbedOption('movie')">
                                <div class="option-title">🎬 Movies</div>
                                <div class="option-description">Latest movie collection</div>
                            </div>
                            <div class="option-card" onclick="selectLetsEmbedOption('tv')">
                                <div class="option-title">📺 TV Shows</div>
                                <div class="option-description">Popular TV series</div>
                            </div>
                            <div class="option-card" onclick="selectLetsEmbedOption('anime')">
                                <div class="option-title">🎌 Anime</div>
                                <div class="option-description">Anime collection</div>
                            </div>
                            <div class="option-card" onclick="selectLetsEmbedOption('hentai')">
                                <div class="option-title">🔞 Hentai</div>
                                <div class="option-description">Adult anime content</div>
                            </div>
                        </div>
                        <input type="hidden" name="letsembed_type" id="selectedLetsEmbedType" required>

                        <div class="alert alert-warning" style="margin: 15px 0;">
                            <h4>⚠️ Important Notice</h4>
                            <p>LetsEmbed URLs may be temporarily unavailable. If import fails:</p>
                            <ul>
                                <li>Try again later (servers may be down)</li>
                                <li>Use <strong>Custom JSON Import</strong> instead</li>
                                <li>Test URLs first with the <a href="test-json-urls.php" target="_blank">URL Tester</a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="letsembed_batch_size">Batch Size:</label>
                        <select name="batch_size" id="letsembed_batch_size">
                            <option value="50">50 items per batch</option>
                            <option value="100" selected>100 items per batch</option>
                            <option value="200">200 items per batch</option>
                            <option value="500">500 items per batch</option>
                        </select>
                    </div>

                    <div class="progress-bar" id="letsembedProgressBar">
                        <div class="progress-fill" id="letsembedProgressFill"></div>
                    </div>

                    <button type="submit" class="btn btn-primary" id="letsembedImportBtn">Import from LetsEmbed</button>
                </form>
            </div>

            <!-- TMDB Direct Import -->
            <div class="import-section">
                <h2 class="section-title">TMDB Direct Import</h2>
                <p class="section-description">
                    Import individual content by TMDB ID or use TMDB's curated lists.
                </p>
                
                <div style="display: flex; gap: 15px; margin-bottom: 20px; flex-wrap: wrap;">
                    <button class="btn btn-secondary" onclick="bulkImport('trending_movies')">🔥 Trending Movies</button>
                    <button class="btn btn-secondary" onclick="bulkImport('popular_movies')">⭐ Popular Movies</button>
                    <button class="btn btn-secondary" onclick="bulkImport('now_playing')">🎬 Now Playing</button>
                </div>

                <div style="display: flex; gap: 15px; margin-bottom: 20px; flex-wrap: wrap;">
                    <button class="btn btn-secondary" onclick="bulkImport('trending_tv')">🔥 Trending TV</button>
                    <button class="btn btn-secondary" onclick="bulkImport('popular_tv')">⭐ Popular TV</button>
                    <button class="btn btn-secondary" onclick="bulkImport('upcoming')">🆕 Upcoming</button>
                </div>

                <div style="display: flex; gap: 15px; margin-bottom: 20px; flex-wrap: wrap;">
                    <button class="btn btn-secondary" onclick="bulkImport('anime_movies')" style="background: #ff6b6b;">🎌 Anime Movies</button>
                    <button class="btn btn-secondary" onclick="bulkImport('anime_tv')" style="background: #ff6b6b;">🎌 Anime TV</button>
                    <button class="btn btn-secondary" onclick="bulkImport('hentai')" style="background: #e74c3c;">🔞 Hentai</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedType = '';

        function selectAll() {
            document.querySelectorAll('input[name="selected_items[]"]').forEach(checkbox => {
                checkbox.checked = true;
            });
        }

        function selectNone() {
            document.querySelectorAll('input[name="selected_items[]"]').forEach(checkbox => {
                checkbox.checked = false;
            });
        }

        document.getElementById('bulkImportForm')?.addEventListener('submit', function(e) {
            const selected = document.querySelectorAll('input[name="selected_items[]"]:checked');
            const importType = document.getElementById('import_type').value;

            if (selected.length === 0) {
                e.preventDefault();
                alert('Please select at least one item to import.');
                return;
            }

            if (!importType) {
                e.preventDefault();
                alert('Please select an import type.');
                return;
            }

            if (!confirm(`Import ${selected.length} selected items as ${importType}? This may take a while.`)) {
                e.preventDefault();
                return;
            }

            const submitBtn = e.target.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.textContent = 'Importing...';
        });

        function selectOption(type) {
            // Remove previous selection
            document.querySelectorAll('.option-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Add selection to clicked card
            event.target.closest('.option-card').classList.add('selected');

            // Set hidden input value
            document.getElementById('selectedType').value = type;
            selectedType = type;
        }

        function selectLetsEmbedOption(type) {
            // Remove previous selection
            document.querySelectorAll('.letsembed-options .option-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Add selection to clicked card
            event.target.closest('.option-card').classList.add('selected');

            // Set hidden input value
            document.getElementById('selectedLetsEmbedType').value = type;
        }

        function validateURL() {
            const urlInput = document.getElementById('json_url');
            const validationSpan = document.getElementById('urlValidation');
            const url = urlInput.value.trim();

            if (!url) {
                validationSpan.innerHTML = '<span class="validation-error">❌ Please enter a URL</span>';
                return;
            }

            validationSpan.innerHTML = '<span class="validation-loading">🔄 Validating...</span>';

            // Simple URL validation
            try {
                new URL(url);

                // Check if it's a JSON URL
                if (url.includes('.json') || url.includes('json')) {
                    validationSpan.innerHTML = '<span class="validation-success">✅ Valid JSON URL</span>';
                } else {
                    validationSpan.innerHTML = '<span class="validation-error">⚠️ URL should point to a JSON file</span>';
                }
            } catch (e) {
                validationSpan.innerHTML = '<span class="validation-error">❌ Invalid URL format</span>';
            }
        }

        // Use sample JSON for testing
        function useSampleJSON() {
            const urlInput = document.getElementById('json_url');
            const contentTypeSelect = document.getElementById('content_type');

            // Use local sample JSON file
            const sampleURL = window.location.origin + window.location.pathname.replace('/admin/import.php', '/sample-movie-data.json');

            urlInput.value = sampleURL;
            contentTypeSelect.value = 'movie';

            // Validate the URL
            validateURL();

            alert('Sample JSON URL loaded! This contains 10 popular movies for testing.');
        }

        // Auto-fill popular JSON URLs
        function fillPopularURL(type) {
            const urlInput = document.getElementById('json_url');
            const contentTypeSelect = document.getElementById('content_type');

            const popularURLs = {
                'letsembed_movie': {
                    url: 'https://letsembed.cc/list/movie.json',
                    type: 'movie'
                },
                'letsembed_tv': {
                    url: 'https://letsembed.cc/list/tv.json',
                    type: 'tv_show'
                },
                'letsembed_anime': {
                    url: 'https://letsembed.cc/list/anime.json',
                    type: 'anime_movie'
                }
            };

            if (popularURLs[type]) {
                urlInput.value = popularURLs[type].url;
                contentTypeSelect.value = popularURLs[type].type;
                validateURL();
            }
        }

        // Progress tracking for large imports
        function trackImportProgress() {
            const progressBar = document.getElementById('letsembedProgressBar');
            const progressFill = document.getElementById('letsembedProgressFill');

            if (progressBar) {
                progressBar.style.display = 'block';

                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 10;
                    if (progress > 90) progress = 90;

                    progressFill.style.width = progress + '%';

                    if (progress >= 90) {
                        clearInterval(interval);
                    }
                }, 500);
            }
        }

        // Form submission handlers
        document.getElementById('letsembedForm')?.addEventListener('submit', function(e) {
            const selectedType = document.getElementById('selectedLetsEmbedType').value;
            if (!selectedType) {
                e.preventDefault();
                alert('Please select a LetsEmbed content type');
                return;
            }

            if (!confirm(`Import ${selectedType} content from LetsEmbed? This may take several minutes for large datasets.`)) {
                e.preventDefault();
                return;
            }

            trackImportProgress();

            const submitBtn = document.getElementById('letsembedImportBtn');
            submitBtn.disabled = true;
            submitBtn.textContent = 'Importing... Please wait';
        });

        document.getElementById('jsonImportForm')?.addEventListener('submit', function(e) {
            const url = document.getElementById('json_url').value;
            const contentType = document.getElementById('content_type').value;

            if (!url || !contentType) {
                e.preventDefault();
                alert('Please fill in all required fields');
                return;
            }

            if (!confirm(`Import content from JSON URL? This may take a while depending on the file size.`)) {
                e.preventDefault();
                return;
            }

            const submitBtn = e.target.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.textContent = 'Importing... Please wait';
        });
        
        document.getElementById('letsembedForm').addEventListener('submit', function(e) {
            if (!selectedType) {
                e.preventDefault();
                alert('Please select a content type first.');
                return;
            }

            const btn = document.getElementById('importBtn');
            const progressBar = document.getElementById('progressBar');

            btn.disabled = true;
            btn.textContent = 'Importing...';
            progressBar.style.display = 'block';

            // Simulate progress (since we can't track real progress)
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 5;
                if (progress > 85) progress = 85;
                document.getElementById('progressFill').style.width = progress + '%';
            }, 1000);

            // Clean up on form submission
            setTimeout(() => {
                clearInterval(interval);
                document.getElementById('progressFill').style.width = '100%';
            }, 5000);
        });
        
        async function bulkImport(type) {
            if (!confirm(`Import ${type.replace('_', ' ')}? This may take a while.`)) {
                return;
            }
            
            try {
                const response = await fetch('../api/bulk-import.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'import_' + type,
                        type: type.includes('tv') ? 'tv' : 'movie'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert(`Successfully imported ${data.count} items`);
                    location.reload();
                } else {
                    alert('Import failed: ' + data.message);
                }
            } catch (error) {
                alert('Import failed: ' + error.message);
            }
        }
    </script>
</body>
</html>
