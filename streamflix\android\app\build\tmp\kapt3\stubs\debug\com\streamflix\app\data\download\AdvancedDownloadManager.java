package com.streamflix.app.data.download;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkCapabilities;
import android.os.Build;
import androidx.core.app.NotificationCompat;
import androidx.work.*;
import com.streamflix.app.R;
import com.streamflix.app.StreamFlixApplication;
import com.streamflix.app.data.local.UserPreferences;
import com.streamflix.app.data.model.Movie;
import com.streamflix.app.data.model.TvShow;
import com.streamflix.app.data.model.Episode;
import com.streamflix.app.presentation.main.MainActivity;
import dagger.hilt.android.qualifiers.ApplicationContext;
import kotlinx.coroutines.flow.*;
import java.io.File;
import java.util.*;
import javax.inject.Inject;
import javax.inject.Singleton;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0094\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0011\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u0000 U2\u00020\u0001:\u0001UB!\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u0010H\u0082@\u00a2\u0006\u0002\u0010\u001dJ\u000e\u0010\u001e\u001a\u00020\u001fH\u0082@\u00a2\u0006\u0002\u0010 J\u0016\u0010!\u001a\u00020\u001b2\u0006\u0010\"\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010#J\u0016\u0010$\u001a\u00020\u001b2\u0006\u0010\"\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010#J\u0010\u0010%\u001a\u00020\u001b2\u0006\u0010\"\u001a\u00020\fH\u0002J6\u0010&\u001a\b\u0012\u0004\u0012\u00020\f0\'2\u0006\u0010(\u001a\u00020)2\b\b\u0002\u0010*\u001a\u00020\f2\u0006\u0010+\u001a\u00020\fH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b,\u0010-JB\u0010.\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000f0\'2\u0006\u0010/\u001a\u0002002\f\u00101\u001a\b\u0012\u0004\u0012\u0002020\u000f2\b\b\u0002\u0010*\u001a\u00020\fH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b3\u00104J\u0012\u00105\u001a\u0004\u0018\u00010\u00102\u0006\u0010\"\u001a\u00020\fH\u0002J\u0010\u00106\u001a\u00020\f2\u0006\u00107\u001a\u000208H\u0002J\u0016\u00109\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\r0:2\u0006\u0010\"\u001a\u00020\fJ\u0012\u0010;\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u000f0:J\u0016\u0010<\u001a\u00020\u001f2\u0006\u0010=\u001a\u00020>2\u0006\u0010?\u001a\u00020@J\b\u0010A\u001a\u00020\u001fH\u0002J\b\u0010B\u001a\u00020\u001bH\u0002J\u0016\u0010C\u001a\u00020\u001b2\u0006\u0010\"\u001a\u00020\fH\u0082@\u00a2\u0006\u0002\u0010#J\b\u0010D\u001a\u00020\u001bH\u0002J\u0016\u0010E\u001a\u00020\u001b2\u0006\u0010\"\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010#J\u0016\u0010F\u001a\u00020\u001b2\u0006\u0010\"\u001a\u00020\fH\u0082@\u00a2\u0006\u0002\u0010#J\u0016\u0010G\u001a\u00020\u001b2\u0006\u0010\"\u001a\u00020\fH\u0082@\u00a2\u0006\u0002\u0010#J\u0016\u0010H\u001a\u00020\u001b2\u0006\u0010\"\u001a\u00020\fH\u0082@\u00a2\u0006\u0002\u0010#J\u0016\u0010I\u001a\u00020\u001b2\u0006\u0010\"\u001a\u00020\fH\u0082@\u00a2\u0006\u0002\u0010#J\u0016\u0010J\u001a\u00020\u001b2\u0006\u0010\"\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010#J\u0016\u0010K\u001a\u00020\u001b2\u0006\u0010\"\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010#J\u0010\u0010L\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u0010H\u0002J\u0016\u0010M\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u0010H\u0082@\u00a2\u0006\u0002\u0010\u001dJ\u0016\u0010N\u001a\u00020\u001b2\u0006\u0010\"\u001a\u00020\f2\u0006\u0010O\u001a\u00020\rJ\u001e\u0010P\u001a\u00020\u001b2\u0006\u0010\"\u001a\u00020\f2\u0006\u0010Q\u001a\u00020RH\u0082@\u00a2\u0006\u0002\u0010SJ\u0018\u0010T\u001a\u00020\u001b2\u0006\u0010\"\u001a\u00020\f2\u0006\u0010O\u001a\u00020\rH\u0002R \u0010\t\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\r0\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u000f0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u000f0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R#\u0010\u0012\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\r0\u000b0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u001d\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u000f0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0015R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u000f0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0015R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006V"}, d2 = {"Lcom/streamflix/app/data/download/AdvancedDownloadManager;", "", "context", "Landroid/content/Context;", "userPreferences", "Lcom/streamflix/app/data/local/UserPreferences;", "workManager", "Landroidx/work/WorkManager;", "(Landroid/content/Context;Lcom/streamflix/app/data/local/UserPreferences;Landroidx/work/WorkManager;)V", "_activeDownloads", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "", "Lcom/streamflix/app/data/download/DownloadProgress;", "_completedDownloads", "", "Lcom/streamflix/app/data/download/DownloadItem;", "_downloadQueue", "activeDownloads", "Lkotlinx/coroutines/flow/StateFlow;", "getActiveDownloads", "()Lkotlinx/coroutines/flow/StateFlow;", "completedDownloads", "getCompletedDownloads", "downloadQueue", "getDownloadQueue", "addToQueue", "", "downloadItem", "(Lcom/streamflix/app/data/download/DownloadItem;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "canStartDownload", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cancelDownload", "downloadId", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteDownload", "deleteDownloadedFile", "downloadMovie", "Lkotlin/Result;", "movie", "Lcom/streamflix/app/data/model/Movie;", "quality", "serverUrl", "downloadMovie-BWLJW6A", "(Lcom/streamflix/app/data/model/Movie;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "downloadTvShow", "tvShow", "Lcom/streamflix/app/data/model/TvShow;", "episodes", "Lcom/streamflix/app/data/model/Episode;", "downloadTvShow-BWLJW6A", "(Lcom/streamflix/app/data/model/TvShow;Ljava/util/List;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "findDownloadItem", "formatFileSize", "bytes", "", "getDownloadProgress", "Lkotlinx/coroutines/flow/Flow;", "getDownloadedContent", "isContentDownloaded", "contentId", "", "contentType", "Lcom/streamflix/app/data/download/ContentType;", "isWiFiConnected", "loadDownloadHistory", "moveToCompleted", "observeNetworkChanges", "pauseDownload", "removeDownload", "removeFromActive", "removeFromCompleted", "removeFromQueue", "resumeDownload", "retryDownload", "showDownloadNotification", "startDownload", "updateDownloadProgress", "progress", "updateDownloadStatus", "status", "Lcom/streamflix/app/data/download/DownloadStatus;", "(Ljava/lang/String;Lcom/streamflix/app/data/download/DownloadStatus;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateProgressNotification", "Companion", "app_debug"})
public final class AdvancedDownloadManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.streamflix.app.data.local.UserPreferences userPreferences = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.work.WorkManager workManager = null;
    public static final int DOWNLOAD_NOTIFICATION_ID = 1001;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DOWNLOAD_WORK_TAG = "download_work";
    public static final int MAX_CONCURRENT_DOWNLOADS = 3;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.streamflix.app.data.download.DownloadItem>> _downloadQueue = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.streamflix.app.data.download.DownloadItem>> downloadQueue = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.Map<java.lang.String, com.streamflix.app.data.download.DownloadProgress>> _activeDownloads = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.Map<java.lang.String, com.streamflix.app.data.download.DownloadProgress>> activeDownloads = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.streamflix.app.data.download.DownloadItem>> _completedDownloads = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.streamflix.app.data.download.DownloadItem>> completedDownloads = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.streamflix.app.data.download.AdvancedDownloadManager.Companion Companion = null;
    
    @javax.inject.Inject()
    public AdvancedDownloadManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.local.UserPreferences userPreferences, @org.jetbrains.annotations.NotNull()
    androidx.work.WorkManager workManager) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.streamflix.app.data.download.DownloadItem>> getDownloadQueue() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.Map<java.lang.String, com.streamflix.app.data.download.DownloadProgress>> getActiveDownloads() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.streamflix.app.data.download.DownloadItem>> getCompletedDownloads() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object pauseDownload(@org.jetbrains.annotations.NotNull()
    java.lang.String downloadId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object resumeDownload(@org.jetbrains.annotations.NotNull()
    java.lang.String downloadId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object cancelDownload(@org.jetbrains.annotations.NotNull()
    java.lang.String downloadId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object retryDownload(@org.jetbrains.annotations.NotNull()
    java.lang.String downloadId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteDownload(@org.jetbrains.annotations.NotNull()
    java.lang.String downloadId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.streamflix.app.data.download.DownloadItem>> getDownloadedContent() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.streamflix.app.data.download.DownloadProgress> getDownloadProgress(@org.jetbrains.annotations.NotNull()
    java.lang.String downloadId) {
        return null;
    }
    
    public final boolean isContentDownloaded(int contentId, @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.download.ContentType contentType) {
        return false;
    }
    
    private final java.lang.Object canStartDownload(kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    private final boolean isWiFiConnected() {
        return false;
    }
    
    private final java.lang.Object startDownload(com.streamflix.app.data.download.DownloadItem downloadItem, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object addToQueue(com.streamflix.app.data.download.DownloadItem downloadItem, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object updateDownloadStatus(java.lang.String downloadId, com.streamflix.app.data.download.DownloadStatus status, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object moveToCompleted(java.lang.String downloadId, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object removeFromQueue(java.lang.String downloadId, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object removeFromActive(java.lang.String downloadId, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object removeFromCompleted(java.lang.String downloadId, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object removeDownload(java.lang.String downloadId, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final com.streamflix.app.data.download.DownloadItem findDownloadItem(java.lang.String downloadId) {
        return null;
    }
    
    private final void deleteDownloadedFile(java.lang.String downloadId) {
    }
    
    private final void showDownloadNotification(com.streamflix.app.data.download.DownloadItem downloadItem) {
    }
    
    private final void loadDownloadHistory() {
    }
    
    private final void observeNetworkChanges() {
    }
    
    public final void updateDownloadProgress(@org.jetbrains.annotations.NotNull()
    java.lang.String downloadId, @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.download.DownloadProgress progress) {
    }
    
    private final void updateProgressNotification(java.lang.String downloadId, com.streamflix.app.data.download.DownloadProgress progress) {
    }
    
    private final java.lang.String formatFileSize(long bytes) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/streamflix/app/data/download/AdvancedDownloadManager$Companion;", "", "()V", "DOWNLOAD_NOTIFICATION_ID", "", "DOWNLOAD_WORK_TAG", "", "MAX_CONCURRENT_DOWNLOADS", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}