<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent_blue">#0071EB</color>
    <color name="accent_green">#46D369</color>
    <color name="accent_orange">#FF6B35</color>
    <color name="accent_yellow">#F5C518</color>
    <color name="background_dark">#000000</color>
    <color name="background_light">#F5F5F5</color>
    <color name="black">#000000</color>
    <color name="card_border">#333333</color>
    <color name="card_dark">#1E1E1E</color>
    <color name="card_light">#FFFFFF</color>
    <color name="error_color">#F44336</color>
    <color name="info_color">#2196F3</color>
    <color name="live_indicator">#FF0000</color>
    <color name="new_badge">#00FF00</color>
    <color name="premium_gold">#FFD700</color>
    <color name="quality_4k">#FF9800</color>
    <color name="quality_cam">#F44336</color>
    <color name="quality_fhd">#2196F3</color>
    <color name="quality_hd">#4CAF50</color>
    <color name="streamflix_gray">#221F1F</color>
    <color name="streamflix_gray_dark">#141414</color>
    <color name="streamflix_gray_light">#2F2F2F</color>
    <color name="streamflix_red">#E50914</color>
    <color name="streamflix_red_dark">#B20710</color>
    <color name="streamflix_red_light">#FF4757</color>
    <color name="success_color">#4CAF50</color>
    <color name="surface_dark">#141414</color>
    <color name="surface_light">#FFFFFF</color>
    <color name="text_primary">#FFFFFF</color>
    <color name="text_primary_light">#000000</color>
    <color name="text_secondary">#B3B3B3</color>
    <color name="text_secondary_light">#666666</color>
    <color name="text_tertiary">#808080</color>
    <color name="transparent">#00000000</color>
    <color name="trending_badge">#FF6B35</color>
    <color name="warning_color">#FF9800</color>
    <color name="white">#FFFFFF</color>
    <string name="action_add_favorites">Add to Favorites</string>
    <string name="action_add_watchlist">Add to Watchlist</string>
    <string name="action_back">Back</string>
    <string name="action_cancel">Cancel</string>
    <string name="action_close">Close</string>
    <string name="action_delete">Delete</string>
    <string name="action_done">Done</string>
    <string name="action_download">Download</string>
    <string name="action_edit">Edit</string>
    <string name="action_more_info">More Info</string>
    <string name="action_next">Next</string>
    <string name="action_ok">OK</string>
    <string name="action_play">Play</string>
    <string name="action_previous">Previous</string>
    <string name="action_rate">Rate</string>
    <string name="action_refresh">Refresh</string>
    <string name="action_remove_favorites">Remove from Favorites</string>
    <string name="action_remove_watchlist">Remove from Watchlist</string>
    <string name="action_retry">Retry</string>
    <string name="action_review">Write Review</string>
    <string name="action_save">Save</string>
    <string name="action_share">Share</string>
    <string name="action_skip">Skip</string>
    <string name="action_trailer">Watch Trailer</string>
    <string name="app_name">StreamFlix</string>
    <string name="app_name_full">StreamFlix - Movies &amp; TV Shows</string>
    <string name="auth_already_have_account">Already have an account?</string>
    <string name="auth_confirm_password">Confirm Password</string>
    <string name="auth_create_account">Create your account</string>
    <string name="auth_dont_have_account">Don\'t have an account?</string>
    <string name="auth_email">Email</string>
    <string name="auth_forgot_password">Forgot Password?</string>
    <string name="auth_invalid_credentials">Invalid username or password</string>
    <string name="auth_invalid_email">Please enter a valid email address</string>
    <string name="auth_login">Login</string>
    <string name="auth_login_failed">Login failed</string>
    <string name="auth_login_success">Login successful</string>
    <string name="auth_logout">Logout</string>
    <string name="auth_password">Password</string>
    <string name="auth_register">Sign Up</string>
    <string name="auth_register_failed">Registration failed</string>
    <string name="auth_register_success">Registration successful</string>
    <string name="auth_remember_me">Remember me</string>
    <string name="auth_user_exists">User already exists</string>
    <string name="auth_username">Username</string>
    <string name="auth_weak_password">Password must be at least 6 characters</string>
    <string name="auth_welcome_back">Welcome back!</string>
    <string name="content_description_add_watchlist">Add to watchlist</string>
    <string name="content_description_back">Back</string>
    <string name="content_description_backdrop">Movie backdrop</string>
    <string name="content_description_close">Close</string>
    <string name="content_description_download">Download</string>
    <string name="content_description_favorite">Add to favorites</string>
    <string name="content_description_menu">Menu</string>
    <string name="content_description_play_button">Play button</string>
    <string name="content_description_poster">Movie poster</string>
    <string name="content_description_profile">Profile</string>
    <string name="content_description_remove_watchlist">Remove from watchlist</string>
    <string name="content_description_search">Search</string>
    <string name="content_description_settings">Settings</string>
    <string name="content_description_share">Share</string>
    <string name="content_description_unfavorite">Remove from favorites</string>
    <string name="details_cast">Cast</string>
    <string name="details_crew">Crew</string>
    <string name="details_director">Director</string>
    <string name="details_first_air_date">First Air Date</string>
    <string name="details_genres">Genres</string>
    <string name="details_last_air_date">Last Air Date</string>
    <string name="details_overview">Overview</string>
    <string name="details_producer">Producer</string>
    <string name="details_rating">Rating</string>
    <string name="details_recommendations">Recommendations</string>
    <string name="details_release_date">Release Date</string>
    <string name="details_reviews">Reviews</string>
    <string name="details_runtime">Runtime</string>
    <string name="details_similar">Similar</string>
    <string name="details_trailers">Trailers</string>
    <string name="details_writer">Writer</string>
    <string name="downloads_cancel">Cancel</string>
    <string name="downloads_completed">Completed</string>
    <string name="downloads_delete">Delete</string>
    <string name="downloads_downloading">Downloading</string>
    <string name="downloads_failed">Failed</string>
    <string name="downloads_no_downloads">No downloads</string>
    <string name="downloads_pause">Pause</string>
    <string name="downloads_paused">Paused</string>
    <string name="downloads_resume">Resume</string>
    <string name="downloads_retry">Retry</string>
    <string name="downloads_start_downloading">Start downloading your favorite content</string>
    <string name="downloads_storage_full">Storage full</string>
    <string name="downloads_title">Downloads</string>
    <string name="downloads_wifi_required">WiFi required for download</string>
    <string name="empty_downloads">No downloads</string>
    <string name="empty_favorites">No favorites yet</string>
    <string name="empty_history">No watch history</string>
    <string name="empty_movies">No movies found</string>
    <string name="empty_search">No search results</string>
    <string name="empty_tv_shows">No TV shows found</string>
    <string name="empty_watchlist">Your watchlist is empty</string>
    <string name="error_download_failed">Download failed</string>
    <string name="error_forbidden">Access forbidden</string>
    <string name="error_loading_failed">Failed to load content</string>
    <string name="error_maintenance">App is under maintenance</string>
    <string name="error_network">Network error. Please check your connection.</string>
    <string name="error_no_internet">No internet connection</string>
    <string name="error_not_found">Content not found</string>
    <string name="error_playback_failed">Playback failed</string>
    <string name="error_server">Server error. Please try again later.</string>
    <string name="error_timeout">Request timeout</string>
    <string name="error_unauthorized">Unauthorized access</string>
    <string name="error_unknown">An unknown error occurred.</string>
    <string name="error_update_required">Update required</string>
    <string name="favorites_add_content">Mark your favorite content</string>
    <string name="favorites_added">Added to favorites</string>
    <string name="favorites_empty">No favorites yet</string>
    <string name="favorites_removed">Removed from favorites</string>
    <string name="favorites_title">My Favorites</string>
    <string name="home_continue_watching">Continue Watching</string>
    <string name="home_discover">Discover amazing content</string>
    <string name="home_featured">Featured</string>
    <string name="home_latest">Latest</string>
    <string name="home_new_releases">New Releases</string>
    <string name="home_popular">Popular</string>
    <string name="home_recommended">Recommended for You</string>
    <string name="home_top_rated">Top Rated</string>
    <string name="home_trending">Trending Now</string>
    <string name="home_welcome">Welcome to StreamFlix</string>
    <string name="loading">Loading...</string>
    <string name="loading_details">Loading details...</string>
    <string name="loading_more">Loading more...</string>
    <string name="loading_movies">Loading movies...</string>
    <string name="loading_search">Searching...</string>
    <string name="loading_tv_shows">Loading TV shows...</string>
    <string name="maintenance_message">StreamFlix is currently under maintenance. Please try again later.</string>
    <string name="maintenance_title">Under Maintenance</string>
    <string name="movies_all">All Movies</string>
    <string name="movies_featured">Featured Movies</string>
    <string name="movies_genres">Movie Genres</string>
    <string name="movies_latest">Latest Movies</string>
    <string name="movies_popular">Popular Movies</string>
    <string name="movies_title">Movies</string>
    <string name="movies_top_rated">Top Rated Movies</string>
    <string name="movies_trending">Trending Movies</string>
    <string name="nav_downloads">Downloads</string>
    <string name="nav_favorites">Favorites</string>
    <string name="nav_home">Home</string>
    <string name="nav_movies">Movies</string>
    <string name="nav_profile">Profile</string>
    <string name="nav_search">Search</string>
    <string name="nav_settings">Settings</string>
    <string name="nav_tv_shows">TV Shows</string>
    <string name="nav_watchlist">Watchlist</string>
    <string name="notification_download_complete">Download completed</string>
    <string name="notification_download_failed">Download failed</string>
    <string name="notification_new_episode">New episode available</string>
    <string name="notification_new_season">New season available</string>
    <string name="notification_recommendation">New recommendation for you</string>
    <string name="permission_deny">Deny</string>
    <string name="permission_grant">Grant Permission</string>
    <string name="permission_notification_message">Allow notifications to get updates about downloads and new content.</string>
    <string name="permission_notification_title">Notification Permission</string>
    <string name="permission_storage_message">Storage permission is required to download content.</string>
    <string name="permission_storage_title">Storage Permission</string>
    <string name="player_audio_tracks">Audio Tracks</string>
    <string name="player_brightness">Brightness</string>
    <string name="player_buffering">Buffering...</string>
    <string name="player_error">Playback error occurred</string>
    <string name="player_exit_fullscreen">Exit Fullscreen</string>
    <string name="player_forward">Forward</string>
    <string name="player_fullscreen">Fullscreen</string>
    <string name="player_loading">Loading...</string>
    <string name="player_next_episode">Next Episode</string>
    <string name="player_pause">Pause</string>
    <string name="player_play">Play</string>
    <string name="player_previous_episode">Previous Episode</string>
    <string name="player_quality">Quality</string>
    <string name="player_rewind">Rewind</string>
    <string name="player_settings">Player Settings</string>
    <string name="player_speed">Playback Speed</string>
    <string name="player_stop">Stop</string>
    <string name="player_subtitles">Subtitles</string>
    <string name="player_volume">Volume</string>
    <string name="profile_about">About</string>
    <string name="profile_account_settings">Account Settings</string>
    <string name="profile_change_password">Change Password</string>
    <string name="profile_edit">Edit Profile</string>
    <string name="profile_free">Free</string>
    <string name="profile_help">Help &amp; Support</string>
    <string name="profile_language">Language</string>
    <string name="profile_notifications">Notifications</string>
    <string name="profile_premium">Premium</string>
    <string name="profile_privacy">Privacy</string>
    <string name="profile_privacy_policy">Privacy Policy</string>
    <string name="profile_subscription">Subscription</string>
    <string name="profile_terms">Terms of Service</string>
    <string name="profile_title">Profile</string>
    <string name="profile_upgrade">Upgrade to Premium</string>
    <string name="profile_version">Version</string>
    <string name="quality_4k">4K</string>
    <string name="quality_auto">Auto</string>
    <string name="quality_cam">CAM</string>
    <string name="quality_fhd">Full HD</string>
    <string name="quality_hd">HD</string>
    <string name="quality_sd">SD</string>
    <string name="search_clear_history">Clear Search History</string>
    <string name="search_hint">Search movies, TV shows...</string>
    <string name="search_no_results">No results found</string>
    <string name="search_popular">Popular Searches</string>
    <string name="search_recent">Recent Searches</string>
    <string name="search_results">Search Results</string>
    <string name="search_title">Search</string>
    <string name="search_try_different">Try a different search term</string>
    <string name="settings_about">About</string>
    <string name="settings_audio">Audio</string>
    <string name="settings_audio_language">Audio Language</string>
    <string name="settings_autoplay">Auto Play</string>
    <string name="settings_clear_cache">Clear Cache</string>
    <string name="settings_clear_downloads">Clear Downloads</string>
    <string name="settings_download_quality">Download Quality</string>
    <string name="settings_downloads">Downloads</string>
    <string name="settings_general">General</string>
    <string name="settings_notifications">Notifications</string>
    <string name="settings_parental_control">Parental Control</string>
    <string name="settings_privacy">Privacy</string>
    <string name="settings_storage_location">Storage Location</string>
    <string name="settings_subtitle_language">Subtitle Language</string>
    <string name="settings_subtitles">Subtitles</string>
    <string name="settings_theme">Theme</string>
    <string name="settings_theme_dark">Dark</string>
    <string name="settings_theme_light">Light</string>
    <string name="settings_theme_system">System Default</string>
    <string name="settings_title">Settings</string>
    <string name="settings_video">Video</string>
    <string name="settings_video_quality">Video Quality</string>
    <string name="settings_video_quality_4k">4K</string>
    <string name="settings_video_quality_auto">Auto</string>
    <string name="settings_video_quality_fhd">Full HD</string>
    <string name="settings_video_quality_hd">HD</string>
    <string name="settings_wifi_only">WiFi Only Downloads</string>
    <string name="time_days_ago">%d days ago</string>
    <string name="time_hours_ago">%d hours ago</string>
    <string name="time_hours_minutes">%dh %dm</string>
    <string name="time_just_now">Just now</string>
    <string name="time_minutes">%d min</string>
    <string name="time_minutes_ago">%d minutes ago</string>
    <string name="time_weeks_ago">%d weeks ago</string>
    <string name="tv_episode_number">Episode %d</string>
    <string name="tv_episodes">Episodes</string>
    <string name="tv_season_number">Season %d</string>
    <string name="tv_seasons">Seasons</string>
    <string name="tv_shows_all">All TV Shows</string>
    <string name="tv_shows_featured">Featured TV Shows</string>
    <string name="tv_shows_genres">TV Show Genres</string>
    <string name="tv_shows_latest">Latest TV Shows</string>
    <string name="tv_shows_popular">Popular TV Shows</string>
    <string name="tv_shows_title">TV Shows</string>
    <string name="tv_shows_top_rated">Top Rated TV Shows</string>
    <string name="tv_shows_trending">Trending TV Shows</string>
    <string name="update_available">Update Available</string>
    <string name="update_later">Later</string>
    <string name="update_message">A new version of StreamFlix is available.</string>
    <string name="update_now">Update Now</string>
    <string name="update_required">Update Required</string>
    <string name="update_required_message">Please update to the latest version to continue using StreamFlix.</string>
    <string name="watchlist_add_content">Add movies and TV shows to watch later</string>
    <string name="watchlist_added">Added to watchlist</string>
    <string name="watchlist_empty">Your watchlist is empty</string>
    <string name="watchlist_removed">Removed from watchlist</string>
    <string name="watchlist_title">My Watchlist</string>
    <style name="Theme.StreamFlix" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/streamflix_red</item>
        <item name="colorPrimaryVariant">@color/streamflix_red_dark</item>
        <item name="colorOnPrimary">@color/white</item>

        
        <item name="colorSecondary">@color/streamflix_gray</item>
        <item name="colorSecondaryVariant">@color/streamflix_gray_light</item>
        <item name="colorOnSecondary">@color/white</item>

        
        <item name="android:colorBackground">@color/background_dark</item>
        <item name="colorSurface">@color/surface_dark</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="colorOnBackground">@color/text_primary</item>

        
        <item name="colorError">@color/error_color</item>
        <item name="colorOnError">@color/white</item>

        
        <item name="android:statusBarColor">@color/background_dark</item>
        <item name="android:windowLightStatusBar">false</item>

        
        <item name="android:navigationBarColor">@color/background_dark</item>
        <item name="android:windowLightNavigationBar">false</item>

        
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    </style>
    <style name="Theme.StreamFlix.AppCompat" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/streamflix_red</item>
        <item name="colorPrimaryDark">@color/streamflix_red_dark</item>
        <item name="colorAccent">@color/streamflix_red</item>
        
        
        <item name="android:colorBackground">@color/background_dark</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        
        
        <item name="android:statusBarColor">@color/background_dark</item>
        <item name="android:windowLightStatusBar">false</item>
        
        
        <item name="android:navigationBarColor">@color/background_dark</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    </style>
    <style name="Theme.StreamFlix.Player" parent="Theme.StreamFlix">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowBackground">@color/black</item>
    </style>
    <style name="Theme.StreamFlix.Splash" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="android:windowBackground">@color/background_dark</item>
        <item name="android:statusBarColor">@color/background_dark</item>
        <item name="android:navigationBarColor">@color/background_dark</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style>
    <style name="Theme.StreamFlix.Splash.Simple" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="android:windowBackground">@color/background_dark</item>
        <item name="android:statusBarColor">@color/background_dark</item>
        <item name="android:navigationBarColor">@color/background_dark</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style>
</resources>