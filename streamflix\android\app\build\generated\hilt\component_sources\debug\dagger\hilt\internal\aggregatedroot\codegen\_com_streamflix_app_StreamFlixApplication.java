package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.streamflix.app.StreamFlixApplication",
    rootPackage = "com.streamflix.app",
    originatingRoot = "com.streamflix.app.StreamFlixApplication",
    originatingRootPackage = "com.streamflix.app",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "StreamFlixApplication",
    originatingRootSimpleNames = "StreamFlixApplication"
)
public class _com_streamflix_app_StreamFlixApplication {
}
