<?php
require_once 'config/database.php';

echo "<h1>🔞 Convert TV Shows to Hentai Content</h1>";
echo "<p>This script will identify and convert hentai content from TV shows to proper hentai category.</p>";

try {
    $db = new Database();
    $conn = $db->connect();
    
    // Get database name
    $stmt = $conn->query("SELECT DATABASE() as db_name");
    $db_info = $stmt->fetch(PDO::FETCH_ASSOC);
    $database_name = $db_info['db_name'];
    
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>📊 Database Information</h3>";
    echo "<p><strong>Database:</strong> {$database_name}</p>";
    echo "<p><strong>Connection:</strong> ✅ Active</p>";
    echo "</div>";
    
    // Step 1: Find potential hentai content
    echo "<h3>🔍 Step 1: Identifying Hentai Content</h3>";
    
    $hentai_detection_queries = [
        'title_based' => "
            SELECT id, tmdb_id, name, overview, content_type
            FROM tv_shows 
            WHERE (
                LOWER(name) LIKE '%hentai%' 
                OR LOWER(name) LIKE '%ecchi%'
                OR LOWER(name) LIKE '%adult%'
                OR LOWER(name) LIKE '%18+%'
                OR LOWER(name) LIKE '%xxx%'
                OR LOWER(name) LIKE '%erotic%'
                OR LOWER(name) LIKE '%nsfw%'
            )
            AND (content_type != 'hentai' OR content_type IS NULL)
        ",
        'overview_based' => "
            SELECT id, tmdb_id, name, overview, content_type
            FROM tv_shows 
            WHERE (
                LOWER(overview) LIKE '%hentai%' 
                OR LOWER(overview) LIKE '%adult%'
                OR LOWER(overview) LIKE '%erotic%'
                OR LOWER(overview) LIKE '%mature%'
                OR LOWER(overview) LIKE '%sexual%'
            )
            AND (content_type != 'hentai' OR content_type IS NULL)
        ",
        'genre_based' => "
            SELECT DISTINCT t.id, t.tmdb_id, t.name, t.overview, t.content_type
            FROM tv_shows t
            JOIN tv_show_genres tg ON t.id = tg.tv_show_id
            JOIN genres g ON tg.genre_id = g.id
            WHERE (
                LOWER(g.name) LIKE '%hentai%'
                OR LOWER(g.name) LIKE '%adult%'
                OR LOWER(g.name) LIKE '%erotic%'
                OR LOWER(g.name) LIKE '%mature%'
                OR LOWER(g.name) LIKE '%18+%'
            )
            AND (t.content_type != 'hentai' OR t.content_type IS NULL)
        "
    ];
    
    $all_hentai_candidates = [];
    $detection_stats = [];
    
    foreach ($hentai_detection_queries as $method => $query) {
        try {
            $stmt = $conn->query($query);
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $detection_stats[$method] = count($results);
            
            foreach ($results as $result) {
                $all_hentai_candidates[$result['id']] = $result;
            }
            
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
            echo "<h4>📋 " . ucfirst(str_replace('_', ' ', $method)) . " Detection:</h4>";
            echo "<p><strong>Found:</strong> " . count($results) . " items</p>";
            
            if (count($results) > 0) {
                echo "<div style='max-height: 200px; overflow-y: auto; background: white; padding: 10px; border-radius: 5px; margin-top: 10px;'>";
                foreach (array_slice($results, 0, 10) as $item) {
                    echo "<div style='margin-bottom: 8px; padding: 8px; background: #f8f9fa; border-radius: 4px; border-left: 4px solid #dc3545;'>";
                    echo "<strong>{$item['name']}</strong> (ID: {$item['tmdb_id']})<br>";
                    echo "<small>Current Type: " . ($item['content_type'] ?? 'NULL') . "</small>";
                    echo "</div>";
                }
                if (count($results) > 10) {
                    echo "<p><small>... and " . (count($results) - 10) . " more items</small></p>";
                }
                echo "</div>";
            }
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "❌ <strong>Error in {$method} detection:</strong> " . $e->getMessage();
            echo "</div>";
        }
    }
    
    $total_candidates = count($all_hentai_candidates);
    
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>📊 Detection Summary:</h4>";
    echo "<ul>";
    foreach ($detection_stats as $method => $count) {
        echo "<li><strong>" . ucfirst(str_replace('_', ' ', $method)) . ":</strong> {$count} items</li>";
    }
    echo "</ul>";
    echo "<p><strong>Total Unique Candidates:</strong> {$total_candidates}</p>";
    echo "</div>";
    
    // Step 2: Manual review and confirmation
    if ($total_candidates > 0) {
        echo "<h3>👀 Step 2: Manual Review</h3>";
        
        echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h4>🔍 Review Candidates:</h4>";
        echo "<p>Please review the following items before conversion:</p>";
        
        echo "<div style='max-height: 400px; overflow-y: auto; background: white; padding: 15px; border-radius: 8px; margin-top: 15px;'>";
        foreach ($all_hentai_candidates as $candidate) {
            echo "<div style='margin-bottom: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #dc3545;'>";
            echo "<div style='display: flex; justify-content: space-between; align-items: start;'>";
            echo "<div style='flex: 1;'>";
            echo "<h5 style='margin: 0 0 8px 0; color: #dc3545;'>{$candidate['name']}</h5>";
            echo "<p style='margin: 0 0 8px 0; font-size: 0.9rem; color: #666;'><strong>TMDB ID:</strong> {$candidate['tmdb_id']}</p>";
            echo "<p style='margin: 0 0 8px 0; font-size: 0.9rem; color: #666;'><strong>Current Type:</strong> " . ($candidate['content_type'] ?? 'NULL') . "</p>";
            if (!empty($candidate['overview'])) {
                $short_overview = strlen($candidate['overview']) > 150 ? substr($candidate['overview'], 0, 150) . '...' : $candidate['overview'];
                echo "<p style='margin: 0; font-size: 0.85rem; color: #888;'><strong>Overview:</strong> {$short_overview}</p>";
            }
            echo "</div>";
            echo "<div style='margin-left: 15px;'>";
            echo "<input type='checkbox' id='convert_{$candidate['id']}' name='convert_items[]' value='{$candidate['id']}' checked>";
            echo "<label for='convert_{$candidate['id']}' style='margin-left: 5px; font-size: 0.9rem;'>Convert</label>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
        echo "</div>";
        echo "</div>";
        
        // Conversion form
        echo "<h3>🔄 Step 3: Perform Conversion</h3>";
        
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h4>⚙️ Conversion Options:</h4>";
        
        echo "<form method='POST' action='' style='margin-top: 15px;'>";
        echo "<input type='hidden' name='action' value='convert'>";
        
        echo "<div style='margin-bottom: 15px;'>";
        echo "<label style='display: block; margin-bottom: 5px; font-weight: bold;'>Conversion Method:</label>";
        echo "<label style='display: block; margin-bottom: 8px;'>";
        echo "<input type='radio' name='conversion_method' value='all' checked> Convert All Detected Items";
        echo "</label>";
        echo "<label style='display: block; margin-bottom: 8px;'>";
        echo "<input type='radio' name='conversion_method' value='selected'> Convert Only Selected Items (use checkboxes above)";
        echo "</label>";
        echo "<label style='display: block; margin-bottom: 8px;'>";
        echo "<input type='radio' name='conversion_method' value='manual'> Manual ID Entry";
        echo "</label>";
        echo "</div>";
        
        echo "<div id='manualIds' style='display: none; margin-bottom: 15px;'>";
        echo "<label style='display: block; margin-bottom: 5px; font-weight: bold;'>Manual TMDB IDs (comma separated):</label>";
        echo "<textarea name='manual_ids' rows='3' style='width: 100%; padding: 10px; border-radius: 5px; border: 1px solid #ccc;' placeholder='123456, 789012, 345678'></textarea>";
        echo "</div>";
        
        echo "<div style='margin-bottom: 15px;'>";
        echo "<label style='display: flex; align-items: center; gap: 8px;'>";
        echo "<input type='checkbox' name='backup_first' checked>";
        echo "<span>Create backup before conversion (recommended)</span>";
        echo "</label>";
        echo "</div>";
        
        echo "<div style='margin-bottom: 15px;'>";
        echo "<label style='display: flex; align-items: center; gap: 8px;'>";
        echo "<input type='checkbox' name='update_genres' checked>";
        echo "<span>Update/add hentai genre</span>";
        echo "</label>";
        echo "</div>";
        
        echo "<button type='submit' style='background: #dc3545; color: white; padding: 15px 30px; border: none; border-radius: 8px; font-size: 1.1rem; font-weight: bold; cursor: pointer;'>";
        echo "🔄 Start Conversion";
        echo "</button>";
        
        echo "</form>";
        echo "</div>";
        
        // JavaScript for form interaction
        echo "<script>";
        echo "document.addEventListener('DOMContentLoaded', function() {";
        echo "    const radios = document.querySelectorAll('input[name=\"conversion_method\"]');";
        echo "    const manualDiv = document.getElementById('manualIds');";
        echo "    ";
        echo "    radios.forEach(radio => {";
        echo "        radio.addEventListener('change', function() {";
        echo "            if (this.value === 'manual') {";
        echo "                manualDiv.style.display = 'block';";
        echo "            } else {";
        echo "                manualDiv.style.display = 'none';";
        echo "            }";
        echo "        });";
        echo "    });";
        echo "});";
        echo "</script>";
        
    } else {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h4>✅ No Hentai Content Found</h4>";
        echo "<p>No TV shows were detected as hentai content. Your database is already properly categorized!</p>";
        echo "</div>";
    }
    
    // Handle conversion request
    if ($_POST['action'] ?? '' === 'convert') {
        echo "<h3>🔄 Step 4: Performing Conversion</h3>";

        $conversion_method = $_POST['conversion_method'] ?? 'all';
        $backup_first = isset($_POST['backup_first']);
        $update_genres = isset($_POST['update_genres']);

        $items_to_convert = [];

        // Determine which items to convert
        switch ($conversion_method) {
            case 'all':
                $items_to_convert = array_keys($all_hentai_candidates);
                break;
            case 'selected':
                $items_to_convert = $_POST['convert_items'] ?? [];
                break;
            case 'manual':
                $manual_ids = $_POST['manual_ids'] ?? '';
                if (!empty($manual_ids)) {
                    $tmdb_ids = array_map('trim', explode(',', $manual_ids));
                    $tmdb_ids = array_filter($tmdb_ids, 'is_numeric');

                    if (!empty($tmdb_ids)) {
                        $placeholders = str_repeat('?,', count($tmdb_ids) - 1) . '?';
                        $stmt = $conn->prepare("SELECT id FROM tv_shows WHERE tmdb_id IN ($placeholders)");
                        $stmt->execute($tmdb_ids);
                        $items_to_convert = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    }
                }
                break;
        }

        if (empty($items_to_convert)) {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
            echo "❌ <strong>No items selected for conversion!</strong>";
            echo "</div>";
        } else {
            echo "<div style='background: #cff4fc; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
            echo "<h4>🔄 Starting Conversion Process</h4>";
            echo "<p><strong>Items to convert:</strong> " . count($items_to_convert) . "</p>";
            echo "<p><strong>Method:</strong> " . ucfirst($conversion_method) . "</p>";
            echo "<p><strong>Backup:</strong> " . ($backup_first ? 'Yes' : 'No') . "</p>";
            echo "<p><strong>Update Genres:</strong> " . ($update_genres ? 'Yes' : 'No') . "</p>";
            echo "</div>";

            $conversion_results = [
                'success' => 0,
                'failed' => 0,
                'errors' => []
            ];

            // Create backup if requested
            if ($backup_first) {
                echo "<h5>💾 Creating Backup</h5>";
                try {
                    $backup_table = "tv_shows_backup_" . date('Y_m_d_H_i_s');
                    $conn->exec("CREATE TABLE {$backup_table} AS SELECT * FROM tv_shows WHERE id IN (" . implode(',', array_map('intval', $items_to_convert)) . ")");
                    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                    echo "✅ <strong>Backup created:</strong> {$backup_table}";
                    echo "</div>";
                } catch (Exception $e) {
                    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                    echo "❌ <strong>Backup failed:</strong> " . $e->getMessage();
                    echo "</div>";
                }
            }

            // Perform conversions
            echo "<h5>🔄 Converting Items</h5>";

            $update_stmt = $conn->prepare("UPDATE tv_shows SET content_type = 'hentai' WHERE id = ?");

            foreach ($items_to_convert as $item_id) {
                try {
                    // Get item details
                    $stmt = $conn->prepare("SELECT tmdb_id, name FROM tv_shows WHERE id = ?");
                    $stmt->execute([$item_id]);
                    $item = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($item) {
                        // Update content type
                        $update_stmt->execute([$item_id]);

                        // Update genres if requested
                        if ($update_genres) {
                            // Check if hentai genre exists
                            $stmt = $conn->prepare("SELECT id FROM genres WHERE LOWER(name) = 'hentai'");
                            $stmt->execute();
                            $hentai_genre = $stmt->fetch(PDO::FETCH_ASSOC);

                            if (!$hentai_genre) {
                                // Create hentai genre
                                $stmt = $conn->prepare("INSERT INTO genres (tmdb_id, name) VALUES (?, 'Hentai')");
                                $stmt->execute([99999]); // Use a high number for custom genre
                                $hentai_genre_id = $conn->lastInsertId();
                            } else {
                                $hentai_genre_id = $hentai_genre['id'];
                            }

                            // Add hentai genre to the show if not already present
                            $stmt = $conn->prepare("SELECT id FROM tv_show_genres WHERE tv_show_id = ? AND genre_id = ?");
                            $stmt->execute([$item_id, $hentai_genre_id]);

                            if (!$stmt->fetch()) {
                                $stmt = $conn->prepare("INSERT INTO tv_show_genres (tv_show_id, genre_id) VALUES (?, ?)");
                                $stmt->execute([$item_id, $hentai_genre_id]);
                            }
                        }

                        $conversion_results['success']++;
                        echo "<div style='background: #d4edda; padding: 8px; border-radius: 4px; margin: 3px 0;'>";
                        echo "✅ <strong>{$item['name']}</strong> (TMDB: {$item['tmdb_id']}) → Converted to Hentai";
                        echo "</div>";

                    } else {
                        $conversion_results['failed']++;
                        $conversion_results['errors'][] = "Item ID {$item_id} not found";
                    }

                } catch (Exception $e) {
                    $conversion_results['failed']++;
                    $conversion_results['errors'][] = "Item ID {$item_id}: " . $e->getMessage();

                    echo "<div style='background: #f8d7da; padding: 8px; border-radius: 4px; margin: 3px 0;'>";
                    echo "❌ <strong>Error converting item {$item_id}:</strong> " . $e->getMessage();
                    echo "</div>";
                }
            }

            // Final results
            echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
            echo "<h4>📊 Conversion Results</h4>";
            echo "<ul>";
            echo "<li><strong>✅ Successfully Converted:</strong> {$conversion_results['success']}</li>";
            echo "<li><strong>❌ Failed:</strong> {$conversion_results['failed']}</li>";
            echo "<li><strong>📝 Total Processed:</strong> " . count($items_to_convert) . "</li>";
            echo "</ul>";

            if (!empty($conversion_results['errors'])) {
                echo "<h5>❌ Errors:</h5>";
                echo "<ul>";
                foreach ($conversion_results['errors'] as $error) {
                    echo "<li style='color: #dc3545;'>{$error}</li>";
                }
                echo "</ul>";
            }
            echo "</div>";

            // Test links
            if ($conversion_results['success'] > 0) {
                echo "<div style='background: #cff4fc; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
                echo "<h4>🎯 Test Converted Content</h4>";
                echo "<p><a href='test_hentai_servers.php' target='_blank' style='background: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin-right: 10px; display: inline-block; margin-bottom: 10px;'>🧪 Test Hentai Servers</a></p>";
                echo "<p><a href='admin/servers.php' target='_blank' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block; margin-bottom: 10px;'>🖥️ Manage Servers</a></p>";
                echo "</div>";
            }
        }
    }

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4 style='color: #721c24;'>❌ Database Error</h4>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
