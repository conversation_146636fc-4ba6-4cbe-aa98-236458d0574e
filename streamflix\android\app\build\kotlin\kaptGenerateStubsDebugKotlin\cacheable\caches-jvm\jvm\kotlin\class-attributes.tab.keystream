(com.streamflix.app.StreamFlixApplication2com.streamflix.app.StreamFlixApplication.Companion.com.streamflix.app.StreamFlixApplicationSimple8com.streamflix.app.StreamFlixApplicationSimple.Companion0com.streamflix.app.data.api.StreamFlixApiService(com.streamflix.app.data.api.LoginRequest+com.streamflix.app.data.api.RegisterRequest0com.streamflix.app.data.api.UpdateProfileRequest1com.streamflix.app.data.api.ChangePasswordRequest1com.streamflix.app.data.api.AddToWatchlistRequest6com.streamflix.app.data.api.RemoveFromWatchlistRequest1com.streamflix.app.data.api.AddToFavoritesRequest6com.streamflix.app.data.api.RemoveFromFavoritesRequest4com.streamflix.app.data.api.AddToWatchHistoryRequest*com.streamflix.app.data.api.MoviesResponse-com.streamflix.app.data.api.MovieListResponse0com.streamflix.app.data.api.MovieDetailsResponse+com.streamflix.app.data.api.TvShowsResponse.com.streamflix.app.data.api.TvShowListResponse1com.streamflix.app.data.api.TvShowDetailsResponse+com.streamflix.app.data.api.SeasonsResponse,com.streamflix.app.data.api.EpisodesResponse*com.streamflix.app.data.api.GenresResponse+com.streamflix.app.data.api.ServersResponse-com.streamflix.app.data.api.WatchlistResponse-com.streamflix.app.data.api.FavoritesResponse0com.streamflix.app.data.api.WatchHistoryResponse4com.streamflix.app.data.api.ContinueWatchingResponse%com.streamflix.app.data.api.AppConfig'com.streamflix.app.data.api.AppFeatures,com.streamflix.app.data.api.PaginationConfig8com.streamflix.app.data.download.AdvancedDownloadManagerBcom.streamflix.app.data.download.AdvancedDownloadManager.Companion-com.streamflix.app.data.download.DownloadItem1com.streamflix.app.data.download.DownloadProgress,com.streamflix.app.data.download.ContentType/com.streamflix.app.data.download.DownloadStatus/com.streamflix.app.data.download.DownloadWorker-com.streamflix.app.data.local.UserPreferences7com.streamflix.app.data.local.UserPreferences.Companion9com.streamflix.app.data.local.UserPreferences.AppSettings)com.streamflix.app.data.model.ApiResponse/com.streamflix.app.data.model.PaginatedResponse#com.streamflix.app.data.model.Movie$com.streamflix.app.data.model.TvShow#com.streamflix.app.data.model.Genre"com.streamflix.app.data.model.Cast$com.streamflix.app.data.model.Season%com.streamflix.app.data.model.Episode$com.streamflix.app.data.model.Server"com.streamflix.app.data.model.User*com.streamflix.app.data.model.AuthResponse+com.streamflix.app.data.model.WatchlistItem.com.streamflix.app.data.model.WatchlistContent(com.streamflix.app.data.model.AppVersion&com.streamflix.app.data.model.HomeData*com.streamflix.app.data.model.SearchResult/com.streamflix.app.data.model.MovieListResponse+com.streamflix.app.data.model.MovieListData0com.streamflix.app.data.model.TvShowListResponse,com.streamflix.app.data.model.TvShowListData.com.streamflix.app.data.model.WatchHistoryItem2com.streamflix.app.data.offline.OfflineModeManager2com.streamflix.app.data.offline.OfflineStorageInfo2com.streamflix.app.data.offline.OfflineContentItem4com.streamflix.app.data.offline.OfflineSearchHistory6com.streamflix.app.data.offline.OfflineUserPreferences1com.streamflix.app.data.offline.OfflineContentDao7com.streamflix.app.data.offline.OfflineSearchHistoryDao9com.streamflix.app.data.offline.OfflineUserPreferencesDao/<EMAIL>$com.streamflix.app.di.DatabaseModule#com.streamflix.app.di.NetworkModule2com.streamflix.app.presentation.home.HomeViewModel0com.streamflix.app.presentation.home.HomeUiState0com.streamflix.app.presentation.home.ContentItem1com.streamflix.app.presentation.main.MainActivity2com.streamflix.app.presentation.main.BottomNavItem2com.streamflix.app.presentation.main.MainViewModel0com.streamflix.app.presentation.main.MainUiState;com.streamflix.app.presentation.player.AdBlockWebViewClient6com.streamflix.app.presentation.search.SearchViewModel4com.streamflix.app.presentation.search.SearchUiState5com.streamflix.app.presentation.splash.SplashActivity6com.streamflix.app.presentation.splash.SplashViewModel4com.streamflix.app.presentation.splash.SplashUiStateBcom.streamflix.app.presentation.splash.SplashNavigationDestination0com.streamflix.app.ui.theme.StreamFlixTextStyles!com.streamflix.app.utils.Resource)com.streamflix.app.utils.Resource.Success'com.streamflix.app.utils.Resource.Error)com.streamflix.app.utils.Resource.Loading                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              