package com.streamflix.app.presentation.splash;

import androidx.lifecycle.ViewModel;
import com.streamflix.app.BuildConfig;
import com.streamflix.app.data.repository.StreamFlixRepository;
import com.streamflix.app.utils.Resource;
import dagger.hilt.android.lifecycle.HiltViewModel;
import kotlinx.coroutines.flow.*;
import javax.inject.Inject;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0005\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005\u00a8\u0006\u0006"}, d2 = {"Lcom/streamflix/app/presentation/splash/SplashNavigationDestination;", "", "(Ljava/lang/String;I)V", "None", "Main", "Auth", "app_debug"})
public enum SplashNavigationDestination {
    /*public static final*/ None /* = new None() */,
    /*public static final*/ Main /* = new Main() */,
    /*public static final*/ Auth /* = new Auth() */;
    
    SplashNavigationDestination() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.streamflix.app.presentation.splash.SplashNavigationDestination> getEntries() {
        return null;
    }
}