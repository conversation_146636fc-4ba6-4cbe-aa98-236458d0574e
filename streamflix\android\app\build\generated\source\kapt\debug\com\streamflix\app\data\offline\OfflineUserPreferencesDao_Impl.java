package com.streamflix.app.data.offline;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@SuppressWarnings({"unchecked", "deprecation"})
public final class OfflineUserPreferencesDao_Impl implements OfflineUserPreferencesDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<OfflineUserPreferences> __insertionAdapterOfOfflineUserPreferences;

  public OfflineUserPreferencesDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfOfflineUserPreferences = new EntityInsertionAdapter<OfflineUserPreferences>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `offline_user_preferences` (`id`,`preferencesJson`,`lastUpdated`) VALUES (?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final OfflineUserPreferences entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getPreferencesJson() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getPreferencesJson());
        }
        statement.bindLong(3, entity.getLastUpdated());
      }
    };
  }

  @Override
  public Object insertOrUpdatePreferences(final OfflineUserPreferences preferences,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfOfflineUserPreferences.insert(preferences);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPreferences(final Continuation<? super OfflineUserPreferences> $completion) {
    final String _sql = "SELECT * FROM offline_user_preferences WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<OfflineUserPreferences>() {
      @Override
      @Nullable
      public OfflineUserPreferences call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPreferencesJson = CursorUtil.getColumnIndexOrThrow(_cursor, "preferencesJson");
          final int _cursorIndexOfLastUpdated = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUpdated");
          final OfflineUserPreferences _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpPreferencesJson;
            if (_cursor.isNull(_cursorIndexOfPreferencesJson)) {
              _tmpPreferencesJson = null;
            } else {
              _tmpPreferencesJson = _cursor.getString(_cursorIndexOfPreferencesJson);
            }
            final long _tmpLastUpdated;
            _tmpLastUpdated = _cursor.getLong(_cursorIndexOfLastUpdated);
            _result = new OfflineUserPreferences(_tmpId,_tmpPreferencesJson,_tmpLastUpdated);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
