package com.streamflix.app.data.repository

import com.streamflix.app.data.api.*
import com.streamflix.app.data.local.UserPreferences
import com.streamflix.app.data.model.*
import com.streamflix.app.utils.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import retrofit2.Response
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class StreamFlixRepository @Inject constructor(
    private val apiService: StreamFlixApiService,
    private val userPreferences: UserPreferences
) {

    // ==================== Authentication ====================
    
    suspend fun login(username: String, password: String): Flow<Resource<AuthResponse>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.login(LoginRequest(username, password))
            if (response.isSuccessful && response.body()?.success == true) {
                val authResponse = response.body()!!.data!!
                // Save token and user data
                userPreferences.saveAuthToken(authResponse.token)
                userPreferences.saveUser(authResponse.user)
                emit(Resource.Success(authResponse))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Login failed"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun register(username: String, email: String, password: String): Flow<Resource<AuthResponse>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.register(RegisterRequest(username, email, password))
            if (response.isSuccessful && response.body()?.success == true) {
                val authResponse = response.body()!!.data!!
                userPreferences.saveAuthToken(authResponse.token)
                userPreferences.saveUser(authResponse.user)
                emit(Resource.Success(authResponse))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Registration failed"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun logout() {
        userPreferences.clearAuthData()
    }
    
    suspend fun refreshToken(): Flow<Resource<AuthResponse>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.refreshToken()
            if (response.isSuccessful && response.body()?.success == true) {
                val authResponse = response.body()!!.data!!
                userPreferences.saveAuthToken(authResponse.token)
                userPreferences.saveUser(authResponse.user)
                emit(Resource.Success(authResponse))
            } else {
                emit(Resource.Error("Token refresh failed"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun getProfile(): Flow<Resource<User>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getProfile()
            if (response.isSuccessful && response.body()?.success == true) {
                val user = response.body()!!.data!!
                userPreferences.saveUser(user)
                emit(Resource.Success(user))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get profile"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }

    // ==================== Movies ====================
    
    suspend fun getMovies(page: Int = 1, limit: Int = 20): Flow<Resource<com.streamflix.app.data.api.MovieListResponse>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getMovies(page, limit)
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get movies"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun getFeaturedMovies(limit: Int = 10): Flow<Resource<List<Movie>>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getFeaturedMovies(limit)
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!.movies))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get featured movies"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun getTrendingMovies(limit: Int = 10): Flow<Resource<List<Movie>>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getTrendingMovies(limit)
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!.movies))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get trending movies"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun getPopularMovies(limit: Int = 20): Flow<Resource<List<Movie>>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getPopularMovies(limit)
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!.movies))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get popular movies"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun getLatestMovies(limit: Int = 20): Flow<Resource<List<Movie>>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getLatestMovies(limit)
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!.movies))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get latest movies"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun getMovieDetails(movieId: Int): Flow<Resource<Movie>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getMovieDetails(movieId)
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!.movie))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get movie details"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun searchMovies(query: String, page: Int = 1): Flow<Resource<com.streamflix.app.data.api.MovieListResponse>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.searchMovies(query, page)
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to search movies"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun getMovieGenres(): Flow<Resource<List<Genre>>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getMovieGenres()
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!.genres))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get genres"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun getMoviesByGenre(genreId: Int, page: Int = 1): Flow<Resource<com.streamflix.app.data.api.MovieListResponse>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getMoviesByGenre(genreId, page)
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get movies by genre"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun getMovieServers(movieId: Int): Flow<Resource<List<Server>>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getMovieServers(movieId)
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!.servers))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get servers"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }

    // ==================== TV Shows ====================
    
    suspend fun getTvShows(page: Int = 1, limit: Int = 20): Flow<Resource<com.streamflix.app.data.api.TvShowListResponse>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getTvShows(page, limit)
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get TV shows"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun getFeaturedTvShows(limit: Int = 10): Flow<Resource<List<TvShow>>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getFeaturedTvShows(limit)
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!.tv_shows))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get featured TV shows"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun getTrendingTvShows(limit: Int = 10): Flow<Resource<List<TvShow>>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getTrendingTvShows(limit)
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!.tv_shows))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get trending TV shows"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun getTvShowDetails(tvShowId: Int): Flow<Resource<TvShow>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getTvShowDetails(tvShowId)
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!.tv_show))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get TV show details"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun getTvShowSeasons(tvShowId: Int): Flow<Resource<List<Season>>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getTvShowSeasons(tvShowId)
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!.seasons))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get seasons"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun getTvShowEpisodes(seasonId: Int): Flow<Resource<List<Episode>>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getTvShowEpisodes(seasonId)
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!.episodes))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get episodes"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }

    suspend fun getTvShowsByGenre(genreId: Int, page: Int = 1, limit: Int = 20): Flow<Resource<com.streamflix.app.data.api.TvShowListResponse>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getTvShowsByGenre(genreId, page, limit)
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get TV shows by genre"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }

    suspend fun getWatchHistory(): Flow<Resource<List<WatchHistoryItem>>> = flow {
        emit(Resource.Loading())
        try {
            // Mock data for now - replace with actual API call
            val mockHistory = listOf<WatchHistoryItem>()
            emit(Resource.Success(mockHistory))
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Failed to get watch history"))
        }
    }

    suspend fun getPopularTvShows(limit: Int = 20): Flow<Resource<List<TvShow>>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getFeaturedTvShows(limit)
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!.tv_shows))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get popular TV shows"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }

    // ==================== User ====================
    
    suspend fun getWatchlist(page: Int = 1): Flow<Resource<com.streamflix.app.data.api.WatchlistResponse>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getWatchlist(page)
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get watchlist"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun addToWatchlist(type: String, contentId: Int): Flow<Resource<Unit>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.addToWatchlist(AddToWatchlistRequest(type, contentId))
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(Unit))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to add to watchlist"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun removeFromWatchlist(type: String, contentId: Int): Flow<Resource<Unit>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.removeFromWatchlist(RemoveFromWatchlistRequest(type, contentId))
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(Unit))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to remove from watchlist"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun getContinueWatching(): Flow<Resource<List<WatchHistoryItem>>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getContinueWatching()
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!.continue_watching))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get continue watching"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }

    // ==================== App ====================
    
    suspend fun checkAppVersion(clientVersion: String): Flow<Resource<AppVersion>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.checkAppVersion(clientVersion)
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to check app version"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun getHomeData(): Flow<Resource<HomeData>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getHomeData()
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get home data"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun globalSearch(query: String): Flow<Resource<SearchResult>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.globalSearch(query)
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Search failed"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }
    
    suspend fun getAllGenres(): Flow<Resource<List<Genre>>> = flow {
        emit(Resource.Loading())
        try {
            val response = apiService.getAllGenres()
            if (response.isSuccessful && response.body()?.success == true) {
                emit(Resource.Success(response.body()!!.data!!.genres))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Failed to get genres"))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Network error"))
        }
    }

    // ==================== Helper Functions ====================
    
    private suspend fun <T> handleApiResponse(
        response: Response<ApiResponse<T>>,
        emit: suspend (Resource<T>) -> Unit
    ) {
        if (response.isSuccessful && response.body()?.success == true) {
            response.body()!!.data?.let { data ->
                emit(Resource.Success(data))
            } ?: emit(Resource.Error("No data received"))
        } else {
            emit(Resource.Error(response.body()?.message ?: "API call failed"))
        }
    }
    
    fun isLoggedIn(): Flow<Boolean> = userPreferences.isLoggedIn()
    
    fun getAuthToken(): Flow<String?> = userPreferences.getAuthToken()
    
    fun getCurrentUser(): Flow<User?> = userPreferences.getCurrentUser()
}
