package com.streamflix.app.presentation.main

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.streamflix.app.data.repository.StreamFlixRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MainViewModel @Inject constructor(
    private val repository: StreamFlixRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()

    init {
        checkAuthenticationStatus()
    }

    private fun checkAuthenticationStatus() {
        viewModelScope.launch {
            repository.isLoggedIn().collect { isLoggedIn ->
                _uiState.value = _uiState.value.copy(
                    isAuthenticated = isLoggedIn
                )
            }
        }
    }
}

data class MainUiState(
    val isAuthenticated: Boolean = false,
    val currentUser: com.streamflix.app.data.model.User? = null
)
