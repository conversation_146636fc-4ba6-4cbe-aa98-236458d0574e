<?php
/**
 * API Configuration
 * StreamFlix Mobile App API
 */

// CORS Headers for mobile app
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include required files
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

// API Configuration
define('API_VERSION', '1.0');
define('JWT_SECRET', 'streamflix_jwt_secret_key_2024'); // Change this in production
define('JWT_EXPIRY', 7 * 24 * 60 * 60); // 7 days

/**
 * API Response Class
 */
class APIResponse {
    
    public static function success($data = null, $message = 'Success', $code = 200) {
        http_response_code($code);
        echo json_encode([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => time(),
            'version' => API_VERSION
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
    
    public static function error($message = 'Error', $code = 400, $errors = null) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'message' => $message,
            'errors' => $errors,
            'timestamp' => time(),
            'version' => API_VERSION
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
    
    public static function unauthorized($message = 'Unauthorized') {
        self::error($message, 401);
    }
    
    public static function forbidden($message = 'Forbidden') {
        self::error($message, 403);
    }
    
    public static function notFound($message = 'Not Found') {
        self::error($message, 404);
    }
    
    public static function serverError($message = 'Internal Server Error') {
        self::error($message, 500);
    }
}

/**
 * JWT Helper Class
 */
class JWT {
    
    public static function encode($payload) {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode($payload);
        
        $headerEncoded = self::base64UrlEncode($header);
        $payloadEncoded = self::base64UrlEncode($payload);
        
        $signature = hash_hmac('sha256', $headerEncoded . "." . $payloadEncoded, JWT_SECRET, true);
        $signatureEncoded = self::base64UrlEncode($signature);
        
        return $headerEncoded . "." . $payloadEncoded . "." . $signatureEncoded;
    }
    
    public static function decode($jwt) {
        $parts = explode('.', $jwt);
        if (count($parts) !== 3) {
            return false;
        }
        
        $header = json_decode(self::base64UrlDecode($parts[0]), true);
        $payload = json_decode(self::base64UrlDecode($parts[1]), true);
        $signature = self::base64UrlDecode($parts[2]);
        
        $expectedSignature = hash_hmac('sha256', $parts[0] . "." . $parts[1], JWT_SECRET, true);
        
        if (!hash_equals($signature, $expectedSignature)) {
            return false;
        }
        
        if (isset($payload['exp']) && $payload['exp'] < time()) {
            return false;
        }
        
        return $payload;
    }
    
    private static function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
    
    private static function base64UrlDecode($data) {
        return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
    }
}

/**
 * API Authentication
 */
class APIAuth {
    
    public static function authenticate() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        
        if (!$authHeader || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            APIResponse::unauthorized('Missing or invalid authorization header');
        }
        
        $token = $matches[1];
        $payload = JWT::decode($token);
        
        if (!$payload) {
            APIResponse::unauthorized('Invalid or expired token');
        }
        
        // Verify user exists
        try {
            $db = new Database();
            $conn = $db->connect();
            
            $stmt = $conn->prepare("SELECT * FROM users WHERE id = ? AND is_active = 1");
            $stmt->execute([$payload['user_id']]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                APIResponse::unauthorized('User not found or inactive');
            }
            
            return $user;
            
        } catch (Exception $e) {
            APIResponse::serverError('Database error');
        }
    }
    
    public static function optionalAuth() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        
        if (!$authHeader || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return null;
        }
        
        $token = $matches[1];
        $payload = JWT::decode($token);
        
        if (!$payload) {
            return null;
        }
        
        try {
            $db = new Database();
            $conn = $db->connect();
            
            $stmt = $conn->prepare("SELECT * FROM users WHERE id = ? AND is_active = 1");
            $stmt->execute([$payload['user_id']]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $user ?: null;
            
        } catch (Exception $e) {
            return null;
        }
    }
}

/**
 * API Helper Functions
 */
function getRequestMethod() {
    return $_SERVER['REQUEST_METHOD'];
}

function getRequestData() {
    $input = file_get_contents('php://input');
    return json_decode($input, true) ?: [];
}

function validateRequired($data, $required) {
    $missing = [];
    foreach ($required as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            $missing[] = $field;
        }
    }
    
    if (!empty($missing)) {
        APIResponse::error('Missing required fields: ' . implode(', ', $missing), 400);
    }
}

function sanitizeApiInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeApiInput', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function formatMovieForAPI($movie) {
    return [
        'id' => (int)$movie['id'],
        'title' => $movie['title'],
        'overview' => $movie['overview'],
        'poster_path' => $movie['poster_path'],
        'backdrop_path' => $movie['backdrop_path'],
        'release_date' => $movie['release_date'],
        'vote_average' => (float)$movie['vote_average'],
        'vote_count' => (int)$movie['vote_count'],
        'popularity' => (float)$movie['popularity'],
        'runtime' => (int)$movie['runtime'],
        'is_featured' => (bool)$movie['is_featured'],
        'is_trending' => (bool)$movie['is_trending'],
        'created_at' => $movie['created_at']
    ];
}

function formatTVShowForAPI($show) {
    return [
        'id' => (int)$show['id'],
        'name' => $show['name'],
        'overview' => $show['overview'],
        'poster_path' => $show['poster_path'],
        'backdrop_path' => $show['backdrop_path'],
        'first_air_date' => $show['first_air_date'],
        'last_air_date' => $show['last_air_date'],
        'vote_average' => (float)$show['vote_average'],
        'vote_count' => (int)$show['vote_count'],
        'popularity' => (float)$show['popularity'],
        'number_of_seasons' => (int)$show['number_of_seasons'],
        'number_of_episodes' => (int)$show['number_of_episodes'],
        'is_featured' => (bool)$show['is_featured'],
        'is_trending' => (bool)$show['is_trending'],
        'created_at' => $show['created_at']
    ];
}

function formatUserForAPI($user) {
    return [
        'id' => (int)$user['id'],
        'username' => $user['username'],
        'email' => $user['email'],
        'role' => $user['role'],
        'is_premium' => (bool)$user['is_premium'],
        'created_at' => $user['created_at']
    ];
}

// Log API requests
function logAPIRequest($endpoint, $method, $user_id = null) {
    try {
        if (function_exists('logInfo')) {
            logInfo('API Request', "API: {$method} {$endpoint}", $user_id);
        }
    } catch (Exception $e) {
        // Silent fail
    }
}

// Check app maintenance mode
function checkMaintenanceMode() {
    try {
        $db = new Database();
        $conn = $db->connect();
        
        $stmt = $conn->prepare("SELECT setting_value FROM app_settings WHERE setting_key = 'maintenance_mode'");
        $stmt->execute();
        $maintenance = $stmt->fetchColumn();
        
        if ($maintenance == '1') {
            $stmt = $conn->prepare("SELECT setting_value FROM app_settings WHERE setting_key = 'maintenance_message'");
            $stmt->execute();
            $message = $stmt->fetchColumn() ?: 'App is under maintenance. Please try again later.';
            
            APIResponse::error($message, 503);
        }
        
    } catch (Exception $e) {
        // Continue if can't check maintenance mode
    }
}

// Initialize API
checkMaintenanceMode();
?>
