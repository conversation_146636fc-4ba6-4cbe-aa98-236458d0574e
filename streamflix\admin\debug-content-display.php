<?php
session_start();
require_once '../config/database.php';

echo "<h1>🔍 Content Display Debug</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
    .section { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    table { width: 100%; border-collapse: collapse; margin: 15px 0; }
    th, td { padding: 8px; border: 1px solid #ddd; text-align: left; font-size: 0.9rem; }
    th { background: #f8f9fa; }
    .btn { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
    .btn-danger { background: #dc3545; }
    .btn-success { background: #28a745; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 0.85rem; }
</style>";

try {
    $db = new Database();
    $conn = $db->connect();
    
    echo "<div class='section success'>";
    echo "<h3>✅ Database Connection Successful</h3>";
    echo "</div>";
    
    // Check TV shows table
    echo "<div class='section'>";
    echo "<h3>📺 TV Shows Analysis</h3>";
    
    // Total TV shows
    $stmt = $conn->query("SELECT COUNT(*) FROM tv_shows");
    $total_tv_shows = $stmt->fetchColumn();
    
    // Content type distribution
    $stmt = $conn->query("
        SELECT 
            COALESCE(content_type, 'NULL') as content_type,
            COUNT(*) as count
        FROM tv_shows 
        GROUP BY content_type
        ORDER BY count DESC
    ");
    $tv_content_types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Total TV Shows:</strong> {$total_tv_shows}</p>";
    
    echo "<h4>Content Type Distribution:</h4>";
    echo "<table>";
    echo "<tr><th>Content Type</th><th>Count</th><th>Percentage</th></tr>";
    foreach ($tv_content_types as $type) {
        $percentage = round(($type['count'] / $total_tv_shows) * 100, 2);
        echo "<tr>";
        echo "<td>{$type['content_type']}</td>";
        echo "<td>{$type['count']}</td>";
        echo "<td>{$percentage}%</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // Check Movies table
    echo "<div class='section'>";
    echo "<h3>🎬 Movies Analysis</h3>";
    
    // Total movies
    $stmt = $conn->query("SELECT COUNT(*) FROM movies");
    $total_movies = $stmt->fetchColumn();
    
    // Content type distribution
    $stmt = $conn->query("
        SELECT 
            COALESCE(content_type, 'NULL') as content_type,
            COUNT(*) as count
        FROM movies 
        GROUP BY content_type
        ORDER BY count DESC
    ");
    $movie_content_types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Total Movies:</strong> {$total_movies}</p>";
    
    echo "<h4>Content Type Distribution:</h4>";
    echo "<table>";
    echo "<tr><th>Content Type</th><th>Count</th><th>Percentage</th></tr>";
    foreach ($movie_content_types as $type) {
        $percentage = round(($type['count'] / $total_movies) * 100, 2);
        echo "<tr>";
        echo "<td>{$type['content_type']}</td>";
        echo "<td>{$type['count']}</td>";
        echo "<td>{$percentage}%</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // Test hentai content queries
    echo "<div class='section'>";
    echo "<h3>🔞 Hentai Content Test</h3>";
    
    // Current hentai content
    $stmt = $conn->query("SELECT COUNT(*) FROM tv_shows WHERE content_type = 'hentai'");
    $hentai_tv_count = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM movies WHERE content_type = 'hentai'");
    $hentai_movie_count = $stmt->fetchColumn();
    
    echo "<p><strong>Hentai TV Shows:</strong> {$hentai_tv_count}</p>";
    echo "<p><strong>Hentai Movies:</strong> {$hentai_movie_count}</p>";
    echo "<p><strong>Total Hentai:</strong> " . ($hentai_tv_count + $hentai_movie_count) . "</p>";
    
    // Potential hentai content
    $stmt = $conn->query("
        SELECT COUNT(*) FROM tv_shows 
        WHERE (
            LOWER(name) LIKE '%hentai%' OR 
            LOWER(name) LIKE '%ecchi%' OR 
            LOWER(name) LIKE '%adult%' OR 
            LOWER(overview) LIKE '%hentai%' OR 
            LOWER(overview) LIKE '%adult%'
        ) AND (content_type != 'hentai' OR content_type IS NULL)
    ");
    $potential_hentai_tv = $stmt->fetchColumn();
    
    echo "<p><strong>Potential Hentai TV Shows:</strong> {$potential_hentai_tv}</p>";
    
    // Show some examples
    if ($hentai_tv_count > 0) {
        echo "<h4>Current Hentai TV Shows (First 5):</h4>";
        $stmt = $conn->query("
            SELECT id, tmdb_id, name, content_type 
            FROM tv_shows 
            WHERE content_type = 'hentai' 
            LIMIT 5
        ");
        $hentai_examples = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>ID</th><th>TMDB ID</th><th>Name</th><th>Type</th></tr>";
        foreach ($hentai_examples as $item) {
            echo "<tr>";
            echo "<td>{$item['id']}</td>";
            echo "<td>{$item['tmdb_id']}</td>";
            echo "<td>" . htmlspecialchars($item['name']) . "</td>";
            echo "<td>{$item['content_type']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    if ($potential_hentai_tv > 0) {
        echo "<h4>Potential Hentai TV Shows (First 5):</h4>";
        $stmt = $conn->query("
            SELECT id, tmdb_id, name, content_type 
            FROM tv_shows 
            WHERE (
                LOWER(name) LIKE '%hentai%' OR 
                LOWER(name) LIKE '%ecchi%' OR 
                LOWER(name) LIKE '%adult%' OR 
                LOWER(overview) LIKE '%hentai%' OR 
                LOWER(overview) LIKE '%adult%'
            ) AND (content_type != 'hentai' OR content_type IS NULL)
            LIMIT 5
        ");
        $potential_examples = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>ID</th><th>TMDB ID</th><th>Name</th><th>Type</th></tr>";
        foreach ($potential_examples as $item) {
            echo "<tr>";
            echo "<td>{$item['id']}</td>";
            echo "<td>{$item['tmdb_id']}</td>";
            echo "<td>" . htmlspecialchars($item['name']) . "</td>";
            echo "<td>" . ($item['content_type'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";
    
    // Test anime content queries
    echo "<div class='section'>";
    echo "<h3>🎌 Anime Content Test</h3>";
    
    // Current anime content
    $stmt = $conn->query("SELECT COUNT(*) FROM tv_shows WHERE content_type = 'anime'");
    $anime_tv_count = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM movies WHERE content_type = 'anime'");
    $anime_movie_count = $stmt->fetchColumn();
    
    echo "<p><strong>Anime TV Shows:</strong> {$anime_tv_count}</p>";
    echo "<p><strong>Anime Movies:</strong> {$anime_movie_count}</p>";
    echo "<p><strong>Total Anime:</strong> " . ($anime_tv_count + $anime_movie_count) . "</p>";
    
    // Potential anime content
    $stmt = $conn->query("
        SELECT COUNT(*) FROM tv_shows 
        WHERE (
            LOWER(name) LIKE '%anime%' OR 
            LOWER(name) LIKE '%manga%' OR 
            LOWER(overview) LIKE '%anime%' OR 
            LOWER(overview) LIKE '%manga%'
        ) AND (content_type != 'anime' OR content_type IS NULL)
    ");
    $potential_anime_tv = $stmt->fetchColumn();
    
    echo "<p><strong>Potential Anime TV Shows:</strong> {$potential_anime_tv}</p>";
    echo "</div>";
    
    // Test exact queries from the pages
    echo "<div class='section'>";
    echo "<h3>🧪 Page Query Tests</h3>";
    
    // Test hentai-content.php query
    echo "<h4>Hentai Content Page Query:</h4>";
    $filter = 'hentai';
    $where_conditions = [];
    $params = [];
    
    if ($filter === 'hentai') {
        $where_conditions[] = "content_type = 'hentai'";
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    $sql = "SELECT COUNT(*) FROM tv_shows {$where_clause}";
    
    echo "<pre>{$sql}</pre>";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    $hentai_query_result = $stmt->fetchColumn();
    
    echo "<p><strong>Result:</strong> {$hentai_query_result} items</p>";
    
    // Test anime-content.php query
    echo "<h4>Anime Content Page Query:</h4>";
    $filter = 'anime';
    $content_type = 'all';
    
    $union_queries = [];
    $params = [];
    
    // Movies query
    if ($content_type === 'all' || $content_type === 'movies') {
        $movie_where = [];
        if ($filter === 'anime') {
            $movie_where[] = "content_type = 'anime'";
        }
        $movie_where_clause = !empty($movie_where) ? 'WHERE ' . implode(' AND ', $movie_where) : '';
        $union_queries[] = "SELECT COUNT(*) FROM movies {$movie_where_clause}";
    }
    
    // TV Shows query
    if ($content_type === 'all' || $content_type === 'tv_shows') {
        $tv_where = [];
        if ($filter === 'anime') {
            $tv_where[] = "content_type = 'anime'";
        }
        $tv_where_clause = !empty($tv_where) ? 'WHERE ' . implode(' AND ', $tv_where) : '';
        $union_queries[] = "SELECT COUNT(*) FROM tv_shows {$tv_where_clause}";
    }
    
    $total_anime_items = 0;
    foreach ($union_queries as $query) {
        echo "<pre>{$query}</pre>";
        $stmt = $conn->prepare($query);
        $stmt->execute();
        $count = $stmt->fetchColumn();
        echo "<p><strong>Result:</strong> {$count} items</p>";
        $total_anime_items += $count;
    }
    
    echo "<p><strong>Total Anime Items:</strong> {$total_anime_items}</p>";
    echo "</div>";
    
    // Quick conversion suggestions
    echo "<div class='section'>";
    echo "<h3>🔧 Quick Actions</h3>";
    
    if ($potential_hentai_tv > 0) {
        echo "<div class='warning'>";
        echo "<p><strong>⚠️ Found {$potential_hentai_tv} potential hentai items!</strong></p>";
        echo "<a href='quick-hentai-anime-converter.php' class='btn btn-danger'>🔄 Auto-Convert Hentai</a>";
        echo "</div>";
    }
    
    if ($potential_anime_tv > 0) {
        echo "<div class='info'>";
        echo "<p><strong>ℹ️ Found {$potential_anime_tv} potential anime items!</strong></p>";
        echo "<a href='quick-hentai-anime-converter.php' class='btn btn-success'>🔄 Auto-Convert Anime</a>";
        echo "</div>";
    }
    
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='hentai-content.php' class='btn'>🔞 Test Hentai Page</a>";
    echo "<a href='anime-content.php' class='btn'>🎌 Test Anime Page</a>";
    echo "<a href='index.php' class='btn'>📊 Admin Dashboard</a>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section error'>";
    echo "<h3>❌ Error</h3>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
