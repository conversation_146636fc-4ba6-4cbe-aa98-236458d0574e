package com.streamflix.app.data.repository;

import com.streamflix.app.data.api.*;
import com.streamflix.app.data.local.UserPreferences;
import com.streamflix.app.data.model.*;
import com.streamflix.app.utils.Resource;
import kotlinx.coroutines.flow.Flow;
import retrofit2.Response;
import javax.inject.Inject;
import javax.inject.Singleton;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00c0\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000b\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J*\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\b2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010\u000fJ\"\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\t0\b2\u0006\u0010\u0012\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\u0013J \u0010\u0014\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160\u00150\t0\bH\u0086@\u00a2\u0006\u0002\u0010\u0017J\u000e\u0010\u0018\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\f0\bJ \u0010\u0019\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001a0\u00150\t0\bH\u0086@\u00a2\u0006\u0002\u0010\u0017J\u000e\u0010\u001b\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u001c0\bJ*\u0010\u001d\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001e0\u00150\t0\b2\b\b\u0002\u0010\u001f\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010 J*\u0010!\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\"0\u00150\t0\b2\b\b\u0002\u0010\u001f\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010 J\u001a\u0010#\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020$0\t0\bH\u0086@\u00a2\u0006\u0002\u0010\u0017J*\u0010%\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001e0\u00150\t0\b2\b\b\u0002\u0010\u001f\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010 J\"\u0010&\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001e0\t0\b2\u0006\u0010\'\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010 J \u0010(\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160\u00150\t0\bH\u0086@\u00a2\u0006\u0002\u0010\u0017J(\u0010)\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020*0\u00150\t0\b2\u0006\u0010\'\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010 J.\u0010+\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020,0\t0\b2\b\b\u0002\u0010-\u001a\u00020\u000e2\b\b\u0002\u0010\u001f\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010.J,\u0010/\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020,0\t0\b2\u0006\u00100\u001a\u00020\u000e2\b\b\u0002\u0010-\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010.J*\u00101\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001e0\u00150\t0\b2\b\b\u0002\u0010\u001f\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010 J*\u00102\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\"0\u00150\t0\b2\b\b\u0002\u0010\u001f\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010 J\u001a\u00103\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001c0\t0\bH\u0086@\u00a2\u0006\u0002\u0010\u0017J*\u00104\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001e0\u00150\t0\b2\b\b\u0002\u0010\u001f\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010 J*\u00105\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\"0\u00150\t0\b2\b\b\u0002\u0010\u001f\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010 J\"\u00106\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\"0\t0\b2\u0006\u00107\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010 J(\u00108\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002090\u00150\t0\b2\u0006\u0010:\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010 J(\u0010;\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020<0\u00150\t0\b2\u0006\u00107\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010 J.\u0010=\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020>0\t0\b2\b\b\u0002\u0010-\u001a\u00020\u000e2\b\b\u0002\u0010\u001f\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010.J6\u0010?\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020>0\t0\b2\u0006\u00100\u001a\u00020\u000e2\b\b\u0002\u0010-\u001a\u00020\u000e2\b\b\u0002\u0010\u001f\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010@J \u0010A\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001a0\u00150\t0\bH\u0086@\u00a2\u0006\u0002\u0010\u0017J$\u0010B\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020C0\t0\b2\b\b\u0002\u0010-\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010 J\"\u0010D\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020E0\t0\b2\u0006\u0010F\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\u0013JR\u0010G\u001a\u00020\n\"\u0004\b\u0000\u0010H2\u0012\u0010I\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002HH0K0J2(\u0010L\u001a$\b\u0001\u0012\n\u0012\b\u0012\u0004\u0012\u0002HH0\t\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0N\u0012\u0006\u0012\u0004\u0018\u00010\u00010MH\u0082@\u00a2\u0006\u0002\u0010OJ\f\u0010P\u001a\b\u0012\u0004\u0012\u00020Q0\bJ*\u0010R\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020S0\t0\b2\u0006\u0010T\u001a\u00020\f2\u0006\u0010U\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010VJ\u000e\u0010W\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u0017J\u001a\u0010X\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020S0\t0\bH\u0086@\u00a2\u0006\u0002\u0010\u0017J2\u0010Y\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020S0\t0\b2\u0006\u0010T\u001a\u00020\f2\u0006\u0010Z\u001a\u00020\f2\u0006\u0010U\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010[J*\u0010\\\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\b2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010\u000fJ,\u0010]\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020,0\t0\b2\u0006\u0010F\u001a\u00020\f2\b\b\u0002\u0010-\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010\u000fR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006^"}, d2 = {"Lcom/streamflix/app/data/repository/StreamFlixRepository;", "", "apiService", "Lcom/streamflix/app/data/api/StreamFlixApiService;", "userPreferences", "Lcom/streamflix/app/data/local/UserPreferences;", "(Lcom/streamflix/app/data/api/StreamFlixApiService;Lcom/streamflix/app/data/local/UserPreferences;)V", "addToWatchlist", "Lkotlinx/coroutines/flow/Flow;", "Lcom/streamflix/app/utils/Resource;", "", "type", "", "contentId", "", "(Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "checkAppVersion", "Lcom/streamflix/app/data/model/AppVersion;", "clientVersion", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllGenres", "", "Lcom/streamflix/app/data/model/Genre;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAuthToken", "getContinueWatching", "Lcom/streamflix/app/data/model/WatchHistoryItem;", "getCurrentUser", "Lcom/streamflix/app/data/model/User;", "getFeaturedMovies", "Lcom/streamflix/app/data/model/Movie;", "limit", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getFeaturedTvShows", "Lcom/streamflix/app/data/model/TvShow;", "getHomeData", "Lcom/streamflix/app/data/model/HomeData;", "getLatestMovies", "getMovieDetails", "movieId", "getMovieGenres", "getMovieServers", "Lcom/streamflix/app/data/model/Server;", "getMovies", "Lcom/streamflix/app/data/api/MovieListResponse;", "page", "(IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getMoviesByGenre", "genreId", "getPopularMovies", "getPopularTvShows", "getProfile", "getTrendingMovies", "getTrendingTvShows", "getTvShowDetails", "tvShowId", "getTvShowEpisodes", "Lcom/streamflix/app/data/model/Episode;", "seasonId", "getTvShowSeasons", "Lcom/streamflix/app/data/model/Season;", "getTvShows", "Lcom/streamflix/app/data/api/TvShowListResponse;", "getTvShowsByGenre", "(IIILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getWatchHistory", "getWatchlist", "Lcom/streamflix/app/data/api/WatchlistResponse;", "globalSearch", "Lcom/streamflix/app/data/model/SearchResult;", "query", "handleApiResponse", "T", "response", "Lretrofit2/Response;", "Lcom/streamflix/app/data/model/ApiResponse;", "emit", "Lkotlin/Function2;", "Lkotlin/coroutines/Continuation;", "(Lretrofit2/Response;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isLoggedIn", "", "login", "Lcom/streamflix/app/data/model/AuthResponse;", "username", "password", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "logout", "refreshToken", "register", "email", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "removeFromWatchlist", "searchMovies", "app_debug"})
public final class StreamFlixRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.streamflix.app.data.api.StreamFlixApiService apiService = null;
    @org.jetbrains.annotations.NotNull()
    private final com.streamflix.app.data.local.UserPreferences userPreferences = null;
    
    @javax.inject.Inject()
    public StreamFlixRepository(@org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.api.StreamFlixApiService apiService, @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.local.UserPreferences userPreferences) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object login(@org.jetbrains.annotations.NotNull()
    java.lang.String username, @org.jetbrains.annotations.NotNull()
    java.lang.String password, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<com.streamflix.app.data.model.AuthResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object register(@org.jetbrains.annotations.NotNull()
    java.lang.String username, @org.jetbrains.annotations.NotNull()
    java.lang.String email, @org.jetbrains.annotations.NotNull()
    java.lang.String password, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<com.streamflix.app.data.model.AuthResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object logout(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object refreshToken(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<com.streamflix.app.data.model.AuthResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getProfile(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<com.streamflix.app.data.model.User>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getMovies(int page, int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<com.streamflix.app.data.api.MovieListResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getFeaturedMovies(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.Movie>>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTrendingMovies(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.Movie>>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getPopularMovies(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.Movie>>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getLatestMovies(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.Movie>>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getMovieDetails(int movieId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<com.streamflix.app.data.model.Movie>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object searchMovies(@org.jetbrains.annotations.NotNull()
    java.lang.String query, int page, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<com.streamflix.app.data.api.MovieListResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getMovieGenres(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.Genre>>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getMoviesByGenre(int genreId, int page, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<com.streamflix.app.data.api.MovieListResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getMovieServers(int movieId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.Server>>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTvShows(int page, int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<com.streamflix.app.data.api.TvShowListResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getFeaturedTvShows(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.TvShow>>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTrendingTvShows(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.TvShow>>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTvShowDetails(int tvShowId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<com.streamflix.app.data.model.TvShow>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTvShowSeasons(int tvShowId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.Season>>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTvShowEpisodes(int seasonId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.Episode>>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTvShowsByGenre(int genreId, int page, int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<com.streamflix.app.data.api.TvShowListResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getWatchHistory(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.WatchHistoryItem>>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getPopularTvShows(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.TvShow>>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getWatchlist(int page, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<com.streamflix.app.data.api.WatchlistResponse>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object addToWatchlist(@org.jetbrains.annotations.NotNull()
    java.lang.String type, int contentId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<kotlin.Unit>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object removeFromWatchlist(@org.jetbrains.annotations.NotNull()
    java.lang.String type, int contentId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<kotlin.Unit>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getContinueWatching(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.WatchHistoryItem>>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object checkAppVersion(@org.jetbrains.annotations.NotNull()
    java.lang.String clientVersion, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<com.streamflix.app.data.model.AppVersion>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getHomeData(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<com.streamflix.app.data.model.HomeData>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object globalSearch(@org.jetbrains.annotations.NotNull()
    java.lang.String query, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<com.streamflix.app.data.model.SearchResult>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAllGenres(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.Genre>>>> $completion) {
        return null;
    }
    
    private final <T extends java.lang.Object>java.lang.Object handleApiResponse(retrofit2.Response<com.streamflix.app.data.model.ApiResponse<T>> response, kotlin.jvm.functions.Function2<? super com.streamflix.app.utils.Resource<T>, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> emit, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> isLoggedIn() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.String> getAuthToken() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.streamflix.app.data.model.User> getCurrentUser() {
        return null;
    }
}