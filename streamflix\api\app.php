<?php
/**
 * App API Endpoints
 * /api/app.php
 */

require_once 'config.php';

$method = getRequestMethod();
$data = getRequestData();
$path = $_GET['action'] ?? '';

logAPIRequest("app/{$path}", $method);

try {
    $db = new Database();
    $conn = $db->connect();
    
    switch ($path) {
        case 'version':
            handleAppVersion($conn);
            break;
            
        case 'config':
            handleAppConfig($conn);
            break;
            
        case 'search':
            handleGlobalSearch($conn);
            break;
            
        case 'home':
            handleHomePage($conn);
            break;
            
        case 'genres':
            handleAllGenres($conn);
            break;
            
        case 'servers':
            handleServersList($conn);
            break;
            
        default:
            APIResponse::notFound('Endpoint not found');
    }
    
} catch (Exception $e) {
    APIResponse::serverError('Database connection failed');
}

/**
 * Handle app version check
 */
function handleAppVersion($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    try {
        // Get app settings
        $stmt = $conn->prepare("
            SELECT setting_key, setting_value 
            FROM app_settings 
            WHERE setting_key IN ('app_version', 'min_version', 'force_update', 'update_url', 'maintenance_mode', 'maintenance_message')
        ");
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        $response = [
            'current_version' => $settings['app_version'] ?? '1.0.0',
            'minimum_version' => $settings['min_version'] ?? '1.0.0',
            'force_update' => (bool)($settings['force_update'] ?? false),
            'update_url' => $settings['update_url'] ?? '',
            'maintenance_mode' => (bool)($settings['maintenance_mode'] ?? false),
            'maintenance_message' => $settings['maintenance_message'] ?? 'App is under maintenance. Please try again later.'
        ];
        
        // Check if update is required
        $clientVersion = $_GET['client_version'] ?? '1.0.0';
        $response['update_required'] = version_compare($clientVersion, $response['minimum_version'], '<');
        $response['update_available'] = version_compare($clientVersion, $response['current_version'], '<');
        
        APIResponse::success($response);
        
    } catch (Exception $e) {
        // Return default values if settings table doesn't exist
        APIResponse::success([
            'current_version' => '1.0.0',
            'minimum_version' => '1.0.0',
            'force_update' => false,
            'update_url' => '',
            'maintenance_mode' => false,
            'maintenance_message' => 'App is under maintenance. Please try again later.',
            'update_required' => false,
            'update_available' => false
        ]);
    }
}

/**
 * Handle app configuration
 */
function handleAppConfig($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    try {
        // Get app settings
        $stmt = $conn->prepare("SELECT setting_key, setting_value FROM app_settings");
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        $config = [
            'app_name' => $settings['app_name'] ?? 'StreamFlix',
            'api_version' => API_VERSION,
            'features' => [
                'watchlist' => true,
                'favorites' => true,
                'watch_history' => true,
                'user_profiles' => true,
                'search' => true,
                'genres' => true,
                'trending' => true,
                'featured' => true
            ],
            'content_types' => ['movies', 'tv_shows'],
            'supported_qualities' => ['HD', 'FHD', '4K'],
            'pagination' => [
                'default_limit' => 20,
                'max_limit' => 50
            ]
        ];
        
        APIResponse::success($config);
        
    } catch (Exception $e) {
        // Return default config
        APIResponse::success([
            'app_name' => 'StreamFlix',
            'api_version' => API_VERSION,
            'features' => [
                'watchlist' => true,
                'favorites' => true,
                'watch_history' => true,
                'user_profiles' => true,
                'search' => true,
                'genres' => true,
                'trending' => true,
                'featured' => true
            ],
            'content_types' => ['movies', 'tv_shows'],
            'supported_qualities' => ['HD', 'FHD', '4K'],
            'pagination' => [
                'default_limit' => 20,
                'max_limit' => 50
            ]
        ]);
    }
}

/**
 * Handle global search
 */
function handleGlobalSearch($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $query = trim($_GET['q'] ?? '');
    if (empty($query)) {
        APIResponse::error('Search query is required');
    }
    
    $limit = min(20, max(1, (int)($_GET['limit'] ?? 10)));
    $searchTerm = "%{$query}%";
    
    $results = [
        'query' => $query,
        'movies' => [],
        'tv_shows' => [],
        'total_results' => 0
    ];
    
    try {
        // Search movies
        $stmt = $conn->prepare("
            SELECT * FROM movies 
            WHERE title LIKE ? OR overview LIKE ?
            ORDER BY 
                CASE WHEN title LIKE ? THEN 1 ELSE 2 END,
                vote_average DESC,
                popularity DESC
            LIMIT ?
        ");
        $stmt->execute([$searchTerm, $searchTerm, $searchTerm, $limit]);
        $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $results['movies'] = array_map('formatMovieForAPI', $movies);
        
    } catch (Exception $e) {
        // Movies table doesn't exist
    }
    
    try {
        // Search TV shows
        $stmt = $conn->prepare("
            SELECT * FROM tv_shows 
            WHERE name LIKE ? OR overview LIKE ?
            ORDER BY 
                CASE WHEN name LIKE ? THEN 1 ELSE 2 END,
                vote_average DESC,
                popularity DESC
            LIMIT ?
        ");
        $stmt->execute([$searchTerm, $searchTerm, $searchTerm, $limit]);
        $shows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $results['tv_shows'] = array_map('formatTVShowForAPI', $shows);
        
    } catch (Exception $e) {
        // TV shows table doesn't exist
    }
    
    $results['total_results'] = count($results['movies']) + count($results['tv_shows']);
    
    APIResponse::success($results);
}

/**
 * Handle home page data
 */
function handleHomePage($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $user = APIAuth::optionalAuth();
    
    $homeData = [
        'featured_movies' => [],
        'featured_tv_shows' => [],
        'trending_movies' => [],
        'trending_tv_shows' => [],
        'latest_movies' => [],
        'latest_tv_shows' => [],
        'continue_watching' => [],
        'recommended' => []
    ];
    
    try {
        // Featured movies
        $stmt = $conn->prepare("
            SELECT * FROM movies 
            WHERE is_featured = 1 
            ORDER BY vote_average DESC, popularity DESC 
            LIMIT 10
        ");
        $stmt->execute();
        $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $homeData['featured_movies'] = array_map('formatMovieForAPI', $movies);
        
    } catch (Exception $e) {
        // Movies table doesn't exist
    }
    
    try {
        // Featured TV shows
        $stmt = $conn->prepare("
            SELECT * FROM tv_shows 
            WHERE is_featured = 1 
            ORDER BY vote_average DESC, popularity DESC 
            LIMIT 10
        ");
        $stmt->execute();
        $shows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $homeData['featured_tv_shows'] = array_map('formatTVShowForAPI', $shows);
        
    } catch (Exception $e) {
        // TV shows table doesn't exist
    }
    
    try {
        // Trending movies
        $stmt = $conn->prepare("
            SELECT * FROM movies 
            WHERE is_trending = 1 
            ORDER BY popularity DESC, vote_average DESC 
            LIMIT 10
        ");
        $stmt->execute();
        $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $homeData['trending_movies'] = array_map('formatMovieForAPI', $movies);
        
    } catch (Exception $e) {
        // Movies table doesn't exist
    }
    
    try {
        // Trending TV shows
        $stmt = $conn->prepare("
            SELECT * FROM tv_shows 
            WHERE is_trending = 1 
            ORDER BY popularity DESC, vote_average DESC 
            LIMIT 10
        ");
        $stmt->execute();
        $shows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $homeData['trending_tv_shows'] = array_map('formatTVShowForAPI', $shows);
        
    } catch (Exception $e) {
        // TV shows table doesn't exist
    }
    
    try {
        // Latest movies
        $stmt = $conn->prepare("
            SELECT * FROM movies 
            ORDER BY release_date DESC, created_at DESC 
            LIMIT 10
        ");
        $stmt->execute();
        $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $homeData['latest_movies'] = array_map('formatMovieForAPI', $movies);
        
    } catch (Exception $e) {
        // Movies table doesn't exist
    }
    
    try {
        // Latest TV shows
        $stmt = $conn->prepare("
            SELECT * FROM tv_shows 
            ORDER BY first_air_date DESC, created_at DESC 
            LIMIT 10
        ");
        $stmt->execute();
        $shows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $homeData['latest_tv_shows'] = array_map('formatTVShowForAPI', $shows);
        
    } catch (Exception $e) {
        // TV shows table doesn't exist
    }
    
    // Continue watching (for authenticated users)
    if ($user) {
        try {
            $stmt = $conn->prepare("
                SELECT 
                    wh.*,
                    m.title as movie_title, m.poster_path as movie_poster,
                    t.name as tv_title, t.poster_path as tv_poster
                FROM watch_history wh
                LEFT JOIN movies m ON wh.movie_id = m.id
                LEFT JOIN tv_shows t ON wh.tv_show_id = t.id
                WHERE wh.user_id = ? AND wh.completed = 0 AND wh.watch_time > 0
                ORDER BY wh.last_watched DESC
                LIMIT 10
            ");
            $stmt->execute([$user['id']]);
            $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $homeData['continue_watching'] = array_map(function($item) {
                $result = [
                    'watch_time' => (int)$item['watch_time'],
                    'duration' => (int)$item['duration'],
                    'progress' => $item['duration'] > 0 ? round(($item['watch_time'] / $item['duration']) * 100, 2) : 0
                ];
                
                if ($item['movie_id']) {
                    $result['type'] = 'movie';
                    $result['content'] = [
                        'id' => (int)$item['movie_id'],
                        'title' => $item['movie_title'],
                        'poster_path' => $item['movie_poster']
                    ];
                } else {
                    $result['type'] = 'tv_show';
                    $result['content'] = [
                        'id' => (int)$item['tv_show_id'],
                        'title' => $item['tv_title'],
                        'poster_path' => $item['tv_poster']
                    ];
                }
                
                return $result;
            }, $items);
            
        } catch (Exception $e) {
            // Watch history table doesn't exist
        }
    }
    
    APIResponse::success($homeData);
}

/**
 * Handle all genres
 */
function handleAllGenres($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    try {
        $stmt = $conn->query("
            SELECT 
                g.*,
                COALESCE(movie_counts.movie_count, 0) as movie_count,
                COALESCE(tv_counts.tv_count, 0) as tv_count
            FROM genres g
            LEFT JOIN (
                SELECT genre_id, COUNT(*) as movie_count 
                FROM movie_genres 
                GROUP BY genre_id
            ) movie_counts ON g.id = movie_counts.genre_id
            LEFT JOIN (
                SELECT genre_id, COUNT(*) as tv_count 
                FROM tv_show_genres 
                GROUP BY genre_id
            ) tv_counts ON g.id = tv_counts.genre_id
            WHERE (COALESCE(movie_counts.movie_count, 0) > 0 OR COALESCE(tv_counts.tv_count, 0) > 0)
            ORDER BY g.name ASC
        ");
        $genres = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $formattedGenres = array_map(function($genre) {
            return [
                'id' => (int)$genre['id'],
                'name' => $genre['name'],
                'movie_count' => (int)$genre['movie_count'],
                'tv_count' => (int)$genre['tv_count'],
                'total_count' => (int)$genre['movie_count'] + (int)$genre['tv_count']
            ];
        }, $genres);
        
        APIResponse::success(['genres' => $formattedGenres]);
        
    } catch (Exception $e) {
        // Return default genres if tables don't exist
        APIResponse::success(['genres' => [
            ['id' => 1, 'name' => 'Action', 'movie_count' => 0, 'tv_count' => 0, 'total_count' => 0],
            ['id' => 2, 'name' => 'Comedy', 'movie_count' => 0, 'tv_count' => 0, 'total_count' => 0],
            ['id' => 3, 'name' => 'Drama', 'movie_count' => 0, 'tv_count' => 0, 'total_count' => 0],
            ['id' => 4, 'name' => 'Horror', 'movie_count' => 0, 'tv_count' => 0, 'total_count' => 0],
            ['id' => 5, 'name' => 'Romance', 'movie_count' => 0, 'tv_count' => 0, 'total_count' => 0]
        ]]);
    }
}

/**
 * Handle servers list
 */
function handleServersList($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    try {
        $stmt = $conn->query("
            SELECT id, name, is_active, is_premium 
            FROM servers 
            WHERE is_active = 1 
            ORDER BY name ASC
        ");
        $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $formattedServers = array_map(function($server) {
            return [
                'id' => (int)$server['id'],
                'name' => $server['name'],
                'is_premium' => (bool)($server['is_premium'] ?? false)
            ];
        }, $servers);
        
        APIResponse::success(['servers' => $formattedServers]);
        
    } catch (Exception $e) {
        APIResponse::success(['servers' => []]);
    }
}
?>
