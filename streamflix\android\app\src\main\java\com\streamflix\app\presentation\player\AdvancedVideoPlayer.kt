package com.streamflix.app.presentation.player

import android.content.Context
import android.net.Uri
import android.view.ViewGroup
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import com.streamflix.app.data.model.Server
import com.streamflix.app.ui.theme.*
import kotlinx.coroutines.delay
import java.io.ByteArrayInputStream

@Composable
fun AdvancedVideoPlayer(
    servers: List<Server>,
    title: String,
    subtitle: String = "",
    onBack: () -> Unit,
    onNext: (() -> Unit)? = null,
    onPrevious: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    var selectedServer by remember { mutableStateOf(servers.firstOrNull()) }
    var showControls by remember { mutableStateOf(true) }
    var isPlaying by remember { mutableStateOf(false) }
    var currentPosition by remember { mutableStateOf(0L) }
    var duration by remember { mutableStateOf(0L) }
    var isBuffering by remember { mutableStateOf(false) }
    var showServerSelector by remember { mutableStateOf(false) }
    var showQualitySelector by remember { mutableStateOf(false) }
    var showSubtitleSelector by remember { mutableStateOf(false) }
    var playbackSpeed by remember { mutableStateOf(1.0f) }
    var brightness by remember { mutableStateOf(0.5f) }
    var volume by remember { mutableStateOf(0.5f) }
    var isFullscreen by remember { mutableStateOf(true) }

    val context = LocalContext.current

    // Auto-hide controls
    LaunchedEffect(showControls) {
        if (showControls && isPlaying) {
            delay(3000)
            showControls = false
        }
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black)
            .clickable { showControls = !showControls }
    ) {
        // Video Player
        selectedServer?.let { server ->
            if (server.embedUrl.contains("iframe") || server.embedUrl.contains("embed")) {
                // Web-based player with Ad-Block
                AdBlockWebPlayer(
                    url = server.embedUrl,
                    modifier = Modifier.fillMaxSize()
                )
            } else {
                // Native ExoPlayer
                NativeVideoPlayer(
                    url = server.embedUrl,
                    modifier = Modifier.fillMaxSize(),
                    onPlayingChanged = { isPlaying = it },
                    onPositionChanged = { currentPosition = it },
                    onDurationChanged = { duration = it },
                    onBufferingChanged = { isBuffering = it }
                )
            }
        }

        // Loading indicator
        if (isBuffering) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    color = StreamFlixRed,
                    strokeWidth = 4.dp,
                    modifier = Modifier.size(48.dp)
                )
            }
        }

        // Player Controls Overlay
        if (showControls) {
            PlayerControlsOverlay(
                title = title,
                subtitle = subtitle,
                isPlaying = isPlaying,
                currentPosition = currentPosition,
                duration = duration,
                servers = servers,
                selectedServer = selectedServer,
                playbackSpeed = playbackSpeed,
                onBack = onBack,
                onPlayPause = { /* Handle play/pause */ },
                onSeek = { /* Handle seek */ },
                onNext = onNext,
                onPrevious = onPrevious,
                onServerChange = { selectedServer = it },
                onShowServerSelector = { showServerSelector = true },
                onShowQualitySelector = { showQualitySelector = true },
                onShowSubtitleSelector = { showSubtitleSelector = true },
                onSpeedChange = { playbackSpeed = it },
                modifier = Modifier.fillMaxSize()
            )
        }

        // Server Selector Dialog
        if (showServerSelector) {
            ServerSelectorDialog(
                servers = servers,
                selectedServer = selectedServer,
                onServerSelected = { 
                    selectedServer = it
                    showServerSelector = false
                },
                onDismiss = { showServerSelector = false }
            )
        }

        // Quality Selector Dialog
        if (showQualitySelector) {
            QualitySelectorDialog(
                qualities = listOf("Auto", "1080p", "720p", "480p", "360p"),
                selectedQuality = "Auto",
                onQualitySelected = { showQualitySelector = false },
                onDismiss = { showQualitySelector = false }
            )
        }

        // Subtitle Selector Dialog
        if (showSubtitleSelector) {
            SubtitleSelectorDialog(
                subtitles = listOf("Off", "English", "Spanish", "French", "German"),
                selectedSubtitle = "Off",
                onSubtitleSelected = { showSubtitleSelector = false },
                onDismiss = { showSubtitleSelector = false }
            )
        }
    }
}

@Composable
fun AdBlockWebPlayer(
    url: String,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    
    AndroidView(
        factory = { ctx ->
            WebView(ctx).apply {
                layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
                
                settings.apply {
                    javaScriptEnabled = true
                    domStorageEnabled = true
                    allowFileAccess = true
                    allowContentAccess = true
                    setSupportZoom(true)
                    builtInZoomControls = false
                    displayZoomControls = false
                    useWideViewPort = true
                    loadWithOverviewMode = true
                    mediaPlaybackRequiresUserGesture = false
                }
                
                // Advanced Ad-Block WebViewClient
                webViewClient = AdBlockWebViewClient()
                
                loadUrl(url)
            }
        },
        modifier = modifier
    )
}

class AdBlockWebViewClient : WebViewClient() {
    
    // Comprehensive ad-blocking lists
    private val adBlockList = setOf(
        // Google Ads
        "googleads", "googlesyndication", "googleadservices", "google-analytics",
        // Facebook Ads
        "facebook.com/tr", "connect.facebook.net",
        // Common Ad Networks
        "doubleclick", "adsystem", "amazon-adsystem", "adsense",
        "outbrain", "taboola", "revcontent", "mgid",
        "popads", "popcash", "propellerads", "adnxs",
        "adskeeper", "contentad", "smartadserver", "criteo",
        // Video Ad Networks
        "imasdk.googleapis.com", "securepubads.g.doubleclick.net",
        "video-ad", "preroll", "midroll", "postroll",
        // Tracking & Analytics
        "analytics", "tracking", "metrics", "telemetry",
        "hotjar", "mouseflow", "crazyegg", "fullstory",
        // Pop-ups & Redirects
        "popup", "popunder", "redirect", "clickfunnels",
        // Crypto Miners
        "coinhive", "jsecoin", "mineralt", "cryptoloot",
        // Social Media Widgets
        "addthis", "sharethis", "disqus", "livefyre",
        // Common Ad Domains
        "ads.", "ad.", "advert", "banner", "sponsor",
        "promo", "marketing", "affiliate"
    )
    
    private val adBlockPatterns = listOf(
        Regex(".*\\b(ads?|advertisement|advert|adserver|adservice)\\b.*", RegexOption.IGNORE_CASE),
        Regex(".*\\b(popup|popunder|pop-up|pop-under)\\b.*", RegexOption.IGNORE_CASE),
        Regex(".*\\b(tracking|analytics|metrics|telemetry)\\b.*", RegexOption.IGNORE_CASE),
        Regex(".*\\b(banner|sponsor|promo|marketing)\\b.*", RegexOption.IGNORE_CASE)
    )

    override fun shouldInterceptRequest(
        view: WebView?,
        request: WebResourceRequest?
    ): WebResourceResponse? {
        val url = request?.url?.toString()?.lowercase() ?: return null
        
        // Check against ad-block list
        if (adBlockList.any { url.contains(it) }) {
            return createEmptyResponse()
        }
        
        // Check against ad-block patterns
        if (adBlockPatterns.any { it.matches(url) }) {
            return createEmptyResponse()
        }
        
        // Block specific file types commonly used for ads
        if (url.endsWith(".gif") && (url.contains("ad") || url.contains("banner"))) {
            return createEmptyResponse()
        }
        
        return super.shouldInterceptRequest(view, request)
    }
    
    private fun createEmptyResponse(): WebResourceResponse {
        return WebResourceResponse(
            "text/plain",
            "utf-8",
            ByteArrayInputStream("".toByteArray())
        )
    }
}

@Composable
fun NativeVideoPlayer(
    url: String,
    modifier: Modifier = Modifier,
    onPlayingChanged: (Boolean) -> Unit = {},
    onPositionChanged: (Long) -> Unit = {},
    onDurationChanged: (Long) -> Unit = {},
    onBufferingChanged: (Boolean) -> Unit = {}
) {
    val context = LocalContext.current
    
    AndroidView(
        factory = { ctx ->
            PlayerView(ctx).apply {
                val exoPlayer = ExoPlayer.Builder(ctx).build()
                
                // Configure player
                exoPlayer.apply {
                    val mediaItem = MediaItem.fromUri(Uri.parse(url))
                    setMediaItem(mediaItem)
                    prepare()
                    playWhenReady = true
                    
                    addListener(object : Player.Listener {
                        override fun onPlaybackStateChanged(playbackState: Int) {
                            when (playbackState) {
                                Player.STATE_BUFFERING -> onBufferingChanged(true)
                                Player.STATE_READY -> {
                                    onBufferingChanged(false)
                                    onDurationChanged(duration)
                                }
                                Player.STATE_ENDED -> onPlayingChanged(false)
                            }
                        }
                        
                        override fun onIsPlayingChanged(isPlaying: Boolean) {
                            onPlayingChanged(isPlaying)
                        }
                    })
                }
                
                player = exoPlayer
                useController = false // We'll use custom controls
            }
        },
        modifier = modifier
    )
}

@Composable
fun PlayerControlsOverlay(
    title: String,
    subtitle: String,
    isPlaying: Boolean,
    currentPosition: Long,
    duration: Long,
    servers: List<Server>,
    selectedServer: Server?,
    playbackSpeed: Float,
    onBack: () -> Unit,
    onPlayPause: () -> Unit,
    onSeek: (Long) -> Unit,
    onNext: (() -> Unit)?,
    onPrevious: (() -> Unit)?,
    onServerChange: (Server) -> Unit,
    onShowServerSelector: () -> Unit,
    onShowQualitySelector: () -> Unit,
    onShowSubtitleSelector: () -> Unit,
    onSpeedChange: (Float) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        // Top Controls
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
                .background(
                    Color.Black.copy(alpha = 0.7f),
                    RoundedCornerShape(8.dp)
                )
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Back button
            IconButton(onClick = onBack) {
                Icon(
                    Icons.Default.ArrowBack,
                    contentDescription = "Back",
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
            
            // Title and subtitle
            Column(
                modifier = Modifier.weight(1f).padding(horizontal = 16.dp)
            ) {
                Text(
                    text = title,
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    maxLines = 1
                )
                if (subtitle.isNotEmpty()) {
                    Text(
                        text = subtitle,
                        color = Color.White.copy(alpha = 0.8f),
                        fontSize = 14.sp,
                        maxLines = 1
                    )
                }
            }
            
            // Settings menu
            Row {
                // Server selector
                IconButton(onClick = onShowServerSelector) {
                    Icon(
                        Icons.Default.Settings,
                        contentDescription = "Servers",
                        tint = Color.White
                    )
                }
                
                // Quality selector
                IconButton(onClick = onShowQualitySelector) {
                    Icon(
                        Icons.Default.Settings,
                        contentDescription = "Quality",
                        tint = Color.White
                    )
                }
                
                // Subtitle selector
                IconButton(onClick = onShowSubtitleSelector) {
                    Icon(
                        Icons.Default.Settings,
                        contentDescription = "Subtitles",
                        tint = Color.White
                    )
                }
            }
        }
        
        // Center Controls
        Row(
            modifier = Modifier.align(Alignment.Center),
            horizontalArrangement = Arrangement.spacedBy(32.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Previous button
            onPrevious?.let {
                IconButton(
                    onClick = it,
                    modifier = Modifier
                        .size(56.dp)
                        .background(
                            Color.Black.copy(alpha = 0.7f),
                            CircleShape
                        )
                ) {
                    Icon(
                        Icons.Default.ArrowBack,
                        contentDescription = "Previous",
                        tint = Color.White,
                        modifier = Modifier.size(32.dp)
                    )
                }
            }
            
            // Play/Pause button
            IconButton(
                onClick = onPlayPause,
                modifier = Modifier
                    .size(72.dp)
                    .background(
                        StreamFlixRed,
                        CircleShape
                    )
            ) {
                Icon(
                    Icons.Default.PlayArrow,
                    contentDescription = if (isPlaying) "Pause" else "Play",
                    tint = Color.White,
                    modifier = Modifier.size(40.dp)
                )
            }
            
            // Next button
            onNext?.let {
                IconButton(
                    onClick = it,
                    modifier = Modifier
                        .size(56.dp)
                        .background(
                            Color.Black.copy(alpha = 0.7f),
                            CircleShape
                        )
                ) {
                    Icon(
                        Icons.Default.ArrowForward,
                        contentDescription = "Next",
                        tint = Color.White,
                        modifier = Modifier.size(32.dp)
                    )
                }
            }
        }
        
        // Bottom Controls
        Column(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .background(
                    Color.Black.copy(alpha = 0.7f)
                )
                .padding(16.dp)
        ) {
            // Progress bar
            PlayerProgressBar(
                currentPosition = currentPosition,
                duration = duration,
                onSeek = onSeek,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Bottom controls row
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Time display
                Text(
                    text = "${formatTime(currentPosition)} / ${formatTime(duration)}",
                    color = Color.White,
                    fontSize = 14.sp
                )
                
                // Additional controls
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // Playback speed
                    TextButton(
                        onClick = { 
                            val newSpeed = when (playbackSpeed) {
                                0.5f -> 0.75f
                                0.75f -> 1.0f
                                1.0f -> 1.25f
                                1.25f -> 1.5f
                                1.5f -> 2.0f
                                else -> 0.5f
                            }
                            onSpeedChange(newSpeed)
                        }
                    ) {
                        Text(
                            text = "${playbackSpeed}x",
                            color = Color.White,
                            fontSize = 12.sp
                        )
                    }
                    
                    // Fullscreen toggle
                    IconButton(onClick = { /* Toggle fullscreen */ }) {
                        Icon(
                            Icons.Default.Settings,
                            contentDescription = "Fullscreen",
                            tint = Color.White
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun PlayerProgressBar(
    currentPosition: Long,
    duration: Long,
    onSeek: (Long) -> Unit,
    modifier: Modifier = Modifier
) {
    val progress = if (duration > 0) currentPosition.toFloat() / duration.toFloat() else 0f
    
    Slider(
        value = progress,
        onValueChange = { newProgress ->
            val newPosition = (newProgress * duration).toLong()
            onSeek(newPosition)
        },
        modifier = modifier,
        colors = SliderDefaults.colors(
            thumbColor = StreamFlixRed,
            activeTrackColor = StreamFlixRed,
            inactiveTrackColor = Color.White.copy(alpha = 0.3f)
        )
    )
}

private fun formatTime(timeMs: Long): String {
    val totalSeconds = timeMs / 1000
    val hours = totalSeconds / 3600
    val minutes = (totalSeconds % 3600) / 60
    val seconds = totalSeconds % 60
    
    return if (hours > 0) {
        String.format("%d:%02d:%02d", hours, minutes, seconds)
    } else {
        String.format("%d:%02d", minutes, seconds)
    }
}
