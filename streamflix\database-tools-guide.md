# StreamFlix Database Tools - ব্যবহারের গাইড

## 🛠️ তৈরি করা টুলসমূহ

আমি আপনার জন্য ৪টি powerful database management tool তৈরি করেছি:

### 1. 🔍 Database Health Checker (`database-health-checker.php`)
**সবচেয়ে comprehensive tool - সব ধরনের চেক করে**

**ব্যবহার:**
```
https://yourdomain.com/database-health-checker.php?admin_key=streamflix_admin_2024
```

**ফিচার:**
- ✅ Database connection test
- ✅ Table existence check
- ✅ Column structure verification
- ✅ Index optimization check
- ✅ Data integrity analysis
- ✅ Performance metrics
- ✅ Automatic database updates
- ✅ Backup creation
- ✅ Beautiful web interface

### 2. ⚡ Quick Database Test (`quick-db-test.php`)
**দ্রুত basic check এর জন্য**

**ব্যবহার:**
```
https://yourdomain.com/quick-db-test.php?test=db
```

**ফিচার:**
- ✅ Fast connection test
- ✅ Core table check
- ✅ Basic data count
- ✅ Admin user verification
- ✅ Configuration check
- ✅ Performance test

### 3. 🔧 Database Repair Tool (`database-repair.php`)
**সমস্যা সমাধানের জন্য**

**ব্যবহার:**
```
https://yourdomain.com/database-repair.php?repair_key=fix_streamflix_db_2024
```

**ফিচার:**
- ✅ Auto-create missing tables
- ✅ Add missing columns
- ✅ Create performance indexes
- ✅ Fix data integrity issues
- ✅ Optimize tables
- ✅ Create default admin user
- ✅ Insert default settings

### 4. 📋 Database Updates (`config/database-updates.sql`)
**Manual SQL updates এর জন্য**

**ব্যবহার:**
- phpMyAdmin দিয়ে import করুন
- অথবা Health Checker দিয়ে automatic run করুন

## 🚀 কিভাবে ব্যবহার করবেন

### Step 1: Files Upload করুন
```
/your-site-root/
├── database-health-checker.php
├── quick-db-test.php
├── database-repair.php
└── config/
    └── database-updates.sql
```

### Step 2: প্রথমে Quick Test চালান
```
https://yourdomain.com/quick-db-test.php?test=db
```
এটি দিয়ে basic connectivity check করুন।

### Step 3: Full Health Check চালান
```
https://yourdomain.com/database-health-checker.php?admin_key=streamflix_admin_2024
```
সব details দেখুন এবং সমস্যা identify করুন।

### Step 4: প্রয়োজনে Repair চালান
```
https://yourdomain.com/database-repair.php?repair_key=fix_streamflix_db_2024
```
Auto-fix common issues.

## 🎯 কোন Tool কখন ব্যবহার করবেন

### 🔍 Quick Test ব্যবহার করুন যখন:
- সাইট কাজ করছে কিনা দ্রুত check করতে চান
- Basic connectivity test করতে চান
- Performance quick check করতে চান

### 🔧 Health Checker ব্যবহার করুন যখন:
- Detailed analysis চান
- Database update করতে চান
- Backup নিতে চান
- Complete health report চান

### 🛠️ Repair Tool ব্যবহার করুন যখন:
- সাইটে সমস্যা হচ্ছে
- Missing tables/columns আছে
- Database corrupt হয়েছে
- Fresh setup করতে চান

## 🔒 Security Notes

### Access Keys:
- **Health Checker:** `admin_key=streamflix_admin_2024`
- **Quick Test:** `test=db`
- **Repair Tool:** `repair_key=fix_streamflix_db_2024`

### Production এ ব্যবহারের পর:
1. Files delete করুন অথবা rename করুন
2. Access keys change করুন
3. Backup files secure রাখুন

## 📊 Status Indicators

### ✅ Green (OK)
- সবকিছু ঠিক আছে
- কোন action প্রয়োজন নেই

### ⚠️ Yellow (Warning)
- Minor issues আছে
- সাইট চলবে কিন্তু fix করা ভালো

### ❌ Red (Error)
- Critical issues
- Immediate action প্রয়োজন

## 🆘 Troubleshooting

### "Access Denied" Error:
- URL এ সঠিক key আছে কিনা check করুন
- File permissions 644 set করুন

### "Database Connection Failed":
- `config/database.php` file check করুন
- Database credentials verify করুন
- Database server running আছে কিনা দেখুন

### "File Not Found":
- সব files সঠিক location এ upload করেছেন কিনা check করুন
- File permissions check করুন

### Memory/Timeout Errors:
- PHP memory limit increase করুন
- Execution time limit increase করুন
- Large database এর জন্য smaller chunks এ কাজ করুন

## 📈 Best Practices

### নিয়মিত Maintenance:
1. সপ্তাহে একবার Quick Test চালান
2. মাসে একবার Full Health Check করুন
3. Major updates এর আগে Backup নিন

### Performance Optimization:
1. নিয়মিত table optimize করুন
2. Unused data clean করুন
3. Index usage monitor করুন

### Security:
1. Tools ব্যবহারের পর delete করুন
2. Regular backup রাখুন
3. Access logs monitor করুন

## 🎉 Success Indicators

### সব ঠিক থাকলে দেখবেন:
- ✅ All tests passed
- ✅ Database is healthy
- ✅ No missing tables/columns
- ✅ Good performance metrics
- ✅ No data integrity issues

### এখন আপনার সাইট:
- 🚀 Fast loading
- 🔒 Secure database
- 📊 Optimized performance
- 🛡️ Data integrity maintained

---

**মনে রাখবেন:** সব tools ব্যবহারের আগে database backup নিন!

**Support:** কোন সমস্যা হলে error messages screenshot নিয়ে help চান।
