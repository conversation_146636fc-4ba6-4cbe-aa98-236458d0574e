<?php
require_once 'includes/functions.php';

// Test hentai URL generation
$streamflix = new StreamFlix();

echo "<h2>Testing Hentai URL Generation</h2>";

// Test parameters
$tmdb_id = 12345;
$season = 1;
$episode = 1;

echo "<h3>Test Parameters:</h3>";
echo "<p>TMDB ID: {$tmdb_id}</p>";
echo "<p>Season: {$season}</p>";
echo "<p>Episode: {$episode}</p>";

echo "<h3>Regular TV Show URLs:</h3>";
$regular_urls = $streamflix->getEmbedUrls('tv_show', $tmdb_id, $season, $episode, false);
foreach ($regular_urls as $url) {
    echo "<p><strong>{$url['name']}:</strong> {$url['url']}</p>";
}

echo "<h3>Hentai URLs:</h3>";
$hentai_urls = $streamflix->getEmbedUrls('tv_show', $tmdb_id, $season, $episode, true);
foreach ($hentai_urls as $url) {
    echo "<p><strong>{$url['name']}:</strong> {$url['url']}</p>";
}

// Check database status
try {
    $db = new Database();
    $conn = $db->connect();
    
    echo "<h3>Database Status:</h3>";
    
    // Check hentai_url column
    $stmt = $conn->query("SHOW COLUMNS FROM embed_servers LIKE 'hentai_url'");
    echo "<p>Hentai URL column: " . ($stmt->rowCount() > 0 ? "✅ Exists" : "❌ Missing") . "</p>";
    
    // Check LetsEmbed hentai URL
    $stmt = $conn->query("SELECT name, hentai_url FROM embed_servers WHERE name = 'LetsEmbed'");
    $letsembed = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($letsembed) {
        echo "<p>LetsEmbed hentai URL: " . ($letsembed['hentai_url'] ? "✅ " . $letsembed['hentai_url'] : "❌ Not set") . "</p>";
    } else {
        echo "<p>LetsEmbed server: ❌ Not found</p>";
    }
    
    // Check Hentai genre
    $stmt = $conn->query("SELECT COUNT(*) FROM genres WHERE name = 'Hentai'");
    $hentai_genre_count = $stmt->fetchColumn();
    echo "<p>Hentai genre: " . ($hentai_genre_count > 0 ? "✅ Exists" : "❌ Missing") . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}

echo "<p><a href='admin/setup-hentai.php'>Setup Hentai Support</a></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Hentai Test - StreamFlix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #1a1a1a; color: white; }
        h2, h3 { color: #e50914; }
        p { margin: 10px 0; }
        a { color: #e50914; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
</body>
</html>
