<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$user_id = (int)($_GET['id'] ?? 0);
$message = '';
$error = '';

if ($user_id <= 0) {
    redirectTo('users.php');
}

// Prevent admin from deleting themselves
if ($user_id == $_SESSION['user_id']) {
    redirectTo('users.php');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'confirm_delete') {
        try {
            $db = new Database();
            $conn = $db->connect();
            
            // Get user details before deletion
            $stmt = $conn->prepare("SELECT username, email FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            $user_to_delete = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user_to_delete) {
                // Start transaction
                $conn->beginTransaction();
                
                try {
                    // Delete user's watchlist
                    $stmt = $conn->prepare("DELETE FROM watchlist WHERE user_id = ?");
                    $stmt->execute([$user_id]);
                    
                    // Delete user's bans
                    $stmt = $conn->prepare("DELETE FROM user_bans WHERE user_id = ?");
                    $stmt->execute([$user_id]);
                    
                    // Delete user's sessions
                    $stmt = $conn->prepare("DELETE FROM user_sessions WHERE user_id = ?");
                    $stmt->execute([$user_id]);
                    
                    // Delete user's activity
                    $stmt = $conn->prepare("DELETE FROM user_activity WHERE user_id = ?");
                    $stmt->execute([$user_id]);
                    
                    // Finally delete the user
                    $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
                    $stmt->execute([$user_id]);
                    
                    // Commit transaction
                    $conn->commit();
                    
                    // Log the deletion
                    error_log("User deleted by admin: {$user_to_delete['username']} ({$user_to_delete['email']}) deleted by " . $_SESSION['username']);
                    
                    // Redirect with success message
                    $_SESSION['admin_message'] = "User '{$user_to_delete['username']}' has been successfully deleted.";
                    redirectTo('users.php');
                    
                } catch (Exception $e) {
                    // Rollback transaction
                    $conn->rollback();
                    throw $e;
                }
            } else {
                $error = 'User not found.';
            }
            
        } catch (Exception $e) {
            $error = 'Error deleting user: ' . $e->getMessage();
        }
    }
}

// Get user details
try {
    $db = new Database();
    $conn = $db->connect();
    
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        redirectTo('users.php');
    }
    
    // Get user statistics
    $stmt = $conn->prepare("SELECT COUNT(*) FROM watchlist WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $watchlist_count = $stmt->fetchColumn();
    
    // Get user ban count
    $stmt = $conn->prepare("SELECT COUNT(*) FROM user_bans WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $ban_count = $stmt->fetchColumn();
    
} catch (Exception $e) {
    $error = 'Database error: ' . $e->getMessage();
    redirectTo('users.php');
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete User - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container {
            max-width: 600px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .delete-form {
            background: var(--secondary-color);
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 2px solid #dc3545;
        }
        
        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #dc3545, #ff6b6b);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 2rem;
            margin: 0 auto 20px;
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .alert-error {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid #dc3545;
        }
        
        .warning-box {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
            color: #856404;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: var(--dark-bg);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #dc3545;
        }
        
        .user-info {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .danger-list {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid #dc3545;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .danger-list ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .danger-list li {
            margin: 5px 0;
            color: #dc3545;
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1 style="color: #dc3545;">🗑️ Delete User</h1>
            <p>Permanently remove user from the system</p>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <div class="delete-form">
            <div class="user-info">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($user['username'], 0, 2)); ?>
                </div>
                
                <h3 style="color: #dc3545;"><?php echo htmlspecialchars($user['username']); ?></h3>
                <p style="color: var(--text-secondary);"><?php echo htmlspecialchars($user['email']); ?></p>
                <p style="color: var(--text-secondary);">
                    <strong>Role:</strong> <?php echo ucfirst($user['role']); ?> | 
                    <strong>Status:</strong> <?php echo $user['is_active'] ? 'Active' : 'Inactive'; ?> |
                    <strong>Premium:</strong> <?php echo $user['is_premium'] ? 'Yes' : 'No'; ?>
                </p>
                <p style="color: var(--text-secondary);">
                    <strong>Joined:</strong> <?php echo date('M j, Y', strtotime($user['created_at'])); ?>
                </p>
            </div>
            
            <!-- User Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $watchlist_count; ?></div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Watchlist Items</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $ban_count; ?></div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Ban Records</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $user['id']; ?></div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">User ID</div>
                </div>
            </div>

            <div class="warning-box">
                <h4 style="margin-top: 0;">⚠️ Warning: This action cannot be undone!</h4>
                <p>You are about to permanently delete this user account. This action will:</p>
            </div>

            <div class="danger-list">
                <h4 style="color: #dc3545; margin-top: 0;">The following data will be permanently deleted:</h4>
                <ul>
                    <li>User account and login credentials</li>
                    <li>All watchlist items (<?php echo $watchlist_count; ?> items)</li>
                    <li>All ban records (<?php echo $ban_count; ?> records)</li>
                    <li>All user sessions and activity logs</li>
                    <li>Any other user-related data</li>
                </ul>
                <p style="margin-bottom: 0; font-weight: bold; color: #dc3545;">
                    This action is irreversible and the user will not be able to recover their account or data.
                </p>
            </div>

            <form method="POST">
                <input type="hidden" name="action" value="confirm_delete">
                
                <div style="text-align: center; margin: 30px 0;">
                    <label style="display: flex; align-items: center; justify-content: center; gap: 10px; color: var(--text-primary);">
                        <input type="checkbox" required style="width: auto;">
                        I understand that this action is permanent and cannot be undone
                    </label>
                </div>
                
                <div class="button-group">
                    <a href="edit-user.php?id=<?php echo $user['id']; ?>" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn btn-danger" 
                            onclick="return confirm('Are you absolutely sure you want to delete this user? Type DELETE to confirm.')">
                        🗑️ Delete User Permanently
                    </button>
                </div>
            </form>
        </div>
        
        <div style="margin-top: 30px; text-align: center;">
            <a href="users.php" class="btn btn-secondary">← Back to Users</a>
            <a href="edit-user.php?id=<?php echo $user['id']; ?>" class="btn btn-secondary">✏️ Edit User Instead</a>
        </div>
    </div>
</body>
</html>
