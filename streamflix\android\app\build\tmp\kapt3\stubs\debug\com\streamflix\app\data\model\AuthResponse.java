package com.streamflix.app.data.model;

import com.google.gson.annotations.SerializedName;

/**
 * Auth response model
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0007H\u00c6\u0003J\'\u0010\u0012\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0003H\u00d6\u0001R\u0016\u0010\u0004\u001a\u00020\u00058\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0016\u0010\u0006\u001a\u00020\u00078\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u0019"}, d2 = {"Lcom/streamflix/app/data/model/AuthResponse;", "", "token", "", "expiresIn", "", "user", "Lcom/streamflix/app/data/model/User;", "(Ljava/lang/String;JLcom/streamflix/app/data/model/User;)V", "getExpiresIn", "()J", "getToken", "()Ljava/lang/String;", "getUser", "()Lcom/streamflix/app/data/model/User;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
public final class AuthResponse {
    @com.google.gson.annotations.SerializedName(value = "token")
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String token = null;
    @com.google.gson.annotations.SerializedName(value = "expires_in")
    private final long expiresIn = 0L;
    @com.google.gson.annotations.SerializedName(value = "user")
    @org.jetbrains.annotations.NotNull()
    private final com.streamflix.app.data.model.User user = null;
    
    public AuthResponse(@org.jetbrains.annotations.NotNull()
    java.lang.String token, long expiresIn, @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.model.User user) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getToken() {
        return null;
    }
    
    public final long getExpiresIn() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.streamflix.app.data.model.User getUser() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final long component2() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.streamflix.app.data.model.User component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.streamflix.app.data.model.AuthResponse copy(@org.jetbrains.annotations.NotNull()
    java.lang.String token, long expiresIn, @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.model.User user) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}