<?php
require_once 'config/database.php';

echo "<h1>🔧 Fix Hentai Servers - Final Solution</h1>";
echo "<p>Adding hentai server URLs to embed_servers table and fixing any issues.</p>";

try {
    $db = new Database();
    $conn = $db->connect();
    
    // Get database name
    $stmt = $conn->query("SELECT DATABASE() as db_name");
    $db_info = $stmt->fetch(PDO::FETCH_ASSOC);
    $database_name = $db_info['db_name'];
    
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>📊 Database Information</h3>";
    echo "<p><strong>Database:</strong> {$database_name}</p>";
    echo "<p><strong>Connection:</strong> ✅ Active</p>";
    echo "</div>";
    
    // Check if embed_servers table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'embed_servers'");
    $embed_servers_exists = $stmt->rowCount() > 0;
    
    echo "<h3>🔍 Table Status Check</h3>";
    
    if ($embed_servers_exists) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "✅ <strong>embed_servers table exists</strong>";
        echo "</div>";
        
        // Check table structure
        $stmt = $conn->query("SHOW COLUMNS FROM embed_servers");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $column_names = array_column($columns, 'Field');
        
        echo "<h4>📋 Current Columns:</h4>";
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        foreach ($columns as $column) {
            echo "<div>• <strong>{$column['Field']}</strong> - {$column['Type']}</div>";
        }
        echo "</div>";
        
        // Add missing hentai_url column if needed
        if (!in_array('hentai_url', $column_names)) {
            echo "<h4>🔧 Adding hentai_url column</h4>";
            try {
                $conn->exec("ALTER TABLE embed_servers ADD COLUMN hentai_url TEXT");
                echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                echo "✅ <strong>Successfully added hentai_url column</strong>";
                echo "</div>";
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                echo "❌ <strong>Error adding hentai_url column:</strong> " . $e->getMessage();
                echo "</div>";
            }
        } else {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "✅ <strong>hentai_url column already exists</strong>";
            echo "</div>";
        }
        
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "❌ <strong>embed_servers table does not exist</strong>";
        echo "</div>";
        
        // Create embed_servers table
        echo "<h4>🔨 Creating embed_servers table</h4>";
        try {
            $create_sql = "
                CREATE TABLE embed_servers (
                    id int(11) NOT NULL AUTO_INCREMENT,
                    name varchar(100) NOT NULL,
                    movie_url text,
                    tv_url text,
                    anime_url text,
                    hentai_url text,
                    is_active tinyint(1) DEFAULT 1,
                    priority int(11) DEFAULT 1,
                    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
                    updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (id),
                    KEY is_active (is_active),
                    KEY priority (priority)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            $conn->exec($create_sql);
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "✅ <strong>Successfully created embed_servers table</strong>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "❌ <strong>Error creating embed_servers table:</strong> " . $e->getMessage();
            echo "</div>";
        }
    }
    
    // Get current servers
    echo "<h3>🖥️ Current Embed Servers</h3>";
    
    try {
        $stmt = $conn->query("SELECT * FROM embed_servers ORDER BY priority ASC");
        $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h4>📊 Current Servers (" . count($servers) . "):</h4>";
        
        if (count($servers) > 0) {
            echo "<div style='background: white; padding: 10px; border-radius: 5px; margin-top: 10px;'>";
            foreach ($servers as $server) {
                $has_hentai = !empty($server['hentai_url']);
                $status_color = $has_hentai ? '#28a745' : '#dc3545';
                $status_icon = $has_hentai ? '✅' : '❌';
                
                echo "<div style='margin-bottom: 10px; padding: 10px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid {$status_color};'>";
                echo "<strong>{$server['name']}</strong> (Priority: {$server['priority']}) {$status_icon}<br>";
                echo "<small>Movie: " . (!empty($server['movie_url']) ? '✅' : '❌') . " | ";
                echo "TV: " . (!empty($server['tv_url']) ? '✅' : '❌') . " | ";
                echo "Anime: " . (!empty($server['anime_url']) ? '✅' : '❌') . " | ";
                echo "Hentai: " . ($has_hentai ? '✅' : '❌') . "</small>";
                if ($has_hentai) {
                    echo "<br><small><strong>Hentai URL:</strong> {$server['hentai_url']}</small>";
                }
                echo "</div>";
            }
            echo "</div>";
        } else {
            echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin-top: 10px;'>";
            echo "⚠️ <strong>No servers found. Will create default servers.</strong>";
            echo "</div>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "❌ <strong>Error reading servers:</strong> " . $e->getMessage();
        echo "</div>";
    }
    
    // Add/Update hentai servers
    echo "<h3>🔞 Adding/Updating Hentai Servers</h3>";
    
    $hentai_servers = [
        [
            'name' => 'LetsEmbed Hentai',
            'movie_url' => 'https://letsembed.cc/embed/movie/?id={id}',
            'tv_url' => 'https://letsembed.cc/embed/tv/?id={id}&s={season}&e={episode}',
            'anime_url' => 'https://letsembed.cc/embed/anime/?id={id}',
            'hentai_url' => 'https://letsembed.cc/embed/hentai/?id={id}/{season}/{episode}',
            'priority' => 1
        ],
        [
            'name' => 'AutoEmbed',
            'movie_url' => 'https://player.autoembed.cc/embed/movie/{id}',
            'tv_url' => 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}',
            'anime_url' => 'https://player.autoembed.cc/embed/movie/{id}',
            'hentai_url' => 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}',
            'priority' => 2
        ],
        [
            'name' => 'VidSrc',
            'movie_url' => 'https://vidsrc.to/embed/movie/{id}',
            'tv_url' => 'https://vidsrc.to/embed/tv/{id}/{season}/{episode}',
            'anime_url' => 'https://vidsrc.to/embed/movie/{id}',
            'hentai_url' => 'https://vidsrc.to/embed/tv/{id}/{season}/{episode}',
            'priority' => 3
        ],
        [
            'name' => 'SuperEmbed',
            'movie_url' => 'https://multiembed.mov/directstream.php?video_id={id}',
            'tv_url' => 'https://multiembed.mov/directstream.php?video_id={id}&s={season}&e={episode}',
            'anime_url' => 'https://multiembed.mov/directstream.php?video_id={id}',
            'hentai_url' => 'https://multiembed.mov/directstream.php?video_id={id}&s={season}&e={episode}',
            'priority' => 4
        ]
    ];
    
    $added_count = 0;
    $updated_count = 0;
    
    foreach ($hentai_servers as $server_data) {
        try {
            // Check if server exists
            $stmt = $conn->prepare("SELECT id, hentai_url FROM embed_servers WHERE name = ?");
            $stmt->execute([$server_data['name']]);
            $existing_server = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($existing_server) {
                // Update existing server
                if (empty($existing_server['hentai_url'])) {
                    $stmt = $conn->prepare("
                        UPDATE embed_servers 
                        SET hentai_url = ?, movie_url = ?, tv_url = ?, anime_url = ?, priority = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    ");
                    $stmt->execute([
                        $server_data['hentai_url'],
                        $server_data['movie_url'],
                        $server_data['tv_url'],
                        $server_data['anime_url'],
                        $server_data['priority'],
                        $existing_server['id']
                    ]);
                    
                    $updated_count++;
                    echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                    echo "🔄 <strong>Updated {$server_data['name']}</strong> with hentai URL";
                    echo "</div>";
                } else {
                    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                    echo "✅ <strong>{$server_data['name']}</strong> already has hentai URL";
                    echo "</div>";
                }
            } else {
                // Add new server
                $stmt = $conn->prepare("
                    INSERT INTO embed_servers (name, movie_url, tv_url, anime_url, hentai_url, is_active, priority) 
                    VALUES (?, ?, ?, ?, ?, 1, ?)
                ");
                $stmt->execute([
                    $server_data['name'],
                    $server_data['movie_url'],
                    $server_data['tv_url'],
                    $server_data['anime_url'],
                    $server_data['hentai_url'],
                    $server_data['priority']
                ]);
                
                $added_count++;
                echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                echo "✅ <strong>Added new server: {$server_data['name']}</strong>";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "❌ <strong>Error with {$server_data['name']}:</strong> " . $e->getMessage();
            echo "</div>";
        }
    }
    
    // Final status
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>📊 Operation Summary</h4>";
    echo "<ul>";
    echo "<li><strong>Servers Added:</strong> {$added_count}</li>";
    echo "<li><strong>Servers Updated:</strong> {$updated_count}</li>";
    echo "<li><strong>LetsEmbed Hentai URL:</strong> ✅ Added</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test the servers
    echo "<h3>🧪 Server Test</h3>";
    
    $stmt = $conn->query("SELECT * FROM embed_servers WHERE hentai_url IS NOT NULL AND hentai_url != '' ORDER BY priority ASC");
    $hentai_servers_final = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div style='background: #cff4fc; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🔞 Available Hentai Servers (" . count($hentai_servers_final) . "):</h4>";
    
    if (count($hentai_servers_final) > 0) {
        echo "<div style='background: white; padding: 15px; border-radius: 8px; margin-top: 15px;'>";
        foreach ($hentai_servers_final as $server) {
            echo "<div style='margin-bottom: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #28a745;'>";
            echo "<h5 style='margin: 0 0 8px 0; color: #28a745;'>{$server['name']}</h5>";
            echo "<p style='margin: 0; font-size: 0.9rem;'><strong>Hentai URL:</strong> {$server['hentai_url']}</p>";
            echo "<p style='margin: 5px 0 0 0; font-size: 0.85rem; color: #666;'><strong>Priority:</strong> {$server['priority']} | <strong>Active:</strong> " . ($server['is_active'] ? 'Yes' : 'No') . "</p>";
            echo "</div>";
        }
        echo "</div>";
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "✅ <strong>Success!</strong> Hentai servers are now configured and ready to use.";
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "❌ <strong>No hentai servers found!</strong> Something went wrong.";
        echo "</div>";
    }
    echo "</div>";
    
    // Quick links
    echo "<h3>🔗 Quick Links</h3>";
    
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🎯 Test Your Setup:</h4>";
    
    echo "<p><a href='test_hentai_servers.php' target='_blank' style='background: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin-right: 10px; display: inline-block; margin-bottom: 10px;'>🧪 Test Hentai Servers</a></p>";
    
    echo "<p><a href='admin/hentai-management.php' target='_blank' style='background: #e91e63; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin-right: 10px; display: inline-block; margin-bottom: 10px;'>🔞 Hentai Management</a></p>";
    
    echo "<p><a href='admin/servers.php' target='_blank' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block; margin-bottom: 10px;'>🖥️ Server Management</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4 style='color: #721c24;'>❌ Critical Error</h4>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><strong>🎉 Hentai Server Setup Complete!</strong></p>";
echo "<p>LetsEmbed hentai server has been added with the URL pattern you requested.</p>";
?>
