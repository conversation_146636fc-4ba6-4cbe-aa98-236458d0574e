{"logs": [{"outputFile": "com.streamflix.app-mergeDebugResources-83:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d09ac5c5e9f0e76cba4d9b249ad65917\\transformed\\jetified-media3-exoplayer-1.2.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,225,278,350,404,478,554", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "106,162,220,273,345,399,473,549,608"}, "to": {"startLines": "95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6798,6854,6910,6968,7021,7093,7147,7221,7297", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "6849,6905,6963,7016,7088,7142,7216,7292,7351"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\11b247236daf7bf1e583aa11f0369379\\transformed\\jetified-media3-ui-1.2.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,281,444,602,672,741,811,887,962,1017,1078,1152,1226,1288,1349,1408,1473,1562,1648,1737,1800,1867,1932,1987,2061,2134,2195,2258,2310,2368,2415,2476,2532,2594,2651,2711,2767,2822,2885,2947,3010,3059,3112,3179,3246", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,69,68,69,75,74,54,60,73,73,61,60,58,64,88,85,88,62,66,64,54,73,72,60,62,51,57,46,60,55,61,56,59,55,54,62,61,62,48,52,66,66,48", "endOffsets": "276,439,597,667,736,806,882,957,1012,1073,1147,1221,1283,1344,1403,1468,1557,1643,1732,1795,1862,1927,1982,2056,2129,2190,2253,2305,2363,2410,2471,2527,2589,2646,2706,2762,2817,2880,2942,3005,3054,3107,3174,3241,3290"}, "to": {"startLines": "2,11,15,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,376,539,5142,5212,5281,5351,5427,5502,5557,5618,5692,5766,5828,5889,5948,6013,6102,6188,6277,6340,6407,6472,6527,6601,6674,6735,7356,7408,7466,7513,7574,7630,7692,7749,7809,7865,7920,7983,8045,8108,8157,8210,8277,8344", "endLines": "10,14,18,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "17,12,12,69,68,69,75,74,54,60,73,73,61,60,58,64,88,85,88,62,66,64,54,73,72,60,62,51,57,46,60,55,61,56,59,55,54,62,61,62,48,52,66,66,48", "endOffsets": "371,534,692,5207,5276,5346,5422,5497,5552,5613,5687,5761,5823,5884,5943,6008,6097,6183,6272,6335,6402,6467,6522,6596,6669,6730,6793,7403,7461,7508,7569,7625,7687,7744,7804,7860,7915,7978,8040,8103,8152,8205,8272,8339,8388"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5e0eb68a5716cdfe313e221efb4d1df6\\transformed\\core-1.12.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "55,56,57,58,59,60,61,247", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3742,3834,3933,4027,4121,4214,4307,18633", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3829,3928,4022,4116,4209,4302,4398,18729"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1876129b9e490c2861a5ecf91d698967\\transformed\\jetified-foundation-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,136", "endColumns": "80,76", "endOffsets": "131,208"}, "to": {"startLines": "251,252", "startColumns": "4,4", "startOffsets": "18990,19071", "endColumns": "80,76", "endOffsets": "19066,19143"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dfc9ccab22fd45fc4cc7c7544966016d\\transformed\\material-1.11.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,310,371,438,507,584,674,781,854,916,994,1053,1111,1189,1250,1307,1363,1422,1480,1534,1620,1676,1734,1788,1853,1946,2020,2098,2218,2281,2344,2443,2520,2594,2644,2695,2761,2825,2893,2968,3040,3101,3172,3239,3299,3387,3467,3530,3613,3698,3772,3837,3913,3961,4035,4099,4175,4253,4315,4379,4442,4508,4588,4668,4744,4825,4879,4934", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,62,60,66,68,76,89,106,72,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,77,119,62,62,98,76,73,49,50,65,63,67,74,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68", "endOffsets": "242,305,366,433,502,579,669,776,849,911,989,1048,1106,1184,1245,1302,1358,1417,1475,1529,1615,1671,1729,1783,1848,1941,2015,2093,2213,2276,2339,2438,2515,2589,2639,2690,2756,2820,2888,2963,3035,3096,3167,3234,3294,3382,3462,3525,3608,3693,3767,3832,3908,3956,4030,4094,4170,4248,4310,4374,4437,4503,4583,4663,4739,4820,4874,4929,4998"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,70,122,123,126,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,242", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "697,3405,3468,3529,3596,3665,4403,4493,4600,5080,8393,8471,8681,14169,14247,14308,14365,14421,14480,14538,14592,14678,14734,14792,14846,14911,15004,15078,15156,15276,15339,15402,15501,15578,15652,15702,15753,15819,15883,15951,16026,16098,16159,16230,16297,16357,16445,16525,16588,16671,16756,16830,16895,16971,17019,17093,17157,17233,17311,17373,17437,17500,17566,17646,17726,17802,17883,17937,18270", "endLines": "22,50,51,52,53,54,62,63,64,70,122,123,126,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,242", "endColumns": "12,62,60,66,68,76,89,106,72,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,77,119,62,62,98,76,73,49,50,65,63,67,74,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68", "endOffsets": "839,3463,3524,3591,3660,3737,4488,4595,4668,5137,8466,8525,8734,14242,14303,14360,14416,14475,14533,14587,14673,14729,14787,14841,14906,14999,15073,15151,15271,15334,15397,15496,15573,15647,15697,15748,15814,15878,15946,16021,16093,16154,16225,16292,16352,16440,16520,16583,16666,16751,16825,16890,16966,17014,17088,17152,17228,17306,17368,17432,17495,17561,17641,17721,17797,17878,17932,17987,18334"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\47494bae0d5af340ce384dde3add152e\\transformed\\appcompat-1.6.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,245", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "844,939,1032,1132,1214,1311,1419,1496,1571,1663,1757,1848,1944,2039,2133,2229,2321,2413,2505,2583,2679,2774,2869,2966,3062,3160,3311,18487", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "934,1027,1127,1209,1306,1414,1491,1566,1658,1752,1843,1939,2034,2128,2224,2316,2408,2500,2578,2674,2769,2864,2961,3057,3155,3306,3400,18561"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ecb7be72ebd258ebb899bf8355eead54\\transformed\\jetified-ui-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,181,255,343,434,512,586,663,741,815,878,941,1014,1089,1156,1231,1296", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "176,250,338,429,507,581,658,736,810,873,936,1009,1084,1151,1226,1291,1407"}, "to": {"startLines": "65,66,67,68,69,124,125,238,239,240,241,243,244,246,248,249,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4673,4749,4823,4911,5002,8530,8604,17992,18070,18144,18207,18339,18412,18566,18734,18809,18874", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "4744,4818,4906,4997,5075,8599,8676,18065,18139,18202,18265,18407,18482,18628,18804,18869,18985"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac2f50916fa650919f030349484e55e3\\transformed\\jetified-material3-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,364,465,556,645,750,855,960,1076,1158,1254,1338,1426,1531,1644,1745,1853,1959,2067,2183,2288,2390,2495,2601,2686,2781,2886,2995,3085,3187,3285,3394,3508,3608,3699,3772,3862,3951,4034,4116,4205,4285,4367,4464,4558,4651,4744,4828,4924,5020,5115,5223,5303,5395", "endColumns": "102,101,103,100,90,88,104,104,104,115,81,95,83,87,104,112,100,107,105,107,115,104,101,104,105,84,94,104,108,89,101,97,108,113,99,90,72,89,88,82,81,88,79,81,96,93,92,92,83,95,95,94,107,79,91,89", "endOffsets": "153,255,359,460,551,640,745,850,955,1071,1153,1249,1333,1421,1526,1639,1740,1848,1954,2062,2178,2283,2385,2490,2596,2681,2776,2881,2990,3080,3182,3280,3389,3503,3603,3694,3767,3857,3946,4029,4111,4200,4280,4362,4459,4553,4646,4739,4823,4919,5015,5110,5218,5298,5390,5480"}, "to": {"startLines": "127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8739,8842,8944,9048,9149,9240,9329,9434,9539,9644,9760,9842,9938,10022,10110,10215,10328,10429,10537,10643,10751,10867,10972,11074,11179,11285,11370,11465,11570,11679,11769,11871,11969,12078,12192,12292,12383,12456,12546,12635,12718,12800,12889,12969,13051,13148,13242,13335,13428,13512,13608,13704,13799,13907,13987,14079", "endColumns": "102,101,103,100,90,88,104,104,104,115,81,95,83,87,104,112,100,107,105,107,115,104,101,104,105,84,94,104,108,89,101,97,108,113,99,90,72,89,88,82,81,88,79,81,96,93,92,92,83,95,95,94,107,79,91,89", "endOffsets": "8837,8939,9043,9144,9235,9324,9429,9534,9639,9755,9837,9933,10017,10105,10210,10323,10424,10532,10638,10746,10862,10967,11069,11174,11280,11365,11460,11565,11674,11764,11866,11964,12073,12187,12287,12378,12451,12541,12630,12713,12795,12884,12964,13046,13143,13237,13330,13423,13507,13603,13699,13794,13902,13982,14074,14164"}}]}]}