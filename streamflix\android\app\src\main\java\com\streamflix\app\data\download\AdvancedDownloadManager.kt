package com.streamflix.app.data.download

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.work.*
import com.streamflix.app.R
import com.streamflix.app.StreamFlixApplication
import com.streamflix.app.data.local.UserPreferences
import com.streamflix.app.data.model.Movie
import com.streamflix.app.data.model.TvShow
import com.streamflix.app.data.model.Episode
import com.streamflix.app.presentation.main.MainActivity
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.*
import java.io.File
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AdvancedDownloadManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val userPreferences: UserPreferences,
    private val workManager: WorkManager
) {

    companion object {
        const val DOWNLOAD_NOTIFICATION_ID = 1001
        const val DOWNLOAD_WORK_TAG = "download_work"
        const val MAX_CONCURRENT_DOWNLOADS = 3
    }

    private val _downloadQueue = MutableStateFlow<List<DownloadItem>>(emptyList())
    val downloadQueue: StateFlow<List<DownloadItem>> = _downloadQueue.asStateFlow()

    private val _activeDownloads = MutableStateFlow<Map<String, DownloadProgress>>(emptyMap())
    val activeDownloads: StateFlow<Map<String, DownloadProgress>> = _activeDownloads.asStateFlow()

    private val _completedDownloads = MutableStateFlow<List<DownloadItem>>(emptyList())
    val completedDownloads: StateFlow<List<DownloadItem>> = _completedDownloads.asStateFlow()

    init {
        loadDownloadHistory()
        observeNetworkChanges()
    }

    // ==================== Public API ====================

    suspend fun downloadMovie(
        movie: Movie,
        quality: String = "HD",
        serverUrl: String
    ): Result<String> {
        return try {
            val downloadItem = DownloadItem(
                id = UUID.randomUUID().toString(),
                contentId = movie.id,
                contentType = ContentType.MOVIE,
                title = movie.title,
                posterUrl = movie.getFullPosterUrl(),
                quality = quality,
                serverUrl = serverUrl,
                status = DownloadStatus.QUEUED,
                createdAt = System.currentTimeMillis()
            )

            if (canStartDownload()) {
                startDownload(downloadItem)
            } else {
                addToQueue(downloadItem)
            }

            Result.success(downloadItem.id)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun downloadTvShow(
        tvShow: TvShow,
        episodes: List<Episode>,
        quality: String = "HD"
    ): Result<List<String>> {
        return try {
            val downloadIds = mutableListOf<String>()

            episodes.forEach { episode ->
                val downloadItem = DownloadItem(
                    id = UUID.randomUUID().toString(),
                    contentId = episode.id,
                    contentType = ContentType.EPISODE,
                    title = "${tvShow.name} - S${episode.seasonNumber}E${episode.episodeNumber}: ${episode.name}",
                    posterUrl = episode.getFullStillUrl() ?: tvShow.getFullPosterUrl(),
                    quality = quality,
                    serverUrl = "", // Will be resolved later
                    status = DownloadStatus.QUEUED,
                    createdAt = System.currentTimeMillis(),
                    parentId = tvShow.id.toString(),
                    seasonNumber = episode.seasonNumber,
                    episodeNumber = episode.episodeNumber
                )

                downloadIds.add(downloadItem.id)

                if (canStartDownload()) {
                    startDownload(downloadItem)
                } else {
                    addToQueue(downloadItem)
                }
            }

            Result.success(downloadIds)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun pauseDownload(downloadId: String) {
        workManager.cancelUniqueWork("download_$downloadId")
        updateDownloadStatus(downloadId, DownloadStatus.PAUSED)
    }

    suspend fun resumeDownload(downloadId: String) {
        val downloadItem = findDownloadItem(downloadId)
        downloadItem?.let {
            if (canStartDownload()) {
                startDownload(it.copy(status = DownloadStatus.DOWNLOADING))
            } else {
                addToQueue(it.copy(status = DownloadStatus.QUEUED))
            }
        }
    }

    suspend fun cancelDownload(downloadId: String) {
        workManager.cancelUniqueWork("download_$downloadId")
        removeDownload(downloadId)
        deleteDownloadedFile(downloadId)
    }

    suspend fun retryDownload(downloadId: String) {
        val downloadItem = findDownloadItem(downloadId)
        downloadItem?.let {
            startDownload(it.copy(status = DownloadStatus.DOWNLOADING))
        }
    }

    suspend fun deleteDownload(downloadId: String) {
        cancelDownload(downloadId)
        removeFromCompleted(downloadId)
    }

    fun getDownloadedContent(): Flow<List<DownloadItem>> {
        return completedDownloads.map { downloads ->
            downloads.filter { it.status == DownloadStatus.COMPLETED }
        }
    }

    fun getDownloadProgress(downloadId: String): Flow<DownloadProgress?> {
        return activeDownloads.map { it[downloadId] }
    }

    fun isContentDownloaded(contentId: Int, contentType: ContentType): Boolean {
        return _completedDownloads.value.any { 
            it.contentId == contentId && 
            it.contentType == contentType && 
            it.status == DownloadStatus.COMPLETED 
        }
    }

    // ==================== Private Methods ====================

    private suspend fun canStartDownload(): Boolean {
        val settings = userPreferences.getAllSettings().first()
        
        // Check WiFi requirement
        if (settings.wifiOnlyDownload && !isWiFiConnected()) {
            return false
        }

        // Check concurrent downloads limit
        val activeCount = _activeDownloads.value.size
        return activeCount < MAX_CONCURRENT_DOWNLOADS
    }

    private fun isWiFiConnected(): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            networkInfo?.type == ConnectivityManager.TYPE_WIFI
        }
    }

    private suspend fun startDownload(downloadItem: DownloadItem) {
        updateDownloadStatus(downloadItem.id, DownloadStatus.DOWNLOADING)

        val downloadData = workDataOf(
            "download_id" to downloadItem.id,
            "content_id" to downloadItem.contentId,
            "content_type" to downloadItem.contentType.name,
            "title" to downloadItem.title,
            "server_url" to downloadItem.serverUrl,
            "quality" to downloadItem.quality
        )

        val downloadRequest = OneTimeWorkRequestBuilder<DownloadWorker>()
            .setInputData(downloadData)
            .setConstraints(
                Constraints.Builder()
                    .setRequiredNetworkType(
                        if (userPreferences.getAllSettings().first().wifiOnlyDownload) 
                            NetworkType.UNMETERED 
                        else 
                            NetworkType.CONNECTED
                    )
                    .setRequiresStorageNotLow(true)
                    .build()
            )
            .addTag(DOWNLOAD_WORK_TAG)
            .build()

        workManager.enqueueUniqueWork(
            "download_${downloadItem.id}",
            ExistingWorkPolicy.REPLACE,
            downloadRequest
        )

        showDownloadNotification(downloadItem)
    }

    private suspend fun addToQueue(downloadItem: DownloadItem) {
        val currentQueue = _downloadQueue.value.toMutableList()
        currentQueue.add(downloadItem)
        _downloadQueue.value = currentQueue
    }

    private suspend fun updateDownloadStatus(downloadId: String, status: DownloadStatus) {
        val currentQueue = _downloadQueue.value.toMutableList()
        val itemIndex = currentQueue.indexOfFirst { it.id == downloadId }
        
        if (itemIndex != -1) {
            currentQueue[itemIndex] = currentQueue[itemIndex].copy(status = status)
            _downloadQueue.value = currentQueue
        }

        if (status == DownloadStatus.COMPLETED) {
            moveToCompleted(downloadId)
        }
    }

    private suspend fun moveToCompleted(downloadId: String) {
        val downloadItem = findDownloadItem(downloadId)
        downloadItem?.let {
            val currentCompleted = _completedDownloads.value.toMutableList()
            currentCompleted.add(it.copy(status = DownloadStatus.COMPLETED))
            _completedDownloads.value = currentCompleted

            removeFromQueue(downloadId)
            removeFromActive(downloadId)
        }
    }

    private suspend fun removeFromQueue(downloadId: String) {
        val currentQueue = _downloadQueue.value.toMutableList()
        currentQueue.removeAll { it.id == downloadId }
        _downloadQueue.value = currentQueue
    }

    private suspend fun removeFromActive(downloadId: String) {
        val currentActive = _activeDownloads.value.toMutableMap()
        currentActive.remove(downloadId)
        _activeDownloads.value = currentActive
    }

    private suspend fun removeFromCompleted(downloadId: String) {
        val currentCompleted = _completedDownloads.value.toMutableList()
        currentCompleted.removeAll { it.id == downloadId }
        _completedDownloads.value = currentCompleted
    }

    private suspend fun removeDownload(downloadId: String) {
        removeFromQueue(downloadId)
        removeFromActive(downloadId)
        removeFromCompleted(downloadId)
    }

    private fun findDownloadItem(downloadId: String): DownloadItem? {
        return _downloadQueue.value.find { it.id == downloadId }
            ?: _completedDownloads.value.find { it.id == downloadId }
    }

    private fun deleteDownloadedFile(downloadId: String) {
        try {
            val downloadDir = File(context.getExternalFilesDir(null), "downloads")
            val file = File(downloadDir, "$downloadId.mp4")
            if (file.exists()) {
                file.delete()
            }
        } catch (e: Exception) {
            // Handle error silently
        }
    }

    private fun showDownloadNotification(downloadItem: DownloadItem) {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, StreamFlixApplication.DOWNLOAD_CHANNEL_ID)
            .setContentTitle("Downloading ${downloadItem.title}")
            .setContentText("Download started...")
            .setSmallIcon(R.drawable.ic_download)
            .setContentIntent(pendingIntent)
            .setProgress(100, 0, true)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()

        notificationManager.notify(DOWNLOAD_NOTIFICATION_ID, notification)
    }

    private fun loadDownloadHistory() {
        // Load download history from local storage
        // This would typically involve reading from a local database
    }

    private fun observeNetworkChanges() {
        // Observe network changes and resume/pause downloads accordingly
    }

    // ==================== Progress Updates ====================

    fun updateDownloadProgress(downloadId: String, progress: DownloadProgress) {
        val currentActive = _activeDownloads.value.toMutableMap()
        currentActive[downloadId] = progress
        _activeDownloads.value = currentActive

        updateProgressNotification(downloadId, progress)
    }

    private fun updateProgressNotification(downloadId: String, progress: DownloadProgress) {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        val notification = NotificationCompat.Builder(context, StreamFlixApplication.DOWNLOAD_CHANNEL_ID)
            .setContentTitle("Downloading ${progress.title}")
            .setContentText("${progress.percentage}% • ${formatFileSize(progress.downloadedBytes)} / ${formatFileSize(progress.totalBytes)}")
            .setSmallIcon(R.drawable.ic_download)
            .setProgress(100, progress.percentage, false)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()

        notificationManager.notify(DOWNLOAD_NOTIFICATION_ID, notification)
    }

    private fun formatFileSize(bytes: Long): String {
        val kb = bytes / 1024.0
        val mb = kb / 1024.0
        val gb = mb / 1024.0

        return when {
            gb >= 1 -> String.format("%.1f GB", gb)
            mb >= 1 -> String.format("%.1f MB", mb)
            else -> String.format("%.1f KB", kb)
        }
    }
}

// ==================== Data Classes ====================

data class DownloadItem(
    val id: String,
    val contentId: Int,
    val contentType: ContentType,
    val title: String,
    val posterUrl: String?,
    val quality: String,
    val serverUrl: String,
    val status: DownloadStatus,
    val createdAt: Long,
    val completedAt: Long? = null,
    val filePath: String? = null,
    val fileSize: Long = 0,
    val parentId: String? = null, // For episodes, this is the TV show ID
    val seasonNumber: Int? = null,
    val episodeNumber: Int? = null
)

data class DownloadProgress(
    val downloadId: String,
    val title: String,
    val percentage: Int,
    val downloadedBytes: Long,
    val totalBytes: Long,
    val speed: Long, // bytes per second
    val remainingTime: Long // seconds
)

enum class ContentType {
    MOVIE,
    EPISODE
}

enum class DownloadStatus {
    QUEUED,
    DOWNLOADING,
    PAUSED,
    COMPLETED,
    FAILED,
    CANCELLED
}
