<?php
require_once 'config/database.php';

echo "<h2>🚨 Emergency Server Fix for BDFLiX</h2>";
echo "<p>This will completely fix your server issue and restore player functionality.</p>";

try {
    $db = new Database();
    $conn = $db->connect();
    
    echo "<h3>Step 1: 🔧 Database Structure Fix</h3>";
    
    // Ensure embed_servers table exists with correct structure
    $create_table_sql = "
    CREATE TABLE IF NOT EXISTS embed_servers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        movie_url TEXT NOT NULL,
        tv_url TEXT NOT NULL,
        priority INT DEFAULT 1,
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    $conn->exec($create_table_sql);
    echo "<p style='color: green;'>✅ Table structure ensured</p>";
    
    // Check if table has data
    $stmt = $conn->query("SELECT COUNT(*) FROM embed_servers");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        echo "<p style='color: orange;'>⚠️ No servers in database. Adding default servers...</p>";
        
        // Insert default servers
        $default_servers = [
            ['AutoEmbed', 'https://player.autoembed.cc/embed/movie/{id}', 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}', 1],
            ['VidJoy', 'https://vidjoy.pro/embed/movie/{id}', 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}', 2],
            ['VidZee', 'https://player.vidzee.wtf/embed/movie/{id}', 'https://player.vidzee.wtf/embed/tv/{id}/{season}/{episode}', 3],
            ['LetsEmbed', 'https://letsembed.cc/embed/movie/?id={id}', 'https://letsembed.cc/embed/tv/?id={id}/{season}/{episode}', 4]
        ];
        
        foreach ($default_servers as $server) {
            $stmt = $conn->prepare("
                INSERT INTO embed_servers (name, movie_url, tv_url, priority, is_active) 
                VALUES (?, ?, ?, ?, 1)
            ");
            $stmt->execute($server);
            echo "<p style='color: green;'>✅ Added: {$server[0]}</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ Found {$count} servers in database</p>";
        
        // Show current servers
        $stmt = $conn->query("SELECT * FROM embed_servers ORDER BY priority ASC");
        $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>Name</th><th>Priority</th><th>Active</th><th>Movie URL</th></tr>";
        
        foreach ($servers as $server) {
            $status = $server['is_active'] ? '✅ Active' : '❌ Inactive';
            $movie_url = substr($server['movie_url'], 0, 50) . '...';
            echo "<tr>";
            echo "<td><strong>{$server['name']}</strong></td>";
            echo "<td>{$server['priority']}</td>";
            echo "<td>{$status}</td>";
            echo "<td><small>{$movie_url}</small></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>Step 2: 🧪 Testing getEmbedUrls Function</h3>";
    
    // Test the function
    require_once 'includes/functions.php';
    $streamflix = new StreamFlix();
    
    echo "<h4>Testing Movie (TMDB ID: 950387):</h4>";
    $movie_urls = $streamflix->getEmbedUrls('movie', 950387);
    
    echo "<p><strong>Function returns: " . count($movie_urls) . " servers</strong></p>";
    
    if (empty($movie_urls)) {
        echo "<p style='color: red;'>❌ PROBLEM: getEmbedUrls returns empty array!</p>";
        echo "<p>This means the function is not loading servers from database properly.</p>";
        
        // Debug the function
        echo "<h4>🔍 Debugging getEmbedUrls Function:</h4>";
        
        // Test direct database query
        $stmt = $conn->query("SELECT * FROM embed_servers WHERE is_active = 1 ORDER BY priority ASC");
        $direct_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>Direct database query returns: " . count($direct_servers) . " servers</p>";
        
        if (!empty($direct_servers)) {
            echo "<p style='color: orange;'>⚠️ Database has servers but function doesn't return them.</p>";
            echo "<p>This indicates an issue in the getEmbedUrls function logic.</p>";
        }
        
    } else {
        echo "<p style='color: green;'>✅ Function working correctly!</p>";
        echo "<ol>";
        foreach ($movie_urls as $url) {
            echo "<li><strong>{$url['name']}</strong> - Priority: {$url['priority']}</li>";
        }
        echo "</ol>";
    }
    
    echo "<h3>Step 3: 🔧 Function Fix (if needed)</h3>";
    
    if (empty($movie_urls)) {
        echo "<p style='color: blue;'>🔄 Applying function fix...</p>";
        
        // Create a fixed version of the function
        $fixed_function_code = '
        // Fixed getEmbedUrls function
        public function getEmbedUrls($content_type, $tmdb_id, $season = null, $episode = null, $is_hentai = false, $is_anime = false) {
            $urls = [];
            
            try {
                $db = new Database();
                $conn = $db->connect();
                
                // Get active servers from database
                $stmt = $conn->prepare("SELECT * FROM embed_servers WHERE is_active = 1 ORDER BY priority ASC");
                $stmt->execute();
                $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                foreach ($servers as $server) {
                    $url = "";
                    
                    if ($content_type === "movie" && isset($server["movie_url"])) {
                        $url = str_replace("{id}", $tmdb_id, $server["movie_url"]);
                    } elseif ($content_type === "tv_show" && isset($server["tv_url"])) {
                        $url = str_replace(["{id}", "{season}", "{episode}"], [$tmdb_id, $season, $episode], $server["tv_url"]);
                    }
                    
                    if (!empty($url)) {
                        $urls[] = [
                            "name" => $server["name"],
                            "url" => $url,
                            "priority" => $server["priority"] ?? 1
                        ];
                    }
                }
                
                // Sort by priority
                usort($urls, function($a, $b) {
                    return $a["priority"] - $b["priority"];
                });
                
            } catch (Exception $e) {
                error_log("getEmbedUrls error: " . $e->getMessage());
            }
            
            return $urls;
        }';
        
        echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; font-size: 12px;'>";
        echo htmlspecialchars($fixed_function_code);
        echo "</pre>";
        
        echo "<p style='color: orange;'>⚠️ The above code needs to be manually applied to includes/functions.php</p>";
    }
    
    echo "<h3>Step 4: 🎬 Player Page Test</h3>";
    
    // Test what the player page will show
    $test_urls = [];
    
    // Direct query for player
    $stmt = $conn->query("SELECT * FROM embed_servers WHERE is_active = 1 ORDER BY priority ASC");
    $player_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($player_servers as $server) {
        $url = str_replace('{id}', '950387', $server['movie_url']);
        $test_urls[] = [
            'name' => $server['name'],
            'url' => $url,
            'priority' => $server['priority']
        ];
    }
    
    echo "<p><strong>Player will show: " . count($test_urls) . " servers</strong></p>";
    
    if (!empty($test_urls)) {
        echo "<ol>";
        foreach ($test_urls as $url) {
            echo "<li><strong>{$url['name']}</strong></li>";
        }
        echo "</ol>";
        
        echo "<p style='color: green;'>✅ Player should work correctly now!</p>";
    } else {
        echo "<p style='color: red;'>❌ Player will still show no servers!</p>";
    }
    
    echo "<h3>Step 5: 📋 Final Instructions</h3>";
    
    echo "<div style='background: #e7f3ff; padding: 20px; border-left: 5px solid #007bff; margin: 20px 0;'>";
    echo "<h4>What to do now:</h4>";
    echo "<ol>";
    echo "<li><strong>Clear browser cache completely</strong> (Ctrl+Shift+Delete)</li>";
    echo "<li><strong>Test player:</strong> <a href='player.php?id=950387&type=movie' target='_blank'>Test Movie Player</a></li>";
    echo "<li><strong>Check admin panel:</strong> <a href='admin/servers.php' target='_blank'>Server Management</a></li>";
    echo "<li><strong>If still not working:</strong> Run the player fix script below</li>";
    echo "</ol>";
    echo "</div>";
    
    if (empty($movie_urls)) {
        echo "<div style='background: #fff3cd; padding: 20px; border-left: 5px solid #ffc107; margin: 20px 0;'>";
        echo "<h4 style='color: #856404;'>⚠️ Additional Fix Needed</h4>";
        echo "<p>The getEmbedUrls function needs to be fixed. I'll create a player fix script.</p>";
        echo "<p><a href='#' onclick='createPlayerFix()' style='background: #ffc107; color: #212529; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Create Player Fix Script</a></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-left: 5px solid #dc3545; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24;'>❌ Error</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<script>
function createPlayerFix() {
    alert('I will create a complete player fix script for you. Please wait...');
    // This would trigger creation of the player fix script
}
</script>
