<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$update_results = [];
$update_errors = [];

// Handle hentai support addition
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    try {
        $db = new Database();
        $conn = $db->connect();
        
        if ($action === 'add_hentai_column') {
            // Add hentai_url column to embed_servers table
            try {
                // Check if column already exists
                $stmt = $conn->query("SHOW COLUMNS FROM embed_servers LIKE 'hentai_url'");
                if ($stmt->rowCount() == 0) {
                    $conn->exec("ALTER TABLE embed_servers ADD COLUMN hentai_url TEXT NULL AFTER tv_url");
                    $update_results[] = "✅ Added hentai_url column to embed_servers table";
                } else {
                    $update_results[] = "ℹ️ hentai_url column already exists";
                }
            } catch (Exception $e) {
                $update_errors[] = "❌ Error adding hentai_url column: " . $e->getMessage();
            }
        }
        
        if ($action === 'update_letsembed_server') {
            // Update LetsEmbed server with hentai URL
            try {
                $stmt = $conn->prepare("UPDATE embed_servers SET hentai_url = ? WHERE name = 'LetsEmbed'");
                $hentai_url = 'https://letsembed.cc/embed/hentai/?id={tmdb_id}/{season_number}/{episode_number}';
                $stmt->execute([$hentai_url]);
                
                if ($stmt->rowCount() > 0) {
                    $update_results[] = "✅ Updated LetsEmbed server with hentai URL";
                } else {
                    $update_errors[] = "❌ LetsEmbed server not found or already updated";
                }
            } catch (Exception $e) {
                $update_errors[] = "❌ Error updating LetsEmbed server: " . $e->getMessage();
            }
        }
        
        if ($action === 'add_hentai_genre') {
            // Add Hentai genre if it doesn't exist
            try {
                $stmt = $conn->prepare("INSERT IGNORE INTO genres (name, tmdb_id) VALUES (?, ?)");
                $stmt->execute(['Hentai', 99999]); // Using a high number for custom genre
                
                if ($stmt->rowCount() > 0) {
                    $update_results[] = "✅ Added Hentai genre";
                } else {
                    $update_results[] = "ℹ️ Hentai genre already exists";
                }
            } catch (Exception $e) {
                $update_errors[] = "❌ Error adding Hentai genre: " . $e->getMessage();
            }
        }
        
        if ($action === 'mark_hentai_content') {
            // Mark existing content as hentai based on title keywords
            try {
                // Get hentai genre ID
                $stmt = $conn->prepare("SELECT id FROM genres WHERE name = 'Hentai'");
                $stmt->execute();
                $hentai_genre = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($hentai_genre) {
                    $hentai_keywords = ['hentai', 'ecchi', 'adult', '18+', 'xxx', 'erotic'];
                    $marked_count = 0;
                    
                    foreach ($hentai_keywords as $keyword) {
                        // Mark TV shows
                        $stmt = $conn->prepare("
                            INSERT IGNORE INTO tv_show_genres (tv_show_id, genre_id)
                            SELECT id, ? FROM tv_shows 
                            WHERE LOWER(name) LIKE ? OR LOWER(overview) LIKE ?
                        ");
                        $like_pattern = '%' . strtolower($keyword) . '%';
                        $stmt->execute([$hentai_genre['id'], $like_pattern, $like_pattern]);
                        $marked_count += $stmt->rowCount();
                        
                        // Mark movies
                        $stmt = $conn->prepare("
                            INSERT IGNORE INTO movie_genres (movie_id, genre_id)
                            SELECT id, ? FROM movies 
                            WHERE LOWER(title) LIKE ? OR LOWER(overview) LIKE ?
                        ");
                        $stmt->execute([$hentai_genre['id'], $like_pattern, $like_pattern]);
                        $marked_count += $stmt->rowCount();
                    }
                    
                    $update_results[] = "✅ Marked {$marked_count} content items as hentai";
                } else {
                    $update_errors[] = "❌ Hentai genre not found. Please add it first.";
                }
            } catch (Exception $e) {
                $update_errors[] = "❌ Error marking hentai content: " . $e->getMessage();
            }
        }
        
        if ($action === 'full_setup') {
            // Run all setup steps
            $steps = ['add_hentai_column', 'update_letsembed_server', 'add_hentai_genre', 'mark_hentai_content'];
            
            foreach ($steps as $step) {
                $_POST['action'] = $step;
                // Recursively call the same logic
                // This is a simplified approach - in production, you'd want to refactor this
            }
        }
        
    } catch (Exception $e) {
        $update_errors[] = "❌ Database error: " . $e->getMessage();
    }
}

// Check current status
try {
    $db = new Database();
    $conn = $db->connect();
    
    $status = [];
    
    // Check if hentai_url column exists
    $stmt = $conn->query("SHOW COLUMNS FROM embed_servers LIKE 'hentai_url'");
    $status['hentai_column'] = $stmt->rowCount() > 0;
    
    // Check if LetsEmbed has hentai URL
    $stmt = $conn->query("SELECT hentai_url FROM embed_servers WHERE name = 'LetsEmbed'");
    $letsembed = $stmt->fetch(PDO::FETCH_ASSOC);
    $status['letsembed_hentai'] = $letsembed && !empty($letsembed['hentai_url']);
    
    // Check if Hentai genre exists
    $stmt = $conn->query("SELECT COUNT(*) FROM genres WHERE name = 'Hentai'");
    $status['hentai_genre'] = $stmt->fetchColumn() > 0;
    
    // Count hentai content
    $stmt = $conn->query("
        SELECT COUNT(*) FROM tv_show_genres tsg 
        JOIN genres g ON tsg.genre_id = g.id 
        WHERE g.name = 'Hentai'
    ");
    $status['hentai_tv_count'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("
        SELECT COUNT(*) FROM movie_genres mg 
        JOIN genres g ON mg.genre_id = g.id 
        WHERE g.name = 'Hentai'
    ");
    $status['hentai_movie_count'] = $stmt->fetchColumn();
    
} catch (Exception $e) {
    $status = ['error' => $e->getMessage()];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Hentai Support - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container {
            max-width: 800px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .setup-section {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .setup-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-weight: 500;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .result-item {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 4px;
            background: var(--dark-bg);
        }
        
        .success {
            border-left: 4px solid #28a745;
        }
        
        .error {
            border-left: 4px solid #dc3545;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .status-ok {
            color: #28a745;
        }
        
        .status-missing {
            color: #dc3545;
        }
        
        .warning-box {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid #ffc107;
            color: #ffc107;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <span>Welcome, <?php echo $_SESSION['username']; ?></span>
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <h1>🔞 Add Hentai Support</h1>
        <p>Configure hentai content support with LetsEmbed server</p>
        
        <div class="warning-box">
            <strong>⚠️ Warning:</strong> This will add support for adult/hentai content. Make sure this is appropriate for your site and complies with local laws.
        </div>
        
        <!-- Current Status -->
        <div class="setup-section">
            <h3>📊 Current Status</h3>
            
            <div class="status-item">
                <span>Hentai URL Column</span>
                <span class="<?php echo $status['hentai_column'] ? 'status-ok' : 'status-missing'; ?>">
                    <?php echo $status['hentai_column'] ? '✅ Added' : '❌ Missing'; ?>
                </span>
            </div>
            
            <div class="status-item">
                <span>LetsEmbed Hentai URL</span>
                <span class="<?php echo $status['letsembed_hentai'] ? 'status-ok' : 'status-missing'; ?>">
                    <?php echo $status['letsembed_hentai'] ? '✅ Configured' : '❌ Not Set'; ?>
                </span>
            </div>
            
            <div class="status-item">
                <span>Hentai Genre</span>
                <span class="<?php echo $status['hentai_genre'] ? 'status-ok' : 'status-missing'; ?>">
                    <?php echo $status['hentai_genre'] ? '✅ Added' : '❌ Missing'; ?>
                </span>
            </div>
            
            <div class="status-item">
                <span>Hentai TV Shows</span>
                <span class="status-ok"><?php echo $status['hentai_tv_count'] ?? 0; ?> shows</span>
            </div>
            
            <div class="status-item">
                <span>Hentai Movies</span>
                <span class="status-ok"><?php echo $status['hentai_movie_count'] ?? 0; ?> movies</span>
            </div>
        </div>

        <!-- Setup Actions -->
        <div class="setup-section">
            <h3>🔧 Setup Actions</h3>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="add_hentai_column">
                <button type="submit" class="setup-btn btn-primary">1. Add Hentai URL Column</button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="update_letsembed_server">
                <button type="submit" class="setup-btn btn-primary">2. Update LetsEmbed Server</button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="add_hentai_genre">
                <button type="submit" class="setup-btn btn-primary">3. Add Hentai Genre</button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="mark_hentai_content">
                <button type="submit" class="setup-btn btn-primary">4. Mark Existing Content</button>
            </form>
            
            <br><br>
            
            <form method="POST" style="display: inline;" onsubmit="return confirm('This will run all setup steps. Continue?')">
                <input type="hidden" name="action" value="full_setup">
                <button type="submit" class="setup-btn btn-success">🚀 Complete Setup</button>
            </form>
        </div>

        <!-- Results -->
        <?php if (!empty($update_results)): ?>
            <div class="setup-section">
                <h3>✅ Setup Results</h3>
                <?php foreach ($update_results as $result): ?>
                    <div class="result-item success"><?php echo htmlspecialchars($result); ?></div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($update_errors)): ?>
            <div class="setup-section">
                <h3>❌ Setup Errors</h3>
                <?php foreach ($update_errors as $error): ?>
                    <div class="result-item error"><?php echo htmlspecialchars($error); ?></div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <div class="setup-section">
            <h3>ℹ️ Information</h3>
            <p><strong>Hentai URL Pattern:</strong> <code>https://letsembed.cc/embed/hentai/?id={tmdb_id}/{season_number}/{episode_number}</code></p>
            <p><strong>How it works:</strong> When hentai content is detected (by genre), the system will use the special hentai URL instead of the regular TV show URL.</p>
            <p><strong>Content Detection:</strong> Content is marked as hentai based on title/description keywords or manual genre assignment.</p>
        </div>
        
        <div class="setup-section">
            <p>
                <a href="servers.php">← Back to Servers</a> | 
                <a href="import.php">Import Content</a> | 
                <a href="index.php">Admin Dashboard</a>
            </p>
        </div>
    </div>
</body>
</html>
