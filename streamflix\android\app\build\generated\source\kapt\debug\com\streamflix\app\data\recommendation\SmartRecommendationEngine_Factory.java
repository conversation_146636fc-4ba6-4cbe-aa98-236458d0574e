// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.streamflix.app.data.recommendation;

import com.streamflix.app.data.local.UserPreferences;
import com.streamflix.app.data.repository.StreamFlixRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SmartRecommendationEngine_Factory implements Factory<SmartRecommendationEngine> {
  private final Provider<StreamFlixRepository> repositoryProvider;

  private final Provider<UserPreferences> userPreferencesProvider;

  public SmartRecommendationEngine_Factory(Provider<StreamFlixRepository> repositoryProvider,
      Provider<UserPreferences> userPreferencesProvider) {
    this.repositoryProvider = repositoryProvider;
    this.userPreferencesProvider = userPreferencesProvider;
  }

  @Override
  public SmartRecommendationEngine get() {
    return newInstance(repositoryProvider.get(), userPreferencesProvider.get());
  }

  public static SmartRecommendationEngine_Factory create(
      Provider<StreamFlixRepository> repositoryProvider,
      Provider<UserPreferences> userPreferencesProvider) {
    return new SmartRecommendationEngine_Factory(repositoryProvider, userPreferencesProvider);
  }

  public static SmartRecommendationEngine newInstance(StreamFlixRepository repository,
      UserPreferences userPreferences) {
    return new SmartRecommendationEngine(repository, userPreferences);
  }
}
