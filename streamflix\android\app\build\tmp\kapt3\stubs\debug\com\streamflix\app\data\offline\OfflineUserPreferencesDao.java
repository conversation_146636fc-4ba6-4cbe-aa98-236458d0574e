package com.streamflix.app.data.offline;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import androidx.room.*;
import com.google.gson.Gson;
import com.streamflix.app.data.local.UserPreferences;
import com.streamflix.app.data.model.*;
import dagger.hilt.android.qualifiers.ApplicationContext;
import kotlinx.coroutines.flow.*;
import java.io.File;
import javax.inject.Inject;
import javax.inject.Singleton;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\bg\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u0004\u0018\u00010\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\b\u00a8\u0006\t"}, d2 = {"Lcom/streamflix/app/data/offline/OfflineUserPreferencesDao;", "", "getPreferences", "Lcom/streamflix/app/data/offline/OfflineUserPreferences;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertOrUpdatePreferences", "", "preferences", "(Lcom/streamflix/app/data/offline/OfflineUserPreferences;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao()
public abstract interface OfflineUserPreferencesDao {
    
    @androidx.room.Query(value = "SELECT * FROM offline_user_preferences WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPreferences(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.streamflix.app.data.offline.OfflineUserPreferences> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertOrUpdatePreferences(@org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.offline.OfflineUserPreferences preferences, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}