<?php
require_once '../includes/functions.php';

header('Content-Type: application/json');

$tmdb_id = isset($_GET['tmdb_id']) ? (int)$_GET['tmdb_id'] : 0;
$season_number = isset($_GET['season']) ? (int)$_GET['season'] : 1;

if (!$tmdb_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid TMDB ID']);
    exit();
}

try {
    $streamflix = new StreamFlix();
    
    // Fetch episodes from TMDB API
    $endpoint = "/tv/{$tmdb_id}/season/{$season_number}";
    $data = $streamflix->fetchFromTMDB($endpoint);
    
    if ($data && isset($data['episodes'])) {
        $episodes = [];
        foreach ($data['episodes'] as $episode) {
            $episodes[] = [
                'episode_number' => $episode['episode_number'],
                'name' => $episode['name'],
                'overview' => $episode['overview'],
                'still_path' => $episode['still_path'],
                'runtime' => $episode['runtime'] ?? null,
                'air_date' => $episode['air_date'],
                'vote_average' => $episode['vote_average']
            ];
        }
        
        echo json_encode([
            'success' => true,
            'episodes' => $episodes
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Episodes not found'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
