<?php
session_start();
require_once '../config/database.php';

echo "<h1>🔍 Session Debug Information</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
    .info-box { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    .btn { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
</style>";

echo "<div class='info-box'>";
echo "<h3>📊 Current Session Data</h3>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";
echo "</div>";

echo "<div class='info-box'>";
echo "<h3>🔐 Authentication Status</h3>";

$is_logged_in = isset($_SESSION['user_id']);
$is_admin = false;

if ($is_logged_in) {
    echo "<div class='success'>✅ User is logged in (ID: {$_SESSION['user_id']})</div>";
    
    // Check admin status
    if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
        $is_admin = true;
        echo "<div class='success'>✅ User has admin role in session</div>";
    } elseif (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
        $is_admin = true;
        echo "<div class='success'>✅ User has is_admin flag in session</div>";
    } else {
        echo "<div class='warning'>⚠️ No admin role found in session</div>";
        
        // Check database
        try {
            $db = new Database();
            $conn = $db->connect();
            
            $stmt = $conn->prepare("SELECT id, username, email, role FROM users WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                echo "<div class='info-box'>";
                echo "<h4>👤 User Database Info:</h4>";
                echo "<pre>" . print_r($user, true) . "</pre>";
                echo "</div>";
                
                if ($user['role'] === 'admin') {
                    $is_admin = true;
                    echo "<div class='success'>✅ User is admin in database</div>";
                    
                    // Update session
                    $_SESSION['role'] = 'admin';
                    echo "<div class='success'>✅ Session updated with admin role</div>";
                } else {
                    echo "<div class='error'>❌ User is not admin in database (Role: {$user['role']})</div>";
                }
            } else {
                echo "<div class='error'>❌ User not found in database</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
        }
    }
} else {
    echo "<div class='error'>❌ User is not logged in</div>";
}

echo "</div>";

echo "<div class='info-box'>";
echo "<h3>🎯 Access Test Results</h3>";

if ($is_logged_in && $is_admin) {
    echo "<div class='success'>✅ Should have access to admin pages</div>";
} else {
    echo "<div class='error'>❌ Should be redirected to login</div>";
}

echo "</div>";

echo "<div class='info-box'>";
echo "<h3>🔗 Quick Links</h3>";
echo "<a href='index.php' class='btn'>📊 Admin Dashboard</a>";
echo "<a href='hentai-management.php' class='btn'>🔞 Hentai Management</a>";
echo "<a href='quick-hentai-convert.php' class='btn'>⚡ Quick Converter</a>";
echo "<a href='../login.php' class='btn'>🔐 Login Page</a>";
echo "<a href='../logout.php' class='btn'>🚪 Logout</a>";
echo "</div>";

echo "<div class='info-box'>";
echo "<h3>🛠️ Troubleshooting</h3>";
echo "<ul>";
echo "<li><strong>If not logged in:</strong> Go to login page and login with admin credentials</li>";
echo "<li><strong>If logged in but not admin:</strong> Check user role in database</li>";
echo "<li><strong>If session issues:</strong> Clear browser cookies and login again</li>";
echo "<li><strong>If database issues:</strong> Check database connection and user table</li>";
echo "</ul>";
echo "</div>";

// Show all users for debugging
try {
    $db = new Database();
    $conn = $db->connect();
    
    $stmt = $conn->query("SELECT id, username, email, role FROM users ORDER BY id");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='info-box'>";
    echo "<h3>👥 All Users in Database</h3>";
    if (!empty($users)) {
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>ID</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>Username</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>Email</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>Role</th>";
        echo "</tr>";
        
        foreach ($users as $user) {
            $bg_color = $user['role'] === 'admin' ? '#d4edda' : '#ffffff';
            echo "<tr style='background: {$bg_color};'>";
            echo "<td style='padding: 10px; border: 1px solid #ddd;'>{$user['id']}</td>";
            echo "<td style='padding: 10px; border: 1px solid #ddd;'>{$user['username']}</td>";
            echo "<td style='padding: 10px; border: 1px solid #ddd;'>{$user['email']}</td>";
            echo "<td style='padding: 10px; border: 1px solid #ddd;'><strong>{$user['role']}</strong></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='warning'>⚠️ No users found in database</div>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='info-box error'>";
    echo "<h3>❌ Database Error</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<div class='info-box'>";
echo "<h3>📝 Server Information</h3>";
echo "<ul>";
echo "<li><strong>PHP Version:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>Session ID:</strong> " . session_id() . "</li>";
echo "<li><strong>Session Save Path:</strong> " . session_save_path() . "</li>";
echo "<li><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</li>";
echo "</ul>";
echo "</div>";
?>
