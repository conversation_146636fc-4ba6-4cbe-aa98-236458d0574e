<?php
/**
 * Movies API Endpoints
 * /api/movies.php
 */

require_once 'config.php';

$method = getRequestMethod();
$data = getRequestData();
$path = $_GET['action'] ?? '';
$id = $_GET['id'] ?? null;

logAPIRequest("movies/{$path}", $method);

try {
    $db = new Database();
    $conn = $db->connect();
    
    switch ($path) {
        case 'list':
            handleMoviesList($conn);
            break;
            
        case 'featured':
            handleFeaturedMovies($conn);
            break;
            
        case 'trending':
            handleTrendingMovies($conn);
            break;
            
        case 'popular':
            handlePopularMovies($conn);
            break;
            
        case 'latest':
            handleLatestMovies($conn);
            break;
            
        case 'details':
            handleMovieDetails($conn, $id);
            break;
            
        case 'search':
            handleMovieSearch($conn);
            break;
            
        case 'genres':
            handleMovieGenres($conn);
            break;
            
        case 'by-genre':
            handleMoviesByGenre($conn);
            break;
            
        case 'servers':
            handleMovieServers($conn, $id);
            break;
            
        default:
            APIResponse::notFound('Endpoint not found');
    }
    
} catch (Exception $e) {
    APIResponse::serverError('Database connection failed');
}

/**
 * Handle movies list with pagination
 */
function handleMoviesList($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $page = max(1, (int)($_GET['page'] ?? 1));
    $limit = min(50, max(1, (int)($_GET['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    // Get total count
    $stmt = $conn->query("SELECT COUNT(*) FROM movies");
    $total = $stmt->fetchColumn();
    
    // Get movies
    $stmt = $conn->prepare("
        SELECT * FROM movies 
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$limit, $offset]);
    $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $formattedMovies = array_map('formatMovieForAPI', $movies);
    
    APIResponse::success([
        'movies' => $formattedMovies,
        'pagination' => [
            'current_page' => $page,
            'per_page' => $limit,
            'total' => (int)$total,
            'total_pages' => ceil($total / $limit),
            'has_next' => $page < ceil($total / $limit),
            'has_prev' => $page > 1
        ]
    ]);
}

/**
 * Handle featured movies
 */
function handleFeaturedMovies($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $limit = min(20, max(1, (int)($_GET['limit'] ?? 10)));
    
    $stmt = $conn->prepare("
        SELECT * FROM movies 
        WHERE is_featured = 1 
        ORDER BY vote_average DESC, popularity DESC 
        LIMIT ?
    ");
    $stmt->execute([$limit]);
    $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $formattedMovies = array_map('formatMovieForAPI', $movies);
    
    APIResponse::success(['movies' => $formattedMovies]);
}

/**
 * Handle trending movies
 */
function handleTrendingMovies($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $limit = min(20, max(1, (int)($_GET['limit'] ?? 10)));
    
    $stmt = $conn->prepare("
        SELECT * FROM movies 
        WHERE is_trending = 1 
        ORDER BY popularity DESC, vote_average DESC 
        LIMIT ?
    ");
    $stmt->execute([$limit]);
    $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $formattedMovies = array_map('formatMovieForAPI', $movies);
    
    APIResponse::success(['movies' => $formattedMovies]);
}

/**
 * Handle popular movies
 */
function handlePopularMovies($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $limit = min(50, max(1, (int)($_GET['limit'] ?? 20)));
    
    $stmt = $conn->prepare("
        SELECT * FROM movies 
        ORDER BY popularity DESC, vote_average DESC 
        LIMIT ?
    ");
    $stmt->execute([$limit]);
    $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $formattedMovies = array_map('formatMovieForAPI', $movies);
    
    APIResponse::success(['movies' => $formattedMovies]);
}

/**
 * Handle latest movies
 */
function handleLatestMovies($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $limit = min(50, max(1, (int)($_GET['limit'] ?? 20)));
    
    $stmt = $conn->prepare("
        SELECT * FROM movies 
        ORDER BY release_date DESC, created_at DESC 
        LIMIT ?
    ");
    $stmt->execute([$limit]);
    $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $formattedMovies = array_map('formatMovieForAPI', $movies);
    
    APIResponse::success(['movies' => $formattedMovies]);
}

/**
 * Handle movie details
 */
function handleMovieDetails($conn, $id) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    if (!$id) {
        APIResponse::error('Movie ID is required');
    }
    
    // Get movie details
    $stmt = $conn->prepare("SELECT * FROM movies WHERE id = ?");
    $stmt->execute([$id]);
    $movie = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$movie) {
        APIResponse::notFound('Movie not found');
    }
    
    $movieData = formatMovieForAPI($movie);
    
    // Get genres
    try {
        $stmt = $conn->prepare("
            SELECT g.* FROM genres g
            JOIN movie_genres mg ON g.id = mg.genre_id
            WHERE mg.movie_id = ?
        ");
        $stmt->execute([$id]);
        $movieData['genres'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        $movieData['genres'] = [];
    }
    
    // Get cast (if table exists)
    try {
        $stmt = $conn->prepare("
            SELECT * FROM movie_cast 
            WHERE movie_id = ? 
            ORDER BY cast_order ASC 
            LIMIT 10
        ");
        $stmt->execute([$id]);
        $movieData['cast'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        $movieData['cast'] = [];
    }
    
    // Check if user has this in watchlist
    $user = APIAuth::optionalAuth();
    if ($user) {
        try {
            $stmt = $conn->prepare("SELECT id FROM watchlist WHERE user_id = ? AND movie_id = ?");
            $stmt->execute([$user['id'], $id]);
            $movieData['in_watchlist'] = $stmt->rowCount() > 0;
        } catch (Exception $e) {
            $movieData['in_watchlist'] = false;
        }
    } else {
        $movieData['in_watchlist'] = false;
    }
    
    APIResponse::success(['movie' => $movieData]);
}

/**
 * Handle movie search
 */
function handleMovieSearch($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $query = trim($_GET['q'] ?? '');
    if (empty($query)) {
        APIResponse::error('Search query is required');
    }
    
    $page = max(1, (int)($_GET['page'] ?? 1));
    $limit = min(50, max(1, (int)($_GET['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    $searchTerm = "%{$query}%";
    
    // Get total count
    $stmt = $conn->prepare("
        SELECT COUNT(*) FROM movies 
        WHERE title LIKE ? OR overview LIKE ?
    ");
    $stmt->execute([$searchTerm, $searchTerm]);
    $total = $stmt->fetchColumn();
    
    // Get movies
    $stmt = $conn->prepare("
        SELECT * FROM movies 
        WHERE title LIKE ? OR overview LIKE ?
        ORDER BY 
            CASE WHEN title LIKE ? THEN 1 ELSE 2 END,
            vote_average DESC,
            popularity DESC
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$searchTerm, $searchTerm, $searchTerm, $limit, $offset]);
    $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $formattedMovies = array_map('formatMovieForAPI', $movies);
    
    APIResponse::success([
        'movies' => $formattedMovies,
        'search_query' => $query,
        'pagination' => [
            'current_page' => $page,
            'per_page' => $limit,
            'total' => (int)$total,
            'total_pages' => ceil($total / $limit),
            'has_next' => $page < ceil($total / $limit),
            'has_prev' => $page > 1
        ]
    ]);
}

/**
 * Handle movie genres
 */
function handleMovieGenres($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    try {
        $stmt = $conn->query("
            SELECT g.*, COUNT(mg.movie_id) as movie_count
            FROM genres g
            LEFT JOIN movie_genres mg ON g.id = mg.genre_id
            GROUP BY g.id
            HAVING movie_count > 0
            ORDER BY g.name ASC
        ");
        $genres = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Format genres
        $formattedGenres = array_map(function($genre) {
            return [
                'id' => (int)$genre['id'],
                'name' => $genre['name'],
                'movie_count' => (int)$genre['movie_count']
            ];
        }, $genres);
        
        APIResponse::success(['genres' => $formattedGenres]);
        
    } catch (Exception $e) {
        // If genres table doesn't exist, return empty array
        APIResponse::success(['genres' => []]);
    }
}

/**
 * Handle movies by genre
 */
function handleMoviesByGenre($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $genreId = $_GET['genre_id'] ?? null;
    if (!$genreId) {
        APIResponse::error('Genre ID is required');
    }
    
    $page = max(1, (int)($_GET['page'] ?? 1));
    $limit = min(50, max(1, (int)($_GET['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    try {
        // Get total count
        $stmt = $conn->prepare("
            SELECT COUNT(DISTINCT m.id) 
            FROM movies m
            JOIN movie_genres mg ON m.id = mg.movie_id
            WHERE mg.genre_id = ?
        ");
        $stmt->execute([$genreId]);
        $total = $stmt->fetchColumn();
        
        // Get movies
        $stmt = $conn->prepare("
            SELECT DISTINCT m.* 
            FROM movies m
            JOIN movie_genres mg ON m.id = mg.movie_id
            WHERE mg.genre_id = ?
            ORDER BY m.vote_average DESC, m.popularity DESC
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$genreId, $limit, $offset]);
        $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $formattedMovies = array_map('formatMovieForAPI', $movies);
        
        APIResponse::success([
            'movies' => $formattedMovies,
            'genre_id' => (int)$genreId,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => (int)$total,
                'total_pages' => ceil($total / $limit),
                'has_next' => $page < ceil($total / $limit),
                'has_prev' => $page > 1
            ]
        ]);
        
    } catch (Exception $e) {
        APIResponse::error('Genre not found or no movies available');
    }
}

/**
 * Handle movie servers
 */
function handleMovieServers($conn, $id) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    if (!$id) {
        APIResponse::error('Movie ID is required');
    }
    
    // Verify movie exists
    $stmt = $conn->prepare("SELECT id FROM movies WHERE id = ?");
    $stmt->execute([$id]);
    if (!$stmt->fetch()) {
        APIResponse::notFound('Movie not found');
    }
    
    try {
        $stmt = $conn->prepare("
            SELECT s.*, ms.embed_url, ms.quality
            FROM servers s
            JOIN movie_servers ms ON s.id = ms.server_id
            WHERE ms.movie_id = ? AND s.is_active = 1
            ORDER BY s.name ASC
        ");
        $stmt->execute([$id]);
        $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $formattedServers = array_map(function($server) {
            return [
                'id' => (int)$server['id'],
                'name' => $server['name'],
                'embed_url' => $server['embed_url'],
                'quality' => $server['quality'] ?? 'HD',
                'is_premium' => (bool)($server['is_premium'] ?? false)
            ];
        }, $servers);
        
        APIResponse::success(['servers' => $formattedServers]);
        
    } catch (Exception $e) {
        // If movie_servers table doesn't exist, return empty array
        APIResponse::success(['servers' => []]);
    }
}
?>
