/ Header Record For PersistentHashMapValueStorage= android.app.Application$androidx.work.Configuration.Provider kotlin.Enum kotlin.Enum androidx.work.CoroutineWorker androidx.room.RoomDatabase kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel android.webkit.WebViewClient androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel kotlin.Enum" !com.streamflix.app.utils.Resource" !com.streamflix.app.utils.Resource" !com.streamflix.app.utils.Resource