# StreamFlix - Premium Movie & TV Streaming Platform

একটি সম্পূর্ণ Netflix-স্টাইল মুভি এবং টিভি শো স্ট্রিমিং প্ল্যাটফর্ম PHP দিয়ে তৈরি।

## ✨ Features

### 🎬 Content Management
- **TMDB Integration**: Automatic movie/TV show import from TMDB API
- **Multiple Embed Servers**: <PERSON>Embed, Vid<PERSON><PERSON>, Vid<PERSON>ee support
- **Ad-Free Player**: Advanced ad blocking for smooth streaming
- **Auto Server Selection**: Multiple backup servers for reliability

### 🎨 User Experience
- **Netflix-Style UI**: Modern, responsive design
- **Mobile Optimized**: Works perfectly on all devices
- **Fast Search**: Real-time search with autocomplete
- **Watchlist**: Personal movie/TV show lists
- **Watch History**: Resume watching from where you left off

### 🔧 Admin Features
- **Complete Control**: Full admin panel for content management
- **Bulk Import**: Import trending/popular content automatically
- **User Management**: Manage users and subscriptions
- **Analytics**: View platform statistics

### 🛡️ Security & Performance
- **Ad Blocking**: Built-in ad blocker for embedded players
- **Secure Streaming**: Sandboxed iframes with security headers
- **Fast Loading**: Optimized for speed and performance
- **SEO Friendly**: Proper meta tags and structured data

## 🚀 Installation

### Requirements
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- TMDB API Key

### Setup Steps

1. **Upload Files**
   ```bash
   # Upload all files to your web hosting directory
   # e.g., public_html/streamflix/
   ```

2. **Database Setup**
   ```sql
   # Import the database schema
   mysql -u username -p database_name < config/install.sql
   ```

3. **Configuration**
   ```php
   # Edit config/database.php
   - Update database credentials
   - Add your TMDB API key
   - Set site URL and name
   ```

4. **TMDB API Key**
   - Go to https://www.themoviedb.org/settings/api
   - Create an account and get your API key
   - Add it to `config/database.php`

5. **Permissions**
   ```bash
   # Set proper file permissions
   chmod 755 assets/
   chmod 644 *.php
   ```

## 📖 Usage

### Admin Panel
1. Login with admin credentials (default: admin/password)
2. Go to `/admin/` directory
3. Import content using TMDB IDs
4. Manage users and settings

### Content Import
```php
# Single Import
- Enter TMDB ID in admin panel
- Select Movie or TV Show
- Click Import

# Bulk Import
- Use "Import Trending" buttons
- Use "Import Popular" buttons
- Automatic server assignment
```

### Embed Servers
The system automatically assigns multiple embed servers:
- **AutoEmbed**: `https://player.autoembed.cc/embed/movie/{id}`
- **VidJoy**: `https://vidjoy.pro/embed/movie/{id}`
- **VidZee**: `https://player.vidzee.wtf/embed/movie/{id}`

For TV Shows:
- **Format**: `/embed/tv/{id}/{season}/{episode}`

## 🎯 Key Features Explained

### Ad-Free Streaming
- JavaScript ad blocker blocks common ad domains
- CSS rules hide ad elements
- Iframe sandboxing prevents malicious scripts
- Custom player controls

### TMDB Integration
```php
# Import a movie
$streamflix->importMovie(385687); // Fast X

# Import a TV show
$streamflix->importTVShow(94997); // House of the Dragon

# Bulk import trending
$streamflix->importTrending('movie');
```

### Server Management
- Automatic failover between servers
- Priority-based server selection
- Error handling and retry logic
- User can manually switch servers

## 🔧 Customization

### Adding New Embed Servers
```php
# In config/database.php
$embed_servers['new_server'] = [
    'name' => 'New Server',
    'movie_url' => 'https://example.com/embed/movie/{id}',
    'tv_url' => 'https://example.com/embed/tv/{id}/{season}/{episode}',
    'priority' => 4
];
```

### Styling
- Edit `assets/css/style.css` for design changes
- All CSS variables are in `:root` for easy theming
- Responsive design with mobile-first approach

### Database Schema
- Movies: `movies` table
- TV Shows: `tv_shows`, `seasons`, `episodes` tables
- Users: `users`, `watchlist`, `watch_history` tables
- Settings: `site_settings` table

## 📱 Mobile Support
- Fully responsive design
- Touch-friendly interface
- Mobile player optimization
- Progressive Web App ready

## 🛡️ Security Features
- SQL injection protection with PDO
- XSS protection with input sanitization
- CSRF protection for forms
- Secure session management
- Admin role verification

## 🎨 UI Components
- Netflix-style hero section
- Horizontal content sliders
- Modal popups for content details
- Loading animations
- Error handling

## 📊 Analytics
- User watch history
- Popular content tracking
- Search analytics
- Admin dashboard statistics

## 🔄 Updates
- Easy to update via file replacement
- Database migrations for schema changes
- Backward compatibility maintained

## 📞 Support
- Well-documented code
- Modular architecture
- Easy to extend and customize
- Community support

## 📄 License
This project is for educational purposes. Make sure to comply with:
- TMDB API terms of service
- Embed server terms of service
- Local copyright laws
- Hosting provider policies

## 🚀 Performance Tips
1. Use CDN for static assets
2. Enable gzip compression
3. Optimize database queries
4. Use caching for TMDB API calls
5. Implement lazy loading for images

---

**Note**: This is a complete streaming platform. Make sure you have proper licensing for any content you stream and comply with all applicable laws and regulations.
