<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h2>🔞 Test Hentai Server Selection</h2>";
echo "<p>Testing if hentai content uses hentai-specific servers.</p>";

try {
    $db = new Database();
    $conn = $db->connect();
    $streamflix = new StreamFlix();
    
    echo "<h3>📊 Hentai Content Analysis</h3>";
    
    // Find hentai content
    $stmt = $conn->query("
        SELECT t.id, t.tmdb_id, t.name, t.content_type, t.overview
        FROM tv_shows t
        WHERE t.content_type = 'hentai'
        OR EXISTS (
            SELECT 1 FROM tv_show_genres tg
            JOIN genres g ON tg.genre_id = g.id
            WHERE tg.tv_show_id = t.id
            AND (g.name LIKE '%hentai%' OR g.name LIKE '%adult%' OR g.name LIKE '%18+%')
        )
        OR t.name LIKE '%hentai%'
        OR t.overview LIKE '%hentai%'
        LIMIT 10
    ");
    $hentai_content = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🔍 Found Hentai Content:</h4>";
    echo "<p><strong>Total Items:</strong> " . count($hentai_content) . "</p>";
    
    if (count($hentai_content) > 0) {
        echo "<div style='max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; margin-top: 10px;'>";
        foreach ($hentai_content as $item) {
            echo "<div style='margin-bottom: 10px; padding: 10px; background: white; border-radius: 5px; border-left: 4px solid #dc3545;'>";
            echo "<strong>{$item['name']}</strong><br>";
            echo "<small>TMDB ID: {$item['tmdb_id']} | Content Type: {$item['content_type']}</small>";
            echo "</div>";
        }
        echo "</div>";
    }
    echo "</div>";
    
    // Check available hentai servers
    echo "<h3>🖥️ Available Hentai Servers</h3>";
    
    $stmt = $conn->query("SELECT * FROM embed_servers WHERE hentai_url IS NOT NULL AND hentai_url != '' AND is_active = 1 ORDER BY priority ASC");
    $hentai_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🖥️ Hentai Servers:</h4>";
    echo "<p><strong>Available Servers:</strong> " . count($hentai_servers) . "</p>";
    
    if (count($hentai_servers) > 0) {
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin-top: 10px;'>";
        foreach ($hentai_servers as $server) {
            echo "<div style='margin-bottom: 10px; padding: 10px; background: white; border-radius: 5px;'>";
            echo "<strong>{$server['name']}</strong> (Priority: {$server['priority']})<br>";
            echo "<small>Hentai URL: {$server['hentai_url']}</small>";
            echo "</div>";
        }
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin-top: 10px;'>";
        echo "❌ <strong>No hentai servers configured!</strong>";
        echo "</div>";
    }
    echo "</div>";
    
    // Test server selection for hentai content
    if (count($hentai_content) > 0 && count($hentai_servers) > 0) {
        echo "<h3>🧪 Server Selection Test</h3>";
        
        $test_item = $hentai_content[0];
        
        echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h4>🎯 Testing: {$test_item['name']}</h4>";
        echo "<p><strong>TMDB ID:</strong> {$test_item['tmdb_id']}</p>";
        echo "<p><strong>Content Type:</strong> {$test_item['content_type']}</p>";
        
        // Test getEmbedUrls function
        $embed_urls = $streamflix->getEmbedUrls('tv_show', $test_item['tmdb_id'], 1, 1, true, false);
        
        echo "<h5>📺 Generated Server URLs:</h5>";
        if (!empty($embed_urls)) {
            echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin-top: 10px;'>";
            foreach ($embed_urls as $url_info) {
                echo "<div style='margin-bottom: 10px; padding: 10px; background: white; border-radius: 5px; border-left: 4px solid #28a745;'>";
                echo "<strong>{$url_info['name']}</strong><br>";
                echo "<small>{$url_info['url']}</small>";
                echo "</div>";
            }
            echo "</div>";
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
            echo "✅ <strong>Success!</strong> Found " . count($embed_urls) . " hentai servers for this content.";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
            echo "❌ <strong>Error!</strong> No servers found for hentai content.";
            echo "</div>";
        }
        echo "</div>";
        
        // Test regular TV show for comparison
        $stmt = $conn->query("SELECT * FROM tv_shows WHERE content_type = 'tv_show' OR content_type IS NULL LIMIT 1");
        $regular_tv = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($regular_tv) {
            echo "<h4>🔄 Comparison: Regular TV Show</h4>";
            echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
            echo "<p><strong>Testing:</strong> {$regular_tv['name']} (TMDB: {$regular_tv['tmdb_id']})</p>";
            
            $regular_urls = $streamflix->getEmbedUrls('tv_show', $regular_tv['tmdb_id'], 1, 1, false, false);
            
            echo "<p><strong>Regular TV Servers:</strong> " . count($regular_urls) . "</p>";
            if (!empty($regular_urls)) {
                echo "<small>Uses regular embed servers (movie_url/tv_url)</small>";
            }
            echo "</div>";
        }
    }
    
    // Manual server test
    echo "<h3>🔧 Manual Server Configuration Test</h3>";
    
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>⚙️ Server Configuration Check:</h4>";
    
    $stmt = $conn->query("SELECT * FROM embed_servers ORDER BY priority ASC");
    $all_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $servers_needing_hentai = [];
    foreach ($all_servers as $server) {
        if (empty($server['hentai_url'])) {
            $servers_needing_hentai[] = $server;
        }
    }
    
    if (count($servers_needing_hentai) > 0) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "⚠️ <strong>Servers Missing Hentai URLs:</strong> " . count($servers_needing_hentai);
        echo "<ul>";
        foreach ($servers_needing_hentai as $server) {
            echo "<li>{$server['name']}</li>";
        }
        echo "</ul>";
        echo "<p><a href='fix_hentai_servers.php' style='background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>🔧 Fix Missing URLs</a></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "✅ <strong>All servers have hentai URLs configured!</strong>";
        echo "</div>";
    }
    echo "</div>";
    
    // Test links
    echo "<h3>🔗 Test Links</h3>";
    
    echo "<div style='background: #cff4fc; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🎯 Test Hentai Player:</h4>";
    
    if (count($hentai_content) > 0) {
        foreach (array_slice($hentai_content, 0, 3) as $item) {
            echo "<p><a href='player.php?id={$item['tmdb_id']}&type=tv_show' target='_blank' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; margin-right: 10px; display: inline-block; margin-bottom: 10px;'>🔞 {$item['name']}</a></p>";
        }
    }
    
    echo "<p><a href='admin/servers.php' target='_blank' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin-right: 10px; display: inline-block; margin-bottom: 10px;'>🖥️ Manage Servers</a></p>";
    
    echo "<p><a href='fix_hentai_servers.php' target='_blank' style='background: #ffc107; color: #212529; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block; margin-bottom: 10px;'>🔧 Fix Hentai Servers</a></p>";
    echo "</div>";
    
    // Summary
    echo "<h3>📋 Summary</h3>";
    
    $hentai_content_count = count($hentai_content);
    $hentai_servers_count = count($hentai_servers);
    $working = ($hentai_content_count > 0 && $hentai_servers_count > 0);
    
    echo "<div style='background: " . ($working ? "#d4edda" : "#f8d7da") . "; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>" . ($working ? "✅ System Status: Working" : "❌ System Status: Needs Fix") . "</h4>";
    echo "<ul>";
    echo "<li><strong>Hentai Content Found:</strong> {$hentai_content_count}</li>";
    echo "<li><strong>Hentai Servers Available:</strong> {$hentai_servers_count}</li>";
    echo "<li><strong>Server Selection:</strong> " . ($working ? "✅ Working" : "❌ Needs Configuration") . "</li>";
    echo "</ul>";
    
    if (!$working) {
        echo "<p><strong>Action Required:</strong></p>";
        echo "<ul>";
        if ($hentai_content_count === 0) {
            echo "<li>Import or mark content as hentai</li>";
        }
        if ($hentai_servers_count === 0) {
            echo "<li>Configure hentai URLs for servers</li>";
        }
        echo "</ul>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4 style='color: #721c24;'>❌ Error</h4>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
