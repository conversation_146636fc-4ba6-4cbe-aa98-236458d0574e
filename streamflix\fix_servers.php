<?php
require_once 'config/database.php';

echo "<h2>🔧 Server Fix Script</h2>";

try {
    $db = new Database();
    $conn = $db->connect();
    
    echo "<h3>📊 Current Status</h3>";
    
    // Check current embed_servers table structure
    $stmt = $conn->query("SHOW COLUMNS FROM embed_servers");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Current embed_servers table columns:</p>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>{$column['Field']} - {$column['Type']}</li>";
    }
    echo "</ul>";
    
    // Check if content-specific columns exist
    $has_movie_column = false;
    $has_tv_column = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'is_active_movies') $has_movie_column = true;
        if ($column['Field'] === 'is_active_tv') $has_tv_column = true;
    }
    
    echo "<h3>🔧 Fixing Server Configuration</h3>";
    
    // Add content-specific columns if they don't exist
    if (!$has_movie_column) {
        echo "<p>Adding is_active_movies column...</p>";
        $conn->exec("ALTER TABLE embed_servers ADD COLUMN is_active_movies BOOLEAN DEFAULT 1");
        echo "<p>✅ Added is_active_movies column</p>";
    } else {
        echo "<p>✅ is_active_movies column already exists</p>";
    }
    
    if (!$has_tv_column) {
        echo "<p>Adding is_active_tv column...</p>";
        $conn->exec("ALTER TABLE embed_servers ADD COLUMN is_active_tv BOOLEAN DEFAULT 1");
        echo "<p>✅ Added is_active_tv column</p>";
    } else {
        echo "<p>✅ is_active_tv column already exists</p>";
    }
    
    // Get current servers
    $stmt = $conn->query("SELECT * FROM embed_servers ORDER BY priority ASC");
    $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>📋 Current Servers in Database</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Priority</th><th>Active</th><th>Active Movies</th><th>Active TV</th></tr>";
    foreach ($servers as $server) {
        echo "<tr>";
        echo "<td>{$server['id']}</td>";
        echo "<td>{$server['name']}</td>";
        echo "<td>{$server['priority']}</td>";
        echo "<td>" . ($server['is_active'] ? 'Yes' : 'No') . "</td>";
        echo "<td>" . ($server['is_active_movies'] ? 'Yes' : 'No') . "</td>";
        echo "<td>" . ($server['is_active_tv'] ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>🧪 Testing Fixed Configuration</h3>";
    
    // Test with a sample movie
    require_once 'includes/functions.php';
    $streamflix = new StreamFlix();
    
    echo "<h4>Testing Movie (TMDB ID: 550):</h4>";
    $movie_urls = $streamflix->getEmbedUrls('movie', 550);
    echo "<p>Found " . count($movie_urls) . " servers for movies:</p>";
    echo "<ol>";
    foreach ($movie_urls as $url) {
        echo "<li><strong>{$url['name']}</strong> (Priority: {$url['priority']})</li>";
    }
    echo "</ol>";
    
    echo "<h4>Testing TV Show (TMDB ID: 1399, S1E1):</h4>";
    $tv_urls = $streamflix->getEmbedUrls('tv_show', 1399, 1, 1);
    echo "<p>Found " . count($tv_urls) . " servers for TV shows:</p>";
    echo "<ol>";
    foreach ($tv_urls as $url) {
        echo "<li><strong>{$url['name']}</strong> (Priority: {$url['priority']})</li>";
    }
    echo "</ol>";
    
    echo "<h3>✅ Fix Complete</h3>";
    echo "<p><strong>Summary:</strong></p>";
    echo "<ul>";
    echo "<li>Database servers are now properly configured</li>";
    echo "<li>No more fallback to config file servers</li>";
    echo "<li>Admin panel server activation/deactivation will now work correctly</li>";
    echo "<li>Player will only show active servers from database</li>";
    echo "</ul>";
    
    echo "<p><a href='admin/servers.php' style='background: #e50914; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Admin Server Management</a></p>";
    echo "<p><a href='player.php?id=550&type=movie' style='background: #333; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Movie Player</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>
