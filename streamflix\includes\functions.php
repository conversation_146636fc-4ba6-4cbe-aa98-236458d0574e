<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ' . dirname($_SERVER['PHP_SELF']) . '/install.php');
    exit('Please run the installer first.');
}

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/logger.php';

class StreamFlix {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->connect();
    }
    
    // TMDB API Functions
    public function fetchFromTMDB($endpoint, $params = []) {
        $url = TMDB_BASE_URL . $endpoint . '?api_key=' . TMDB_API_KEY;
        
        if (!empty($params)) {
            $url .= '&' . http_build_query($params);
        }
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            return json_decode($response, true);
        }
        
        return false;
    }

    // Search TMDB
    public function searchTMDB($query, $type = 'multi', $year = '') {
        $params = [
            'query' => $query,
            'include_adult' => 'true'
        ];

        if (!empty($year)) {
            if ($type === 'movie') {
                $params['year'] = $year;
            } elseif ($type === 'tv') {
                $params['first_air_date_year'] = $year;
            }
        }

        $endpoint = '/search/' . $type;
        return $this->fetchFromTMDB($endpoint, $params);
    }

    // Import Movie from TMDB
    public function importMovie($tmdb_id) {
        $movie_data = $this->fetchFromTMDB("/movie/{$tmdb_id}");
        
        if (!$movie_data) {
            return false;
        }
        
        try {
            $stmt = $this->db->prepare("
                INSERT INTO movies (
                    tmdb_id, title, original_title, overview, poster_path, backdrop_path,
                    release_date, runtime, vote_average, vote_count, popularity, adult,
                    original_language, status, tagline, budget, revenue, imdb_id, homepage
                ) VALUES (
                    :tmdb_id, :title, :original_title, :overview, :poster_path, :backdrop_path,
                    :release_date, :runtime, :vote_average, :vote_count, :popularity, :adult,
                    :original_language, :status, :tagline, :budget, :revenue, :imdb_id, :homepage
                ) ON DUPLICATE KEY UPDATE
                    title = VALUES(title),
                    overview = VALUES(overview),
                    poster_path = VALUES(poster_path),
                    backdrop_path = VALUES(backdrop_path),
                    updated_at = CURRENT_TIMESTAMP
            ");
            
            $stmt->execute([
                ':tmdb_id' => $movie_data['id'],
                ':title' => $movie_data['title'],
                ':original_title' => $movie_data['original_title'],
                ':overview' => $movie_data['overview'],
                ':poster_path' => $movie_data['poster_path'],
                ':backdrop_path' => $movie_data['backdrop_path'],
                ':release_date' => $movie_data['release_date'] ?: null,
                ':runtime' => $movie_data['runtime'],
                ':vote_average' => $movie_data['vote_average'],
                ':vote_count' => $movie_data['vote_count'],
                ':popularity' => $movie_data['popularity'],
                ':adult' => $movie_data['adult'] ? 1 : 0,
                ':original_language' => $movie_data['original_language'],
                ':status' => $movie_data['status'],
                ':tagline' => $movie_data['tagline'],
                ':budget' => $movie_data['budget'],
                ':revenue' => $movie_data['revenue'],
                ':imdb_id' => $movie_data['imdb_id'],
                ':homepage' => $movie_data['homepage']
            ]);
            
            $movie_id = $this->db->lastInsertId() ?: $this->getMovieIdByTmdbId($tmdb_id);
            
            // Import genres
            if (isset($movie_data['genres']) && is_array($movie_data['genres'])) {
                $this->importGenres($movie_data['genres']);
                $this->linkMovieGenres($movie_id, $movie_data['genres']);
            }
            
            return $movie_id;
            
        } catch (PDOException $e) {
            error_log("Error importing movie: " . $e->getMessage());
            return false;
        }
    }
    
    // Import TV Show from TMDB
    public function importTVShow($tmdb_id) {
        $tv_data = $this->fetchFromTMDB("/tv/{$tmdb_id}");
        
        if (!$tv_data) {
            return false;
        }
        
        try {
            $stmt = $this->db->prepare("
                INSERT INTO tv_shows (
                    tmdb_id, name, original_name, overview, poster_path, backdrop_path,
                    first_air_date, last_air_date, vote_average, vote_count, popularity,
                    adult, original_language, status, tagline, homepage, number_of_episodes, number_of_seasons
                ) VALUES (
                    :tmdb_id, :name, :original_name, :overview, :poster_path, :backdrop_path,
                    :first_air_date, :last_air_date, :vote_average, :vote_count, :popularity,
                    :adult, :original_language, :status, :tagline, :homepage, :number_of_episodes, :number_of_seasons
                ) ON DUPLICATE KEY UPDATE
                    name = VALUES(name),
                    overview = VALUES(overview),
                    poster_path = VALUES(poster_path),
                    backdrop_path = VALUES(backdrop_path),
                    updated_at = CURRENT_TIMESTAMP
            ");
            
            $stmt->execute([
                ':tmdb_id' => $tv_data['id'],
                ':name' => $tv_data['name'],
                ':original_name' => $tv_data['original_name'],
                ':overview' => $tv_data['overview'],
                ':poster_path' => $tv_data['poster_path'],
                ':backdrop_path' => $tv_data['backdrop_path'],
                ':first_air_date' => $tv_data['first_air_date'] ?: null,
                ':last_air_date' => $tv_data['last_air_date'] ?: null,
                ':vote_average' => $tv_data['vote_average'],
                ':vote_count' => $tv_data['vote_count'],
                ':popularity' => $tv_data['popularity'],
                ':adult' => $tv_data['adult'] ? 1 : 0,
                ':original_language' => $tv_data['original_language'],
                ':status' => $tv_data['status'],
                ':tagline' => $tv_data['tagline'],
                ':homepage' => $tv_data['homepage'],
                ':number_of_episodes' => $tv_data['number_of_episodes'],
                ':number_of_seasons' => $tv_data['number_of_seasons']
            ]);
            
            $tv_show_id = $this->db->lastInsertId() ?: $this->getTVShowIdByTmdbId($tmdb_id);
            
            // Import genres
            if (isset($tv_data['genres']) && is_array($tv_data['genres'])) {
                $this->importGenres($tv_data['genres']);
                $this->linkTVShowGenres($tv_show_id, $tv_data['genres']);
            }
            
            // Import seasons and episodes
            if (isset($tv_data['seasons']) && is_array($tv_data['seasons'])) {
                foreach ($tv_data['seasons'] as $season_data) {
                    $this->importSeason($tv_show_id, $season_data['season_number']);
                }
            }
            
            return $tv_show_id;
            
        } catch (PDOException $e) {
            error_log("Error importing TV show: " . $e->getMessage());
            return false;
        }
    }
    
    // Import Season and Episodes
    public function importSeason($tv_show_id, $season_number) {
        $tv_show = $this->getTVShowById($tv_show_id);
        if (!$tv_show) return false;
        
        $season_data = $this->fetchFromTMDB("/tv/{$tv_show['tmdb_id']}/season/{$season_number}");
        
        if (!$season_data) return false;
        
        try {
            // Insert season
            $stmt = $this->db->prepare("
                INSERT INTO seasons (tv_show_id, tmdb_id, season_number, name, overview, poster_path, air_date, episode_count)
                VALUES (:tv_show_id, :tmdb_id, :season_number, :name, :overview, :poster_path, :air_date, :episode_count)
                ON DUPLICATE KEY UPDATE
                    name = VALUES(name),
                    overview = VALUES(overview),
                    poster_path = VALUES(poster_path)
            ");
            
            $stmt->execute([
                ':tv_show_id' => $tv_show_id,
                ':tmdb_id' => $season_data['id'],
                ':season_number' => $season_number,
                ':name' => $season_data['name'],
                ':overview' => $season_data['overview'],
                ':poster_path' => $season_data['poster_path'],
                ':air_date' => $season_data['air_date'] ?: null,
                ':episode_count' => count($season_data['episodes'])
            ]);
            
            $season_id = $this->db->lastInsertId() ?: $this->getSeasonId($tv_show_id, $season_number);
            
            // Insert episodes
            if (isset($season_data['episodes']) && is_array($season_data['episodes'])) {
                foreach ($season_data['episodes'] as $episode_data) {
                    $this->importEpisode($season_id, $episode_data);
                }
            }
            
            return $season_id;
            
        } catch (PDOException $e) {
            error_log("Error importing season: " . $e->getMessage());
            return false;
        }
    }
    
    // Import Episode
    public function importEpisode($season_id, $episode_data) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO episodes (season_id, tmdb_id, episode_number, name, overview, still_path, air_date, runtime, vote_average, vote_count)
                VALUES (:season_id, :tmdb_id, :episode_number, :name, :overview, :still_path, :air_date, :runtime, :vote_average, :vote_count)
                ON DUPLICATE KEY UPDATE
                    name = VALUES(name),
                    overview = VALUES(overview),
                    still_path = VALUES(still_path)
            ");
            
            $stmt->execute([
                ':season_id' => $season_id,
                ':tmdb_id' => $episode_data['id'],
                ':episode_number' => $episode_data['episode_number'],
                ':name' => $episode_data['name'],
                ':overview' => $episode_data['overview'],
                ':still_path' => $episode_data['still_path'],
                ':air_date' => $episode_data['air_date'] ?: null,
                ':runtime' => $episode_data['runtime'],
                ':vote_average' => $episode_data['vote_average'],
                ':vote_count' => $episode_data['vote_count']
            ]);
            
            return true;
            
        } catch (PDOException $e) {
            error_log("Error importing episode: " . $e->getMessage());
            return false;
        }
    }
    
    // Helper Functions
    public function getMovieIdByTmdbId($tmdb_id) {
        $stmt = $this->db->prepare("SELECT id FROM movies WHERE tmdb_id = :tmdb_id");
        $stmt->execute([':tmdb_id' => $tmdb_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['id'] : null;
    }

    public function getTVShowIdByTmdbId($tmdb_id) {
        $stmt = $this->db->prepare("SELECT id FROM tv_shows WHERE tmdb_id = :tmdb_id");
        $stmt->execute([':tmdb_id' => $tmdb_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['id'] : null;
    }

    public function getTVShowById($id) {
        $stmt = $this->db->prepare("SELECT * FROM tv_shows WHERE id = :id");
        $stmt->execute([':id' => $id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getSeasonId($tv_show_id, $season_number) {
        $stmt = $this->db->prepare("SELECT id FROM seasons WHERE tv_show_id = :tv_show_id AND season_number = :season_number");
        $stmt->execute([':tv_show_id' => $tv_show_id, ':season_number' => $season_number]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['id'] : null;
    }

    // Import and link genres
    public function importGenres($genres) {
        foreach ($genres as $genre) {
            try {
                $stmt = $this->db->prepare("
                    INSERT INTO genres (tmdb_id, name)
                    VALUES (:tmdb_id, :name)
                    ON DUPLICATE KEY UPDATE name = VALUES(name)
                ");
                $stmt->execute([
                    ':tmdb_id' => $genre['id'],
                    ':name' => $genre['name']
                ]);
            } catch (PDOException $e) {
                error_log("Error importing genre: " . $e->getMessage());
            }
        }
    }

    public function linkMovieGenres($movie_id, $genres) {
        // Clear existing genres
        $stmt = $this->db->prepare("DELETE FROM movie_genres WHERE movie_id = :movie_id");
        $stmt->execute([':movie_id' => $movie_id]);

        // Add new genres
        foreach ($genres as $genre) {
            try {
                $stmt = $this->db->prepare("
                    INSERT INTO movie_genres (movie_id, genre_id)
                    SELECT :movie_id, id FROM genres WHERE tmdb_id = :tmdb_id
                ");
                $stmt->execute([
                    ':movie_id' => $movie_id,
                    ':tmdb_id' => $genre['id']
                ]);
            } catch (PDOException $e) {
                error_log("Error linking movie genre: " . $e->getMessage());
            }
        }
    }

    public function linkTVShowGenres($tv_show_id, $genres) {
        // Clear existing genres
        $stmt = $this->db->prepare("DELETE FROM tv_show_genres WHERE tv_show_id = :tv_show_id");
        $stmt->execute([':tv_show_id' => $tv_show_id]);

        // Add new genres
        foreach ($genres as $genre) {
            try {
                $stmt = $this->db->prepare("
                    INSERT INTO tv_show_genres (tv_show_id, genre_id)
                    SELECT :tv_show_id, id FROM genres WHERE tmdb_id = :tmdb_id
                ");
                $stmt->execute([
                    ':tv_show_id' => $tv_show_id,
                    ':tmdb_id' => $genre['id']
                ]);
            } catch (PDOException $e) {
                error_log("Error linking TV show genre: " . $e->getMessage());
            }
        }
    }


    
    // Generate Embed URLs
    public function getEmbedUrls($content_type, $tmdb_id, $season = null, $episode = null, $is_hentai = false, $is_anime = false) {
        $urls = [];

        try {
            $db = new Database();
            $conn = $db->connect();

            // Enhanced content type detection
            if (!$is_hentai && !$is_anime) {
                if ($content_type === 'tv_show') {
                    // Get content info with content_type
                    $stmt = $conn->prepare("
                        SELECT content_type, name, overview FROM tv_shows WHERE tmdb_id = ?
                    ");
                    $stmt->execute([$tmdb_id]);
                    $content_info = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($content_info) {
                        // Check content_type first (most reliable)
                        if (isset($content_info['content_type'])) {
                            $is_hentai = ($content_info['content_type'] === 'hentai');
                            $is_anime = ($content_info['content_type'] === 'anime');
                        }

                        // Fallback: Check by genre if content_type is not conclusive
                        if (!$is_hentai && !$is_anime) {
                            // Check for hentai by genre
                            $stmt = $conn->prepare("
                                SELECT COUNT(*) FROM tv_show_genres tsg
                                JOIN genres g ON tsg.genre_id = g.id
                                WHERE tsg.tv_show_id = (SELECT id FROM tv_shows WHERE tmdb_id = ?)
                                AND (g.name LIKE '%hentai%' OR g.name LIKE '%adult%' OR g.name LIKE '%18+%')
                            ");
                            $stmt->execute([$tmdb_id]);
                            $is_hentai = $stmt->fetchColumn() > 0;

                            // Check for anime by genre if not hentai
                            if (!$is_hentai) {
                                $stmt = $conn->prepare("
                                    SELECT COUNT(*) FROM tv_show_genres tsg
                                    JOIN genres g ON tsg.genre_id = g.id
                                    WHERE tsg.tv_show_id = (SELECT id FROM tv_shows WHERE tmdb_id = ?)
                                    AND (g.name LIKE '%anime%' OR g.name = 'Animation')
                                ");
                                $stmt->execute([$tmdb_id]);
                                $is_anime = $stmt->fetchColumn() > 0;
                            }
                        }

                        // Additional check: by title/name
                        if (!$is_hentai && !$is_anime && $content_info) {
                            $title_lower = strtolower($content_info['name'] ?? '');
                            $overview_lower = strtolower($content_info['overview'] ?? '');

                            if (strpos($title_lower, 'hentai') !== false || strpos($overview_lower, 'hentai') !== false) {
                                $is_hentai = true;
                            } elseif (strpos($title_lower, 'anime') !== false || strpos($overview_lower, 'anime') !== false) {
                                $is_anime = true;
                            }
                        }
                    }

                } elseif ($content_type === 'movie') {
                    // Check for anime movies
                    $stmt = $conn->prepare("
                        SELECT content_type FROM movies WHERE tmdb_id = ?
                    ");
                    $stmt->execute([$tmdb_id]);
                    $content_type_db = $stmt->fetchColumn();
                    $is_anime = ($content_type_db === 'anime');

                    // Fallback: Check by genre if content_type not set
                    if (!$is_anime) {
                        $stmt = $conn->prepare("
                            SELECT COUNT(*) FROM movie_genres mg
                            JOIN genres g ON mg.genre_id = g.id
                            WHERE mg.movie_id = (SELECT id FROM movies WHERE tmdb_id = ?)
                            AND (g.name LIKE '%anime%' OR g.name = 'Animation')
                        ");
                        $stmt->execute([$tmdb_id]);
                        $is_anime = $stmt->fetchColumn() > 0;
                    }
                }
            }

            // Get servers based on content type
            if ($is_hentai) {
                // Get hentai-specific servers from embed_servers with hentai_url
                $stmt = $conn->prepare("SELECT *, hentai_url as url FROM embed_servers WHERE hentai_url IS NOT NULL AND hentai_url != '' AND is_active = 1 ORDER BY priority ASC");
                $stmt->execute();
                $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } elseif ($is_anime) {
                // Get anime-specific servers from embed_servers with anime_url
                $stmt = $conn->prepare("SELECT *, anime_url as url FROM embed_servers WHERE anime_url IS NOT NULL AND anime_url != '' AND is_active = 1 ORDER BY priority ASC");
                $stmt->execute();
                $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } else {
                // Get movie/TV embed servers - ONLY from database, no config fallback
                try {
                    // Check if content-specific columns exist
                    $stmt = $conn->query("SHOW COLUMNS FROM embed_servers LIKE 'is_active_movies'");
                    $has_movie_column = $stmt->rowCount() > 0;

                    $stmt = $conn->query("SHOW COLUMNS FROM embed_servers LIKE 'is_active_tv'");
                    $has_tv_column = $stmt->rowCount() > 0;

                    if ($has_movie_column && $has_tv_column) {
                        // Use content-specific active columns
                        $content_active_column = ($content_type === 'movie') ? 'is_active_movies' : 'is_active_tv';
                        $stmt = $conn->prepare("SELECT * FROM embed_servers WHERE is_active = 1 AND {$content_active_column} = 1 ORDER BY priority ASC");
                        $stmt->execute();
                        $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    } else {
                        // Use general is_active only
                        $stmt = $conn->prepare("SELECT * FROM embed_servers WHERE is_active = 1 ORDER BY priority ASC");
                        $stmt->execute();
                        $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    }
                } catch (Exception $e) {
                    // If database query fails, return empty array (no config fallback)
                    error_log("Server query error: " . $e->getMessage());
                    $servers = [];
                }
            }

        } catch (Exception $e) {
            // No fallback to config servers - return empty array
            error_log("getEmbedUrls error: " . $e->getMessage());
            $servers = [];
        }

        foreach ($servers as $server) {
            $url = '';

            if ($is_hentai) {
                // For hentai content, use hentai servers
                if (isset($server['url'])) {
                    if (strpos($server['url'], 'letsembed.cc') !== false) {
                        // LetsEmbed hentai format: https://letsembed.cc/embed/hentai/?id={tmdb_id}/{season}/{episode}
                        if ($content_type === 'movie') {
                            $url = $server['url'] . "?id={$tmdb_id}";
                        } else {
                            $url = $server['url'] . "?id={$tmdb_id}/{$season}/{$episode}";
                        }
                    } else {
                        // Other hentai servers
                        if ($content_type === 'movie') {
                            $url = $server['url'] . "/movie/{$tmdb_id}";
                        } else {
                            $url = $server['url'] . "/tv/{$tmdb_id}/{$season}/{$episode}";
                        }
                    }
                }
            } elseif ($is_anime) {
                // For anime content, use anime servers
                if (isset($server['url'])) {
                    if (strpos($server['url'], 'letsembed.cc') !== false) {
                        // LetsEmbed anime format: https://letsembed.cc/embed/anime/?id={tmdb_id}/{season}/{episode}
                        if ($content_type === 'movie') {
                            $url = $server['url'] . "?id={$tmdb_id}";
                        } else {
                            $url = $server['url'] . "?id={$tmdb_id}/{$season}/{$episode}";
                        }
                    } else {
                        // Other anime servers
                        if ($content_type === 'movie') {
                            $url = $server['url'] . "/movie/{$tmdb_id}";
                        } else {
                            $url = $server['url'] . "/tv/{$tmdb_id}/{$season}/{$episode}";
                        }
                    }
                }
            } else {
                // For movie/TV content, use embed servers
                if ($content_type === 'movie' && isset($server['movie_url'])) {
                    $url = str_replace('{id}', $tmdb_id, $server['movie_url']);
                } elseif ($content_type === 'tv_show' && isset($server['tv_url'])) {
                    $url = str_replace(['{id}', '{season}', '{episode}'], [$tmdb_id, $season, $episode], $server['tv_url']);
                }
            }

            if (!empty($url)) {
                $urls[] = [
                    'name' => $server['name'],
                    'url' => $url,
                    'priority' => $server['priority'] ?? 1
                ];
            }
        }

        // Sort by priority
        usort($urls, function($a, $b) {
            return $a['priority'] - $b['priority'];
        });

        return $urls;
    }

    // Mark content as trending
    public function markAsTrending($content_id, $content_type) {
        try {
            $db = new Database();
            $conn = $db->connect();

            if ($content_type === 'movie') {
                $stmt = $conn->prepare("UPDATE movies SET is_trending = 1 WHERE id = ?");
            } else {
                $stmt = $conn->prepare("UPDATE tv_shows SET is_trending = 1 WHERE id = ?");
            }
            $stmt->execute([$content_id]);
            return true;
        } catch (Exception $e) {
            error_log("Error marking as trending: " . $e->getMessage());
            return false;
        }
    }

    // Mark content as featured
    public function markAsFeatured($content_id, $content_type) {
        try {
            $db = new Database();
            $conn = $db->connect();

            if ($content_type === 'movie') {
                $stmt = $conn->prepare("UPDATE movies SET is_featured = 1 WHERE id = ?");
            } else {
                $stmt = $conn->prepare("UPDATE tv_shows SET is_featured = 1 WHERE id = ?");
            }
            $stmt->execute([$content_id]);
            return true;
        } catch (Exception $e) {
            error_log("Error marking as featured: " . $e->getMessage());
            return false;
        }
    }
}

// Utility Functions
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function isAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

function redirectTo($url) {
    header("Location: $url");
    exit();
}

function formatRuntime($minutes) {
    if ($minutes < 60) {
        return $minutes . 'm';
    }
    $hours = floor($minutes / 60);
    $mins = $minutes % 60;
    return $hours . 'h ' . $mins . 'm';
}

function getImageUrl($path, $size = 'w500') {
    if (empty($path)) {
        return 'assets/images/no-image.svg';
    }
    return TMDB_IMAGE_BASE . $size . $path;
}
?>
