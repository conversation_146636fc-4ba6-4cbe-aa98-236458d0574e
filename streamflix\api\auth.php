<?php
/**
 * Authentication API Endpoints
 * /api/auth.php
 */

require_once 'config.php';

$method = getRequestMethod();
$data = getRequestData();
$path = $_GET['action'] ?? '';

logAPIRequest("auth/{$path}", $method);

try {
    $db = new Database();
    $conn = $db->connect();
    
    switch ($path) {
        case 'login':
            handleLogin($conn, $data);
            break;
            
        case 'register':
            handleRegister($conn, $data);
            break;
            
        case 'refresh':
            handleRefreshToken($conn);
            break;
            
        case 'logout':
            handleLogout($conn);
            break;
            
        case 'profile':
            handleProfile($conn);
            break;
            
        case 'update-profile':
            handleUpdateProfile($conn, $data);
            break;
            
        case 'change-password':
            handleChangePassword($conn, $data);
            break;
            
        default:
            APIResponse::notFound('Endpoint not found');
    }
    
} catch (Exception $e) {
    APIResponse::serverError('Database connection failed');
}

/**
 * Handle user login
 */
function handleLogin($conn, $data) {
    if (getRequestMethod() !== 'POST') {
        APIResponse::error('Method not allowed', 405);
    }
    
    validateRequired($data, ['username', 'password']);
    
    $username = sanitizeApiInput($data['username']);
    $password = $data['password'];
    
    // Find user
    $stmt = $conn->prepare("SELECT * FROM users WHERE (username = ? OR email = ?) AND is_active = 1");
    $stmt->execute([$username, $username]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user || !password_verify($password, $user['password'])) {
        logSecurity('Failed Login', "Failed login attempt for: {$username}");
        APIResponse::error('Invalid credentials', 401);
    }
    
    // Generate JWT token
    $payload = [
        'user_id' => $user['id'],
        'username' => $user['username'],
        'role' => $user['role'],
        'iat' => time(),
        'exp' => time() + JWT_EXPIRY
    ];
    
    $token = JWT::encode($payload);
    
    // Log successful login
    logLogin($user['id'], $user['username']);
    
    APIResponse::success([
        'token' => $token,
        'expires_in' => JWT_EXPIRY,
        'user' => formatUserForAPI($user)
    ], 'Login successful');
}

/**
 * Handle user registration
 */
function handleRegister($conn, $data) {
    if (getRequestMethod() !== 'POST') {
        APIResponse::error('Method not allowed', 405);
    }
    
    validateRequired($data, ['username', 'email', 'password']);
    
    $username = sanitizeApiInput($data['username']);
    $email = sanitizeApiInput($data['email']);
    $password = $data['password'];
    
    // Validate input
    if (strlen($username) < 3) {
        APIResponse::error('Username must be at least 3 characters');
    }
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        APIResponse::error('Invalid email format');
    }
    
    if (strlen($password) < 6) {
        APIResponse::error('Password must be at least 6 characters');
    }
    
    // Check if user exists
    $stmt = $conn->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
    $stmt->execute([$username, $email]);
    
    if ($stmt->rowCount() > 0) {
        APIResponse::error('Username or email already exists');
    }
    
    // Create user
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    $stmt = $conn->prepare("
        INSERT INTO users (username, email, password, role, is_active, created_at) 
        VALUES (?, ?, ?, 'user', 1, NOW())
    ");
    
    if ($stmt->execute([$username, $email, $hashedPassword])) {
        $userId = $conn->lastInsertId();
        
        // Get created user
        $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Generate JWT token
        $payload = [
            'user_id' => $user['id'],
            'username' => $user['username'],
            'role' => $user['role'],
            'iat' => time(),
            'exp' => time() + JWT_EXPIRY
        ];
        
        $token = JWT::encode($payload);
        
        // Log registration
        logUserManagement('User Registration', "New user registered: {$username}", $userId);
        
        APIResponse::success([
            'token' => $token,
            'expires_in' => JWT_EXPIRY,
            'user' => formatUserForAPI($user)
        ], 'Registration successful', 201);
        
    } else {
        APIResponse::serverError('Failed to create user');
    }
}

/**
 * Handle token refresh
 */
function handleRefreshToken($conn) {
    if (getRequestMethod() !== 'POST') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $user = APIAuth::authenticate();
    
    // Generate new token
    $payload = [
        'user_id' => $user['id'],
        'username' => $user['username'],
        'role' => $user['role'],
        'iat' => time(),
        'exp' => time() + JWT_EXPIRY
    ];
    
    $token = JWT::encode($payload);
    
    APIResponse::success([
        'token' => $token,
        'expires_in' => JWT_EXPIRY,
        'user' => formatUserForAPI($user)
    ], 'Token refreshed');
}

/**
 * Handle logout
 */
function handleLogout($conn) {
    if (getRequestMethod() !== 'POST') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $user = APIAuth::authenticate();
    
    // Log logout
    logLogout($user['id'], $user['username']);
    
    APIResponse::success(null, 'Logout successful');
}

/**
 * Handle get profile
 */
function handleProfile($conn) {
    if (getRequestMethod() !== 'GET') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $user = APIAuth::authenticate();
    
    // Get additional profile data
    $stmt = $conn->prepare("
        SELECT 
            u.*,
            COUNT(w.id) as watchlist_count
        FROM users u
        LEFT JOIN watchlist w ON u.id = w.user_id
        WHERE u.id = ?
        GROUP BY u.id
    ");
    $stmt->execute([$user['id']]);
    $profile = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $profileData = formatUserForAPI($profile);
    $profileData['watchlist_count'] = (int)$profile['watchlist_count'];
    
    APIResponse::success($profileData, 'Profile retrieved');
}

/**
 * Handle update profile
 */
function handleUpdateProfile($conn, $data) {
    if (getRequestMethod() !== 'PUT') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $user = APIAuth::authenticate();
    
    $allowedFields = ['username', 'email'];
    $updateFields = [];
    $updateValues = [];
    
    foreach ($allowedFields as $field) {
        if (isset($data[$field]) && !empty($data[$field])) {
            $value = sanitizeApiInput($data[$field]);
            
            // Validate email
            if ($field === 'email' && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                APIResponse::error('Invalid email format');
            }
            
            // Check if username/email already exists for other users
            if (in_array($field, ['username', 'email'])) {
                $stmt = $conn->prepare("SELECT id FROM users WHERE {$field} = ? AND id != ?");
                $stmt->execute([$value, $user['id']]);
                
                if ($stmt->rowCount() > 0) {
                    APIResponse::error(ucfirst($field) . ' already exists');
                }
            }
            
            $updateFields[] = "{$field} = ?";
            $updateValues[] = $value;
        }
    }
    
    if (empty($updateFields)) {
        APIResponse::error('No valid fields to update');
    }
    
    $updateValues[] = $user['id'];
    
    $stmt = $conn->prepare("UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?");
    
    if ($stmt->execute($updateValues)) {
        // Get updated user
        $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$user['id']]);
        $updatedUser = $stmt->fetch(PDO::FETCH_ASSOC);
        
        logUserManagement('Profile Update', "User updated profile: {$user['username']}", $user['id']);
        
        APIResponse::success(formatUserForAPI($updatedUser), 'Profile updated');
    } else {
        APIResponse::serverError('Failed to update profile');
    }
}

/**
 * Handle change password
 */
function handleChangePassword($conn, $data) {
    if (getRequestMethod() !== 'PUT') {
        APIResponse::error('Method not allowed', 405);
    }
    
    $user = APIAuth::authenticate();
    
    validateRequired($data, ['current_password', 'new_password']);
    
    $currentPassword = $data['current_password'];
    $newPassword = $data['new_password'];
    
    // Verify current password
    if (!password_verify($currentPassword, $user['password'])) {
        APIResponse::error('Current password is incorrect', 401);
    }
    
    // Validate new password
    if (strlen($newPassword) < 6) {
        APIResponse::error('New password must be at least 6 characters');
    }
    
    // Update password
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    $stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
    
    if ($stmt->execute([$hashedPassword, $user['id']])) {
        logSecurity('Password Change', "User changed password: {$user['username']}", $user['id']);
        APIResponse::success(null, 'Password changed successfully');
    } else {
        APIResponse::serverError('Failed to change password');
    }
}
?>
