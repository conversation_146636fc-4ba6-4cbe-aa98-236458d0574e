#!/bin/bash

echo "🚀 Building StreamFlix Android App..."

# Clean previous builds
echo "🧹 Cleaning previous builds..."
./gradlew clean

# Build the app with minimal checks
echo "🔨 Building APK..."
./gradlew assembleDebug --no-daemon --offline --build-cache

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo "📱 APK location: app/build/outputs/apk/debug/app-debug.apk"
    ls -la app/build/outputs/apk/debug/
else
    echo "❌ Build failed. Trying alternative build..."
    # Try with fewer optimizations
    ./gradlew assembleDebug --no-daemon --no-build-cache --no-parallel
fi

echo "🎉 Build process completed!"
