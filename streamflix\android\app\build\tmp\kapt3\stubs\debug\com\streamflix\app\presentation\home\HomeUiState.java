package com.streamflix.app.presentation.home;

import androidx.lifecycle.ViewModel;
import com.streamflix.app.data.model.*;
import com.streamflix.app.data.repository.StreamFlixRepository;
import com.streamflix.app.utils.Resource;
import dagger.hilt.android.lifecycle.HiltViewModel;
import kotlinx.coroutines.flow.*;
import javax.inject.Inject;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0017\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u00a5\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u0012\u0016\b\u0002\u0010\u0007\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b\u0018\u00010\u0005\u0012\u0016\b\u0002\u0010\n\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\b\u0018\u00010\u0005\u0012\u0016\b\u0002\u0010\f\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b\u0018\u00010\u0005\u0012\u0016\b\u0002\u0010\r\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00010\b\u0018\u00010\u0005\u0012\u0016\b\u0002\u0010\u000e\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\b\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0011\u00a2\u0006\u0002\u0010\u0012J\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\u0011\u0010\u001e\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J\u0017\u0010\u001f\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b\u0018\u00010\u0005H\u00c6\u0003J\u0017\u0010 \u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\b\u0018\u00010\u0005H\u00c6\u0003J\u0017\u0010!\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b\u0018\u00010\u0005H\u00c6\u0003J\u0017\u0010\"\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00010\b\u0018\u00010\u0005H\u00c6\u0003J\u0017\u0010#\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\b\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010$\u001a\u0004\u0018\u00010\u0011H\u00c6\u0003J\u00a9\u0001\u0010%\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00052\u0016\b\u0002\u0010\u0007\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b\u0018\u00010\u00052\u0016\b\u0002\u0010\n\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\b\u0018\u00010\u00052\u0016\b\u0002\u0010\f\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b\u0018\u00010\u00052\u0016\b\u0002\u0010\r\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00010\b\u0018\u00010\u00052\u0016\b\u0002\u0010\u000e\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\b\u0018\u00010\u00052\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0011H\u00c6\u0001J\u0013\u0010&\u001a\u00020\u00032\b\u0010\'\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010(\u001a\u00020)H\u00d6\u0001J\t\u0010*\u001a\u00020\u0011H\u00d6\u0001R\u001f\u0010\u000e\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\b\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0013\u0010\u0010\u001a\u0004\u0018\u00010\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0019\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0014R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0018R\u001f\u0010\f\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0014R\u001f\u0010\n\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\b\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0014R\u001f\u0010\r\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00010\b\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0014R\u001f\u0010\u0007\u001a\u0010\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0014\u00a8\u0006+"}, d2 = {"Lcom/streamflix/app/presentation/home/<USER>", "", "isLoading", "", "featuredContent", "Lcom/streamflix/app/utils/Resource;", "Lcom/streamflix/app/data/model/HomeData;", "trendingMovies", "", "Lcom/streamflix/app/data/model/Movie;", "popularTvShows", "Lcom/streamflix/app/data/model/TvShow;", "latestMovies", "topRated", "continueWatching", "Lcom/streamflix/app/data/model/WatchHistoryItem;", "error", "", "(ZLcom/streamflix/app/utils/Resource;Lcom/streamflix/app/utils/Resource;Lcom/streamflix/app/utils/Resource;Lcom/streamflix/app/utils/Resource;Lcom/streamflix/app/utils/Resource;Lcom/streamflix/app/utils/Resource;Ljava/lang/String;)V", "getContinueWatching", "()Lcom/streamflix/app/utils/Resource;", "getError", "()Ljava/lang/String;", "getFeaturedContent", "()Z", "getLatestMovies", "getPopularTvShows", "getTopRated", "getTrendingMovies", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class HomeUiState {
    private final boolean isLoading = false;
    @org.jetbrains.annotations.Nullable()
    private final com.streamflix.app.utils.Resource<com.streamflix.app.data.model.HomeData> featuredContent = null;
    @org.jetbrains.annotations.Nullable()
    private final com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.Movie>> trendingMovies = null;
    @org.jetbrains.annotations.Nullable()
    private final com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.TvShow>> popularTvShows = null;
    @org.jetbrains.annotations.Nullable()
    private final com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.Movie>> latestMovies = null;
    @org.jetbrains.annotations.Nullable()
    private final com.streamflix.app.utils.Resource<java.util.List<java.lang.Object>> topRated = null;
    @org.jetbrains.annotations.Nullable()
    private final com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.WatchHistoryItem>> continueWatching = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String error = null;
    
    public HomeUiState(boolean isLoading, @org.jetbrains.annotations.Nullable()
    com.streamflix.app.utils.Resource<com.streamflix.app.data.model.HomeData> featuredContent, @org.jetbrains.annotations.Nullable()
    com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.Movie>> trendingMovies, @org.jetbrains.annotations.Nullable()
    com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.TvShow>> popularTvShows, @org.jetbrains.annotations.Nullable()
    com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.Movie>> latestMovies, @org.jetbrains.annotations.Nullable()
    com.streamflix.app.utils.Resource<java.util.List<java.lang.Object>> topRated, @org.jetbrains.annotations.Nullable()
    com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.WatchHistoryItem>> continueWatching, @org.jetbrains.annotations.Nullable()
    java.lang.String error) {
        super();
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.streamflix.app.utils.Resource<com.streamflix.app.data.model.HomeData> getFeaturedContent() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.Movie>> getTrendingMovies() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.TvShow>> getPopularTvShows() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.Movie>> getLatestMovies() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.streamflix.app.utils.Resource<java.util.List<java.lang.Object>> getTopRated() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.WatchHistoryItem>> getContinueWatching() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getError() {
        return null;
    }
    
    public HomeUiState() {
        super();
    }
    
    public final boolean component1() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.streamflix.app.utils.Resource<com.streamflix.app.data.model.HomeData> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.Movie>> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.TvShow>> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.Movie>> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.streamflix.app.utils.Resource<java.util.List<java.lang.Object>> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.WatchHistoryItem>> component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.streamflix.app.presentation.home.HomeUiState copy(boolean isLoading, @org.jetbrains.annotations.Nullable()
    com.streamflix.app.utils.Resource<com.streamflix.app.data.model.HomeData> featuredContent, @org.jetbrains.annotations.Nullable()
    com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.Movie>> trendingMovies, @org.jetbrains.annotations.Nullable()
    com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.TvShow>> popularTvShows, @org.jetbrains.annotations.Nullable()
    com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.Movie>> latestMovies, @org.jetbrains.annotations.Nullable()
    com.streamflix.app.utils.Resource<java.util.List<java.lang.Object>> topRated, @org.jetbrains.annotations.Nullable()
    com.streamflix.app.utils.Resource<java.util.List<com.streamflix.app.data.model.WatchHistoryItem>> continueWatching, @org.jetbrains.annotations.Nullable()
    java.lang.String error) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}