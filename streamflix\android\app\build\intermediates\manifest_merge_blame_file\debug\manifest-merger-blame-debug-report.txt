1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.streamflix.app.debug"
4    android:versionCode="1"
5    android:versionName="1.0.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Internet permission for API calls -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- Storage permissions for downloads -->
16    <uses-permission
16-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:10:5-11:38
17        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
17-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:10:22-78
18        android:maxSdkVersion="28" />
18-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:11:9-35
19    <uses-permission
19-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:12:5-13:38
20        android:name="android.permission.READ_EXTERNAL_STORAGE"
20-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:12:22-77
21        android:maxSdkVersion="32" />
21-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:13:9-35
22
23    <!-- Media permissions for Android 13+ -->
24    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
24-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:16:5-75
24-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:16:22-72
25    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
25-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:17:5-75
25-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:17:22-72
26    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
26-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:18:5-76
26-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:18:22-73
27
28    <!-- Wake lock for video playback -->
29    <uses-permission android:name="android.permission.WAKE_LOCK" />
29-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:21:5-68
29-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:21:22-65
30
31    <!-- Notification permission for Android 13+ -->
32    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
32-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:24:5-77
32-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:24:22-74
33    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
33-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
33-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
34    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
34-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
34-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:22-74
35
36    <permission
36-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
37        android:name="com.streamflix.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
37-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
38        android:protectionLevel="signature" />
38-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
39
40    <uses-permission android:name="com.streamflix.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
40-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
40-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
41
42    <application
42-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:26:5-120:19
43        android:name="com.streamflix.app.StreamFlixApplication"
43-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:27:9-46
44        android:allowBackup="true"
44-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:28:9-35
45        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
45-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e0eb68a5716cdfe313e221efb4d1df6\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
46        android:dataExtractionRules="@xml/data_extraction_rules"
46-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:29:9-65
47        android:debuggable="true"
48        android:extractNativeLibs="false"
49        android:fullBackupContent="@xml/backup_rules"
49-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:30:9-54
50        android:icon="@drawable/ic_streamflix_logo"
50-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:31:9-52
51        android:label="@string/app_name"
51-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:32:9-41
52        android:roundIcon="@drawable/ic_streamflix_logo"
52-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:33:9-57
53        android:supportsRtl="true"
53-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:34:9-35
54        android:theme="@style/Theme.StreamFlix.AppCompat"
54-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:35:9-58
55        android:usesCleartextTraffic="true" >
55-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:36:9-44
56
57        <!-- Splash Screen Activity -->
58        <activity
58-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:40:9-48:20
59            android:name="com.streamflix.app.presentation.splash.SplashActivity"
59-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:41:13-63
60            android:exported="true"
60-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:42:13-36
61            android:theme="@style/Theme.StreamFlix.Splash.Simple" >
61-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:43:13-66
62            <intent-filter>
62-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:44:13-47:29
63                <action android:name="android.intent.action.MAIN" />
63-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:45:17-69
63-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:45:25-66
64
65                <category android:name="android.intent.category.LAUNCHER" />
65-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:46:17-77
65-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:46:27-74
66            </intent-filter>
67        </activity>
68
69        <!-- Main Activity -->
70        <activity
70-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:51:9-56:77
71            android:name="com.streamflix.app.presentation.main.MainActivity"
71-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:52:13-59
72            android:configChanges="orientation|screenSize|keyboardHidden"
72-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:56:13-74
73            android:exported="false"
73-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:53:13-37
74            android:screenOrientation="portrait"
74-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:55:13-49
75            android:theme="@style/Theme.StreamFlix" /> <!-- Deep link handling -->
75-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:54:13-52
76        <activity-alias
76-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:101:9-118:26
77            android:name="com.streamflix.app.DeepLinkActivity"
77-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:102:13-45
78            android:exported="true"
78-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:104:13-36
79            android:targetActivity="com.streamflix.app.presentation.main.MainActivity" >
79-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:103:13-69
80            <intent-filter android:autoVerify="true" >
80-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:105:13-111:29
80-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:105:28-53
81                <action android:name="android.intent.action.VIEW" />
81-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:106:17-69
81-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:106:25-66
82
83                <category android:name="android.intent.category.DEFAULT" />
83-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:107:17-76
83-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:107:27-73
84                <category android:name="android.intent.category.BROWSABLE" />
84-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:108:17-78
84-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:108:27-75
85
86                <data
86-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:109:17-110:53
87                    android:host="streamflix.app"
87-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:110:21-50
88                    android:scheme="https" />
88-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:109:23-45
89            </intent-filter>
90            <intent-filter>
90-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:112:13-117:29
91                <action android:name="android.intent.action.VIEW" />
91-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:106:17-69
91-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:106:25-66
92
93                <category android:name="android.intent.category.DEFAULT" />
93-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:107:17-76
93-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:107:27-73
94                <category android:name="android.intent.category.BROWSABLE" />
94-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:108:17-78
94-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:108:27-75
95
96                <data android:scheme="streamflix" />
96-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:109:17-110:53
96-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:109:23-45
97            </intent-filter>
98        </activity-alias>
99
100        <!-- Video Player Activity -->
101        <activity
101-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:59:9-65:46
102            android:name="com.streamflix.app.presentation.player.VideoPlayerActivity"
102-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:60:13-68
103            android:configChanges="orientation|screenSize|keyboardHidden"
103-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:64:13-74
104            android:exported="false"
104-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:61:13-37
105            android:launchMode="singleTop"
105-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:65:13-43
106            android:screenOrientation="landscape"
106-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:63:13-50
107            android:theme="@style/Theme.StreamFlix.Player" />
107-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:62:13-59
108
109        <!-- Auth Activity -->
110        <activity
110-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:68:9-73:58
111            android:name="com.streamflix.app.presentation.auth.AuthActivity"
111-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:69:13-59
112            android:exported="false"
112-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:70:13-37
113            android:screenOrientation="portrait"
113-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:72:13-49
114            android:theme="@style/Theme.StreamFlix"
114-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:71:13-52
115            android:windowSoftInputMode="adjustResize" />
115-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:73:13-55
116
117        <!-- Settings Activity -->
118        <activity
118-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:76:9-80:52
119            android:name="com.streamflix.app.presentation.settings.SettingsActivity"
119-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:77:13-67
120            android:exported="false"
120-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:78:13-37
121            android:screenOrientation="portrait"
121-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:80:13-49
122            android:theme="@style/Theme.StreamFlix" />
122-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:79:13-52
123
124        <!-- Download Service -->
125        <service
125-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:83:9-87:56
126            android:name="com.streamflix.app.data.service.DownloadService"
126-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:84:13-57
127            android:enabled="true"
127-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:85:13-35
128            android:exported="false"
128-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:86:13-37
129            android:foregroundServiceType="dataSync" />
129-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:87:13-53
130
131        <!-- File Provider for sharing -->
132        <provider
133            android:name="androidx.core.content.FileProvider"
133-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:91:13-62
134            android:authorities="com.streamflix.app.debug.fileprovider"
134-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:92:13-64
135            android:exported="false"
135-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:93:13-37
136            android:grantUriPermissions="true" >
136-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:94:13-47
137            <meta-data
137-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:95:13-97:54
138                android:name="android.support.FILE_PROVIDER_PATHS"
138-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:96:17-67
139                android:resource="@xml/file_paths" />
139-->D:\xampp\htdocs\streamflix\android\app\src\main\AndroidManifest.xml:97:17-51
140        </provider>
141        <provider
141-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
142            android:name="androidx.startup.InitializationProvider"
142-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
143            android:authorities="com.streamflix.app.debug.androidx-startup"
143-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
144            android:exported="false" >
144-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
145            <meta-data
145-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
146                android:name="androidx.work.WorkManagerInitializer"
146-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
147                android:value="androidx.startup" />
147-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
148            <meta-data
148-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c64c7ec30f2123dde7c90104a4a3f41\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
149                android:name="androidx.emoji2.text.EmojiCompatInitializer"
149-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c64c7ec30f2123dde7c90104a4a3f41\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
150                android:value="androidx.startup" />
150-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c64c7ec30f2123dde7c90104a4a3f41\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
151            <meta-data
151-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbb704dc1ff4ebb18b8ea1582f368b0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
152                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
152-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbb704dc1ff4ebb18b8ea1582f368b0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
153                android:value="androidx.startup" />
153-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbb704dc1ff4ebb18b8ea1582f368b0\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
154            <meta-data
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
155                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
156                android:value="androidx.startup" />
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
157        </provider>
158
159        <service
159-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
160            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
160-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
161            android:directBootAware="false"
161-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
162            android:enabled="@bool/enable_system_alarm_service_default"
162-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
163            android:exported="false" />
163-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
164        <service
164-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
165            android:name="androidx.work.impl.background.systemjob.SystemJobService"
165-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
166            android:directBootAware="false"
166-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
167            android:enabled="@bool/enable_system_job_service_default"
167-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
168            android:exported="true"
168-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
169            android:permission="android.permission.BIND_JOB_SERVICE" />
169-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
170        <service
170-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
171            android:name="androidx.work.impl.foreground.SystemForegroundService"
171-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
172            android:directBootAware="false"
172-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
173            android:enabled="@bool/enable_system_foreground_service_default"
173-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
174            android:exported="false" />
174-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
175
176        <receiver
176-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
177            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
177-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
178            android:directBootAware="false"
178-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
179            android:enabled="true"
179-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
180            android:exported="false" />
180-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
181        <receiver
181-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
182            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
182-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
183            android:directBootAware="false"
183-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
184            android:enabled="false"
184-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
185            android:exported="false" >
185-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
186            <intent-filter>
186-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
187                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
187-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
187-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
188                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
188-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
188-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
189            </intent-filter>
190        </receiver>
191        <receiver
191-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
192            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
192-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
193            android:directBootAware="false"
193-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
194            android:enabled="false"
194-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
195            android:exported="false" >
195-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
196            <intent-filter>
196-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
197                <action android:name="android.intent.action.BATTERY_OKAY" />
197-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
197-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
198                <action android:name="android.intent.action.BATTERY_LOW" />
198-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
198-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
199            </intent-filter>
200        </receiver>
201        <receiver
201-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
202            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
202-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
203            android:directBootAware="false"
203-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
204            android:enabled="false"
204-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
205            android:exported="false" >
205-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
206            <intent-filter>
206-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
207                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
207-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
207-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
208                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
208-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
208-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
209            </intent-filter>
210        </receiver>
211        <receiver
211-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
212            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
212-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
213            android:directBootAware="false"
213-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
214            android:enabled="false"
214-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
215            android:exported="false" >
215-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
216            <intent-filter>
216-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
217                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
217-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
217-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
218            </intent-filter>
219        </receiver>
220        <receiver
220-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
221            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
221-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
222            android:directBootAware="false"
222-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
223            android:enabled="false"
223-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
224            android:exported="false" >
224-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
225            <intent-filter>
225-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
226                <action android:name="android.intent.action.BOOT_COMPLETED" />
226-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
226-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
227                <action android:name="android.intent.action.TIME_SET" />
227-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
227-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
228                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
228-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
228-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
229            </intent-filter>
230        </receiver>
231        <receiver
231-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
232            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
232-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
233            android:directBootAware="false"
233-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
234            android:enabled="@bool/enable_system_alarm_service_default"
234-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
235            android:exported="false" >
235-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
236            <intent-filter>
236-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
237                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
237-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
237-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
238            </intent-filter>
239        </receiver>
240        <receiver
240-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
241            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
241-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
242            android:directBootAware="false"
242-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
243            android:enabled="true"
243-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
244            android:exported="true"
244-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
245            android:permission="android.permission.DUMP" >
245-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
246            <intent-filter>
246-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
247                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
247-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
247-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9dc2ee2702f1f73e551894123196f3\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
248            </intent-filter>
249        </receiver>
250
251        <activity
251-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2aebeb42b9af17cd6250188a293ec9ec\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:23:9-25:39
252            android:name="androidx.activity.ComponentActivity"
252-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2aebeb42b9af17cd6250188a293ec9ec\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:24:13-63
253            android:exported="true" />
253-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2aebeb42b9af17cd6250188a293ec9ec\transformed\jetified-ui-test-manifest-1.6.1\AndroidManifest.xml:25:13-36
254        <activity
254-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\76f12246c68803abbe67308b93e3c08b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
255            android:name="androidx.compose.ui.tooling.PreviewActivity"
255-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\76f12246c68803abbe67308b93e3c08b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
256            android:exported="true" />
256-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\76f12246c68803abbe67308b93e3c08b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
257
258        <service
258-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
259            android:name="androidx.room.MultiInstanceInvalidationService"
259-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
260            android:directBootAware="true"
260-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
261            android:exported="false" />
261-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d60c7f6567bbcc8bf451aecd552af68\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
262
263        <receiver
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
264            android:name="androidx.profileinstaller.ProfileInstallReceiver"
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
265            android:directBootAware="false"
265-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
266            android:enabled="true"
266-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
267            android:exported="true"
267-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
268            android:permission="android.permission.DUMP" >
268-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
269            <intent-filter>
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
270                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
270-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
270-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
271            </intent-filter>
272            <intent-filter>
272-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
273                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
273-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
273-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
274            </intent-filter>
275            <intent-filter>
275-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
276                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
276-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
276-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
277            </intent-filter>
278            <intent-filter>
278-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
279                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
279-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
279-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8030e88230b3f9380b89cbf856049bed\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
280            </intent-filter>
281        </receiver>
282    </application>
283
284</manifest>
