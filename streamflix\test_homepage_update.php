<?php
require_once 'config/database.php';

echo "<h2>🏠 Homepage Content Update Test</h2>";
echo "<p>Testing the updated homepage with 15 items per section.</p>";

try {
    $db = new Database();
    $conn = $db->connect();
    
    echo "<h3>✅ Homepage Updates</h3>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>📊 Content Display Changes:</h4>";
    echo "<ul>";
    echo "<li>✅ <strong>Featured Movies:</strong> Now shows 15 items (was 12)</li>";
    echo "<li>✅ <strong>Featured TV Shows:</strong> Now shows 15 items (was 12)</li>";
    echo "<li>✅ <strong>Trending Movies:</strong> Now shows 15 items (was 12)</li>";
    echo "<li>✅ <strong>Trending TV Shows:</strong> Now shows 15 items (was 12)</li>";
    echo "<li>✅ <strong>Grid Layout:</strong> 5 columns on desktop, responsive on mobile</li>";
    echo "<li>✅ <strong>Fallback Queries:</strong> Added for trending content</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>📊 Current Content Statistics</h3>";
    
    // Get content counts
    $stmt = $conn->query("SELECT COUNT(*) as total FROM movies");
    $total_movies = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    $stmt = $conn->query("SELECT COUNT(*) as featured FROM movies WHERE is_featured = 1");
    $featured_movies = $stmt->fetch(PDO::FETCH_ASSOC)['featured'];
    
    $stmt = $conn->query("SELECT COUNT(*) as trending FROM movies WHERE is_trending = 1");
    $trending_movies = $stmt->fetch(PDO::FETCH_ASSOC)['trending'];
    
    $stmt = $conn->query("SELECT COUNT(*) as total FROM tv_shows");
    $total_tv = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    $stmt = $conn->query("SELECT COUNT(*) as featured FROM tv_shows WHERE is_featured = 1");
    $featured_tv = $stmt->fetch(PDO::FETCH_ASSOC)['featured'];
    
    $stmt = $conn->query("SELECT COUNT(*) as trending FROM tv_shows WHERE is_trending = 1");
    $trending_tv = $stmt->fetch(PDO::FETCH_ASSOC)['trending'];
    
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🎬 Movies:</h4>";
    echo "<ul>";
    echo "<li><strong>Total Movies:</strong> {$total_movies}</li>";
    echo "<li><strong>Featured Movies:</strong> {$featured_movies} (showing 15 on homepage)</li>";
    echo "<li><strong>Trending Movies:</strong> {$trending_movies} (showing 15 on homepage)</li>";
    echo "</ul>";
    
    echo "<h4>📺 TV Shows:</h4>";
    echo "<ul>";
    echo "<li><strong>Total TV Shows:</strong> {$total_tv}</li>";
    echo "<li><strong>Featured TV Shows:</strong> {$featured_tv} (showing 15 on homepage)</li>";
    echo "<li><strong>Trending TV Shows:</strong> {$trending_tv} (showing 15 on homepage)</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🎨 Grid Layout Updates</h3>";
    
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>📱 Responsive Grid:</h4>";
    echo "<ul>";
    echo "<li>🖥️ <strong>Desktop (>1200px):</strong> 5 columns (15 items = 3 rows)</li>";
    echo "<li>💻 <strong>Laptop (900-1200px):</strong> 4 columns (15 items = 4 rows)</li>";
    echo "<li>📱 <strong>Tablet (768-900px):</strong> 3 columns (15 items = 5 rows)</li>";
    echo "<li>📱 <strong>Mobile (<768px):</strong> 2 columns (15 items = 8 rows)</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🔧 Technical Improvements</h3>";
    
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>⚙️ Database Query Updates:</h4>";
    echo "<ul>";
    echo "<li>✅ <strong>Featured Content:</strong> LIMIT 12 → LIMIT 15</li>";
    echo "<li>✅ <strong>Trending Content:</strong> LIMIT 12 → LIMIT 15</li>";
    echo "<li>✅ <strong>Fallback Queries:</strong> Added for trending content</li>";
    echo "<li>✅ <strong>Error Handling:</strong> Graceful fallbacks maintained</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🧪 Test Instructions</h3>";
    
    echo "<div style='background: #e2e3e5; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>📋 Testing Steps:</h4>";
    echo "<ol>";
    echo "<li><strong>Visit Homepage:</strong> Check if 15 items are displayed per section</li>";
    echo "<li><strong>Test Responsive:</strong> Resize browser to test different grid layouts</li>";
    echo "<li><strong>Check Mobile:</strong> Test on mobile device for 2-column layout</li>";
    echo "<li><strong>Verify Sections:</strong> Ensure all 4 sections show content</li>";
    echo "<li><strong>Test Fallbacks:</strong> Verify content shows even without featured/trending flags</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h3>🔗 Test Links</h3>";
    
    echo "<div style='background: #cff4fc; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🎯 Test the Updated Homepage:</h4>";
    
    echo "<p><a href='index.php' target='_blank' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin-right: 10px; display: inline-block; margin-bottom: 10px;'>🏠 View Homepage</a></p>";
    
    echo "<p><a href='admin/movies.php' target='_blank' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin-right: 10px; display: inline-block; margin-bottom: 10px;'>🎬 Manage Movies</a></p>";
    
    echo "<p><a href='admin/tv-shows.php' target='_blank' style='background: #17a2b8; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin-right: 10px; display: inline-block; margin-bottom: 10px;'>📺 Manage TV Shows</a></p>";
    
    echo "<p><a href='admin/import.php' target='_blank' style='background: #ffc107; color: #212529; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block; margin-bottom: 10px;'>📥 Import Content</a></p>";
    echo "</div>";
    
    echo "<h3>📊 Content Recommendations</h3>";
    
    echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>💡 To Get Better Results:</h4>";
    echo "<ul>";
    echo "<li>🎯 <strong>Mark as Featured:</strong> Set is_featured = 1 for at least 15 movies and TV shows</li>";
    echo "<li>🔥 <strong>Mark as Trending:</strong> Set is_trending = 1 for at least 15 movies and TV shows</li>";
    echo "<li>📥 <strong>Import More Content:</strong> Use the enhanced import system to add more content</li>";
    echo "<li>⭐ <strong>Update Ratings:</strong> Ensure vote_average is set for better fallback sorting</li>";
    echo "</ul>";
    echo "</div>";
    
    // Check if we have enough content for 15 items per section
    $warnings = [];
    
    if ($featured_movies < 15) {
        $warnings[] = "Featured Movies: Only {$featured_movies} available (need 15)";
    }
    
    if ($featured_tv < 15) {
        $warnings[] = "Featured TV Shows: Only {$featured_tv} available (need 15)";
    }
    
    if ($trending_movies < 15) {
        $warnings[] = "Trending Movies: Only {$trending_movies} available (need 15)";
    }
    
    if ($trending_tv < 15) {
        $warnings[] = "Trending TV Shows: Only {$trending_tv} available (need 15)";
    }
    
    if (!empty($warnings)) {
        echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h4 style='color: #856404;'>⚠️ Content Warnings:</h4>";
        echo "<ul>";
        foreach ($warnings as $warning) {
            echo "<li style='color: #856404;'>{$warning}</li>";
        }
        echo "</ul>";
        echo "<p style='color: #856404;'><strong>Note:</strong> Fallback queries will fill missing slots with latest/highest-rated content.</p>";
        echo "</div>";
    }
    
    echo "<hr>";
    echo "<p><strong>🎉 Homepage Updated Successfully!</strong></p>";
    echo "<p>Each section now displays 15 items instead of 12, with responsive grid layout.</p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4 style='color: #721c24;'>❌ Error</h4>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
