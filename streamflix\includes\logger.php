<?php
/**
 * System Logger Class
 * Logs system activities and user actions
 */

class SystemLogger {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
        $this->createLogTable();
    }
    
    /**
     * Create system_logs table if not exists
     */
    private function createLogTable() {
        try {
            $conn = $this->db->connect();
            $conn->exec("
                CREATE TABLE IF NOT EXISTS system_logs (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    log_type VARCHAR(50),
                    action VARCHAR(100),
                    description TEXT,
                    user_id INT,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_log_type (log_type),
                    INDEX idx_created_at (created_at),
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
                )
            ");
        } catch (Exception $e) {
            // Silently fail if table creation fails
        }
    }
    
    /**
     * Log an activity
     */
    public function log($type, $action, $description = '', $user_id = null) {
        try {
            $conn = $this->db->connect();
            
            // Get current user if not provided
            if ($user_id === null && isset($_SESSION['user_id'])) {
                $user_id = $_SESSION['user_id'];
            }
            
            // Get IP address
            $ip_address = $this->getClientIP();
            
            // Get user agent
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            
            $stmt = $conn->prepare("
                INSERT INTO system_logs (log_type, action, description, user_id, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([$type, $action, $description, $user_id, $ip_address, $user_agent]);
            
        } catch (Exception $e) {
            // Silently fail if logging fails
            error_log("Logger error: " . $e->getMessage());
        }
    }
    
    /**
     * Log user login
     */
    public function logLogin($user_id, $username) {
        $this->log('login', 'User Login', "User '{$username}' logged in", $user_id);
    }
    
    /**
     * Log user logout
     */
    public function logLogout($user_id, $username) {
        $this->log('logout', 'User Logout', "User '{$username}' logged out", $user_id);
    }
    
    /**
     * Log admin action
     */
    public function logAdmin($action, $description, $user_id = null) {
        $this->log('admin', $action, $description, $user_id);
    }
    
    /**
     * Log content action
     */
    public function logContent($action, $description, $user_id = null) {
        $this->log('content', $action, $description, $user_id);
    }
    
    /**
     * Log system action
     */
    public function logSystem($action, $description) {
        $this->log('system', $action, $description);
    }
    
    /**
     * Log error
     */
    public function logError($action, $description, $user_id = null) {
        $this->log('error', $action, $description, $user_id);
    }
    
    /**
     * Log warning
     */
    public function logWarning($action, $description, $user_id = null) {
        $this->log('warning', $action, $description, $user_id);
    }
    
    /**
     * Log info
     */
    public function logInfo($action, $description, $user_id = null) {
        $this->log('info', $action, $description, $user_id);
    }
    
    /**
     * Log success
     */
    public function logSuccess($action, $description, $user_id = null) {
        $this->log('success', $action, $description, $user_id);
    }
    
    /**
     * Log security event
     */
    public function logSecurity($action, $description, $user_id = null) {
        $this->log('security', $action, $description, $user_id);
    }
    
    /**
     * Log user management action
     */
    public function logUserManagement($action, $description, $user_id = null) {
        $this->log('user', $action, $description, $user_id);
    }
    
    /**
     * Get client IP address
     */
    private function getClientIP() {
        $ip_keys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    
                    if (filter_var($ip, FILTER_VALIDATE_IP, 
                        FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * Clean old logs (older than specified days)
     */
    public function cleanOldLogs($days = 30) {
        try {
            $conn = $this->db->connect();
            $stmt = $conn->prepare("DELETE FROM system_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)");
            $stmt->execute([$days]);
            
            $deleted = $stmt->rowCount();
            $this->logSystem('Log Cleanup', "Deleted {$deleted} old log entries (older than {$days} days)");
            
            return $deleted;
        } catch (Exception $e) {
            $this->logError('Log Cleanup Failed', $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get log statistics
     */
    public function getLogStats() {
        try {
            $conn = $this->db->connect();
            
            $stats = [];
            
            // Total logs
            $stmt = $conn->query("SELECT COUNT(*) FROM system_logs");
            $stats['total'] = $stmt->fetchColumn();
            
            // Today's logs
            $stmt = $conn->query("SELECT COUNT(*) FROM system_logs WHERE DATE(created_at) = CURDATE()");
            $stats['today'] = $stmt->fetchColumn();
            
            // Logs by type
            $stmt = $conn->query("
                SELECT log_type, COUNT(*) as count 
                FROM system_logs 
                GROUP BY log_type 
                ORDER BY count DESC
            ");
            $stats['by_type'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            
            // Recent activity
            $stmt = $conn->query("
                SELECT sl.*, u.username 
                FROM system_logs sl 
                LEFT JOIN users u ON sl.user_id = u.id 
                ORDER BY sl.created_at DESC 
                LIMIT 10
            ");
            $stats['recent'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return $stats;
            
        } catch (Exception $e) {
            return [
                'total' => 0,
                'today' => 0,
                'by_type' => [],
                'recent' => []
            ];
        }
    }
}

// Global logger instance
$logger = new SystemLogger();

/**
 * Helper functions for easy logging
 */
function logActivity($type, $action, $description = '', $user_id = null) {
    global $logger;
    $logger->log($type, $action, $description, $user_id);
}

function logLogin($user_id, $username) {
    global $logger;
    $logger->logLogin($user_id, $username);
}

function logLogout($user_id, $username) {
    global $logger;
    $logger->logLogout($user_id, $username);
}

function logAdmin($action, $description, $user_id = null) {
    global $logger;
    $logger->logAdmin($action, $description, $user_id);
}

function logContent($action, $description, $user_id = null) {
    global $logger;
    $logger->logContent($action, $description, $user_id);
}

function logSystem($action, $description) {
    global $logger;
    $logger->logSystem($action, $description);
}

function logError($action, $description, $user_id = null) {
    global $logger;
    $logger->logError($action, $description, $user_id);
}

function logWarning($action, $description, $user_id = null) {
    global $logger;
    $logger->logWarning($action, $description, $user_id);
}

function logInfo($action, $description, $user_id = null) {
    global $logger;
    $logger->logInfo($action, $description, $user_id);
}

function logSuccess($action, $description, $user_id = null) {
    global $logger;
    $logger->logSuccess($action, $description, $user_id);
}

function logSecurity($action, $description, $user_id = null) {
    global $logger;
    $logger->logSecurity($action, $description, $user_id);
}

function logUserManagement($action, $description, $user_id = null) {
    global $logger;
    $logger->logUserManagement($action, $description, $user_id);
}
?>
