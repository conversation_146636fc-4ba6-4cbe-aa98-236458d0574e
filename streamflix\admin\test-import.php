<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$test_results = [];
$test_errors = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'test_tmdb') {
        try {
            $streamflix = new StreamFlix();
            
            // Test TMDB connection
            $data = $streamflix->fetchFromTMDB('/movie/popular');
            
            if ($data && isset($data['results'])) {
                $test_results[] = "✅ TMDB API connection successful";
                $test_results[] = "✅ Found " . count($data['results']) . " popular movies";
                
                // Test importing one movie
                if (!empty($data['results'])) {
                    $first_movie = $data['results'][0];
                    $test_results[] = "🎬 Testing with movie: " . $first_movie['title'] . " (ID: " . $first_movie['id'] . ")";
                    
                    try {
                        $result = $streamflix->importMovie($first_movie['id']);
                        if ($result) {
                            $test_results[] = "✅ Successfully imported movie with ID: " . $result;
                        } else {
                            $test_errors[] = "❌ Failed to import movie (might already exist)";
                        }
                    } catch (Exception $e) {
                        $test_errors[] = "❌ Import error: " . $e->getMessage();
                    }
                }
            } else {
                $test_errors[] = "❌ TMDB API returned invalid data";
            }
            
        } catch (Exception $e) {
            $test_errors[] = "❌ TMDB API error: " . $e->getMessage();
        }
    }
    
    if ($action === 'test_tv') {
        try {
            $streamflix = new StreamFlix();
            
            // Test TV shows
            $data = $streamflix->fetchFromTMDB('/tv/popular');
            
            if ($data && isset($data['results'])) {
                $test_results[] = "✅ TMDB TV API connection successful";
                $test_results[] = "✅ Found " . count($data['results']) . " popular TV shows";
                
                // Test importing one TV show
                if (!empty($data['results'])) {
                    $first_show = $data['results'][0];
                    $test_results[] = "📺 Testing with TV show: " . $first_show['name'] . " (ID: " . $first_show['id'] . ")";
                    
                    try {
                        $result = $streamflix->importTVShow($first_show['id']);
                        if ($result) {
                            $test_results[] = "✅ Successfully imported TV show with ID: " . $result;
                        } else {
                            $test_errors[] = "❌ Failed to import TV show (might already exist)";
                        }
                    } catch (Exception $e) {
                        $test_errors[] = "❌ Import error: " . $e->getMessage();
                    }
                }
            } else {
                $test_errors[] = "❌ TMDB TV API returned invalid data";
            }
            
        } catch (Exception $e) {
            $test_errors[] = "❌ TMDB TV API error: " . $e->getMessage();
        }
    }
    
    if ($action === 'check_database') {
        try {
            $db = new Database();
            $conn = $db->connect();
            
            // Check tables
            $tables = ['movies', 'tv_shows', 'genres', 'embed_servers'];
            foreach ($tables as $table) {
                try {
                    $stmt = $conn->query("SELECT COUNT(*) FROM {$table}");
                    $count = $stmt->fetchColumn();
                    $test_results[] = "✅ Table '{$table}': {$count} records";
                } catch (Exception $e) {
                    $test_errors[] = "❌ Table '{$table}' error: " . $e->getMessage();
                }
            }
            
            // Check TMDB API key
            if (defined('TMDB_API_KEY') && !empty(TMDB_API_KEY)) {
                $test_results[] = "✅ TMDB API key is configured";
            } else {
                $test_errors[] = "❌ TMDB API key is not configured";
            }
            
        } catch (Exception $e) {
            $test_errors[] = "❌ Database error: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Import - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container {
            max-width: 800px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .test-section {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .test-btn {
            background: var(--primary-color);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-result {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 4px;
            background: var(--dark-bg);
        }
        
        .success {
            border-left: 4px solid #28a745;
        }
        
        .error {
            border-left: 4px solid #dc3545;
        }
        
        .info-box {
            background: rgba(23, 162, 184, 0.1);
            border: 1px solid #17a2b8;
            color: #17a2b8;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <span>Welcome, <?php echo $_SESSION['username']; ?></span>
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <h1>🧪 Test Import System</h1>
        <p>Test TMDB API connection and import functionality</p>
        
        <div class="info-box">
            <strong>ℹ️ How to test:</strong>
            <ol style="margin: 10px 0; padding-left: 20px;">
                <li>First check database and API configuration</li>
                <li>Test TMDB movie import</li>
                <li>Test TMDB TV show import</li>
                <li>If successful, use the main import page</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>🔧 Test Actions</h3>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="check_database">
                <button type="submit" class="test-btn">Check Database & Config</button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="test_tmdb">
                <button type="submit" class="test-btn">Test Movie Import</button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="test_tv">
                <button type="submit" class="test-btn">Test TV Import</button>
            </form>
        </div>

        <?php if (!empty($test_results)): ?>
            <div class="test-section">
                <h3>✅ Test Results</h3>
                <?php foreach ($test_results as $result): ?>
                    <div class="test-result success"><?php echo htmlspecialchars($result); ?></div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($test_errors)): ?>
            <div class="test-section">
                <h3>❌ Test Errors</h3>
                <?php foreach ($test_errors as $error): ?>
                    <div class="test-result error"><?php echo htmlspecialchars($error); ?></div>
                <?php endforeach; ?>
                
                <div style="margin-top: 15px; padding: 10px; background: rgba(220, 53, 69, 0.1); border-radius: 4px;">
                    <strong>💡 Troubleshooting:</strong>
                    <ul style="margin: 5px 0; padding-left: 20px;">
                        <li>Make sure TMDB API key is configured in config/database.php</li>
                        <li>Check database connection</li>
                        <li>Ensure all required tables exist (run database updater)</li>
                        <li>Check internet connection for TMDB API</li>
                    </ul>
                </div>
            </div>
        <?php endif; ?>
        
        <div class="test-section">
            <p>
                <a href="import.php">← Back to Import</a> | 
                <a href="database-updater.php">Database Updater</a> | 
                <a href="settings.php">Settings</a>
            </p>
        </div>
    </div>
</body>
</html>
