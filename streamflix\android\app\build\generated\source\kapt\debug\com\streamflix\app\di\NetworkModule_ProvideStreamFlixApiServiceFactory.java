// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.streamflix.app.di;

import com.streamflix.app.data.api.StreamFlixApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvideStreamFlixApiServiceFactory implements Factory<StreamFlixApiService> {
  private final Provider<Retrofit> retrofitProvider;

  public NetworkModule_ProvideStreamFlixApiServiceFactory(Provider<Retrofit> retrofitProvider) {
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public StreamFlixApiService get() {
    return provideStreamFlixApiService(retrofitProvider.get());
  }

  public static NetworkModule_ProvideStreamFlixApiServiceFactory create(
      Provider<Retrofit> retrofitProvider) {
    return new NetworkModule_ProvideStreamFlixApiServiceFactory(retrofitProvider);
  }

  public static StreamFlixApiService provideStreamFlixApiService(Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideStreamFlixApiService(retrofit));
  }
}
