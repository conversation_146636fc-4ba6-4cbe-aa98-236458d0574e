<?php
require_once 'config/database.php';

echo "<h2>🔧 Server Synchronization Fix</h2>";
echo "<p>This script will sync your database servers with the expected configuration.</p>";

try {
    $db = new Database();
    $conn = $db->connect();

    echo "<h3>🔧 Checking Table Structure</h3>";

    // Check if embed_servers table exists and has correct structure
    $stmt = $conn->query("SHOW TABLES LIKE 'embed_servers'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ embed_servers table does not exist! Creating...</p>";

        $create_sql = "
        CREATE TABLE embed_servers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            movie_url TEXT NOT NULL,
            tv_url TEXT NOT NULL,
            priority INT DEFAULT 1,
            is_active BOOLEAN DEFAULT 1,
            is_active_movies BOOLEAN DEFAULT 1,
            is_active_tv BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";

        $conn->exec($create_sql);
        echo "<p style='color: green;'>✅ embed_servers table created!</p>";
    }

    // Check for required columns and add if missing
    $required_columns = [
        'movie_url' => 'TEXT NOT NULL DEFAULT ""',
        'tv_url' => 'TEXT NOT NULL DEFAULT ""',
        'is_active_movies' => 'BOOLEAN DEFAULT 1',
        'is_active_tv' => 'BOOLEAN DEFAULT 1',
        'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];

    foreach ($required_columns as $column => $definition) {
        $stmt = $conn->query("SHOW COLUMNS FROM embed_servers LIKE '{$column}'");
        if ($stmt->rowCount() == 0) {
            try {
                $conn->exec("ALTER TABLE embed_servers ADD COLUMN {$column} {$definition}");
                echo "<p style='color: green;'>✅ Added missing column: {$column}</p>";
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ Could not add {$column}: " . $e->getMessage() . "</p>";
            }
        }
    }

    echo "<h3>📊 Current Database Status</h3>";

    // Check current servers in database
    $stmt = $conn->query("SELECT * FROM embed_servers ORDER BY priority ASC");
    $current_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Current servers in database: " . count($current_servers) . "</p>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Priority</th><th>Active</th><th>Movie URL</th></tr>";
    
    foreach ($current_servers as $server) {
        $status = $server['is_active'] ? '✅ Active' : '❌ Inactive';
        $movie_url = substr($server['movie_url'], 0, 50) . '...';
        echo "<tr>";
        echo "<td>{$server['id']}</td>";
        echo "<td>{$server['name']}</td>";
        echo "<td>{$server['priority']}</td>";
        echo "<td>{$status}</td>";
        echo "<td>{$movie_url}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Expected servers (what should be in database)
    $expected_servers = [
        ['AutoEmbed', 'https://player.autoembed.cc/embed/movie/{id}', 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}', 1],
        ['VidJoy', 'https://vidjoy.pro/embed/movie/{id}', 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}', 2],
        ['VidZee', 'https://player.vidzee.wtf/embed/movie/{id}', 'https://player.vidzee.wtf/embed/tv/{id}/{season}/{episode}', 3],
        ['LetsEmbed', 'https://letsembed.cc/embed/movie/?id={id}', 'https://letsembed.cc/embed/tv/?id={id}/{season}/{episode}', 4]
    ];
    
    echo "<h3>🔄 Synchronization Process</h3>";
    
    $changes_made = false;
    
    foreach ($expected_servers as $expected) {
        $name = $expected[0];
        $movie_url = $expected[1];
        $tv_url = $expected[2];
        $priority = $expected[3];
        
        // Check if server exists
        $stmt = $conn->prepare("SELECT * FROM embed_servers WHERE name = ?");
        $stmt->execute([$name]);
        $existing = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$existing) {
            // Server doesn't exist, add it
            echo "<p>➕ Adding missing server: <strong>{$name}</strong></p>";
            
            $stmt = $conn->prepare("
                INSERT INTO embed_servers (name, movie_url, tv_url, priority, is_active, created_at)
                VALUES (?, ?, ?, ?, 1, NOW())
            ");
            $stmt->execute([$name, $movie_url, $tv_url, $priority]);
            $changes_made = true;
            
        } else {
            // Server exists, check if URLs need updating
            if ($existing['movie_url'] !== $movie_url || $existing['tv_url'] !== $tv_url) {
                echo "<p>🔄 Updating server URLs: <strong>{$name}</strong></p>";
                
                $stmt = $conn->prepare("
                    UPDATE embed_servers 
                    SET movie_url = ?, tv_url = ?, priority = ?, updated_at = NOW()
                    WHERE name = ?
                ");
                $stmt->execute([$movie_url, $tv_url, $priority, $name]);
                $changes_made = true;
            } else {
                echo "<p>✅ Server OK: <strong>{$name}</strong></p>";
            }
        }
    }
    
    if (!$changes_made) {
        echo "<p style='color: green;'>✅ All servers are already synchronized!</p>";
    } else {
        echo "<p style='color: green;'>✅ Synchronization completed!</p>";
    }
    
    echo "<h3>📊 Updated Database Status</h3>";
    
    // Show updated servers
    $stmt = $conn->query("SELECT * FROM embed_servers ORDER BY priority ASC");
    $updated_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Total servers in database: " . count($updated_servers) . "</p>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Priority</th><th>Active</th><th>Movie URL</th></tr>";
    
    foreach ($updated_servers as $server) {
        $status = $server['is_active'] ? '✅ Active' : '❌ Inactive';
        $movie_url = substr($server['movie_url'], 0, 50) . '...';
        echo "<tr>";
        echo "<td>{$server['id']}</td>";
        echo "<td>{$server['name']}</td>";
        echo "<td>{$server['priority']}</td>";
        echo "<td>{$status}</td>";
        echo "<td>{$movie_url}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>🧪 Testing Server Loading</h3>";
    
    // Test the getEmbedUrls function
    require_once 'includes/functions.php';
    $streamflix = new StreamFlix();
    
    echo "<h4>Testing Movie (TMDB ID: 550 - Fight Club):</h4>";
    $movie_urls = $streamflix->getEmbedUrls('movie', 550);
    echo "<p>Found " . count($movie_urls) . " servers for movies:</p>";
    echo "<ol>";
    foreach ($movie_urls as $url) {
        echo "<li><strong>{$url['name']}</strong> (Priority: {$url['priority']})</li>";
    }
    echo "</ol>";
    
    echo "<h4>Testing TV Show (TMDB ID: 1399 - Game of Thrones S1E1):</h4>";
    $tv_urls = $streamflix->getEmbedUrls('tv_show', 1399, 1, 1);
    echo "<p>Found " . count($tv_urls) . " servers for TV shows:</p>";
    echo "<ol>";
    foreach ($tv_urls as $url) {
        echo "<li><strong>{$url['name']}</strong> (Priority: {$url['priority']})</li>";
    }
    echo "</ol>";
    
    if (count($movie_urls) === 4 && count($tv_urls) === 4) {
        echo "<p style='color: green; font-weight: bold;'>✅ SUCCESS: All 4 servers are now loading correctly!</p>";
        echo "<p style='color: green;'>Your player should now show 4 servers and they should match your database.</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>⚠️ WARNING: Expected 4 servers but found different counts.</p>";
        echo "<p>Movies: " . count($movie_urls) . " servers, TV: " . count($tv_urls) . " servers</p>";
    }
    
    echo "<hr>";
    echo "<h3>📝 Next Steps</h3>";
    echo "<ul>";
    echo "<li>✅ Config file hardcoded servers have been removed</li>";
    echo "<li>✅ Database servers have been synchronized</li>";
    echo "<li>🔄 Clear your browser cache and refresh the player page</li>";
    echo "<li>🎬 Test playing a movie - you should now see exactly " . count($updated_servers) . " servers</li>";
    echo "<li>⚙️ Use the admin panel to manage servers (add/edit/remove/activate/deactivate)</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>
