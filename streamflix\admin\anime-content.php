<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$db = new Database();
$conn = $db->connect();

$message = '';
$error = '';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        if ($action === 'convert_to_anime') {
            $selected_ids = $_POST['selected_items'] ?? [];
            if (!empty($selected_ids)) {
                $success_count = 0;
                foreach ($selected_ids as $id) {
                    // Check if it's a movie or TV show
                    $stmt = $conn->prepare("SELECT 'movie' as type FROM movies WHERE id = ? UNION SELECT 'tv_show' as type FROM tv_shows WHERE id = ?");
                    $stmt->execute([$id, $id]);
                    $item_type = $stmt->fetchColumn();
                    
                    if ($item_type === 'movie') {
                        $stmt = $conn->prepare("UPDATE movies SET content_type = 'anime' WHERE id = ?");
                    } else {
                        $stmt = $conn->prepare("UPDATE tv_shows SET content_type = 'anime' WHERE id = ?");
                    }
                    $stmt->execute([$id]);
                    $success_count++;
                }
                $message = "Successfully converted {$success_count} items to anime content.";
            }
        }
        
        if ($action === 'revert_content') {
            $selected_ids = $_POST['selected_items'] ?? [];
            if (!empty($selected_ids)) {
                $success_count = 0;
                foreach ($selected_ids as $id) {
                    // Check if it's a movie or TV show
                    $stmt = $conn->prepare("SELECT 'movie' as type FROM movies WHERE id = ? UNION SELECT 'tv_show' as type FROM tv_shows WHERE id = ?");
                    $stmt->execute([$id, $id]);
                    $item_type = $stmt->fetchColumn();
                    
                    if ($item_type === 'movie') {
                        $stmt = $conn->prepare("UPDATE movies SET content_type = 'movie' WHERE id = ?");
                    } else {
                        $stmt = $conn->prepare("UPDATE tv_shows SET content_type = 'tv_show' WHERE id = ?");
                    }
                    $stmt->execute([$id]);
                    $success_count++;
                }
                $message = "Successfully reverted {$success_count} items from anime.";
            }
        }
        
        if ($action === 'delete_content') {
            $selected_ids = $_POST['selected_items'] ?? [];
            if (!empty($selected_ids)) {
                $success_count = 0;
                foreach ($selected_ids as $id) {
                    // Try to delete from both tables
                    $stmt = $conn->prepare("DELETE FROM movie_genres WHERE movie_id = ?");
                    $stmt->execute([$id]);
                    $stmt = $conn->prepare("DELETE FROM movies WHERE id = ?");
                    $stmt->execute([$id]);
                    
                    $stmt = $conn->prepare("DELETE FROM tv_show_genres WHERE tv_show_id = ?");
                    $stmt->execute([$id]);
                    $stmt = $conn->prepare("DELETE FROM tv_shows WHERE id = ?");
                    $stmt->execute([$id]);
                    
                    $success_count++;
                }
                $message = "Successfully deleted {$success_count} items.";
            }
        }
        
    } catch (Exception $e) {
        $error = 'Error: ' . $e->getMessage();
    }
}

// Get filter parameters
$filter = $_GET['filter'] ?? 'anime';
$search = $_GET['search'] ?? '';
$content_type = $_GET['content_type'] ?? 'all'; // movies, tv_shows, all
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build query for anime content
$union_queries = [];
$params = [];

if ($content_type === 'all' || $content_type === 'movies') {
    $movie_where = [];
    if ($filter === 'anime') {
        $movie_where[] = "content_type = 'anime'";
    } elseif ($filter === 'potential') {
        $movie_where[] = "(
            LOWER(title) LIKE '%anime%' OR 
            LOWER(title) LIKE '%manga%' OR 
            LOWER(overview) LIKE '%anime%' OR 
            LOWER(overview) LIKE '%manga%'
        ) AND (content_type != 'anime' OR content_type IS NULL)";
    }
    
    if (!empty($search)) {
        $movie_where[] = "(title LIKE ? OR overview LIKE ?)";
        $params[] = "%{$search}%";
        $params[] = "%{$search}%";
    }
    
    $movie_where_clause = !empty($movie_where) ? 'WHERE ' . implode(' AND ', $movie_where) : '';
    
    $union_queries[] = "
        SELECT id, tmdb_id, title as name, overview, content_type, vote_average, release_date as air_date, poster_path, runtime as duration, 'movie' as item_type
        FROM movies 
        {$movie_where_clause}
    ";
}

if ($content_type === 'all' || $content_type === 'tv_shows') {
    $tv_where = [];
    if ($filter === 'anime') {
        $tv_where[] = "content_type = 'anime'";
    } elseif ($filter === 'potential') {
        $tv_where[] = "(
            LOWER(name) LIKE '%anime%' OR 
            LOWER(name) LIKE '%manga%' OR 
            LOWER(overview) LIKE '%anime%' OR 
            LOWER(overview) LIKE '%manga%'
        ) AND (content_type != 'anime' OR content_type IS NULL)";
    }
    
    if (!empty($search)) {
        $tv_where[] = "(name LIKE ? OR overview LIKE ?)";
        $params[] = "%{$search}%";
        $params[] = "%{$search}%";
    }
    
    $tv_where_clause = !empty($tv_where) ? 'WHERE ' . implode(' AND ', $tv_where) : '';
    
    $union_queries[] = "
        SELECT id, tmdb_id, name, overview, content_type, vote_average, first_air_date as air_date, poster_path, number_of_episodes as duration, 'tv_show' as item_type
        FROM tv_shows 
        {$tv_where_clause}
    ";
}

$full_query = implode(' UNION ALL ', $union_queries) . " ORDER BY name ASC LIMIT {$per_page} OFFSET {$offset}";

// Get total count
$count_queries = [];
$count_params = [];

if ($content_type === 'all' || $content_type === 'movies') {
    $movie_where = [];
    if ($filter === 'anime') {
        $movie_where[] = "content_type = 'anime'";
    } elseif ($filter === 'potential') {
        $movie_where[] = "(
            LOWER(title) LIKE '%anime%' OR 
            LOWER(title) LIKE '%manga%' OR 
            LOWER(overview) LIKE '%anime%' OR 
            LOWER(overview) LIKE '%manga%'
        ) AND (content_type != 'anime' OR content_type IS NULL)";
    }
    
    if (!empty($search)) {
        $movie_where[] = "(title LIKE ? OR overview LIKE ?)";
        $count_params[] = "%{$search}%";
        $count_params[] = "%{$search}%";
    }
    
    $movie_where_clause = !empty($movie_where) ? 'WHERE ' . implode(' AND ', $movie_where) : '';
    $count_queries[] = "SELECT COUNT(*) FROM movies {$movie_where_clause}";
}

if ($content_type === 'all' || $content_type === 'tv_shows') {
    $tv_where = [];
    if ($filter === 'anime') {
        $tv_where[] = "content_type = 'anime'";
    } elseif ($filter === 'potential') {
        $tv_where[] = "(
            LOWER(name) LIKE '%anime%' OR 
            LOWER(name) LIKE '%manga%' OR 
            LOWER(overview) LIKE '%anime%' OR 
            LOWER(overview) LIKE '%manga%'
        ) AND (content_type != 'anime' OR content_type IS NULL)";
    }
    
    if (!empty($search)) {
        $tv_where[] = "(name LIKE ? OR overview LIKE ?)";
        $count_params[] = "%{$search}%";
        $count_params[] = "%{$search}%";
    }
    
    $tv_where_clause = !empty($tv_where) ? 'WHERE ' . implode(' AND ', $tv_where) : '';
    $count_queries[] = "SELECT COUNT(*) FROM tv_shows {$tv_where_clause}";
}

$total_items = 0;
foreach ($count_queries as $i => $count_query) {
    $stmt = $conn->prepare($count_query);
    $start_param = $i * 2; // Each query uses 2 params if search is provided
    $query_params = array_slice($count_params, $start_param, 2);
    $stmt->execute($query_params);
    $total_items += $stmt->fetchColumn();
}

$total_pages = ceil($total_items / $per_page);

// Get content items
$stmt = $conn->prepare($full_query);
$stmt->execute($params);
$content_items = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get statistics
$stats = [];
$stmt = $conn->query("SELECT COUNT(*) FROM movies WHERE content_type = 'anime'");
$stats['anime_movies'] = $stmt->fetchColumn();

$stmt = $conn->query("SELECT COUNT(*) FROM tv_shows WHERE content_type = 'anime'");
$stats['anime_tv'] = $stmt->fetchColumn();

$stats['total_anime'] = $stats['anime_movies'] + $stats['anime_tv'];

$stmt = $conn->query("SELECT COUNT(*) FROM embed_servers WHERE anime_url IS NOT NULL AND anime_url != ''");
$stats['anime_servers'] = $stmt->fetchColumn();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anime Content Management - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .page-header {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: var(--secondary-color);
            border: 2px solid #ff6b35;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #ff6b35;
            margin-bottom: 5px;
        }
        
        .filters {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            border: 1px solid var(--border-color);
        }
        
        .filter-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .content-card {
            background: var(--secondary-color);
            border-radius: 12px;
            overflow: hidden;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .content-card:hover {
            border-color: #ff6b35;
            transform: translateY(-2px);
        }
        
        .content-card.anime {
            border-color: #ff6b35;
        }
        
        .content-poster {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .content-info {
            padding: 15px;
        }
        
        .content-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-primary);
        }
        
        .content-meta {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-bottom: 10px;
        }
        
        .content-type {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
            margin-bottom: 10px;
        }
        
        .type-anime {
            background: #ff6b35;
            color: white;
        }
        
        .type-movie {
            background: #28a745;
            color: white;
        }
        
        .type-tv_show {
            background: #17a2b8;
            color: white;
        }
        
        .type-null {
            background: #6c757d;
            color: white;
        }
        
        .item-type-badge {
            background: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .content-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 0.8rem;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }
        
        .bulk-actions {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            border: 1px solid var(--border-color);
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }
        
        .pagination a, .pagination span {
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            border: 1px solid var(--border-color);
        }
        
        .pagination .current {
            background: #ff6b35;
            color: white;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 1px solid #28a745;
        }
        
        .alert-error {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid #dc3545;
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="nav-links">
                <a href="index.php">📊 Dashboard</a>
                <a href="hentai-content.php">🔞 Hentai Content</a>
                <a href="anime-content.php" class="active">🎌 Anime Content</a>
                <a href="anime-servers.php">🖥️ Anime Servers</a>
            </div>
            <div class="user-menu">
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="page-header">
            <h1><i class="fas fa-torii-gate"></i> Anime Content Management</h1>
            <p>Manage anime movies and series, convert between categories, and organize anime content</p>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['total_anime']); ?></div>
                <div class="stat-label">Total Anime</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['anime_movies']); ?></div>
                <div class="stat-label">Anime Movies</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['anime_tv']); ?></div>
                <div class="stat-label">Anime Series</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['anime_servers']); ?></div>
                <div class="stat-label">Anime Servers</div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters">
            <form method="GET" class="filter-row">
                <div>
                    <label for="filter">Filter:</label>
                    <select name="filter" id="filter" onchange="this.form.submit()">
                        <option value="anime" <?= $filter === 'anime' ? 'selected' : '' ?>>Anime Content</option>
                        <option value="potential" <?= $filter === 'potential' ? 'selected' : '' ?>>Potential Anime</option>
                        <option value="all" <?= $filter === 'all' ? 'selected' : '' ?>>All Content</option>
                    </select>
                </div>
                <div>
                    <label for="content_type">Type:</label>
                    <select name="content_type" id="content_type" onchange="this.form.submit()">
                        <option value="all" <?= $content_type === 'all' ? 'selected' : '' ?>>Movies & TV Shows</option>
                        <option value="movies" <?= $content_type === 'movies' ? 'selected' : '' ?>>Movies Only</option>
                        <option value="tv_shows" <?= $content_type === 'tv_shows' ? 'selected' : '' ?>>TV Shows Only</option>
                    </select>
                </div>
                <div>
                    <label for="search">Search:</label>
                    <input type="text" name="search" id="search" value="<?= htmlspecialchars($search) ?>" placeholder="Search by name or description">
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Search
                </button>
                <a href="?" class="btn btn-secondary">
                    <i class="fas fa-refresh"></i> Reset
                </a>
            </form>
        </div>

        <!-- Bulk Actions -->
        <form method="POST" id="bulkForm">
            <div class="bulk-actions">
                <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px;">
                    <div>
                        <button type="button" onclick="selectAll()" class="btn btn-secondary">
                            <i class="fas fa-check-square"></i> Select All
                        </button>
                        <button type="button" onclick="selectNone()" class="btn btn-secondary">
                            <i class="fas fa-square"></i> Select None
                        </button>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button type="submit" name="action" value="convert_to_anime" class="btn btn-warning" onclick="return confirm('Convert selected items to anime?')">
                            <i class="fas fa-torii-gate"></i> Convert to Anime
                        </button>
                        <button type="submit" name="action" value="revert_content" class="btn btn-info" onclick="return confirm('Revert selected items from anime?')">
                            <i class="fas fa-undo"></i> Revert from Anime
                        </button>
                        <button type="submit" name="action" value="delete_content" class="btn btn-danger" onclick="return confirm('Delete selected items permanently?')">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            </div>

            <!-- Content Grid -->
            <?php if (empty($content_items)): ?>
                <div style="text-align: center; padding: 60px; color: var(--text-secondary);">
                    <i class="fas fa-search" style="font-size: 4rem; margin-bottom: 20px; opacity: 0.3;"></i>
                    <h3>No anime content found</h3>
                    <p>Current filter: <strong><?= htmlspecialchars($filter) ?></strong></p>
                    <p>Content type: <strong><?= htmlspecialchars($content_type) ?></strong></p>
                    <p>Total items in query: <strong><?= $total_items ?></strong></p>

                    <?php if ($filter === 'anime'): ?>
                        <div style="margin: 20px 0;">
                            <p>No content is currently marked as anime. Try these options:</p>
                            <a href="?filter=potential" class="btn btn-primary">🔍 Find Potential Anime Content</a>
                            <a href="?filter=all" class="btn btn-secondary">📺 View All Content</a>
                            <a href="quick-hentai-anime-converter.php" class="btn btn-warning">🔄 Auto-Convert Content</a>
                        </div>
                    <?php elseif ($filter === 'potential'): ?>
                        <div style="margin: 20px 0;">
                            <p>No potential anime content detected. Try:</p>
                            <a href="?filter=all" class="btn btn-secondary">📺 View All Content</a>
                            <a href="?search=anime" class="btn btn-primary">🔍 Search "anime"</a>
                        </div>
                    <?php else: ?>
                        <div style="margin: 20px 0;">
                            <p>No content matches your search criteria.</p>
                            <a href="?" class="btn btn-secondary">🔄 Reset Filters</a>
                        </div>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <div class="content-grid">
                    <?php foreach ($content_items as $item): ?>
                        <div class="content-card <?= $item['content_type'] === 'anime' ? 'anime' : '' ?>">
                            <div class="item-type-badge"><?= strtoupper($item['item_type']) ?></div>

                            <img src="https://image.tmdb.org/t/p/w300<?= htmlspecialchars($item['poster_path'] ?? '') ?>"
                                 alt="<?= htmlspecialchars($item['name']) ?>"
                                 class="content-poster"
                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMwMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMjAgODBIMTgwVjkwSDEyMFY4MFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHA+PC9wYXRoPgo8L3N2Zz4K'">

                            <div class="content-info">
                                <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 10px;">
                                    <input type="checkbox" name="selected_items[]" value="<?= $item['id'] ?>" class="item-checkbox">
                                    <span class="content-type type-<?= $item['content_type'] ?? 'null' ?>">
                                        <?= $item['content_type'] ?? 'Uncategorized' ?>
                                    </span>
                                </div>

                                <h4 class="content-title"><?= htmlspecialchars($item['name']) ?></h4>

                                <div class="content-meta">
                                    <div><strong>TMDB ID:</strong> <?= $item['tmdb_id'] ?></div>
                                    <div><strong>Rating:</strong> ⭐ <?= number_format($item['vote_average'], 1) ?>/10</div>
                                    <div><strong>Year:</strong> <?= $item['air_date'] ? date('Y', strtotime($item['air_date'])) : 'N/A' ?></div>
                                    <div><strong>Duration:</strong> <?= $item['duration'] ?? 'N/A' ?></div>
                                </div>

                                <?php if (!empty($item['overview'])): ?>
                                <div style="margin: 10px 0; font-size: 0.85rem; color: var(--text-secondary);">
                                    <?= htmlspecialchars(strlen($item['overview']) > 100 ? substr($item['overview'], 0, 100) . '...' : $item['overview']) ?>
                                </div>
                                <?php endif; ?>

                                <div class="content-actions">
                                    <a href="../player.php?id=<?= $item['tmdb_id'] ?>&type=<?= $item['item_type'] ?>"
                                       target="_blank"
                                       class="btn-small btn-primary"
                                       title="Play">
                                        <i class="fas fa-play"></i> Play
                                    </a>

                                    <?php if ($item['content_type'] !== 'anime'): ?>
                                    <button type="button"
                                            onclick="convertSingle(<?= $item['id'] ?>, '<?= htmlspecialchars($item['name']) ?>')"
                                            class="btn-small btn-warning"
                                            title="Convert to Anime">
                                        <i class="fas fa-torii-gate"></i> To Anime
                                    </button>
                                    <?php else: ?>
                                    <button type="button"
                                            onclick="revertSingle(<?= $item['id'] ?>, '<?= htmlspecialchars($item['name']) ?>')"
                                            class="btn-small btn-info"
                                            title="Revert from Anime">
                                        <i class="fas fa-undo"></i> Revert
                                    </button>
                                    <?php endif; ?>

                                    <button type="button"
                                            onclick="deleteSingle(<?= $item['id'] ?>, '<?= htmlspecialchars($item['name']) ?>')"
                                            class="btn-small btn-danger"
                                            title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </form>

        <!-- Quick Links -->
        <div style="margin-top: 40px; text-align: center;">
            <a href="index.php" class="btn btn-secondary">← Back to Dashboard</a>
            <a href="hentai-content.php" class="btn btn-secondary">🔞 Hentai Content</a>
            <a href="anime-servers.php" class="btn btn-secondary">🖥️ Anime Servers</a>
            <a href="quick-hentai-anime-converter.php" class="btn btn-primary">🔄 Quick Converter</a>
        </div>
    </div>

    <script>
        function selectAll() {
            document.querySelectorAll('.item-checkbox').forEach(cb => cb.checked = true);
        }

        function selectNone() {
            document.querySelectorAll('.item-checkbox').forEach(cb => cb.checked = false);
        }

        function convertSingle(id, name) {
            if (confirm(`Convert "${name}" to anime content?`)) {
                submitSingleAction('convert_to_anime', id);
            }
        }

        function revertSingle(id, name) {
            if (confirm(`Revert "${name}" from anime?`)) {
                submitSingleAction('revert_content', id);
            }
        }

        function deleteSingle(id, name) {
            if (confirm(`Delete "${name}" permanently?`)) {
                submitSingleAction('delete_content', id);
            }
        }

        function submitSingleAction(action, id) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="${action}">
                <input type="hidden" name="selected_items[]" value="${id}">
            `;
            document.body.appendChild(form);
            form.submit();
        }

        // Auto-submit search form on Enter
        document.getElementById('search').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                this.form.submit();
            }
        });
    </script>
</body>
</html>
