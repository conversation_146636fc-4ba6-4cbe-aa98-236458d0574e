package com.streamflix.app.data.offline;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import androidx.room.*;
import com.google.gson.Gson;
import com.streamflix.app.data.local.UserPreferences;
import com.streamflix.app.data.model.*;
import dagger.hilt.android.qualifiers.ApplicationContext;
import kotlinx.coroutines.flow.*;
import java.io.File;
import javax.inject.Inject;
import javax.inject.Singleton;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\'\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H&J\b\u0010\u0005\u001a\u00020\u0006H&J\b\u0010\u0007\u001a\u00020\bH&\u00a8\u0006\t"}, d2 = {"Lcom/streamflix/app/data/offline/OfflineDatabase;", "Landroidx/room/RoomDatabase;", "()V", "offlineContentDao", "Lcom/streamflix/app/data/offline/OfflineContentDao;", "searchHistoryDao", "Lcom/streamflix/app/data/offline/OfflineSearchHistoryDao;", "userPreferencesDao", "Lcom/streamflix/app/data/offline/OfflineUserPreferencesDao;", "app_debug"})
@androidx.room.Database(entities = {com.streamflix.app.data.offline.OfflineContentItem.class, com.streamflix.app.data.offline.OfflineSearchHistory.class, com.streamflix.app.data.offline.OfflineUserPreferences.class}, version = 1, exportSchema = false)
public abstract class OfflineDatabase extends androidx.room.RoomDatabase {
    
    public OfflineDatabase() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.streamflix.app.data.offline.OfflineContentDao offlineContentDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.streamflix.app.data.offline.OfflineSearchHistoryDao searchHistoryDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.streamflix.app.data.offline.OfflineUserPreferencesDao userPreferencesDao();
}