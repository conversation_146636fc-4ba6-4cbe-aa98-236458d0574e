package com.streamflix.app.data.local

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.*
import androidx.datastore.preferences.preferencesDataStore
import com.google.gson.Gson
import com.streamflix.app.data.model.User
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "user_preferences")

@Singleton
class UserPreferences @Inject constructor(
    @ApplicationContext private val context: Context,
    private val gson: Gson
) {

    companion object {
        private val AUTH_TOKEN = stringPreferencesKey("auth_token")
        private val USER_DATA = stringPreferencesKey("user_data")
        private val IS_FIRST_LAUNCH = booleanPreferencesKey("is_first_launch")
        private val THEME_MODE = stringPreferencesKey("theme_mode")
        private val VIDEO_QUALITY = stringPreferencesKey("video_quality")
        private val AUTO_PLAY = booleanPreferencesKey("auto_play")
        private val DOWNLOAD_QUALITY = stringPreferencesKey("download_quality")
        private val WIFI_ONLY_DOWNLOAD = booleanPreferencesKey("wifi_only_download")
        private val NOTIFICATIONS_ENABLED = booleanPreferencesKey("notifications_enabled")
        private val PARENTAL_CONTROL = booleanPreferencesKey("parental_control")
        private val SUBTITLE_LANGUAGE = stringPreferencesKey("subtitle_language")
        private val AUDIO_LANGUAGE = stringPreferencesKey("audio_language")
        private val LAST_APP_VERSION = stringPreferencesKey("last_app_version")
    }

    // ==================== Authentication ====================
    
    suspend fun saveAuthToken(token: String) {
        context.dataStore.edit { preferences ->
            preferences[AUTH_TOKEN] = token
        }
    }
    
    fun getAuthToken(): Flow<String?> {
        return context.dataStore.data.map { preferences ->
            preferences[AUTH_TOKEN]
        }
    }
    
    suspend fun saveUser(user: User) {
        context.dataStore.edit { preferences ->
            preferences[USER_DATA] = gson.toJson(user)
        }
    }
    
    fun getCurrentUser(): Flow<User?> {
        return context.dataStore.data.map { preferences ->
            preferences[USER_DATA]?.let { userJson ->
                try {
                    gson.fromJson(userJson, User::class.java)
                } catch (e: Exception) {
                    null
                }
            }
        }
    }
    
    fun isLoggedIn(): Flow<Boolean> {
        return context.dataStore.data.map { preferences ->
            !preferences[AUTH_TOKEN].isNullOrEmpty()
        }
    }
    
    suspend fun clearAuthData() {
        context.dataStore.edit { preferences ->
            preferences.remove(AUTH_TOKEN)
            preferences.remove(USER_DATA)
        }
    }

    // ==================== App Settings ====================
    
    suspend fun setFirstLaunch(isFirstLaunch: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[IS_FIRST_LAUNCH] = isFirstLaunch
        }
    }
    
    fun isFirstLaunch(): Flow<Boolean> {
        return context.dataStore.data.map { preferences ->
            preferences[IS_FIRST_LAUNCH] ?: true
        }
    }
    
    suspend fun setThemeMode(themeMode: String) {
        context.dataStore.edit { preferences ->
            preferences[THEME_MODE] = themeMode
        }
    }
    
    fun getThemeMode(): Flow<String> {
        return context.dataStore.data.map { preferences ->
            preferences[THEME_MODE] ?: "system"
        }
    }

    // ==================== Video Settings ====================
    
    suspend fun setVideoQuality(quality: String) {
        context.dataStore.edit { preferences ->
            preferences[VIDEO_QUALITY] = quality
        }
    }
    
    fun getVideoQuality(): Flow<String> {
        return context.dataStore.data.map { preferences ->
            preferences[VIDEO_QUALITY] ?: "auto"
        }
    }
    
    suspend fun setAutoPlay(autoPlay: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[AUTO_PLAY] = autoPlay
        }
    }
    
    fun getAutoPlay(): Flow<Boolean> {
        return context.dataStore.data.map { preferences ->
            preferences[AUTO_PLAY] ?: true
        }
    }
    
    suspend fun setSubtitleLanguage(language: String) {
        context.dataStore.edit { preferences ->
            preferences[SUBTITLE_LANGUAGE] = language
        }
    }
    
    fun getSubtitleLanguage(): Flow<String> {
        return context.dataStore.data.map { preferences ->
            preferences[SUBTITLE_LANGUAGE] ?: "en"
        }
    }
    
    suspend fun setAudioLanguage(language: String) {
        context.dataStore.edit { preferences ->
            preferences[AUDIO_LANGUAGE] = language
        }
    }
    
    fun getAudioLanguage(): Flow<String> {
        return context.dataStore.data.map { preferences ->
            preferences[AUDIO_LANGUAGE] ?: "en"
        }
    }

    // ==================== Download Settings ====================
    
    suspend fun setDownloadQuality(quality: String) {
        context.dataStore.edit { preferences ->
            preferences[DOWNLOAD_QUALITY] = quality
        }
    }
    
    fun getDownloadQuality(): Flow<String> {
        return context.dataStore.data.map { preferences ->
            preferences[DOWNLOAD_QUALITY] ?: "hd"
        }
    }
    
    suspend fun setWifiOnlyDownload(wifiOnly: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[WIFI_ONLY_DOWNLOAD] = wifiOnly
        }
    }
    
    fun getWifiOnlyDownload(): Flow<Boolean> {
        return context.dataStore.data.map { preferences ->
            preferences[WIFI_ONLY_DOWNLOAD] ?: true
        }
    }

    // ==================== Notification Settings ====================
    
    suspend fun setNotificationsEnabled(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[NOTIFICATIONS_ENABLED] = enabled
        }
    }
    
    fun getNotificationsEnabled(): Flow<Boolean> {
        return context.dataStore.data.map { preferences ->
            preferences[NOTIFICATIONS_ENABLED] ?: true
        }
    }

    // ==================== Parental Control ====================
    
    suspend fun setParentalControl(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[PARENTAL_CONTROL] = enabled
        }
    }
    
    fun getParentalControl(): Flow<Boolean> {
        return context.dataStore.data.map { preferences ->
            preferences[PARENTAL_CONTROL] ?: false
        }
    }

    // ==================== App Version ====================
    
    suspend fun setLastAppVersion(version: String) {
        context.dataStore.edit { preferences ->
            preferences[LAST_APP_VERSION] = version
        }
    }
    
    fun getLastAppVersion(): Flow<String?> {
        return context.dataStore.data.map { preferences ->
            preferences[LAST_APP_VERSION]
        }
    }

    // ==================== Bulk Operations ====================
    
    suspend fun clearAllData() {
        context.dataStore.edit { preferences ->
            preferences.clear()
        }
    }
    
    suspend fun resetToDefaults() {
        context.dataStore.edit { preferences ->
            // Keep auth data but reset settings
            val authToken = preferences[AUTH_TOKEN]
            val userData = preferences[USER_DATA]
            
            preferences.clear()
            
            // Restore auth data
            authToken?.let { preferences[AUTH_TOKEN] = it }
            userData?.let { preferences[USER_DATA] = it }
            
            // Set default values
            preferences[THEME_MODE] = "system"
            preferences[VIDEO_QUALITY] = "auto"
            preferences[AUTO_PLAY] = true
            preferences[DOWNLOAD_QUALITY] = "hd"
            preferences[WIFI_ONLY_DOWNLOAD] = true
            preferences[NOTIFICATIONS_ENABLED] = true
            preferences[PARENTAL_CONTROL] = false
            preferences[SUBTITLE_LANGUAGE] = "en"
            preferences[AUDIO_LANGUAGE] = "en"
        }
    }
    
    // ==================== Settings Data Class ====================
    
    data class AppSettings(
        val themeMode: String = "system",
        val videoQuality: String = "auto",
        val autoPlay: Boolean = true,
        val downloadQuality: String = "hd",
        val wifiOnlyDownload: Boolean = true,
        val notificationsEnabled: Boolean = true,
        val parentalControl: Boolean = false,
        val subtitleLanguage: String = "en",
        val audioLanguage: String = "en"
    )
    
    fun getAllSettings(): Flow<AppSettings> {
        return context.dataStore.data.map { preferences ->
            AppSettings(
                themeMode = preferences[THEME_MODE] ?: "system",
                videoQuality = preferences[VIDEO_QUALITY] ?: "auto",
                autoPlay = preferences[AUTO_PLAY] ?: true,
                downloadQuality = preferences[DOWNLOAD_QUALITY] ?: "hd",
                wifiOnlyDownload = preferences[WIFI_ONLY_DOWNLOAD] ?: true,
                notificationsEnabled = preferences[NOTIFICATIONS_ENABLED] ?: true,
                parentalControl = preferences[PARENTAL_CONTROL] ?: false,
                subtitleLanguage = preferences[SUBTITLE_LANGUAGE] ?: "en",
                audioLanguage = preferences[AUDIO_LANGUAGE] ?: "en"
            )
        }
    }
}
