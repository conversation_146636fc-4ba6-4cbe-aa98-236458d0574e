package com.streamflix.app.presentation.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.streamflix.app.data.model.*
import com.streamflix.app.presentation.home.toContentItem
import com.streamflix.app.presentation.search.GenreCard
import com.streamflix.app.ui.theme.*

@Composable
fun TopRatedSection(
    items: List<Any>,
    onItemClick: (Any) -> Unit
) {
    Column(modifier = Modifier.padding(vertical = 16.dp)) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Top Rated",
                style = MaterialTheme.typography.titleLarge.copy(
                    fontWeight = FontWeight.Bold,
                    fontSize = 20.sp
                ),
                color = TextPrimary
            )
        }
        
        Spacer(modifier = Modifier.height(12.dp))
        
        LazyRow(
            contentPadding = PaddingValues(horizontal = 16.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            itemsIndexed(items) { index, item ->
                when (item) {
                    is Movie -> {
                        TopRatedCard(
                            item = item.toContentItem(),
                            rank = index + 1,
                            onClick = { onItemClick(item) }
                        )
                    }
                    is TvShow -> {
                        TopRatedCard(
                            item = item.toContentItem(),
                            rank = index + 1,
                            onClick = { onItemClick(item) }
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun GenresSection(
    onGenreClick: (Genre) -> Unit
) {
    Column(modifier = Modifier.padding(vertical = 16.dp)) {
        Text(
            text = "Browse by Genre",
            style = MaterialTheme.typography.titleLarge.copy(
                fontWeight = FontWeight.Bold,
                fontSize = 20.sp
            ),
            color = TextPrimary,
            modifier = Modifier.padding(horizontal = 16.dp).padding(bottom = 12.dp)
        )
        
        val genres = listOf(
            Genre(1, "Action", 0, 0, 0),
            Genre(2, "Comedy", 0, 0, 0),
            Genre(3, "Drama", 0, 0, 0),
            Genre(4, "Horror", 0, 0, 0),
            Genre(5, "Romance", 0, 0, 0),
            Genre(6, "Sci-Fi", 0, 0, 0)
        )
        
        LazyRow(
            contentPadding = PaddingValues(horizontal = 16.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(genres) { genre ->
                GenreCard(
                    genre = genre,
                    onClick = { onGenreClick(genre) },
                    modifier = Modifier.width(120.dp)
                )
            }
        }
    }
}
