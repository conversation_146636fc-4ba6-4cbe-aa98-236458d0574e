<?php
require_once 'config/database.php';

echo "<h2>🔧 Complete Server Fix - One Click Solution</h2>";
echo "<p>This script will completely fix your server synchronization issue.</p>";

try {
    $db = new Database();
    $conn = $db->connect();
    
    echo "<h3>Step 1: 🗃️ Fixing Database Structure</h3>";
    
    // Check if embed_servers table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'embed_servers'");
    if ($stmt->rowCount() == 0) {
        echo "<p>❌ embed_servers table missing. Creating...</p>";
        
        $create_sql = "
        CREATE TABLE embed_servers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            movie_url TEXT NOT NULL,
            tv_url TEXT NOT NULL,
            priority INT DEFAULT 1,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $conn->exec($create_sql);
        echo "<p style='color: green;'>✅ Table created successfully!</p>";
    } else {
        echo "<p style='color: green;'>✅ embed_servers table exists</p>";
        
        // Check and add missing columns
        $columns_to_check = [
            'movie_url' => 'TEXT',
            'tv_url' => 'TEXT',
            'priority' => 'INT DEFAULT 1',
            'is_active' => 'BOOLEAN DEFAULT 1'
        ];
        
        foreach ($columns_to_check as $column => $type) {
            $stmt = $conn->query("SHOW COLUMNS FROM embed_servers LIKE '{$column}'");
            if ($stmt->rowCount() == 0) {
                try {
                    $conn->exec("ALTER TABLE embed_servers ADD COLUMN {$column} {$type}");
                    echo "<p style='color: green;'>✅ Added missing column: {$column}</p>";
                } catch (Exception $e) {
                    echo "<p style='color: orange;'>⚠️ Column {$column} issue: " . $e->getMessage() . "</p>";
                }
            }
        }
    }
    
    echo "<h3>Step 2: 🧹 Cleaning Existing Data</h3>";
    
    // Clear existing servers to avoid duplicates
    $stmt = $conn->query("SELECT COUNT(*) FROM embed_servers");
    $existing_count = $stmt->fetchColumn();
    
    if ($existing_count > 0) {
        echo "<p>Found {$existing_count} existing servers. Clearing for fresh setup...</p>";
        $conn->exec("DELETE FROM embed_servers");
        echo "<p style='color: green;'>✅ Cleared existing servers</p>";
    }
    
    echo "<h3>Step 3: 📥 Adding Standard Servers</h3>";
    
    // Insert the 4 standard servers
    $servers = [
        ['AutoEmbed', 'https://player.autoembed.cc/embed/movie/{id}', 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}', 1],
        ['VidJoy', 'https://vidjoy.pro/embed/movie/{id}', 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}', 2],
        ['VidZee', 'https://player.vidzee.wtf/embed/movie/{id}', 'https://player.vidzee.wtf/embed/tv/{id}/{season}/{episode}', 3],
        ['LetsEmbed', 'https://letsembed.cc/embed/movie/?id={id}', 'https://letsembed.cc/embed/tv/?id={id}/{season}/{episode}', 4]
    ];
    
    foreach ($servers as $server) {
        try {
            $stmt = $conn->prepare("
                INSERT INTO embed_servers (name, movie_url, tv_url, priority, is_active) 
                VALUES (?, ?, ?, ?, 1)
            ");
            $stmt->execute($server);
            echo "<p style='color: green;'>✅ Added: <strong>{$server[0]}</strong> (Priority: {$server[3]})</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Failed to add {$server[0]}: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h3>Step 4: 📊 Verification</h3>";
    
    // Verify the setup
    $stmt = $conn->query("SELECT * FROM embed_servers ORDER BY priority ASC");
    $final_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Total servers in database: " . count($final_servers) . "</strong></p>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Name</th><th>Priority</th><th>Status</th><th>Movie URL</th></tr>";
    
    foreach ($final_servers as $server) {
        $status = $server['is_active'] ? '✅ Active' : '❌ Inactive';
        $movie_url = substr($server['movie_url'], 0, 50) . '...';
        echo "<tr>";
        echo "<td>{$server['id']}</td>";
        echo "<td><strong>{$server['name']}</strong></td>";
        echo "<td>{$server['priority']}</td>";
        echo "<td>{$status}</td>";
        echo "<td><small>{$movie_url}</small></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Step 5: 🧪 Testing Player Function</h3>";
    
    // Test the getEmbedUrls function
    require_once 'includes/functions.php';
    $streamflix = new StreamFlix();
    
    echo "<h4>Testing Movie (Fight Club - TMDB ID: 550):</h4>";
    $movie_urls = $streamflix->getEmbedUrls('movie', 550);
    echo "<p>Player will show <strong>" . count($movie_urls) . " servers</strong>:</p>";
    
    echo "<ol>";
    foreach ($movie_urls as $url) {
        echo "<li><strong>{$url['name']}</strong> (Priority: {$url['priority']})</li>";
    }
    echo "</ol>";
    
    echo "<h4>Testing TV Show (Game of Thrones S1E1 - TMDB ID: 1399):</h4>";
    $tv_urls = $streamflix->getEmbedUrls('tv_show', 1399, 1, 1);
    echo "<p>Player will show <strong>" . count($tv_urls) . " servers</strong>:</p>";
    
    echo "<ol>";
    foreach ($tv_urls as $url) {
        echo "<li><strong>{$url['name']}</strong> (Priority: {$url['priority']})</li>";
    }
    echo "</ol>";
    
    // Final status
    if (count($movie_urls) == 4 && count($tv_urls) == 4) {
        echo "<div style='background: #d4edda; padding: 20px; border-left: 5px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724;'>🎉 SUCCESS!</h3>";
        echo "<p><strong>Your server synchronization is now fixed!</strong></p>";
        echo "<ul>";
        echo "<li>✅ Database has 4 servers</li>";
        echo "<li>✅ Player will show 4 servers</li>";
        echo "<li>✅ Admin panel changes will reflect in player</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; padding: 20px; border-left: 5px solid #ffc107; margin: 20px 0;'>";
        echo "<h3 style='color: #856404;'>⚠️ Partial Success</h3>";
        echo "<p>Database setup completed but player function needs attention.</p>";
        echo "</div>";
    }
    
    echo "<h3>Step 6: 📋 Final Instructions</h3>";
    
    echo "<div style='background: #e7f3ff; padding: 20px; border-left: 5px solid #007bff; margin: 20px 0;'>";
    echo "<h4 style='color: #004085;'>What to do now:</h4>";
    echo "<ol>";
    echo "<li><strong>Clear your browser cache completely</strong> (Ctrl+Shift+Delete)</li>";
    echo "<li><strong>Hard refresh</strong> any open player pages (Ctrl+F5)</li>";
    echo "<li><strong>Test a movie</strong> - you should see exactly 4 servers</li>";
    echo "<li><strong>Test admin panel</strong> - changes should now reflect in player</li>";
    echo "</ol>";
    
    echo "<h4 style='color: #004085;'>Admin Panel:</h4>";
    echo "<p><a href='admin/servers.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 Manage Servers</a></p>";
    echo "</div>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 20px 0;'>";
    echo "<h4>🗑️ Cleanup</h4>";
    echo "<p>You can now safely delete these temporary files:</p>";
    echo "<ul>";
    echo "<li>complete_server_fix.php (this file)</li>";
    echo "<li>fix_server_sync.php</li>";
    echo "<li>diagnose_server_issue.php</li>";
    echo "<li>check_table_structure.php</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-left: 5px solid #dc3545; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24;'>❌ Database Error</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration and try again.</p>";
    echo "</div>";
}
?>
