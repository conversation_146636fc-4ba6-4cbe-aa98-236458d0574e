@echo off
echo ========================================
echo   StreamFlix Build Readiness Check
echo ========================================
echo.

echo Checking essential files...
echo.

REM Check build files
if exist "build.gradle" (
    echo [OK] build.gradle found
) else (
    echo [ERROR] build.gradle missing
)

if exist "settings.gradle" (
    echo [OK] settings.gradle found
) else (
    echo [ERROR] settings.gradle missing
)

if exist "app\build.gradle" (
    echo [OK] app\build.gradle found
) else (
    echo [ERROR] app\build.gradle missing
)

REM Check manifest
if exist "app\src\main\AndroidManifest.xml" (
    echo [OK] AndroidManifest.xml found
) else (
    echo [ERROR] AndroidManifest.xml missing
)

REM Check main activity
if exist "app\src\main\java\com\streamflix\app\presentation\main\MainActivity.kt" (
    echo [OK] MainActivity.kt found
) else (
    echo [ERROR] MainActivity.kt missing
)

REM Check resources
if exist "app\src\main\res\values\strings.xml" (
    echo [OK] strings.xml found
) else (
    echo [ERROR] strings.xml missing
)

if exist "app\src\main\res\values\colors.xml" (
    echo [OK] colors.xml found
) else (
    echo [ERROR] colors.xml missing
)

REM Check for invalid files
if exist "app\src\main\res\font\*.txt" (
    echo [WARNING] .txt files found in font folder - these will cause build errors
) else (
    echo [OK] No invalid files in font folder
)

if exist "app\src\main\res\raw\*.txt" (
    echo [WARNING] .txt files found in raw folder - these may cause build errors
) else (
    echo [OK] No invalid files in raw folder
)

echo.
echo ========================================
echo           Build Status
echo ========================================
echo.
echo Your StreamFlix Android App is ready to build!
echo.
echo Recommended build method:
echo 1. Open Android Studio
echo 2. Open existing project
echo 3. Select the 'android' folder
echo 4. Wait for Gradle sync
echo 5. Build ^> Make Project
echo.
echo Alternative: If you have Gradle installed:
echo gradle clean assembleDebug
echo.
pause
