package com.streamflix.app.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

private val DarkColorScheme = darkColorScheme(
    primary = StreamFlixRed,
    onPrimary = TextPrimary,
    primaryContainer = StreamFlixRedDark,
    onPrimaryContainer = TextPrimary,
    
    secondary = StreamFlixGray,
    onSecondary = TextPrimary,
    secondaryContainer = StreamFlixGrayLight,
    onSecondaryContainer = TextPrimary,
    
    tertiary = AccentBlue,
    onTertiary = TextPrimary,
    tertiaryContainer = AccentBlue,
    onTertiaryContainer = TextPrimary,
    
    background = BackgroundDark,
    onBackground = TextPrimary,
    
    surface = SurfaceDark,
    onSurface = TextPrimary,
    surfaceVariant = CardDark,
    onSurfaceVariant = TextSecondary,
    
    surfaceTint = StreamFlixRed,
    inverseSurface = SurfaceLight,
    inverseOnSurface = TextPrimaryLight,
    
    error = ErrorColor,
    onError = TextPrimary,
    errorContainer = ErrorColor,
    onErrorContainer = TextPrimary,
    
    outline = BorderDark,
    outlineVariant = BorderDark,
    
    scrim = OverlayDark
)

private val LightColorScheme = lightColorScheme(
    primary = StreamFlixRed,
    onPrimary = TextPrimary,
    primaryContainer = StreamFlixRedLight,
    onPrimaryContainer = TextPrimaryLight,
    
    secondary = StreamFlixGray,
    onSecondary = TextPrimary,
    secondaryContainer = StreamFlixGrayLight,
    onSecondaryContainer = TextPrimary,
    
    tertiary = AccentBlue,
    onTertiary = TextPrimary,
    tertiaryContainer = AccentBlue,
    onTertiaryContainer = TextPrimary,
    
    background = BackgroundLight,
    onBackground = TextPrimaryLight,
    
    surface = SurfaceLight,
    onSurface = TextPrimaryLight,
    surfaceVariant = CardLight,
    onSurfaceVariant = TextSecondaryLight,
    
    surfaceTint = StreamFlixRed,
    inverseSurface = SurfaceDark,
    inverseOnSurface = TextPrimary,
    
    error = ErrorColor,
    onError = TextPrimary,
    errorContainer = ErrorColor,
    onErrorContainer = TextPrimary,
    
    outline = BorderLight,
    outlineVariant = BorderLight,
    
    scrim = OverlayLight
)

@Composable
fun StreamFlixTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = false,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }

        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }
    
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            window.statusBarColor = colorScheme.background.toArgb()
            window.navigationBarColor = colorScheme.background.toArgb()
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = !darkTheme
            WindowCompat.getInsetsController(window, view).isAppearanceLightNavigationBars = !darkTheme
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}

// Custom theme for video player (always dark)
@Composable
fun StreamFlixPlayerTheme(
    content: @Composable () -> Unit
) {
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            window.statusBarColor = PlayerBackground.toArgb()
            window.navigationBarColor = PlayerBackground.toArgb()
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = false
            WindowCompat.getInsetsController(window, view).isAppearanceLightNavigationBars = false
        }
    }

    MaterialTheme(
        colorScheme = DarkColorScheme.copy(
            background = PlayerBackground,
            surface = PlayerBackground
        ),
        typography = Typography,
        content = content
    )
}

// Custom theme for splash screen
@Composable
fun StreamFlixSplashTheme(
    content: @Composable () -> Unit
) {
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            window.statusBarColor = BackgroundDark.toArgb()
            window.navigationBarColor = BackgroundDark.toArgb()
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = false
            WindowCompat.getInsetsController(window, view).isAppearanceLightNavigationBars = false
        }
    }

    MaterialTheme(
        colorScheme = DarkColorScheme,
        typography = Typography,
        content = content
    )
}
