// StreamFlix Main JavaScript
class StreamFlix {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupScrollEffects();
        this.setupSearch();
        this.setupModals();
        this.setupPlayer();
    }

    setupEventListeners() {
        // Mobile menu toggle
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navLinks = document.querySelector('.nav-links');
        
        if (mobileMenuBtn) {
            mobileMenuBtn.addEventListener('click', () => {
                navLinks.classList.toggle('active');
            });
        }

        // Content card clicks
        document.addEventListener('click', (e) => {
            const contentCard = e.target.closest('.content-card');
            if (contentCard) {
                const contentId = contentCard.dataset.id;
                const contentType = contentCard.dataset.type;
                console.log('Card clicked:', contentId, contentType); // Debug log
                this.openContentModal(contentId, contentType);
            }
        });

        // Server selector
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('server-btn')) {
                this.switchServer(e.target);
            }
        });

        // Watchlist buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('watchlist-btn')) {
                this.toggleWatchlist(e.target);
            }
        });
    }

    setupScrollEffects() {
        const header = document.querySelector('.header');
        
        window.addEventListener('scroll', () => {
            if (window.scrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });

        // Infinite scroll for content
        window.addEventListener('scroll', () => {
            if ((window.innerHeight + window.scrollY) >= document.body.offsetHeight - 1000) {
                this.loadMoreContent();
            }
        });
    }

    setupSearch() {
        const searchInput = document.querySelector('.search-input');
        const searchResults = document.querySelector('.search-results');
        let searchTimeout;

        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                const query = e.target.value.trim();

                if (query.length > 2) {
                    searchTimeout = setTimeout(() => {
                        this.performSearch(query);
                    }, 300);
                } else {
                    this.hideSearchResults();
                }
            });

            // Hide search results when clicking outside
            document.addEventListener('click', (e) => {
                if (!e.target.closest('.search-box')) {
                    this.hideSearchResults();
                }
            });
        }
    }

    setupModals() {
        // Close modal when clicking outside
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal();
            }
        });

        // Close modal with close button
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('close')) {
                this.closeModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });
    }

    setupPlayer() {
        // Ad blocking for embedded players
        this.setupAdBlocker();
        
        // Player error handling
        this.setupPlayerErrorHandling();
    }

    setupAdBlocker() {
        // Block common ad domains and scripts
        const adDomains = [
            'googlesyndication.com',
            'doubleclick.net',
            'googletagmanager.com',
            'facebook.com/tr',
            'analytics.google.com',
            'google-analytics.com'
        ];

        // Override iframe src to add ad blocking parameters
        const originalCreateElement = document.createElement;
        document.createElement = function(tagName) {
            const element = originalCreateElement.call(document, tagName);
            
            if (tagName.toLowerCase() === 'iframe') {
                const originalSetAttribute = element.setAttribute;
                element.setAttribute = function(name, value) {
                    if (name === 'src' && value) {
                        // Add ad blocking parameters
                        const url = new URL(value);
                        url.searchParams.set('adblock', '1');
                        url.searchParams.set('ads', '0');
                        value = url.toString();
                    }
                    return originalSetAttribute.call(this, name, value);
                };
            }
            
            return element;
        };
    }

    setupPlayerErrorHandling() {
        document.addEventListener('error', (e) => {
            if (e.target.tagName === 'IFRAME') {
                this.handlePlayerError(e.target);
            }
        }, true);
    }

    handlePlayerError(iframe) {
        const playerContainer = iframe.closest('.player-container');
        if (playerContainer) {
            const errorMessage = document.createElement('div');
            errorMessage.className = 'player-error';
            errorMessage.innerHTML = `
                <div class="error-content">
                    <h3>Player Error</h3>
                    <p>Unable to load this server. Please try another server.</p>
                    <button class="btn btn-primary" onclick="location.reload()">Retry</button>
                </div>
            `;
            
            iframe.style.display = 'none';
            playerContainer.appendChild(errorMessage);
        }
    }

    async performSearch(query) {
        try {
            const response = await fetch(`api/search.php?q=${encodeURIComponent(query)}`);
            const data = await response.json();
            
            if (data.success) {
                this.displaySearchResults(data.results);
            }
        } catch (error) {
            console.error('Search error:', error);
        }
    }

    displaySearchResults(results) {
        const searchResults = document.querySelector('.search-results');
        if (!searchResults) return;

        if (results.length === 0) {
            searchResults.innerHTML = '<div class="no-results">No results found</div>';
        } else {
            const resultsHTML = results.map(item => `
                <div class="search-result-item" data-id="${item.id}" data-type="${item.type}">
                    <img src="${this.getImageUrl(item.poster_path, 'w92')}" alt="${item.title || item.name}">
                    <div class="result-info">
                        <h4>${item.title || item.name}</h4>
                        <p>${item.release_date || item.first_air_date || ''}</p>
                    </div>
                </div>
            `).join('');
            
            searchResults.innerHTML = resultsHTML;
        }
        
        searchResults.classList.add('visible');
    }

    hideSearchResults() {
        const searchResults = document.querySelector('.search-results');
        if (searchResults) {
            searchResults.classList.remove('visible');
        }
    }

    async openContentModal(contentId, contentType) {
        try {
            console.log('Opening modal for:', contentId, contentType); // Debug log
            const response = await fetch(`api/content.php?id=${contentId}&type=${contentType}`);
            console.log('Response status:', response.status); // Debug log

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Response data:', data); // Debug log

            if (data.success) {
                this.displayContentModal(data.content);
            } else {
                console.error('API error:', data.message);
                alert('Failed to load content details: ' + data.message);
            }
        } catch (error) {
            console.error('Content modal error:', error);
            alert('Failed to load content details. Please try again.');
        }
    }

    displayContentModal(content) {
        let modal = document.getElementById('contentModal');

        // Create modal if it doesn't exist
        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'contentModal';
            modal.className = 'modal';
            modal.innerHTML = `
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <div class="modal-body"></div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        const modalContent = modal.querySelector('.modal-body');

        const isTV = content.type === 'tv_show';
        const title = content.title || content.name;
        const releaseDate = content.release_date || content.first_air_date;

        modalContent.innerHTML = `
            <div class="content-details" style="display: flex; gap: 30px; align-items: flex-start;">
                <div class="content-poster" style="flex-shrink: 0;">
                    <img src="${this.getImageUrl(content.poster_path, 'w500')}" alt="${title}" style="width: 300px; border-radius: 8px;">
                </div>
                <div class="content-info" style="flex: 1;">
                    <h2 style="font-size: 2rem; margin-bottom: 15px; color: var(--text-primary);">${title}</h2>
                    <div class="content-meta" style="display: flex; gap: 20px; margin-bottom: 15px; color: var(--text-secondary);">
                        <span class="year">${new Date(releaseDate).getFullYear()}</span>
                        <span class="rating">★ ${content.vote_average}/10</span>
                        ${content.runtime ? `<span class="runtime">${this.formatRuntime(content.runtime)}</span>` : ''}
                    </div>
                    <p class="overview" style="line-height: 1.6; margin-bottom: 25px; color: var(--text-secondary);">${content.overview}</p>
                    <div class="action-buttons" style="display: flex; gap: 15px; margin-bottom: 20px;">
                        <button class="btn btn-primary play-btn" data-tmdb-id="${content.tmdb_id}" data-type="${content.type}">
                            ▶ Play
                        </button>
                        <button class="btn btn-secondary watchlist-btn" data-id="${content.id}" data-type="${content.type}">
                            + My List
                        </button>
                    </div>
                    ${isTV ? this.generateSeasonSelector(content) : ''}
                </div>
            </div>
        `;

        modal.style.display = 'block';

        // Setup play button
        const playBtn = modal.querySelector('.play-btn');
        playBtn.addEventListener('click', () => {
            const tmdbId = playBtn.dataset.tmdbId;
            const type = playBtn.dataset.type;
            this.playContent(tmdbId, type);
        });

        // Setup close button
        modal.querySelector('.close').addEventListener('click', () => {
            this.closeModal();
        });
    }

    generateSeasonSelector(tvShow) {
        if (!tvShow.seasons || tvShow.seasons.length === 0) {
            return '';
        }
        
        const seasonsHTML = tvShow.seasons.map(season => `
            <option value="${season.season_number}">Season ${season.season_number}</option>
        `).join('');
        
        return `
            <div class="season-selector">
                <label for="seasonSelect">Select Season:</label>
                <select id="seasonSelect" class="form-control">
                    ${seasonsHTML}
                </select>
            </div>
        `;
    }

    playContent(tmdbId, type) {
        const isTV = type === 'tv_show';
        let season = 1;
        let episode = 1;

        if (isTV) {
            const seasonSelect = document.getElementById('seasonSelect');
            season = seasonSelect ? seasonSelect.value : 1;
            // For now, default to episode 1. Later we can add episode selector
        }

        const playerUrl = `player.php?id=${tmdbId}&type=${type}${isTV ? `&season=${season}&episode=${episode}` : ''}`;
        console.log('Opening player:', playerUrl); // Debug log
        window.open(playerUrl, '_blank');
    }

    switchServer(button) {
        const serverButtons = document.querySelectorAll('.server-btn');
        const playerIframe = document.querySelector('.player-iframe');
        
        serverButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
        
        const serverUrl = button.dataset.url;
        if (playerIframe && serverUrl) {
            playerIframe.src = serverUrl;
        }
    }

    async toggleWatchlist(button) {
        const contentId = button.dataset.id;
        const contentType = button.dataset.type;
        
        try {
            const response = await fetch('api/watchlist.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content_id: contentId,
                    content_type: contentType,
                    action: 'toggle'
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                button.textContent = data.added ? '✓ In My List' : '+ My List';
                button.classList.toggle('added', data.added);
            }
        } catch (error) {
            console.error('Watchlist error:', error);
        }
    }

    async loadMoreContent() {
        // Implement infinite scroll loading
        const contentGrid = document.querySelector('.content-grid');
        if (!contentGrid || contentGrid.dataset.loading === 'true') return;
        
        contentGrid.dataset.loading = 'true';
        
        try {
            const page = parseInt(contentGrid.dataset.page || '1') + 1;
            const response = await fetch(`api/content.php?page=${page}`);
            const data = await response.json();
            
            if (data.success && data.content.length > 0) {
                data.content.forEach(item => {
                    const card = this.createContentCard(item);
                    contentGrid.appendChild(card);
                });
                
                contentGrid.dataset.page = page;
            }
        } catch (error) {
            console.error('Load more content error:', error);
        } finally {
            contentGrid.dataset.loading = 'false';
        }
    }

    createContentCard(content) {
        const card = document.createElement('div');
        card.className = 'content-card';
        card.dataset.id = content.id;
        card.dataset.type = content.type;
        
        const title = content.title || content.name;
        const releaseDate = content.release_date || content.first_air_date;
        
        card.innerHTML = `
            <img src="${this.getImageUrl(content.poster_path, 'w500')}" alt="${title}" loading="lazy">
            <div class="card-overlay">
                <div class="card-title">${title}</div>
                <div class="card-info">
                    <span>${new Date(releaseDate).getFullYear()}</span>
                    <div class="rating">
                        <span class="star">★</span>
                        <span>${content.vote_average}</span>
                    </div>
                </div>
            </div>
        `;
        
        return card;
    }

    closeModal() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.style.display = 'none';
        });
    }

    getImageUrl(path, size = 'w500') {
        if (!path) {
            return 'assets/images/no-image.jpg';
        }
        return `https://image.tmdb.org/t/p/${size}${path}`;
    }

    formatRuntime(minutes) {
        if (minutes < 60) {
            return `${minutes}m`;
        }
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return `${hours}h ${mins}m`;
    }
}

// Initialize StreamFlix when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new StreamFlix();
});

// Service Worker for offline functionality
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}
