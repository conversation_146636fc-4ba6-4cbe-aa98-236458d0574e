package com.streamflix.app.presentation.search

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.*
import androidx.compose.foundation.lazy.grid.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.streamflix.app.data.model.*
import com.streamflix.app.presentation.components.*
import com.streamflix.app.presentation.home.ContentItem
import com.streamflix.app.presentation.home.toContentItem
import com.streamflix.app.ui.theme.*
import com.streamflix.app.utils.Resource
import kotlinx.coroutines.delay

@Composable
fun AdvancedSearchScreen(
    onMovieClick: (Movie) -> Unit,
    onTvShowClick: (TvShow) -> Unit,
    onGenreClick: (Genre) -> Unit,
    viewModel: SearchViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val keyboardController = LocalSoftwareKeyboardController.current
    val focusRequester = remember { FocusRequester() }
    
    var searchQuery by remember { mutableStateOf("") }
    var isSearchActive by remember { mutableStateOf(false) }
    
    LaunchedEffect(Unit) {
        delay(300)
        focusRequester.requestFocus()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(BackgroundDark)
    ) {
        // Search Header
        SearchHeader(
            query = searchQuery,
            onQueryChange = { 
                searchQuery = it
                if (it.isNotEmpty()) {
                    viewModel.search(it)
                    isSearchActive = true
                } else {
                    isSearchActive = false
                }
            },
            onClearQuery = { 
                searchQuery = ""
                isSearchActive = false
                viewModel.clearSearch()
            },
            focusRequester = focusRequester,
            keyboardController = keyboardController
        )
        
        // Content based on search state
        if (isSearchActive) {
            // Search Results
            SearchResults(
                uiState = uiState,
                onMovieClick = onMovieClick,
                onTvShowClick = onTvShowClick,
                onRetry = { viewModel.search(searchQuery) }
            )
        } else {
            // Search Discovery
            SearchDiscovery(
                onGenreClick = onGenreClick,
                onTrendingClick = { /* Handle trending */ },
                onRecentSearchClick = { query ->
                    searchQuery = query
                    viewModel.search(query)
                    isSearchActive = true
                },
                viewModel = viewModel
            )
        }
    }
}

@Composable
fun SearchHeader(
    query: String,
    onQueryChange: (String) -> Unit,
    onClearQuery: () -> Unit,
    focusRequester: FocusRequester,
    keyboardController: androidx.compose.ui.platform.SoftwareKeyboardController?
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = SurfaceDark
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                Icons.Default.Search,
                contentDescription = "Search",
                tint = TextSecondary,
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            OutlinedTextField(
                value = query,
                onValueChange = onQueryChange,
                placeholder = {
                    Text(
                        text = "Search movies, TV shows...",
                        color = TextSecondary
                    )
                },
                modifier = Modifier
                    .weight(1f)
                    .focusRequester(focusRequester),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedTextColor = TextPrimary,
                    unfocusedTextColor = TextPrimary,
                    focusedBorderColor = StreamFlixRed,
                    unfocusedBorderColor = Color.Transparent,
                    cursorColor = StreamFlixRed
                ),
                keyboardOptions = KeyboardOptions(
                    imeAction = ImeAction.Search
                ),
                keyboardActions = KeyboardActions(
                    onSearch = {
                        keyboardController?.hide()
                    }
                ),
                singleLine = true
            )
            
            if (query.isNotEmpty()) {
                Spacer(modifier = Modifier.width(8.dp))
                IconButton(onClick = onClearQuery) {
                    Icon(
                        Icons.Default.Clear,
                        contentDescription = "Clear",
                        tint = TextSecondary
                    )
                }
            }
        }
    }
}

@Composable
fun SearchResults(
    uiState: SearchUiState,
    onMovieClick: (Movie) -> Unit,
    onTvShowClick: (TvShow) -> Unit,
    onRetry: () -> Unit
) {
    when (val resource = uiState.searchResults) {
        is Resource.Loading -> {
            SearchLoadingState()
        }
        is Resource.Success -> {
            val results = resource.data
            if (results != null && (results.movies.isNotEmpty() || results.tvShows.isNotEmpty())) {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(16.dp),
                    verticalArrangement = Arrangement.spacedBy(24.dp)
                ) {
                    // Search summary
                    item {
                        SearchSummary(
                            query = results.query,
                            totalResults = results.totalResults
                        )
                    }
                    
                    // Movies section
                    if (results.movies.isNotEmpty()) {
                        item {
                            Text(
                                text = "Movies (${results.movies.size})",
                                style = MaterialTheme.typography.titleLarge.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                color = TextPrimary,
                                modifier = Modifier.padding(bottom = 12.dp)
                            )
                        }
                        
                        item {
                            LazyRow(
                                horizontalArrangement = Arrangement.spacedBy(12.dp),
                                contentPadding = PaddingValues(horizontal = 4.dp)
                            ) {
                                items(results.movies) { movie ->
                                    ContentCard(
                                        item = movie.toContentItem(),
                                        onClick = { onMovieClick(movie) },
                                        modifier = Modifier.width(140.dp)
                                    )
                                }
                            }
                        }
                    }
                    
                    // TV Shows section
                    if (results.tvShows.isNotEmpty()) {
                        item {
                            Text(
                                text = "TV Shows (${results.tvShows.size})",
                                style = MaterialTheme.typography.titleLarge.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                color = TextPrimary,
                                modifier = Modifier.padding(bottom = 12.dp)
                            )
                        }
                        
                        item {
                            LazyRow(
                                horizontalArrangement = Arrangement.spacedBy(12.dp),
                                contentPadding = PaddingValues(horizontal = 4.dp)
                            ) {
                                items(results.tvShows) { tvShow ->
                                    ContentCard(
                                        item = tvShow.toContentItem(),
                                        onClick = { onTvShowClick(tvShow) },
                                        modifier = Modifier.width(140.dp)
                                    )
                                }
                            }
                        }
                    }
                }
            } else {
                SearchEmptyState(query = results?.query ?: "")
            }
        }
        is Resource.Error -> {
            SearchErrorState(
                message = resource.message ?: "Search failed",
                onRetry = onRetry
            )
        }
        null -> {
            // Initial state - show nothing or placeholder
        }
    }
}

@Composable
fun SearchDiscovery(
    onGenreClick: (Genre) -> Unit,
    onTrendingClick: () -> Unit,
    onRecentSearchClick: (String) -> Unit,
    viewModel: SearchViewModel
) {
    val genresState by viewModel.genresState.collectAsState()
    val recentSearches by viewModel.recentSearches.collectAsState()
    
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        // Recent searches
        if (recentSearches.isNotEmpty()) {
            item {
                RecentSearchesSection(
                    searches = recentSearches,
                    onSearchClick = onRecentSearchClick,
                    onClearAll = { viewModel.clearRecentSearches() }
                )
            }
        }
        
        // Trending searches
        item {
            TrendingSearchesSection(
                onTrendingClick = onTrendingClick
            )
        }
        
        // Browse by genre
        item {
            Text(
                text = "Browse by Genre",
                style = MaterialTheme.typography.titleLarge.copy(
                    fontWeight = FontWeight.Bold
                ),
                color = TextPrimary,
                modifier = Modifier.padding(bottom = 12.dp)
            )
        }
        
        when (val resource = genresState) {
            is Resource.Success -> {
                val genres = resource.data ?: emptyList()
                items(genres.chunked(2)) { genreRow ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        genreRow.forEach { genre ->
                            GenreCard(
                                genre = genre,
                                onClick = { onGenreClick(genre) },
                                modifier = Modifier.weight(1f)
                            )
                        }
                        // Fill remaining space if odd number of genres
                        if (genreRow.size == 1) {
                            Spacer(modifier = Modifier.weight(1f))
                        }
                    }
                }
            }
            is Resource.Loading -> {
                item {
                    GenresLoadingState()
                }
            }
            is Resource.Error -> {
                item {
                    ErrorMessage(
                        message = resource.message ?: "Failed to load genres",
                        onRetry = { viewModel.loadGenres() }
                    )
                }
            }
        }
    }
}

@Composable
fun SearchSummary(
    query: String,
    totalResults: Int
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = SurfaceDark
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "Search Results",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    color = TextPrimary
                )
                Text(
                    text = "\"$query\"",
                    style = MaterialTheme.typography.bodyMedium,
                    color = TextSecondary
                )
            }
            
            Text(
                text = "$totalResults results",
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.Medium
                ),
                color = StreamFlixRed
            )
        }
    }
}

@Composable
fun RecentSearchesSection(
    searches: List<String>,
    onSearchClick: (String) -> Unit,
    onClearAll: () -> Unit
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Recent Searches",
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.Bold
                ),
                color = TextPrimary
            )
            
            TextButton(onClick = onClearAll) {
                Text(
                    text = "Clear All",
                    color = StreamFlixRed,
                    fontSize = 12.sp
                )
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(searches) { search ->
                RecentSearchChip(
                    search = search,
                    onClick = { onSearchClick(search) }
                )
            }
        }
    }
}

@Composable
fun RecentSearchChip(
    search: String,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier.clickable { onClick() },
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(
            containerColor = SurfaceDark
        )
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            Icon(
                Icons.Default.Settings,
                contentDescription = null,
                tint = TextSecondary,
                modifier = Modifier.size(16.dp)
            )
            Text(
                text = search,
                color = TextPrimary,
                fontSize = 14.sp
            )
        }
    }
}

@Composable
fun TrendingSearchesSection(
    onTrendingClick: () -> Unit
) {
    val trendingSearches = listOf(
        "Action Movies", "Netflix Series", "Marvel", "Horror", "Comedy",
        "Sci-Fi", "Romance", "Thriller", "Documentary", "Anime"
    )
    
    Column {
        Text(
            text = "Trending Searches",
            style = MaterialTheme.typography.titleMedium.copy(
                fontWeight = FontWeight.Bold
            ),
            color = TextPrimary,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyVerticalGrid(
            columns = GridCells.Fixed(2),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.height(200.dp)
        ) {
            items(trendingSearches) { trending ->
                TrendingSearchCard(
                    text = trending,
                    onClick = onTrendingClick
                )
            }
        }
    }
}

@Composable
fun TrendingSearchCard(
    text: String,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = SurfaceDark
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = text,
                color = TextPrimary,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun GenreCard(
    genre: Genre,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .height(60.dp)
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = GenreColors[genre.id % GenreColors.size]
        )
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = genre.name,
                color = Color.White,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun SearchLoadingState() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator(
                color = StreamFlixRed,
                strokeWidth = 3.dp
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "Searching...",
                color = TextSecondary,
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}

@Composable
fun SearchEmptyState(query: String) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(32.dp)
        ) {
            Icon(
                Icons.Default.Search,
                contentDescription = null,
                tint = TextSecondary,
                modifier = Modifier.size(64.dp)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "No results found",
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.Bold
                ),
                color = TextPrimary,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "Try searching for \"$query\" with different keywords",
                style = MaterialTheme.typography.bodyMedium,
                color = TextSecondary,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun SearchErrorState(
    message: String,
    onRetry: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(32.dp)
        ) {
            Icon(
                Icons.Default.Warning,
                contentDescription = null,
                tint = ErrorColor,
                modifier = Modifier.size(64.dp)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "Search Error",
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.Bold
                ),
                color = TextPrimary,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                color = TextSecondary,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(16.dp))
            Button(
                onClick = onRetry,
                colors = ButtonDefaults.buttonColors(
                    containerColor = StreamFlixRed
                )
            ) {
                Text("Retry")
            }
        }
    }
}
