package com.streamflix.app.presentation.search;

import androidx.compose.animation.*;
import androidx.compose.animation.core.*;
import androidx.compose.foundation.*;
import androidx.compose.foundation.layout.*;
import androidx.compose.foundation.lazy.*;
import androidx.compose.foundation.lazy.grid.*;
import androidx.compose.foundation.text.KeyboardOptions;
import androidx.compose.material.icons.Icons;
import androidx.compose.material.icons.filled.*;
import androidx.compose.material.icons.outlined.*;
import androidx.compose.material3.*;
import androidx.compose.runtime.*;
import androidx.compose.ui.Alignment;
import androidx.compose.ui.Modifier;
import androidx.compose.ui.focus.FocusRequester;
import androidx.compose.ui.text.font.FontWeight;
import androidx.compose.ui.text.input.ImeAction;
import androidx.compose.ui.text.style.TextAlign;
import com.streamflix.app.data.model.*;
import com.streamflix.app.presentation.components.*;
import com.streamflix.app.presentation.home.ContentItem;
import com.streamflix.app.ui.theme.*;
import com.streamflix.app.utils.Resource;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000b\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\u001aN\u0010\u0000\u001a\u00020\u00012\u0012\u0010\u0002\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u00032\u0012\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00032\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\t\u001a\u00020\nH\u0007\u001a(\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\b2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u0010H\u0007\u001a\u001e\u0010\u0011\u001a\u00020\u00012\u0006\u0010\u0012\u001a\u00020\u00132\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u000eH\u0007\u001a8\u0010\u0014\u001a\u00020\u00012\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00130\u00162\u0012\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00010\u000eH\u0007\u001aF\u0010\u0019\u001a\u00020\u00012\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00010\u000e2\u0012\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00010\u00032\u0006\u0010\t\u001a\u00020\nH\u0007\u001a\u0010\u0010\u001c\u001a\u00020\u00012\u0006\u0010\u001d\u001a\u00020\u0013H\u0007\u001a\u001e\u0010\u001e\u001a\u00020\u00012\u0006\u0010\u001f\u001a\u00020\u00132\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00010\u000eH\u0007\u001aD\u0010!\u001a\u00020\u00012\u0006\u0010\u001d\u001a\u00020\u00132\u0012\u0010\"\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00010\u000e2\u0006\u0010$\u001a\u00020%2\b\u0010&\u001a\u0004\u0018\u00010\'H\u0007\u001a\b\u0010(\u001a\u00020\u0001H\u0007\u001aF\u0010)\u001a\u00020\u00012\u0006\u0010*\u001a\u00020+2\u0012\u0010\u0002\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u00032\u0012\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00010\u000eH\u0007\u001a\u0018\u0010,\u001a\u00020\u00012\u0006\u0010\u001d\u001a\u00020\u00132\u0006\u0010-\u001a\u00020.H\u0007\u001a\u001e\u0010/\u001a\u00020\u00012\u0006\u00100\u001a\u00020\u00132\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u000eH\u0007\u001a\u0016\u00101\u001a\u00020\u00012\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00010\u000eH\u0007\u00a8\u00062"}, d2 = {"AdvancedSearchScreen", "", "onMovieClick", "Lkotlin/Function1;", "Lcom/streamflix/app/data/model/Movie;", "onTvShowClick", "Lcom/streamflix/app/data/model/TvShow;", "onGenreClick", "Lcom/streamflix/app/data/model/Genre;", "viewModel", "Lcom/streamflix/app/presentation/search/SearchViewModel;", "GenreCard", "genre", "onClick", "Lkotlin/Function0;", "modifier", "Landroidx/compose/ui/Modifier;", "RecentSearchChip", "search", "", "RecentSearchesSection", "searches", "", "onSearchClick", "onClearAll", "SearchDiscovery", "onTrendingClick", "onRecentSearchClick", "SearchEmptyState", "query", "SearchErrorState", "message", "onRetry", "SearchHeader", "onQueryChange", "onClearQuery", "focusRequester", "Landroidx/compose/ui/focus/FocusRequester;", "keyboardController", "Landroidx/compose/ui/platform/SoftwareKeyboardController;", "SearchLoadingState", "SearchResults", "uiState", "Lcom/streamflix/app/presentation/search/SearchUiState;", "SearchSummary", "totalResults", "", "TrendingSearchCard", "text", "TrendingSearchesSection", "app_debug"})
public final class AdvancedSearchScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void AdvancedSearchScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.streamflix.app.data.model.Movie, kotlin.Unit> onMovieClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.streamflix.app.data.model.TvShow, kotlin.Unit> onTvShowClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.streamflix.app.data.model.Genre, kotlin.Unit> onGenreClick, @org.jetbrains.annotations.NotNull()
    com.streamflix.app.presentation.search.SearchViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SearchHeader(@org.jetbrains.annotations.NotNull()
    java.lang.String query, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onQueryChange, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClearQuery, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.focus.FocusRequester focusRequester, @org.jetbrains.annotations.Nullable()
    androidx.compose.ui.platform.SoftwareKeyboardController keyboardController) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SearchResults(@org.jetbrains.annotations.NotNull()
    com.streamflix.app.presentation.search.SearchUiState uiState, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.streamflix.app.data.model.Movie, kotlin.Unit> onMovieClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.streamflix.app.data.model.TvShow, kotlin.Unit> onTvShowClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRetry) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SearchDiscovery(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.streamflix.app.data.model.Genre, kotlin.Unit> onGenreClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onTrendingClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onRecentSearchClick, @org.jetbrains.annotations.NotNull()
    com.streamflix.app.presentation.search.SearchViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SearchSummary(@org.jetbrains.annotations.NotNull()
    java.lang.String query, int totalResults) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void RecentSearchesSection(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> searches, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onSearchClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClearAll) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void RecentSearchChip(@org.jetbrains.annotations.NotNull()
    java.lang.String search, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TrendingSearchesSection(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onTrendingClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TrendingSearchCard(@org.jetbrains.annotations.NotNull()
    java.lang.String text, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void GenreCard(@org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.model.Genre genre, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SearchLoadingState() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SearchEmptyState(@org.jetbrains.annotations.NotNull()
    java.lang.String query) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SearchErrorState(@org.jetbrains.annotations.NotNull()
    java.lang.String message, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRetry) {
    }
}