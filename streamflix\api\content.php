<?php
require_once '../includes/functions.php';

header('Content-Type: application/json');

$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$type = isset($_GET['type']) ? sanitizeInput($_GET['type']) : 'movie';

if (!$id) {
    echo json_encode(['success' => false, 'message' => 'Invalid ID']);
    exit();
}

try {
    $db = new Database();
    $conn = $db->connect();
    
    if ($type === 'movie') {
        // Get movie details
        $stmt = $conn->prepare("
            SELECT m.*, GROUP_CONCAT(g.name) as genres
            FROM movies m
            LEFT JOIN movie_genres mg ON m.id = mg.movie_id
            LEFT JOIN genres g ON mg.genre_id = g.id
            WHERE m.id = :id
            GROUP BY m.id
        ");
        $stmt->execute([':id' => $id]);
        $content = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($content) {
            $content['type'] = 'movie';
            $content['genres'] = $content['genres'] ? explode(',', $content['genres']) : [];
        }
        
    } else {
        // Get TV show details with seasons
        $stmt = $conn->prepare("
            SELECT t.*, GROUP_CONCAT(g.name) as genres
            FROM tv_shows t
            LEFT JOIN tv_show_genres tg ON t.id = tg.tv_show_id
            LEFT JOIN genres g ON tg.genre_id = g.id
            WHERE t.id = :id
            GROUP BY t.id
        ");
        $stmt->execute([':id' => $id]);
        $content = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($content) {
            $content['type'] = 'tv_show';
            $content['genres'] = $content['genres'] ? explode(',', $content['genres']) : [];
            
            // Get seasons
            $stmt = $conn->prepare("
                SELECT s.*, COUNT(e.id) as episode_count
                FROM seasons s
                LEFT JOIN episodes e ON s.id = e.season_id
                WHERE s.tv_show_id = :tv_show_id
                GROUP BY s.id
                ORDER BY s.season_number
            ");
            $stmt->execute([':tv_show_id' => $content['id']]);
            $content['seasons'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    }
    
    if ($content) {
        echo json_encode([
            'success' => true,
            'content' => $content
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Content not found'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
