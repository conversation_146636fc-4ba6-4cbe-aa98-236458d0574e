<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    
    <!-- Background -->
    <path
        android:fillColor="#000000"
        android:pathData="M0,0h108v108h-108z"/>
    
    <!-- StreamFlix Red Circle -->
    <path
        android:fillColor="#E50914"
        android:pathData="M54,54m-40,0a40,40 0,1 1,80 0a40,40 0,1 1,-80 0"/>
    
    <!-- Inner Dark Circle -->
    <path
        android:fillColor="#000000"
        android:pathData="M54,54m-35,0a35,35 0,1 1,70 0a35,35 0,1 1,-70 0"/>
</vector>
