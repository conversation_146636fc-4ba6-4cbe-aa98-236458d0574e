package com.streamflix.app.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0012\n\u0000\n\u0002\u0018\u0002\n\u0002\b*\n\u0002\u0010 \n\u0002\b[\"\u0013\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0002\u0010\u0003\"\u0013\u0010\u0005\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0006\u0010\u0003\"\u0013\u0010\u0007\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\b\u0010\u0003\"\u0013\u0010\t\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\n\u0010\u0003\"\u0013\u0010\u000b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\f\u0010\u0003\"\u0013\u0010\r\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u000e\u0010\u0003\"\u0013\u0010\u000f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0010\u0010\u0003\"\u0013\u0010\u0011\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0012\u0010\u0003\"\u0013\u0010\u0013\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0014\u0010\u0003\"\u0013\u0010\u0015\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0016\u0010\u0003\"\u0013\u0010\u0017\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0018\u0010\u0003\"\u0013\u0010\u0019\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001a\u0010\u0003\"\u0013\u0010\u001b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001c\u0010\u0003\"\u0013\u0010\u001d\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001e\u0010\u0003\"\u0013\u0010\u001f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b \u0010\u0003\"\u0013\u0010!\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\"\u0010\u0003\"\u0013\u0010#\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b$\u0010\u0003\"\u0013\u0010%\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b&\u0010\u0003\"\u0013\u0010\'\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b(\u0010\u0003\"\u0013\u0010)\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b*\u0010\u0003\"\u0017\u0010+\u001a\b\u0012\u0004\u0012\u00020\u00010,\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010.\"\u0013\u0010/\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b0\u0010\u0003\"\u0013\u00101\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b2\u0010\u0003\"\u0013\u00103\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b4\u0010\u0003\"\u0013\u00105\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b6\u0010\u0003\"\u0013\u00107\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b8\u0010\u0003\"\u0013\u00109\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b:\u0010\u0003\"\u0013\u0010;\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b<\u0010\u0003\"\u0013\u0010=\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b>\u0010\u0003\"\u0013\u0010?\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b@\u0010\u0003\"\u0013\u0010A\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bB\u0010\u0003\"\u0013\u0010C\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bD\u0010\u0003\"\u0013\u0010E\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bF\u0010\u0003\"\u0013\u0010G\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bH\u0010\u0003\"\u0013\u0010I\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bJ\u0010\u0003\"\u0013\u0010K\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bL\u0010\u0003\"\u0013\u0010M\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bN\u0010\u0003\"\u0013\u0010O\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bP\u0010\u0003\"\u0013\u0010Q\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bR\u0010\u0003\"\u0013\u0010S\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bT\u0010\u0003\"\u0013\u0010U\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bV\u0010\u0003\"\u0013\u0010W\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bX\u0010\u0003\"\u0013\u0010Y\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bZ\u0010\u0003\"\u0013\u0010[\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\\\u0010\u0003\"\u0013\u0010]\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b^\u0010\u0003\"\u0013\u0010_\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b`\u0010\u0003\"\u0013\u0010a\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bb\u0010\u0003\"\u0017\u0010c\u001a\b\u0012\u0004\u0012\u00020\u00010,\u00a2\u0006\b\n\u0000\u001a\u0004\bd\u0010.\"\u0017\u0010e\u001a\b\u0012\u0004\u0012\u00020\u00010,\u00a2\u0006\b\n\u0000\u001a\u0004\bf\u0010.\"\u0013\u0010g\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bh\u0010\u0003\"\u0013\u0010i\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bj\u0010\u0003\"\u0013\u0010k\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bl\u0010\u0003\"\u0013\u0010m\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bn\u0010\u0003\"\u0013\u0010o\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bp\u0010\u0003\"\u0013\u0010q\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\br\u0010\u0003\"\u0013\u0010s\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bt\u0010\u0003\"\u0013\u0010u\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bv\u0010\u0003\"\u0013\u0010w\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bx\u0010\u0003\"\u0013\u0010y\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bz\u0010\u0003\"\u0013\u0010{\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b|\u0010\u0003\"\u0013\u0010}\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b~\u0010\u0003\"\u0014\u0010\u007f\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u0080\u0001\u0010\u0003\"\u0015\u0010\u0081\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u0082\u0001\u0010\u0003\"\u0015\u0010\u0083\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u0084\u0001\u0010\u0003\"\u0015\u0010\u0085\u0001\u001a\u00020\u0001\u00a2\u0006\u000b\n\u0002\u0010\u0004\u001a\u0005\b\u0086\u0001\u0010\u0003\u00a8\u0006\u0087\u0001"}, d2 = {"AccentBlue", "Landroidx/compose/ui/graphics/Color;", "getAccentBlue", "()J", "J", "AccentGreen", "getAccentGreen", "AccentOrange", "getAccentOrange", "AccentYellow", "getAccentYellow", "BackgroundDark", "getBackgroundDark", "BackgroundLight", "getBackgroundLight", "BorderDark", "getBorderDark", "BorderLight", "getBorderLight", "ButtonDisabled", "getButtonDisabled", "ButtonPrimary", "getButtonPrimary", "ButtonSecondary", "getButtonSecondary", "CardBorder", "getCardBorder", "CardDark", "getCardDark", "CardLight", "getCardLight", "DownloadCompleted", "getDownloadCompleted", "DownloadError", "getDownloadError", "DownloadPaused", "getDownloadPaused", "DownloadPending", "getDownloadPending", "DownloadProgress", "getDownloadProgress", "ErrorColor", "getErrorColor", "GenreColors", "", "getGenreColors", "()Ljava/util/List;", "GradientEnd", "getGradientEnd", "GradientStart", "getGradientStart", "IconDisabled", "getIconDisabled", "IconPrimary", "getIconPrimary", "IconSecondary", "getIconSecondary", "InfoColor", "getInfoColor", "LiveIndicator", "getLiveIndicator", "NewBadge", "getNewBadge", "OverlayDark", "getOverlayDark", "OverlayLight", "getOverlayLight", "PlayerBackground", "getPlayerBackground", "PlayerControls", "getPlayerControls", "PlayerSeekBar", "getPlayerSeekBar", "PlayerSeekBarBackground", "getPlayerSeekBarBackground", "PremiumGold", "getPremiumGold", "ProgressBackground", "getProgressBackground", "ProgressForeground", "getProgressForeground", "ProgressWatched", "getProgressWatched", "Quality4K", "getQuality4K", "QualityCAM", "getQualityCAM", "QualityFHD", "getQualityFHD", "QualityHD", "getQualityHD", "RatingAverage", "getRatingAverage", "RatingExcellent", "getRatingExcellent", "RatingGood", "getRatingGood", "RatingPoor", "getRatingPoor", "ShimmerColorsDark", "getShimmerColorsDark", "ShimmerColorsLight", "getShimmerColorsLight", "StreamFlixGray", "getStreamFlixGray", "StreamFlixGrayDark", "getStreamFlixGrayDark", "StreamFlixGrayLight", "getStreamFlixGrayLight", "StreamFlixRed", "getStreamFlixRed", "StreamFlixRedDark", "getStreamFlixRedDark", "StreamFlixRedLight", "getStreamFlixRedLight", "SuccessColor", "getSuccessColor", "SurfaceDark", "getSurfaceDark", "SurfaceLight", "getSurfaceLight", "TextPrimary", "getTextPrimary", "TextPrimaryLight", "getTextPrimaryLight", "TextSecondary", "getTextSecondary", "TextSecondaryLight", "getTextSecondaryLight", "TextTertiary", "getTextTertiary", "TrendingBadge", "getTrendingBadge", "WarningColor", "getWarningColor", "app_debug"})
public final class ColorKt {
    private static final long StreamFlixRed = 0L;
    private static final long StreamFlixRedDark = 0L;
    private static final long StreamFlixRedLight = 0L;
    private static final long StreamFlixGray = 0L;
    private static final long StreamFlixGrayLight = 0L;
    private static final long StreamFlixGrayDark = 0L;
    private static final long BackgroundDark = 0L;
    private static final long BackgroundLight = 0L;
    private static final long SurfaceDark = 0L;
    private static final long SurfaceLight = 0L;
    private static final long TextPrimary = 0L;
    private static final long TextSecondary = 0L;
    private static final long TextTertiary = 0L;
    private static final long TextPrimaryLight = 0L;
    private static final long TextSecondaryLight = 0L;
    private static final long AccentGreen = 0L;
    private static final long AccentBlue = 0L;
    private static final long AccentYellow = 0L;
    private static final long AccentOrange = 0L;
    private static final long SuccessColor = 0L;
    private static final long ErrorColor = 0L;
    private static final long WarningColor = 0L;
    private static final long InfoColor = 0L;
    private static final long RatingExcellent = 0L;
    private static final long RatingGood = 0L;
    private static final long RatingAverage = 0L;
    private static final long RatingPoor = 0L;
    private static final long GradientStart = 0L;
    private static final long GradientEnd = 0L;
    private static final long CardDark = 0L;
    private static final long CardLight = 0L;
    private static final long CardBorder = 0L;
    private static final long ButtonPrimary = 0L;
    private static final long ButtonSecondary = 0L;
    private static final long ButtonDisabled = 0L;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<androidx.compose.ui.graphics.Color> ShimmerColorsDark = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<androidx.compose.ui.graphics.Color> ShimmerColorsLight = null;
    private static final long QualityHD = 0L;
    private static final long QualityFHD = 0L;
    private static final long Quality4K = 0L;
    private static final long QualityCAM = 0L;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<androidx.compose.ui.graphics.Color> GenreColors = null;
    private static final long ProgressBackground = 0L;
    private static final long ProgressForeground = 0L;
    private static final long ProgressWatched = 0L;
    private static final long OverlayDark = 0L;
    private static final long OverlayLight = 0L;
    private static final long BorderDark = 0L;
    private static final long BorderLight = 0L;
    private static final long IconPrimary = 0L;
    private static final long IconSecondary = 0L;
    private static final long IconDisabled = 0L;
    private static final long PremiumGold = 0L;
    private static final long LiveIndicator = 0L;
    private static final long NewBadge = 0L;
    private static final long TrendingBadge = 0L;
    private static final long PlayerBackground = 0L;
    private static final long PlayerControls = 0L;
    private static final long PlayerSeekBar = 0L;
    private static final long PlayerSeekBarBackground = 0L;
    private static final long DownloadPending = 0L;
    private static final long DownloadProgress = 0L;
    private static final long DownloadCompleted = 0L;
    private static final long DownloadError = 0L;
    private static final long DownloadPaused = 0L;
    
    public static final long getStreamFlixRed() {
        return 0L;
    }
    
    public static final long getStreamFlixRedDark() {
        return 0L;
    }
    
    public static final long getStreamFlixRedLight() {
        return 0L;
    }
    
    public static final long getStreamFlixGray() {
        return 0L;
    }
    
    public static final long getStreamFlixGrayLight() {
        return 0L;
    }
    
    public static final long getStreamFlixGrayDark() {
        return 0L;
    }
    
    public static final long getBackgroundDark() {
        return 0L;
    }
    
    public static final long getBackgroundLight() {
        return 0L;
    }
    
    public static final long getSurfaceDark() {
        return 0L;
    }
    
    public static final long getSurfaceLight() {
        return 0L;
    }
    
    public static final long getTextPrimary() {
        return 0L;
    }
    
    public static final long getTextSecondary() {
        return 0L;
    }
    
    public static final long getTextTertiary() {
        return 0L;
    }
    
    public static final long getTextPrimaryLight() {
        return 0L;
    }
    
    public static final long getTextSecondaryLight() {
        return 0L;
    }
    
    public static final long getAccentGreen() {
        return 0L;
    }
    
    public static final long getAccentBlue() {
        return 0L;
    }
    
    public static final long getAccentYellow() {
        return 0L;
    }
    
    public static final long getAccentOrange() {
        return 0L;
    }
    
    public static final long getSuccessColor() {
        return 0L;
    }
    
    public static final long getErrorColor() {
        return 0L;
    }
    
    public static final long getWarningColor() {
        return 0L;
    }
    
    public static final long getInfoColor() {
        return 0L;
    }
    
    public static final long getRatingExcellent() {
        return 0L;
    }
    
    public static final long getRatingGood() {
        return 0L;
    }
    
    public static final long getRatingAverage() {
        return 0L;
    }
    
    public static final long getRatingPoor() {
        return 0L;
    }
    
    public static final long getGradientStart() {
        return 0L;
    }
    
    public static final long getGradientEnd() {
        return 0L;
    }
    
    public static final long getCardDark() {
        return 0L;
    }
    
    public static final long getCardLight() {
        return 0L;
    }
    
    public static final long getCardBorder() {
        return 0L;
    }
    
    public static final long getButtonPrimary() {
        return 0L;
    }
    
    public static final long getButtonSecondary() {
        return 0L;
    }
    
    public static final long getButtonDisabled() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<androidx.compose.ui.graphics.Color> getShimmerColorsDark() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<androidx.compose.ui.graphics.Color> getShimmerColorsLight() {
        return null;
    }
    
    public static final long getQualityHD() {
        return 0L;
    }
    
    public static final long getQualityFHD() {
        return 0L;
    }
    
    public static final long getQuality4K() {
        return 0L;
    }
    
    public static final long getQualityCAM() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<androidx.compose.ui.graphics.Color> getGenreColors() {
        return null;
    }
    
    public static final long getProgressBackground() {
        return 0L;
    }
    
    public static final long getProgressForeground() {
        return 0L;
    }
    
    public static final long getProgressWatched() {
        return 0L;
    }
    
    public static final long getOverlayDark() {
        return 0L;
    }
    
    public static final long getOverlayLight() {
        return 0L;
    }
    
    public static final long getBorderDark() {
        return 0L;
    }
    
    public static final long getBorderLight() {
        return 0L;
    }
    
    public static final long getIconPrimary() {
        return 0L;
    }
    
    public static final long getIconSecondary() {
        return 0L;
    }
    
    public static final long getIconDisabled() {
        return 0L;
    }
    
    public static final long getPremiumGold() {
        return 0L;
    }
    
    public static final long getLiveIndicator() {
        return 0L;
    }
    
    public static final long getNewBadge() {
        return 0L;
    }
    
    public static final long getTrendingBadge() {
        return 0L;
    }
    
    public static final long getPlayerBackground() {
        return 0L;
    }
    
    public static final long getPlayerControls() {
        return 0L;
    }
    
    public static final long getPlayerSeekBar() {
        return 0L;
    }
    
    public static final long getPlayerSeekBarBackground() {
        return 0L;
    }
    
    public static final long getDownloadPending() {
        return 0L;
    }
    
    public static final long getDownloadProgress() {
        return 0L;
    }
    
    public static final long getDownloadCompleted() {
        return 0L;
    }
    
    public static final long getDownloadError() {
        return 0L;
    }
    
    public static final long getDownloadPaused() {
        return 0L;
    }
}