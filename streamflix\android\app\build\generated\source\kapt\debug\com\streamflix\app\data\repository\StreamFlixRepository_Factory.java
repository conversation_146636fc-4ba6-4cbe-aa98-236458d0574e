// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.streamflix.app.data.repository;

import com.streamflix.app.data.api.StreamFlixApiService;
import com.streamflix.app.data.local.UserPreferences;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class StreamFlixRepository_Factory implements Factory<StreamFlixRepository> {
  private final Provider<StreamFlixApiService> apiServiceProvider;

  private final Provider<UserPreferences> userPreferencesProvider;

  public StreamFlixRepository_Factory(Provider<StreamFlixApiService> apiServiceProvider,
      Provider<UserPreferences> userPreferencesProvider) {
    this.apiServiceProvider = apiServiceProvider;
    this.userPreferencesProvider = userPreferencesProvider;
  }

  @Override
  public StreamFlixRepository get() {
    return newInstance(apiServiceProvider.get(), userPreferencesProvider.get());
  }

  public static StreamFlixRepository_Factory create(
      Provider<StreamFlixApiService> apiServiceProvider,
      Provider<UserPreferences> userPreferencesProvider) {
    return new StreamFlixRepository_Factory(apiServiceProvider, userPreferencesProvider);
  }

  public static StreamFlixRepository newInstance(StreamFlixApiService apiService,
      UserPreferences userPreferences) {
    return new StreamFlixRepository(apiService, userPreferences);
  }
}
