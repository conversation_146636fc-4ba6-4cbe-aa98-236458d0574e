// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.streamflix.app.data.offline;

import android.content.Context;
import com.google.gson.Gson;
import com.streamflix.app.data.local.UserPreferences;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class OfflineModeManager_Factory implements Factory<OfflineModeManager> {
  private final Provider<Context> contextProvider;

  private final Provider<OfflineDatabase> offlineDatabaseProvider;

  private final Provider<UserPreferences> userPreferencesProvider;

  private final Provider<Gson> gsonProvider;

  public OfflineModeManager_Factory(Provider<Context> contextProvider,
      Provider<OfflineDatabase> offlineDatabaseProvider,
      Provider<UserPreferences> userPreferencesProvider, Provider<Gson> gsonProvider) {
    this.contextProvider = contextProvider;
    this.offlineDatabaseProvider = offlineDatabaseProvider;
    this.userPreferencesProvider = userPreferencesProvider;
    this.gsonProvider = gsonProvider;
  }

  @Override
  public OfflineModeManager get() {
    return newInstance(contextProvider.get(), offlineDatabaseProvider.get(), userPreferencesProvider.get(), gsonProvider.get());
  }

  public static OfflineModeManager_Factory create(Provider<Context> contextProvider,
      Provider<OfflineDatabase> offlineDatabaseProvider,
      Provider<UserPreferences> userPreferencesProvider, Provider<Gson> gsonProvider) {
    return new OfflineModeManager_Factory(contextProvider, offlineDatabaseProvider, userPreferencesProvider, gsonProvider);
  }

  public static OfflineModeManager newInstance(Context context, OfflineDatabase offlineDatabase,
      UserPreferences userPreferences, Gson gson) {
    return new OfflineModeManager(context, offlineDatabase, userPreferences, gson);
  }
}
