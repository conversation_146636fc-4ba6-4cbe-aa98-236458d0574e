package com.streamflix.app.presentation.player;

import androidx.compose.foundation.layout.*;
import androidx.compose.material.icons.Icons;
import androidx.compose.material.icons.filled.*;
import androidx.compose.material.icons.outlined.*;
import androidx.compose.material3.*;
import androidx.compose.runtime.*;
import androidx.compose.ui.Alignment;
import androidx.compose.ui.Modifier;
import androidx.compose.ui.text.font.FontWeight;
import com.streamflix.app.data.model.Server;
import com.streamflix.app.ui.theme.*;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000@\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\b\u001a@\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a\b\u0010\u0007\u001a\u00020\u0001H\u0007\u001a\u0010\u0010\b\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\nH\u0007\u001a&\u0010\u000b\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\f\u001a\u00020\r2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a@\u0010\u000f\u001a\u00020\u00012\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\n0\u00112\u0006\u0010\u0012\u001a\u00020\n2\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00010\u00142\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a&\u0010\u0015\u001a\u00020\u00012\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010\f\u001a\u00020\r2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001aB\u0010\u0018\u001a\u00020\u00012\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00170\u00112\b\u0010\u001a\u001a\u0004\u0018\u00010\u00172\u0012\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\u0017\u0012\u0004\u0012\u00020\u00010\u00142\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a.\u0010\u001c\u001a\u00020\u00012\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\n2\u0006\u0010 \u001a\u00020\n2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a&\u0010!\u001a\u00020\u00012\u0006\u0010 \u001a\u00020\n2\u0006\u0010\f\u001a\u00020\r2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a@\u0010\"\u001a\u00020\u00012\f\u0010#\u001a\b\u0012\u0004\u0012\u00020\n0\u00112\u0006\u0010$\u001a\u00020\n2\u0012\u0010%\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00010\u00142\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u00a8\u0006&"}, d2 = {"PlayerSettingsDialog", "", "onDismiss", "Lkotlin/Function0;", "onShowQualitySelector", "onShowSubtitleSelector", "onShowSpeedSelector", "PremiumBadge", "QualityBadge", "quality", "", "QualityOption", "isSelected", "", "onClick", "QualitySelectorDialog", "qualities", "", "selectedQuality", "onQualitySelected", "Lkotlin/Function1;", "ServerItem", "server", "Lcom/streamflix/app/data/model/Server;", "ServerSelectorDialog", "servers", "selectedServer", "onServerSelected", "SettingsOption", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "title", "subtitle", "SubtitleOption", "SubtitleSelectorDialog", "subtitles", "selectedSubtitle", "onSubtitleSelected", "app_debug"})
public final class PlayerDialogsKt {
    
    @androidx.compose.runtime.Composable()
    public static final void ServerSelectorDialog(@org.jetbrains.annotations.NotNull()
    java.util.List<com.streamflix.app.data.model.Server> servers, @org.jetbrains.annotations.Nullable()
    com.streamflix.app.data.model.Server selectedServer, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.streamflix.app.data.model.Server, kotlin.Unit> onServerSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ServerItem(@org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.model.Server server, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void QualityBadge(@org.jetbrains.annotations.NotNull()
    java.lang.String quality) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PremiumBadge() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void QualitySelectorDialog(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> qualities, @org.jetbrains.annotations.NotNull()
    java.lang.String selectedQuality, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onQualitySelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void QualityOption(@org.jetbrains.annotations.NotNull()
    java.lang.String quality, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SubtitleSelectorDialog(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> subtitles, @org.jetbrains.annotations.NotNull()
    java.lang.String selectedSubtitle, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onSubtitleSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SubtitleOption(@org.jetbrains.annotations.NotNull()
    java.lang.String subtitle, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PlayerSettingsDialog(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onShowQualitySelector, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onShowSubtitleSelector, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onShowSpeedSelector) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SettingsOption(@org.jetbrains.annotations.NotNull()
    androidx.compose.ui.graphics.vector.ImageVector icon, @org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String subtitle, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
}