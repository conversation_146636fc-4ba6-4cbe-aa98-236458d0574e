package com.streamflix.app.presentation.home

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.streamflix.app.data.model.*
import com.streamflix.app.data.repository.StreamFlixRepository
import com.streamflix.app.utils.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val repository: StreamFlixRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()

    init {
        loadHomeData()
    }

    fun loadHomeData() {
        viewModelScope.launch {
            // Load featured content
            repository.getHomeData().collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        val homeData = resource.data
                        if (homeData != null) {
                            _uiState.value = _uiState.value.copy(
                                featuredContent = Resource.Success(homeData),
                                isLoading = false
                            )
                        }
                    }
                    is Resource.Error -> {
                        _uiState.value = _uiState.value.copy(
                            featuredContent = Resource.Error(resource.message ?: "Failed to load home data"),
                            isLoading = false
                        )
                    }
                    is Resource.Loading -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = true
                        )
                    }
                }
            }
        }

        // Load trending movies
        viewModelScope.launch {
            repository.getTrendingMovies(10).collect { resource ->
                _uiState.value = _uiState.value.copy(
                    trendingMovies = resource
                )
            }
        }

        // Load popular TV shows
        viewModelScope.launch {
            repository.getFeaturedTvShows(10).collect { resource ->
                _uiState.value = _uiState.value.copy(
                    popularTvShows = resource
                )
            }
        }

        // Load latest movies
        viewModelScope.launch {
            repository.getLatestMovies(10).collect { resource ->
                _uiState.value = _uiState.value.copy(
                    latestMovies = resource
                )
            }
        }

        // Load continue watching (if user is authenticated)
        viewModelScope.launch {
            repository.isLoggedIn().collect { isLoggedIn ->
                if (isLoggedIn) {
                    repository.getContinueWatching().collect { resource ->
                        _uiState.value = _uiState.value.copy(
                            continueWatching = resource
                        )
                    }
                }
            }
        }
    }

    fun refresh() {
        loadHomeData()
    }
}

data class HomeUiState(
    val isLoading: Boolean = false,
    val featuredContent: Resource<HomeData>? = null,
    val trendingMovies: Resource<List<Movie>>? = null,
    val popularTvShows: Resource<List<TvShow>>? = null,
    val latestMovies: Resource<List<Movie>>? = null,
    val topRated: Resource<List<Any>>? = null,
    val continueWatching: Resource<List<WatchHistoryItem>>? = null,
    val error: String? = null
)
