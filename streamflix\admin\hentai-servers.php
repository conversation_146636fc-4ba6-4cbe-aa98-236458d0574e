<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $db = new Database();
        $conn = $db->connect();
        
        if ($action === 'add_server') {
            $name = sanitizeInput($_POST['name'] ?? '');
            $url = sanitizeInput($_POST['url'] ?? '');

            if (!empty($name) && !empty($url)) {
                // Check if embed_servers table exists
                $stmt = $conn->query("SHOW TABLES LIKE 'embed_servers'");
                $embed_servers_exists = $stmt->rowCount() > 0;

                if ($embed_servers_exists) {
                    // Add to embed_servers table
                    $stmt = $conn->prepare("
                        INSERT INTO embed_servers (name, movie_url, tv_url, anime_url, hentai_url, is_active, priority)
                        VALUES (?, ?, ?, ?, ?, 1, ?)
                    ");
                    // Get next priority
                    $priority_stmt = $conn->query("SELECT COALESCE(MAX(priority), 0) + 1 as next_priority FROM embed_servers");
                    $next_priority = $priority_stmt->fetchColumn();

                    $stmt->execute([
                        $name,
                        $url, // Use same URL for movie
                        $url, // Use same URL for TV
                        $url, // Use same URL for anime
                        $url, // Hentai URL
                        $next_priority
                    ]);
                } else {
                    // Fallback to servers table
                    $stmt = $conn->prepare("INSERT INTO servers (name, url_pattern, type, is_active) VALUES (?, ?, 'hentai', 1)");
                    $stmt->execute([$name, $url]);
                }

                $message = "Hentai server '{$name}' added successfully!";
            } else {
                $error = 'Name and URL are required.';
            }
        }
        
        if ($action === 'toggle_server') {
            $server_id = (int)($_POST['server_id'] ?? 0);

            if ($server_id > 0) {
                // Check if embed_servers table exists
                $stmt = $conn->query("SHOW TABLES LIKE 'embed_servers'");
                $embed_servers_exists = $stmt->rowCount() > 0;

                if ($embed_servers_exists) {
                    $stmt = $conn->prepare("UPDATE embed_servers SET is_active = NOT is_active WHERE id = ?");
                    $stmt->execute([$server_id]);
                } else {
                    $stmt = $conn->prepare("UPDATE servers SET is_active = NOT is_active WHERE id = ? AND type = 'hentai'");
                    $stmt->execute([$server_id]);
                }
                $message = 'Server status updated successfully!';
            }
        }

        if ($action === 'delete_server') {
            $server_id = (int)($_POST['server_id'] ?? 0);

            if ($server_id > 0) {
                // Check if embed_servers table exists
                $stmt = $conn->query("SHOW TABLES LIKE 'embed_servers'");
                $embed_servers_exists = $stmt->rowCount() > 0;

                if ($embed_servers_exists) {
                    $stmt = $conn->prepare("DELETE FROM embed_servers WHERE id = ?");
                    $stmt->execute([$server_id]);
                } else {
                    // Delete server associations first
                    $stmt = $conn->prepare("DELETE FROM hentai_servers WHERE server_id = ?");
                    $stmt->execute([$server_id]);

                    // Delete the server
                    $stmt = $conn->prepare("DELETE FROM servers WHERE id = ? AND type = 'hentai'");
                    $stmt->execute([$server_id]);
                }

                $message = 'Hentai server deleted successfully!';
            }
        }

        if ($action === 'update_server') {
            $server_id = (int)($_POST['server_id'] ?? 0);
            $name = sanitizeInput($_POST['name'] ?? '');
            $url = sanitizeInput($_POST['url'] ?? '');

            if ($server_id > 0 && !empty($name) && !empty($url)) {
                // Check if embed_servers table exists
                $stmt = $conn->query("SHOW TABLES LIKE 'embed_servers'");
                $embed_servers_exists = $stmt->rowCount() > 0;

                if ($embed_servers_exists) {
                    $stmt = $conn->prepare("
                        UPDATE embed_servers
                        SET name = ?, hentai_url = ?, movie_url = ?, tv_url = ?, anime_url = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    ");
                    $stmt->execute([$name, $url, $url, $url, $url, $server_id]);
                } else {
                    $stmt = $conn->prepare("UPDATE servers SET name = ?, url_pattern = ? WHERE id = ? AND type = 'hentai'");
                    $stmt->execute([$name, $url, $server_id]);
                }
                $message = 'Hentai server updated successfully!';
            }
        }
        
    } catch (Exception $e) {
        $error = 'Error: ' . $e->getMessage();
    }
}

// Get hentai servers from embed_servers table
try {
    $db = new Database();
    $conn = $db->connect();

    // Check if embed_servers table exists and has hentai_url column
    $stmt = $conn->query("SHOW TABLES LIKE 'embed_servers'");
    $embed_servers_exists = $stmt->rowCount() > 0;

    if ($embed_servers_exists) {
        // Check if hentai_url column exists
        $stmt = $conn->query("SHOW COLUMNS FROM embed_servers LIKE 'hentai_url'");
        $hentai_url_exists = $stmt->rowCount() > 0;

        if ($hentai_url_exists) {
            // Get servers with hentai URLs
            $stmt = $conn->prepare("
                SELECT id, name, hentai_url as url, is_active, priority, created_at, updated_at
                FROM embed_servers
                WHERE hentai_url IS NOT NULL AND hentai_url != ''
                ORDER BY priority ASC, name ASC
            ");
            $stmt->execute();
            $hentai_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } else {
            // Fallback to servers table
            $stmt = $conn->prepare("SELECT * FROM servers WHERE type = 'hentai' ORDER BY name");
            $stmt->execute();
            $hentai_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    } else {
        // Fallback to servers table
        $stmt = $conn->prepare("SELECT * FROM servers WHERE type = 'hentai' ORDER BY name");
        $stmt->execute();
        $hentai_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get hentai content counts
    $stmt = $conn->prepare("
        SELECT COUNT(*) as hentai_shows
        FROM tv_shows
        WHERE content_type = 'hentai'
    ");
    $stmt->execute();
    $hentai_shows_count = $stmt->fetchColumn();

    // Also check genre-based hentai content
    $stmt = $conn->prepare("
        SELECT COUNT(DISTINCT t.id) as genre_hentai_shows
        FROM tv_shows t
        INNER JOIN tv_show_genres tg ON t.id = tg.tv_show_id
        INNER JOIN genres g ON tg.genre_id = g.id
        WHERE LOWER(g.name) LIKE '%hentai%'
    ");
    $stmt->execute();
    $genre_hentai_count = $stmt->fetchColumn();

    // Use the higher count
    $hentai_shows_count = max($hentai_shows_count, $genre_hentai_count);

} catch (Exception $e) {
    $error = 'Database error: ' . $e->getMessage();
    $hentai_servers = [];
    $hentai_shows_count = 0;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hentai Servers - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .server-form {
            background: var(--secondary-color);
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 30px;
            border: 2px solid #ff1744;
        }
        
        .server-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .server-card {
            background: var(--secondary-color);
            border: 2px solid #ff1744;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        .server-card.inactive {
            border-color: var(--border-color);
            opacity: 0.6;
        }
        
        .server-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(255, 23, 68, 0.3);
        }
        
        .server-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .server-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #ff1744;
            margin: 0;
        }
        
        .server-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-active {
            background: #28a745;
            color: white;
        }
        
        .status-inactive {
            background: #dc3545;
            color: white;
        }
        
        .server-url {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 15px;
            word-break: break-all;
        }
        
        .server-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--dark-bg);
            color: var(--text-primary);
            font-size: 14px;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 1px solid #28a745;
        }
        
        .alert-error {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid #dc3545;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: var(--secondary-color);
            border: 2px solid #ff1744;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #ff1744;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }
        
        .hentai-theme {
            background: linear-gradient(135deg, #ff1744, #e91e63);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .warning-box {
            background: rgba(255, 23, 68, 0.1);
            border: 1px solid #ff1744;
            color: #ff1744;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1 class="hentai-theme">🔞 Hentai Server Management</h1>
            <p>Manage servers specifically for adult anime content</p>
        </div>

        <div class="warning-box">
            <strong>⚠️ Adult Content Warning:</strong> This section is for managing servers that host adult anime content. 
            Ensure compliance with local laws and platform policies.
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo count($hentai_servers); ?></div>
                <div class="stat-label">Hentai Servers</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $hentai_shows_count; ?></div>
                <div class="stat-label">Hentai Series</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">18+</div>
                <div class="stat-label">Age Restriction</div>
            </div>
        </div>

        <!-- Add New Server -->
        <div class="server-form">
            <h3>➕ Add New Hentai Server</h3>
            <form method="POST">
                <input type="hidden" name="action" value="add_server">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="name">Server Name</label>
                        <input type="text" id="name" name="name" 
                               placeholder="e.g., LetsEmbed Hentai, HentaiStream" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="url">Server URL</label>
                        <input type="url" id="url" name="url"
                               placeholder="https://letsembed.cc/embed/hentai/?id={id}/{season}/{episode}" required>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary">Add Hentai Server</button>
            </form>

            <div style="margin-top: 20px; padding: 15px; background: rgba(255, 23, 68, 0.1); border-radius: 6px; border: 1px solid #ff1744;">
                <h4 style="margin: 0 0 10px 0; color: #ff1744;">📝 URL Pattern Examples:</h4>
                <ul style="margin: 0; padding-left: 20px; color: var(--text-secondary);">
                    <li><strong>LetsEmbed:</strong> https://letsembed.cc/embed/hentai/?id={id}/{season}/{episode}</li>
                    <li><strong>AutoEmbed:</strong> https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}</li>
                    <li><strong>VidSrc:</strong> https://vidsrc.to/embed/tv/{id}/{season}/{episode}</li>
                    <li><strong>Custom:</strong> Use {id}, {season}, {episode} placeholders</li>
                </ul>
            </div>
        </div>

        <!-- Existing Servers -->
        <div>
            <h3>🖥️ Existing Hentai Servers</h3>

            <?php if (empty($hentai_servers)): ?>
                <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
                    <h4>🔞 No Hentai Servers Found</h4>
                    <p>Add your first hentai server using the form above.</p>
                </div>
            <?php else: ?>
                <div class="server-grid">
                    <?php foreach ($hentai_servers as $server): ?>
                        <div class="server-card <?php echo $server['is_active'] ? '' : 'inactive'; ?>">
                            <div class="server-header">
                                <h4 class="server-name"><?php echo htmlspecialchars($server['name']); ?></h4>
                                <span class="server-status <?php echo $server['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                    <?php echo $server['is_active'] ? 'Active' : 'Inactive'; ?>
                                </span>
                            </div>

                            <div class="server-url">
                                <strong>URL:</strong> <?php echo htmlspecialchars($server['url']); ?>
                            </div>

                            <div style="margin-bottom: 15px; font-size: 0.9rem; color: var(--text-secondary);">
                                <div><strong>Added:</strong> <?php echo date('M j, Y', strtotime($server['created_at'])); ?></div>
                                <?php if (isset($server['priority'])): ?>
                                <div><strong>Priority:</strong> <?php echo $server['priority']; ?></div>
                                <?php endif; ?>
                                <?php if (isset($server['updated_at']) && $server['updated_at'] !== $server['created_at']): ?>
                                <div><strong>Updated:</strong> <?php echo date('M j, Y', strtotime($server['updated_at'])); ?></div>
                                <?php endif; ?>
                            </div>

                            <div class="server-actions">
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="toggle_server">
                                    <input type="hidden" name="server_id" value="<?php echo $server['id']; ?>">
                                    <button type="submit" class="btn btn-secondary">
                                        <?php echo $server['is_active'] ? 'Deactivate' : 'Activate'; ?>
                                    </button>
                                </form>

                                <button class="btn btn-primary" onclick="editServer(<?php echo $server['id']; ?>, '<?php echo htmlspecialchars($server['name']); ?>', '<?php echo htmlspecialchars($server['url']); ?>')">
                                    Edit
                                </button>

                                <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this hentai server?')">
                                    <input type="hidden" name="action" value="delete_server">
                                    <input type="hidden" name="server_id" value="<?php echo $server['id']; ?>">
                                    <button type="submit" class="btn btn-danger">Delete</button>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Quick Actions -->
        <div style="background: var(--secondary-color); border: 2px solid #ff1744; border-radius: 8px; padding: 20px; margin: 30px 0;">
            <h3>🔧 Quick Actions</h3>
            <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-top: 15px;">
                <a href="../fix_hentai_servers_final.php" target="_blank" class="btn btn-primary">
                    🔧 Auto-Fix Hentai Servers
                </a>
                <a href="../test_hentai_servers.php" target="_blank" class="btn btn-secondary">
                    🧪 Test Hentai Servers
                </a>
                <a href="hentai-management.php" class="btn btn-secondary">
                    🔞 Hentai Management
                </a>
            </div>
            <p style="margin-top: 15px; font-size: 0.9rem; color: var(--text-secondary);">
                <strong>Note:</strong> If you don't see any servers above, run "Auto-Fix Hentai Servers" to automatically configure them.
            </p>
        </div>

        <div style="margin-top: 40px; text-align: center;">
            <a href="index.php" class="btn btn-secondary">← Back to Dashboard</a>
            <a href="anime-servers.php" class="btn btn-secondary">🎌 Anime Servers</a>
            <a href="servers.php" class="btn btn-secondary">🖥️ Movie/TV Servers</a>
        </div>
    </div>

    <!-- Edit Server Modal -->
    <div id="editModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: var(--secondary-color); padding: 30px; border-radius: 8px; width: 90%; max-width: 500px;">
            <h3>Edit Hentai Server</h3>
            <form method="POST" id="editForm">
                <input type="hidden" name="action" value="update_server">
                <input type="hidden" name="server_id" id="editServerId">

                <div class="form-group">
                    <label for="editName">Server Name</label>
                    <input type="text" id="editName" name="name" required>
                </div>

                <div class="form-group">
                    <label for="editUrl">Server URL</label>
                    <input type="url" id="editUrl" name="url" required>
                </div>

                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button type="button" class="btn btn-secondary" onclick="closeEditModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Server</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function editServer(id, name, url) {
            document.getElementById('editServerId').value = id;
            document.getElementById('editName').value = name;
            document.getElementById('editUrl').value = url;
            document.getElementById('editModal').style.display = 'block';
        }

        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        // Close modal when clicking outside
        document.getElementById('editModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeEditModal();
            }
        });
    </script>
</body>
</html>
