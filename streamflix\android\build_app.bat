@echo off
echo ========================================
echo    StreamFlix Android App Builder
echo ========================================
echo.

echo [1/5] Cleaning previous builds...
call gradlew clean

echo.
echo [2/5] Checking dependencies...
call gradlew dependencies

echo.
echo [3/5] Running lint checks...
call gradlew lint

echo.
echo [4/5] Building debug APK...
call gradlew assembleDebug

echo.
echo [5/5] Building release APK...
call gradlew assembleRelease

echo.
echo ========================================
echo           Build Complete!
echo ========================================
echo.
echo Debug APK: app\build\outputs\apk\debug\app-debug.apk
echo Release APK: app\build\outputs\apk\release\app-release.apk
echo.
echo To install debug APK:
echo adb install app\build\outputs\apk\debug\app-debug.apk
echo.
pause
