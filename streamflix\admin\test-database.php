<?php
// Simple database test script
require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$test_results = [];
$test_errors = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    try {
        $db = new Database();
        $conn = $db->connect();
        
        if ($action === 'test_basic') {
            // Test basic database connection
            $stmt = $conn->query("SELECT 1");
            $test_results[] = "✅ Database connection successful";
            
            // Test existing tables
            $tables = ['movies', 'tv_shows', 'users', 'genres'];
            foreach ($tables as $table) {
                try {
                    $stmt = $conn->query("SELECT COUNT(*) FROM {$table}");
                    $count = $stmt->fetchColumn();
                    $test_results[] = "✅ Table '{$table}' exists with {$count} records";
                } catch (Exception $e) {
                    $test_errors[] = "❌ Table '{$table}' error: " . $e->getMessage();
                }
            }
        }
        
        if ($action === 'create_tables') {
            // Create new tables one by one
            
            // Create embed_servers
            try {
                $conn->exec("
                    CREATE TABLE IF NOT EXISTS embed_servers (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        movie_url TEXT NOT NULL,
                        tv_url TEXT NOT NULL,
                        priority INT DEFAULT 1,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ");
                $test_results[] = "✅ Created embed_servers table";
            } catch (Exception $e) {
                $test_errors[] = "❌ embed_servers table: " . $e->getMessage();
            }
            
            // Create site_settings
            try {
                $conn->exec("
                    CREATE TABLE IF NOT EXISTS site_settings (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        setting_key VARCHAR(100) NOT NULL UNIQUE,
                        setting_value TEXT,
                        setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ");
                $test_results[] = "✅ Created site_settings table";
            } catch (Exception $e) {
                $test_errors[] = "❌ site_settings table: " . $e->getMessage();
            }
            
            // Create user_activity
            try {
                $conn->exec("
                    CREATE TABLE IF NOT EXISTS user_activity (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT,
                        activity_type ENUM('login', 'logout', 'view_movie', 'view_tv_show', 'search') NOT NULL,
                        content_type ENUM('movie', 'tv_show') NULL,
                        content_id INT NULL,
                        ip_address VARCHAR(45),
                        user_agent TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ");
                $test_results[] = "✅ Created user_activity table";
            } catch (Exception $e) {
                $test_errors[] = "❌ user_activity table: " . $e->getMessage();
            }
            
            // Create watchlist
            try {
                $conn->exec("
                    CREATE TABLE IF NOT EXISTS watchlist (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        content_type ENUM('movie', 'tv_show') NOT NULL,
                        content_id INT NOT NULL,
                        added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ");
                $test_results[] = "✅ Created watchlist table";
            } catch (Exception $e) {
                $test_errors[] = "❌ watchlist table: " . $e->getMessage();
            }
        }
        
        if ($action === 'add_columns') {
            // Add missing columns
            $columns_to_add = [
                'movies' => [
                    'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                    'is_featured' => 'BOOLEAN DEFAULT 0',
                    'is_trending' => 'BOOLEAN DEFAULT 0'
                ],
                'tv_shows' => [
                    'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                    'is_featured' => 'BOOLEAN DEFAULT 0',
                    'is_trending' => 'BOOLEAN DEFAULT 0'
                ],
                'users' => [
                    'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
                ]
            ];
            
            foreach ($columns_to_add as $table => $columns) {
                foreach ($columns as $column => $definition) {
                    try {
                        // Check if column exists
                        $stmt = $conn->prepare("SHOW COLUMNS FROM {$table} LIKE ?");
                        $stmt->execute([$column]);
                        
                        if ($stmt->rowCount() == 0) {
                            $conn->exec("ALTER TABLE {$table} ADD COLUMN {$column} {$definition}");
                            $test_results[] = "✅ Added {$column} to {$table}";
                        } else {
                            $test_results[] = "ℹ️ Column {$column} already exists in {$table}";
                        }
                    } catch (Exception $e) {
                        $test_errors[] = "❌ Adding {$column} to {$table}: " . $e->getMessage();
                    }
                }
            }
        }
        
        if ($action === 'insert_data') {
            // Insert default data
            
            // Insert servers
            $servers = [
                ['AutoEmbed', 'https://player.autoembed.cc/embed/movie/{id}', 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}', 1],
                ['VidJoy', 'https://vidjoy.pro/embed/movie/{id}', 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}', 2],
                ['LetsEmbed', 'https://letsembed.cc/embed/movie/?id={id}', 'https://letsembed.cc/embed/tv/?id={id}/{season}/{episode}', 3]
            ];
            
            foreach ($servers as $server) {
                try {
                    $stmt = $conn->prepare("INSERT IGNORE INTO embed_servers (name, movie_url, tv_url, priority, is_active) VALUES (?, ?, ?, ?, 1)");
                    $stmt->execute($server);
                    if ($stmt->rowCount() > 0) {
                        $test_results[] = "✅ Inserted server: {$server[0]}";
                    }
                } catch (Exception $e) {
                    $test_errors[] = "❌ Inserting server {$server[0]}: " . $e->getMessage();
                }
            }
            
            // Insert settings
            $settings = [
                ['site_name', 'StreamFlix', 'string', 'Website name'],
                ['tmdb_api_key', '', 'string', 'TMDB API Key'],
                ['maintenance_mode', '0', 'boolean', 'Maintenance mode']
            ];
            
            foreach ($settings as $setting) {
                try {
                    $stmt = $conn->prepare("INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)");
                    $stmt->execute($setting);
                    if ($stmt->rowCount() > 0) {
                        $test_results[] = "✅ Inserted setting: {$setting[0]}";
                    }
                } catch (Exception $e) {
                    $test_errors[] = "❌ Inserting setting {$setting[0]}: " . $e->getMessage();
                }
            }
        }
        
    } catch (Exception $e) {
        $test_errors[] = "❌ Database error: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Test - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container {
            max-width: 800px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .test-section {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .test-btn {
            background: var(--primary-color);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-result {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 4px;
            background: var(--dark-bg);
        }
        
        .success {
            border-left: 4px solid #28a745;
        }
        
        .error {
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <span>Welcome, <?php echo $_SESSION['username']; ?></span>
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <h1>Database Test</h1>
        
        <div class="test-section">
            <h3>Test Database</h3>
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="test_basic">
                <button type="submit" class="test-btn">Test Basic Connection</button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="create_tables">
                <button type="submit" class="test-btn">Create New Tables</button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="add_columns">
                <button type="submit" class="test-btn">Add Missing Columns</button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="insert_data">
                <button type="submit" class="test-btn">Insert Default Data</button>
            </form>
        </div>

        <?php if (!empty($test_results)): ?>
            <div class="test-section">
                <h3>✅ Test Results</h3>
                <?php foreach ($test_results as $result): ?>
                    <div class="test-result success"><?php echo htmlspecialchars($result); ?></div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($test_errors)): ?>
            <div class="test-section">
                <h3>❌ Test Errors</h3>
                <?php foreach ($test_errors as $error): ?>
                    <div class="test-result error"><?php echo htmlspecialchars($error); ?></div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <div class="test-section">
            <p><a href="quick-setup.php">← Back to Quick Setup</a></p>
        </div>
    </div>
</body>
</html>
