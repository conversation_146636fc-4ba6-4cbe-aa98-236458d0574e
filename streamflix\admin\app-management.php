<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $db = new Database();
        $conn = $db->connect();
        
        if ($action === 'update_app_settings') {
            $app_name = sanitizeInput($_POST['app_name'] ?? '');
            $app_version = sanitizeInput($_POST['app_version'] ?? '');
            $min_version = sanitizeInput($_POST['min_version'] ?? '');
            $update_url = sanitizeInput($_POST['update_url'] ?? '');
            $force_update = isset($_POST['force_update']) ? 1 : 0;
            $maintenance_mode = isset($_POST['maintenance_mode']) ? 1 : 0;
            $maintenance_message = sanitizeInput($_POST['maintenance_message'] ?? '');
            
            // Create app_settings table if not exists
            $conn->exec("
                CREATE TABLE IF NOT EXISTS app_settings (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    setting_key VARCHAR(100) UNIQUE,
                    setting_value TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ");
            
            // Update settings
            $settings = [
                'app_name' => $app_name,
                'app_version' => $app_version,
                'min_version' => $min_version,
                'update_url' => $update_url,
                'force_update' => $force_update,
                'maintenance_mode' => $maintenance_mode,
                'maintenance_message' => $maintenance_message
            ];
            
            foreach ($settings as $key => $value) {
                $stmt = $conn->prepare("
                    INSERT INTO app_settings (setting_key, setting_value) 
                    VALUES (?, ?) 
                    ON DUPLICATE KEY UPDATE setting_value = ?
                ");
                $stmt->execute([$key, $value, $value]);
            }
            
            $message = 'App settings updated successfully!';
        }
        
        elseif ($action === 'send_notification') {
            $title = sanitizeInput($_POST['notification_title'] ?? '');
            $message_text = sanitizeInput($_POST['notification_message'] ?? '');
            $target_users = $_POST['target_users'] ?? 'all';
            
            if (!empty($title) && !empty($message_text)) {
                // Create notifications table if not exists
                $conn->exec("
                    CREATE TABLE IF NOT EXISTS notifications (
                        id INT PRIMARY KEY AUTO_INCREMENT,
                        title VARCHAR(255),
                        message TEXT,
                        target_users VARCHAR(50),
                        sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        is_active BOOLEAN DEFAULT TRUE
                    )
                ");
                
                $stmt = $conn->prepare("
                    INSERT INTO notifications (title, message, target_users) 
                    VALUES (?, ?, ?)
                ");
                $stmt->execute([$title, $message_text, $target_users]);
                
                $message = 'Notification sent successfully!';
            } else {
                $error = 'Title and message are required.';
            }
        }
        
    } catch (Exception $e) {
        $error = 'Error: ' . $e->getMessage();
    }
}

// Get current app settings
try {
    $db = new Database();
    $conn = $db->connect();

    // Create app_settings table if not exists
    $conn->exec("
        CREATE TABLE IF NOT EXISTS app_settings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            setting_key VARCHAR(100) UNIQUE,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");

    // Initialize default settings if table is empty
    $stmt = $conn->query("SELECT COUNT(*) FROM app_settings");
    $count = $stmt->fetchColumn();

    if ($count == 0) {
        $default_settings = [
            'app_name' => 'StreamFlix',
            'app_version' => '1.0.0',
            'min_version' => '1.0.0',
            'update_url' => '',
            'force_update' => '0',
            'maintenance_mode' => '0',
            'maintenance_message' => 'App is under maintenance. Please try again later.'
        ];

        foreach ($default_settings as $key => $value) {
            $stmt = $conn->prepare("INSERT INTO app_settings (setting_key, setting_value) VALUES (?, ?)");
            $stmt->execute([$key, $value]);
        }
    }

    // Get settings
    $stmt = $conn->query("
        SELECT setting_key, setting_value
        FROM app_settings
        WHERE setting_key IN ('app_name', 'app_version', 'min_version', 'update_url', 'force_update', 'maintenance_mode', 'maintenance_message')
    ");
    $settings_data = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

    $app_settings = [
        'app_name' => $settings_data['app_name'] ?? 'StreamFlix',
        'app_version' => $settings_data['app_version'] ?? '1.0.0',
        'min_version' => $settings_data['min_version'] ?? '1.0.0',
        'update_url' => $settings_data['update_url'] ?? '',
        'force_update' => $settings_data['force_update'] ?? 0,
        'maintenance_mode' => $settings_data['maintenance_mode'] ?? 0,
        'maintenance_message' => $settings_data['maintenance_message'] ?? 'App is under maintenance. Please try again later.'
    ];

    // Get app statistics
    try {
        $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
        $new_users = $stmt->fetchColumn();
    } catch (Exception $e) {
        $new_users = 0;
    }

    try {
        $stmt = $conn->query("SELECT COUNT(*) FROM notifications WHERE sent_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
        $recent_notifications = $stmt->fetchColumn();
    } catch (Exception $e) {
        $recent_notifications = 0;
    }

} catch (Exception $e) {
    // Fallback default settings
    $app_settings = [
        'app_name' => 'StreamFlix',
        'app_version' => '1.0.0',
        'min_version' => '1.0.0',
        'update_url' => '',
        'force_update' => 0,
        'maintenance_mode' => 0,
        'maintenance_message' => 'App is under maintenance. Please try again later.'
    ];
    $new_users = 0;
    $recent_notifications = 0;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>App Management - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="assets/admin-style.css">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            color: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .admin-nav {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 30px;
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .admin-nav a {
            padding: 12px 20px;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background: #e50914;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(229, 9, 20, 0.4);
        }
        
        .app-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .app-section {
            background: var(--secondary-color);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .app-section h2 {
            color: var(--text-color);
            margin-bottom: 20px;
            font-size: 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            color: white;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-color);
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #333;
            border-radius: 8px;
            background: var(--bg-color);
            color: var(--text-color);
            font-size: 14px;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: #e50914;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .success-message,
        .error-message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        @media (max-width: 768px) {
            .admin-container {
                margin-top: 80px;
                padding: 0 15px;
            }
            
            .app-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .admin-nav {
                justify-content: center;
            }
            
            .admin-nav a {
                padding: 10px 15px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <span>Welcome, <?php echo $_SESSION['username']; ?></span>
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>📱 App Management</h1>
            <p>Manage mobile app settings, notifications and updates</p>
        </div>

        <nav class="admin-nav">
            <a href="index.php">📊 Dashboard</a>
            <a href="movies.php">🎬 Movies</a>
            <a href="tv-shows.php">📺 TV Shows</a>
            <a href="users.php">👥 Users</a>
            <a href="servers.php">🖥️ Servers</a>
            <a href="analytics.php">📈 Analytics</a>
            <a href="import.php">📥 Import</a>
            <a href="app-management.php" class="active">📱 App Management</a>
            <a href="user-management.php">👤 User Management</a>
            <a href="system-logs.php">📋 System Logs</a>
            <a href="maintenance.php">🔧 Maintenance</a>
            <a href="database-updater.php">🗄️ DB Updater</a>
            <a href="quick-setup.php">🚀 Quick Setup</a>
            <a href="settings.php">⚙️ Settings</a>
        </nav>

        <?php if (isset($message)): ?>
            <div class="success-message"><?php echo $message; ?></div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="error-message"><?php echo $error; ?></div>
        <?php endif; ?>

        <!-- Debug Info (remove in production) -->
        <?php if (isset($_GET['debug'])): ?>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; font-family: monospace; font-size: 12px;">
                <strong>Debug Info:</strong><br>
                App Settings: <?php echo json_encode($app_settings, JSON_PRETTY_PRINT); ?><br>
                New Users: <?php echo $new_users; ?><br>
                Recent Notifications: <?php echo $recent_notifications; ?>
            </div>
        <?php endif; ?>

        <!-- App Statistics -->
        <div class="stats-row">
            <div class="stat-box">
                <div class="stat-number"><?php echo number_format($new_users); ?></div>
                <div class="stat-label">New Users (30 days)</div>
            </div>
            <div class="stat-box">
                <div class="stat-number"><?php echo number_format($recent_notifications); ?></div>
                <div class="stat-label">Notifications (7 days)</div>
            </div>
            <div class="stat-box">
                <div class="stat-number"><?php echo isset($app_settings['app_version']) ? $app_settings['app_version'] : '1.0.0'; ?></div>
                <div class="stat-label">Current Version</div>
            </div>
            <div class="stat-box">
                <div class="stat-number"><?php echo (isset($app_settings['maintenance_mode']) && $app_settings['maintenance_mode']) ? 'ON' : 'OFF'; ?></div>
                <div class="stat-label">Maintenance Mode</div>
            </div>
        </div>

        <div class="app-grid">
            <!-- App Settings -->
            <div class="app-section">
                <h2>⚙️ App Settings</h2>
                
                <form method="POST">
                    <input type="hidden" name="action" value="update_app_settings">
                    
                    <div class="form-group">
                        <label for="app_name">App Name</label>
                        <input type="text" id="app_name" name="app_name"
                               value="<?php echo htmlspecialchars($app_settings['app_name'] ?? 'StreamFlix'); ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="app_version">Current Version</label>
                        <input type="text" id="app_version" name="app_version"
                               value="<?php echo htmlspecialchars($app_settings['app_version'] ?? '1.0.0'); ?>"
                               placeholder="1.0.0" required>
                    </div>

                    <div class="form-group">
                        <label for="min_version">Minimum Required Version</label>
                        <input type="text" id="min_version" name="min_version"
                               value="<?php echo htmlspecialchars($app_settings['min_version'] ?? '1.0.0'); ?>"
                               placeholder="1.0.0" required>
                    </div>

                    <div class="form-group">
                        <label for="update_url">Update URL</label>
                        <input type="url" id="update_url" name="update_url"
                               value="<?php echo htmlspecialchars($app_settings['update_url'] ?? ''); ?>"
                               placeholder="https://example.com/app-update.apk">
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="force_update" name="force_update"
                               <?php echo ($app_settings['force_update'] ?? 0) ? 'checked' : ''; ?>>
                        <label for="force_update">Force Update</label>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="maintenance_mode" name="maintenance_mode"
                               <?php echo ($app_settings['maintenance_mode'] ?? 0) ? 'checked' : ''; ?>>
                        <label for="maintenance_mode">Maintenance Mode</label>
                    </div>

                    <div class="form-group">
                        <label for="maintenance_message">Maintenance Message</label>
                        <textarea id="maintenance_message" name="maintenance_message" rows="3"><?php echo htmlspecialchars($app_settings['maintenance_message'] ?? 'App is under maintenance. Please try again later.'); ?></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">💾 Save Settings</button>
                </form>
            </div>

            <!-- Push Notifications -->
            <div class="app-section">
                <h2>🔔 Send Notification</h2>
                
                <form method="POST">
                    <input type="hidden" name="action" value="send_notification">
                    
                    <div class="form-group">
                        <label for="notification_title">Notification Title</label>
                        <input type="text" id="notification_title" name="notification_title" 
                               placeholder="New movies added!" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="notification_message">Message</label>
                        <textarea id="notification_message" name="notification_message" rows="4" 
                                  placeholder="Check out the latest movies and TV shows..." required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="target_users">Target Users</label>
                        <select id="target_users" name="target_users">
                            <option value="all">All Users</option>
                            <option value="active">Active Users Only</option>
                            <option value="premium">Premium Users</option>
                            <option value="new">New Users (Last 7 days)</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-success">📤 Send Notification</button>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
