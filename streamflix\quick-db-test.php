<?php
/**
 * Quick Database Test Script
 * Simple connectivity and basic table check
 */

// Simple admin check
if (!isset($_GET['test']) || $_GET['test'] !== 'db') {
    die('Access denied. Use: quick-db-test.php?test=db');
}

echo "<h1>🔍 Quick Database Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
    .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
    table { border-collapse: collapse; width: 100%; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

// Test 1: Check if config file exists
echo "<h2>📁 Config File Check</h2>";
if (file_exists('config/database.php')) {
    echo "<div class='success'>✅ config/database.php file exists</div>";
    require_once 'config/database.php';
} else {
    echo "<div class='error'>❌ config/database.php file not found</div>";
    die("Cannot proceed without database configuration.");
}

// Test 2: Database Connection
echo "<h2>🔗 Database Connection Test</h2>";
try {
    $database = new Database();
    $pdo = $database->connect();
    
    if ($pdo) {
        echo "<div class='success'>✅ Database connection successful</div>";
        
        // Get database name
        $stmt = $pdo->query("SELECT DATABASE() as db_name");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<div class='info'>📊 Connected to database: <strong>" . $result['db_name'] . "</strong></div>";
        
    } else {
        echo "<div class='error'>❌ Database connection failed</div>";
        die("Cannot proceed without database connection.");
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Database connection error: " . $e->getMessage() . "</div>";
    die("Cannot proceed without database connection.");
}

// Test 3: Check Tables
echo "<h2>📋 Table Check</h2>";
try {
    $stmt = $pdo->query("SHOW TABLES");
    $tables = [];
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $tables[] = $row[0];
    }
    
    $requiredTables = ['users', 'movies', 'tv_shows', 'genres'];
    $missingTables = array_diff($requiredTables, $tables);
    
    echo "<div class='info'>📊 Total tables found: <strong>" . count($tables) . "</strong></div>";
    
    if (empty($missingTables)) {
        echo "<div class='success'>✅ All core tables exist</div>";
    } else {
        echo "<div class='warning'>⚠️ Missing core tables: " . implode(', ', $missingTables) . "</div>";
    }
    
    // Show all tables
    echo "<h3>All Tables:</h3>";
    echo "<table>";
    echo "<tr><th>Table Name</th><th>Status</th></tr>";
    foreach ($requiredTables as $table) {
        $status = in_array($table, $tables) ? "✅ Exists" : "❌ Missing";
        $class = in_array($table, $tables) ? "success" : "error";
        echo "<tr><td>$table</td><td class='$class'>$status</td></tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error checking tables: " . $e->getMessage() . "</div>";
}

// Test 4: Check Data
echo "<h2>📊 Data Check</h2>";
try {
    // Check users table
    if (in_array('users', $tables)) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $userCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "<div class='info'>👥 Users: <strong>$userCount</strong></div>";
    }
    
    // Check movies table
    if (in_array('movies', $tables)) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM movies");
        $movieCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "<div class='info'>🎬 Movies: <strong>$movieCount</strong></div>";
    }
    
    // Check tv_shows table
    if (in_array('tv_shows', $tables)) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM tv_shows");
        $tvCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "<div class='info'>📺 TV Shows: <strong>$tvCount</strong></div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error checking data: " . $e->getMessage() . "</div>";
}

// Test 5: Check Admin User
echo "<h2>👤 Admin User Check</h2>";
try {
    if (in_array('users', $tables)) {
        $stmt = $pdo->query("SELECT * FROM users WHERE role = 'admin' LIMIT 1");
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($admin) {
            echo "<div class='success'>✅ Admin user found: <strong>" . $admin['username'] . "</strong></div>";
            echo "<div class='info'>📧 Email: " . $admin['email'] . "</div>";
        } else {
            echo "<div class='warning'>⚠️ No admin user found</div>";
        }
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Error checking admin user: " . $e->getMessage() . "</div>";
}

// Test 6: Check Configuration
echo "<h2>⚙️ Configuration Check</h2>";
try {
    // Check if constants are defined
    $configs = [
        'TMDB_API_KEY' => defined('TMDB_API_KEY') ? (TMDB_API_KEY ? '✅ Set' : '⚠️ Empty') : '❌ Not defined',
        'SITE_NAME' => defined('SITE_NAME') ? '✅ Set: ' . SITE_NAME : '❌ Not defined',
        'SITE_URL' => defined('SITE_URL') ? '✅ Set: ' . SITE_URL : '❌ Not defined'
    ];
    
    echo "<table>";
    echo "<tr><th>Configuration</th><th>Status</th></tr>";
    foreach ($configs as $config => $status) {
        echo "<tr><td>$config</td><td>$status</td></tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error checking configuration: " . $e->getMessage() . "</div>";
}

// Test 7: Quick Performance Test
echo "<h2>⚡ Performance Test</h2>";
try {
    $start = microtime(true);
    $stmt = $pdo->query("SELECT 1");
    $end = microtime(true);
    
    $queryTime = round(($end - $start) * 1000, 2);
    
    if ($queryTime < 10) {
        echo "<div class='success'>✅ Database response time: <strong>{$queryTime}ms</strong> (Excellent)</div>";
    } elseif ($queryTime < 50) {
        echo "<div class='info'>✅ Database response time: <strong>{$queryTime}ms</strong> (Good)</div>";
    } else {
        echo "<div class='warning'>⚠️ Database response time: <strong>{$queryTime}ms</strong> (Slow)</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error testing performance: " . $e->getMessage() . "</div>";
}

// Summary
echo "<h2>📋 Summary</h2>";
$issues = 0;

if (!$pdo) $issues++;
if (!empty($missingTables)) $issues++;

if ($issues == 0) {
    echo "<div class='success'>🎉 <strong>All tests passed!</strong> Your database appears to be working correctly.</div>";
} else {
    echo "<div class='warning'>⚠️ <strong>$issues issue(s) found.</strong> Please review the results above.</div>";
}

echo "<div class='info'>
    <h3>Next Steps:</h3>
    <ul>
        <li>If you see missing tables, run the full <strong>database-health-checker.php</strong> script</li>
        <li>If you see connection errors, check your database configuration</li>
        <li>For detailed analysis, use the complete health checker tool</li>
    </ul>
</div>";

echo "<hr>";
echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><a href='database-health-checker.php?admin_key=streamflix_admin_2024'>🔧 Run Full Database Health Checker</a></p>";
?>
