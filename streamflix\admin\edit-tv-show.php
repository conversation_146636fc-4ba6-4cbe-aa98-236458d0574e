<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$tv_show_id = (int)($_GET['id'] ?? 0);
$message = '';
$error = '';

if ($tv_show_id <= 0) {
    redirectTo('tv-shows.php');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $db = new Database();
        $conn = $db->connect();
        
        if ($action === 'update_tv_show') {
            $name = sanitizeInput($_POST['name'] ?? '');
            $overview = sanitizeInput($_POST['overview'] ?? '');
            $first_air_date = sanitizeInput($_POST['first_air_date'] ?? '');
            $vote_average = (float)($_POST['vote_average'] ?? 0);
            $number_of_seasons = (int)($_POST['number_of_seasons'] ?? 0);
            $number_of_episodes = (int)($_POST['number_of_episodes'] ?? 0);
            $is_featured = isset($_POST['is_featured']) ? 1 : 0;
            $is_trending = isset($_POST['is_trending']) ? 1 : 0;
            
            if (!empty($name)) {
                $stmt = $conn->prepare("
                    UPDATE tv_shows 
                    SET name = ?, overview = ?, first_air_date = ?, vote_average = ?, 
                        number_of_seasons = ?, number_of_episodes = ?, is_featured = ?, is_trending = ?
                    WHERE id = ?
                ");
                $stmt->execute([$name, $overview, $first_air_date, $vote_average, $number_of_seasons, $number_of_episodes, $is_featured, $is_trending, $tv_show_id]);
                
                $message = "TV Show updated successfully!";
            } else {
                $error = "Name is required.";
            }
        }
        
        if ($action === 'add_genre') {
            $genre_name = sanitizeInput($_POST['genre_name'] ?? '');
            
            if (!empty($genre_name)) {
                // Create genre if it doesn't exist
                $stmt = $conn->prepare("INSERT IGNORE INTO genres (name, tmdb_id) VALUES (?, ?)");
                $stmt->execute([$genre_name, 99999 + rand(1, 1000)]);
                
                // Get genre ID
                $stmt = $conn->prepare("SELECT id FROM genres WHERE name = ?");
                $stmt->execute([$genre_name]);
                $genre = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($genre) {
                    // Add to tv_show_genres
                    $stmt = $conn->prepare("INSERT IGNORE INTO tv_show_genres (tv_show_id, genre_id) VALUES (?, ?)");
                    $stmt->execute([$tv_show_id, $genre['id']]);
                    $message = "Genre '{$genre_name}' added successfully!";
                }
            }
        }
        
        if ($action === 'remove_genre') {
            $genre_id = (int)($_POST['genre_id'] ?? 0);
            
            if ($genre_id > 0) {
                $stmt = $conn->prepare("DELETE FROM tv_show_genres WHERE tv_show_id = ? AND genre_id = ?");
                $stmt->execute([$tv_show_id, $genre_id]);
                $message = "Genre removed successfully!";
            }
        }
        
        if ($action === 'update_servers') {
            $server_statuses = $_POST['server_status'] ?? [];
            
            // Get all servers
            $stmt = $conn->prepare("SELECT id FROM servers WHERE type = 'tv' OR type = 'all'");
            $stmt->execute();
            $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($servers as $server) {
                $server_id = $server['id'];
                $is_enabled = isset($server_statuses[$server_id]) ? 1 : 0;
                
                // Check if record exists
                $stmt = $conn->prepare("SELECT id FROM tv_show_servers WHERE tv_show_id = ? AND server_id = ?");
                $stmt->execute([$tv_show_id, $server_id]);
                $exists = $stmt->fetch();
                
                if ($exists) {
                    // Update existing record
                    $stmt = $conn->prepare("UPDATE tv_show_servers SET is_enabled = ? WHERE tv_show_id = ? AND server_id = ?");
                    $stmt->execute([$is_enabled, $tv_show_id, $server_id]);
                } else {
                    // Insert new record
                    $stmt = $conn->prepare("INSERT INTO tv_show_servers (tv_show_id, server_id, is_enabled) VALUES (?, ?, ?)");
                    $stmt->execute([$tv_show_id, $server_id, $is_enabled]);
                }
            }
            
            $message = "Server settings updated successfully!";
        }
        
    } catch (Exception $e) {
        $error = 'Error: ' . $e->getMessage();
    }
}

// Get TV show details
try {
    $db = new Database();
    $conn = $db->connect();
    
    $stmt = $conn->prepare("SELECT * FROM tv_shows WHERE id = ?");
    $stmt->execute([$tv_show_id]);
    $tv_show = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tv_show) {
        redirectTo('tv-shows.php');
    }
    
    // Get TV show genres
    $stmt = $conn->prepare("
        SELECT g.id, g.name 
        FROM tv_show_genres tg 
        JOIN genres g ON tg.genre_id = g.id 
        WHERE tg.tv_show_id = ?
    ");
    $stmt->execute([$tv_show_id]);
    $tv_show_genres = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get available servers (with error handling)
    $available_servers = [];
    $tv_show_servers = [];

    try {
        $stmt = $conn->prepare("SELECT * FROM servers WHERE type IN ('tv', 'all') ORDER BY name");
        $stmt->execute();
        $available_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Get TV show server settings
        $stmt = $conn->prepare("
            SELECT server_id, is_enabled
            FROM tv_show_servers
            WHERE tv_show_id = ?
        ");
        $stmt->execute([$tv_show_id]);
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $tv_show_servers[$row['server_id']] = $row['is_enabled'];
        }
    } catch (Exception $e) {
        // Tables don't exist yet, that's okay
        error_log("Server tables not ready: " . $e->getMessage());
    }
    
} catch (Exception $e) {
    $error = 'Database error: ' . $e->getMessage();
    redirectTo('tv-shows.php');
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit TV Show - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container {
            max-width: 800px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .edit-form {
            background: var(--secondary-color);
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--dark-bg);
            color: var(--text-primary);
            font-size: 14px;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        .tv-show-poster {
            width: 200px;
            height: 300px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .genre-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .genre-tag {
            background: var(--primary-color);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .genre-remove {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 1.2rem;
            line-height: 1;
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 1px solid #28a745;
        }
        
        .alert-error {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid #dc3545;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>✏️ Edit TV Show</h1>
            <p>Edit TV show details, genres, and server settings</p>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <div class="edit-form">
            <div style="display: flex; gap: 30px; margin-bottom: 30px;">
                <div>
                    <img src="<?php echo getImageUrl($tv_show['poster_path'], 'w300'); ?>" 
                         alt="<?php echo htmlspecialchars($tv_show['name']); ?>" 
                         class="tv-show-poster">
                </div>
                
                <div style="flex: 1;">
                    <form method="POST">
                        <input type="hidden" name="action" value="update_tv_show">
                        
                        <div class="form-group">
                            <label for="name">Name</label>
                            <input type="text" id="name" name="name" 
                                   value="<?php echo htmlspecialchars($tv_show['name']); ?>" required>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="first_air_date">First Air Date</label>
                                <input type="date" id="first_air_date" name="first_air_date" 
                                       value="<?php echo $tv_show['first_air_date']; ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="vote_average">Rating</label>
                                <input type="number" id="vote_average" name="vote_average" 
                                       step="0.1" min="0" max="10"
                                       value="<?php echo $tv_show['vote_average']; ?>">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="number_of_seasons">Seasons</label>
                                <input type="number" id="number_of_seasons" name="number_of_seasons" 
                                       min="1" value="<?php echo $tv_show['number_of_seasons']; ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="number_of_episodes">Episodes</label>
                                <input type="number" id="number_of_episodes" name="number_of_episodes" 
                                       min="1" value="<?php echo $tv_show['number_of_episodes']; ?>">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="overview">Overview</label>
                            <textarea id="overview" name="overview"><?php echo htmlspecialchars($tv_show['overview']); ?></textarea>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="is_featured" name="is_featured" 
                                           <?php echo $tv_show['is_featured'] ? 'checked' : ''; ?>>
                                    <label for="is_featured">Featured TV Show</label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="is_trending" name="is_trending" 
                                           <?php echo $tv_show['is_trending'] ? 'checked' : ''; ?>>
                                    <label for="is_trending">Trending TV Show</label>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Update TV Show</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Genres Section -->
        <div class="edit-form">
            <h3>🏷️ Genres</h3>

            <div class="genre-list">
                <?php foreach ($tv_show_genres as $genre): ?>
                    <div class="genre-tag">
                        <?php echo htmlspecialchars($genre['name']); ?>
                        <form method="POST" style="display: inline;" onsubmit="return confirm('Remove this genre?')">
                            <input type="hidden" name="action" value="remove_genre">
                            <input type="hidden" name="genre_id" value="<?php echo $genre['id']; ?>">
                            <button type="submit" class="genre-remove">×</button>
                        </form>
                    </div>
                <?php endforeach; ?>
            </div>

            <form method="POST" style="display: flex; gap: 10px; align-items: end;">
                <input type="hidden" name="action" value="add_genre">
                <div class="form-group" style="flex: 1; margin-bottom: 0;">
                    <label for="genre_name">Add Genre</label>
                    <input type="text" id="genre_name" name="genre_name"
                           placeholder="e.g., Drama, Comedy, Anime, Hentai" required>
                </div>
                <button type="submit" class="btn btn-secondary">Add Genre</button>
            </form>
        </div>

        <!-- Server Management -->
        <div class="edit-form">
            <h3>🖥️ Server Management</h3>
            <p style="color: var(--text-secondary); margin-bottom: 20px;">
                Enable or disable specific servers for this TV show. Disabled servers won't appear in the player.
            </p>

            <form method="POST">
                <input type="hidden" name="action" value="update_servers">

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                    <?php foreach ($available_servers as $server): ?>
                        <?php
                        $server_id = $server['id'];
                        $is_enabled = isset($tv_show_servers[$server_id]) ? $tv_show_servers[$server_id] : 1; // Default enabled
                        ?>
                        <div style="background: var(--dark-bg); padding: 15px; border-radius: 8px; border: 2px solid <?php echo $is_enabled ? 'var(--primary-color)' : 'var(--border-color)'; ?>;">
                            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                <input type="checkbox"
                                       id="server_<?php echo $server_id; ?>"
                                       name="server_status[<?php echo $server_id; ?>]"
                                       value="1"
                                       <?php echo $is_enabled ? 'checked' : ''; ?>
                                       onchange="toggleServerCard(this, <?php echo $server_id; ?>)">
                                <label for="server_<?php echo $server_id; ?>" style="font-weight: 600; color: var(--text-primary);">
                                    <?php echo htmlspecialchars($server['name']); ?>
                                </label>
                            </div>

                            <div style="font-size: 0.9rem; color: var(--text-secondary);">
                                <div><strong>URL:</strong> <?php echo htmlspecialchars($server['url']); ?></div>
                                <div><strong>Type:</strong> <?php echo ucfirst($server['type']); ?></div>
                                <div><strong>Status:</strong>
                                    <span style="color: <?php echo $is_enabled ? '#28a745' : '#dc3545'; ?>;">
                                        <?php echo $is_enabled ? '✅ Enabled' : '❌ Disabled'; ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <?php if (empty($available_servers)): ?>
                    <div style="text-align: center; padding: 30px; color: var(--text-secondary);">
                        <h4>🖥️ No Servers Found</h4>
                        <p>No servers are configured for TV shows. Please add servers in the server management section.</p>
                        <a href="servers.php" class="btn btn-primary">Manage Servers</a>
                    </div>
                <?php else: ?>
                    <div style="margin-top: 20px; text-align: center;">
                        <button type="submit" class="btn btn-primary">💾 Update Server Settings</button>
                        <button type="button" class="btn btn-secondary" onclick="toggleAllServers(true)">✅ Enable All</button>
                        <button type="button" class="btn btn-secondary" onclick="toggleAllServers(false)">❌ Disable All</button>
                    </div>
                <?php endif; ?>
            </form>
        </div>

        <!-- TV Show Info -->
        <div class="edit-form">
            <h3>📊 TV Show Information</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div>
                    <strong>TMDB ID:</strong> <?php echo $tv_show['tmdb_id']; ?>
                </div>
                <div>
                    <strong>Seasons:</strong> <?php echo $tv_show['number_of_seasons']; ?>
                </div>
                <div>
                    <strong>Episodes:</strong> <?php echo $tv_show['number_of_episodes']; ?>
                </div>
                <div>
                    <strong>Language:</strong> <?php echo strtoupper($tv_show['original_language'] ?? 'N/A'); ?>
                </div>
                <div>
                    <strong>Added:</strong> <?php echo date('M j, Y', strtotime($tv_show['created_at'])); ?>
                </div>
            </div>
        </div>

        <div style="margin-top: 30px; text-align: center;">
            <a href="tv-shows.php" class="btn btn-secondary">← Back to TV Shows</a>
            <a href="../player.php?id=<?php echo $tv_show['tmdb_id']; ?>&type=tv_show" class="btn btn-primary" target="_blank">▶ Watch TV Show</a>
        </div>
    </div>

    <script>
        function toggleServerCard(checkbox, serverId) {
            const card = checkbox.closest('div[style*="border"]');
            if (checkbox.checked) {
                card.style.borderColor = 'var(--primary-color)';
                card.querySelector('span[style*="color"]').style.color = '#28a745';
                card.querySelector('span[style*="color"]').textContent = '✅ Enabled';
            } else {
                card.style.borderColor = 'var(--border-color)';
                card.querySelector('span[style*="color"]').style.color = '#dc3545';
                card.querySelector('span[style*="color"]').textContent = '❌ Disabled';
            }
        }

        function toggleAllServers(enable) {
            const checkboxes = document.querySelectorAll('input[name^="server_status"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = enable;
                const serverId = checkbox.id.replace('server_', '');
                toggleServerCard(checkbox, serverId);
            });
        }
    </script>
</body>
</html>
