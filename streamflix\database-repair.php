<?php
/**
 * Database Repair Script
 * Automatically fixes common database issues
 */

// Security check
if (!isset($_GET['repair_key']) || $_GET['repair_key'] !== 'fix_streamflix_db_2024') {
    die('Access denied. Use: database-repair.php?repair_key=fix_streamflix_db_2024');
}

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Database Repair Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f2f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .step { border-left: 4px solid #007bff; padding-left: 15px; margin: 20px 0; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔧 Database Repair Tool</h1>";
echo "<p>This tool will automatically detect and fix common database issues.</p>";

class DatabaseRepair {
    private $pdo;
    private $repairLog = [];
    
    public function __construct() {
        try {
            $database = new Database();
            $this->pdo = $database->connect();
            if (!$this->pdo) {
                throw new Exception("Database connection failed");
            }
        } catch (Exception $e) {
            die("<div class='error'>❌ Database connection failed: " . $e->getMessage() . "</div>");
        }
    }
    
    public function runRepair() {
        echo "<h2>🔍 Starting Database Repair Process</h2>";
        
        // Step 1: Check and create missing tables
        $this->createMissingTables();
        
        // Step 2: Add missing columns
        $this->addMissingColumns();
        
        // Step 3: Create missing indexes
        $this->createMissingIndexes();
        
        // Step 4: Fix data integrity issues
        $this->fixDataIntegrity();
        
        // Step 5: Optimize tables
        $this->optimizeTables();
        
        // Step 6: Create default admin user if missing
        $this->createDefaultAdmin();
        
        // Step 7: Insert default settings
        $this->insertDefaultSettings();
        
        echo "<h2>📋 Repair Summary</h2>";
        if (empty($this->repairLog)) {
            echo "<div class='success'>✅ No issues found. Database is healthy!</div>";
        } else {
            echo "<div class='info'>🔧 Repairs completed:</div>";
            foreach ($this->repairLog as $log) {
                echo "<div class='success'>✅ $log</div>";
            }
        }
    }
    
    private function createMissingTables() {
        echo "<div class='step'>";
        echo "<h3>📋 Step 1: Creating Missing Tables</h3>";
        
        $requiredTables = [
            'users' => "CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                role ENUM('admin', 'user') DEFAULT 'user',
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )",
            'movies' => "CREATE TABLE movies (
                id INT AUTO_INCREMENT PRIMARY KEY,
                tmdb_id INT UNIQUE NOT NULL,
                title VARCHAR(255) NOT NULL,
                overview TEXT,
                release_date DATE,
                vote_average DECIMAL(3,1) DEFAULT 0.0,
                poster_path VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_featured BOOLEAN DEFAULT 0,
                is_trending BOOLEAN DEFAULT 0
            )",
            'tv_shows' => "CREATE TABLE tv_shows (
                id INT AUTO_INCREMENT PRIMARY KEY,
                tmdb_id INT UNIQUE NOT NULL,
                name VARCHAR(255) NOT NULL,
                overview TEXT,
                first_air_date DATE,
                vote_average DECIMAL(3,1) DEFAULT 0.0,
                poster_path VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_featured BOOLEAN DEFAULT 0,
                is_trending BOOLEAN DEFAULT 0
            )",
            'genres' => "CREATE TABLE genres (
                id INT AUTO_INCREMENT PRIMARY KEY,
                tmdb_id INT UNIQUE NOT NULL,
                name VARCHAR(100) NOT NULL
            )",
            'embed_servers' => "CREATE TABLE embed_servers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                movie_url TEXT NOT NULL,
                tv_url TEXT NOT NULL,
                priority INT DEFAULT 1,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )",
            'site_settings' => "CREATE TABLE site_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) NOT NULL UNIQUE,
                setting_value TEXT,
                setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )"
        ];
        
        foreach ($requiredTables as $tableName => $createSQL) {
            if (!$this->tableExists($tableName)) {
                try {
                    $this->pdo->exec($createSQL);
                    echo "<div class='success'>✅ Created table: $tableName</div>";
                    $this->repairLog[] = "Created missing table: $tableName";
                } catch (Exception $e) {
                    echo "<div class='error'>❌ Failed to create table $tableName: " . $e->getMessage() . "</div>";
                }
            } else {
                echo "<div class='info'>ℹ️ Table $tableName already exists</div>";
            }
        }
        echo "</div>";
    }
    
    private function addMissingColumns() {
        echo "<div class='step'>";
        echo "<h3>🔧 Step 2: Adding Missing Columns</h3>";
        
        $columnUpdates = [
            'users' => [
                'created_at' => "ALTER TABLE users ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
                'is_active' => "ALTER TABLE users ADD COLUMN is_active BOOLEAN DEFAULT TRUE"
            ],
            'movies' => [
                'created_at' => "ALTER TABLE movies ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
                'is_featured' => "ALTER TABLE movies ADD COLUMN is_featured BOOLEAN DEFAULT 0",
                'is_trending' => "ALTER TABLE movies ADD COLUMN is_trending BOOLEAN DEFAULT 0"
            ],
            'tv_shows' => [
                'created_at' => "ALTER TABLE tv_shows ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
                'is_featured' => "ALTER TABLE tv_shows ADD COLUMN is_featured BOOLEAN DEFAULT 0",
                'is_trending' => "ALTER TABLE tv_shows ADD COLUMN is_trending BOOLEAN DEFAULT 0"
            ]
        ];
        
        foreach ($columnUpdates as $tableName => $columns) {
            if ($this->tableExists($tableName)) {
                foreach ($columns as $columnName => $alterSQL) {
                    if (!$this->columnExists($tableName, $columnName)) {
                        try {
                            $this->pdo->exec($alterSQL);
                            echo "<div class='success'>✅ Added column $columnName to $tableName</div>";
                            $this->repairLog[] = "Added missing column: $tableName.$columnName";
                        } catch (Exception $e) {
                            echo "<div class='error'>❌ Failed to add column $columnName to $tableName: " . $e->getMessage() . "</div>";
                        }
                    } else {
                        echo "<div class='info'>ℹ️ Column $tableName.$columnName already exists</div>";
                    }
                }
            }
        }
        echo "</div>";
    }
    
    private function createMissingIndexes() {
        echo "<div class='step'>";
        echo "<h3>🔍 Step 3: Creating Missing Indexes</h3>";
        
        $indexes = [
            'movies' => [
                'idx_movies_tmdb' => "CREATE INDEX idx_movies_tmdb ON movies(tmdb_id)",
                'idx_movies_featured' => "CREATE INDEX idx_movies_featured ON movies(is_featured)",
                'idx_movies_rating' => "CREATE INDEX idx_movies_rating ON movies(vote_average)"
            ],
            'tv_shows' => [
                'idx_tv_tmdb' => "CREATE INDEX idx_tv_tmdb ON tv_shows(tmdb_id)",
                'idx_tv_featured' => "CREATE INDEX idx_tv_featured ON tv_shows(is_featured)",
                'idx_tv_rating' => "CREATE INDEX idx_tv_rating ON tv_shows(vote_average)"
            ],
            'users' => [
                'idx_users_email' => "CREATE INDEX idx_users_email ON users(email)",
                'idx_users_role' => "CREATE INDEX idx_users_role ON users(role)"
            ]
        ];
        
        foreach ($indexes as $tableName => $tableIndexes) {
            if ($this->tableExists($tableName)) {
                foreach ($tableIndexes as $indexName => $createSQL) {
                    try {
                        $this->pdo->exec($createSQL);
                        echo "<div class='success'>✅ Created index: $indexName</div>";
                        $this->repairLog[] = "Created index: $indexName";
                    } catch (Exception $e) {
                        // Index might already exist, that's okay
                        if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                            echo "<div class='warning'>⚠️ Index $indexName: " . $e->getMessage() . "</div>";
                        } else {
                            echo "<div class='info'>ℹ️ Index $indexName already exists</div>";
                        }
                    }
                }
            }
        }
        echo "</div>";
    }
    
    private function fixDataIntegrity() {
        echo "<div class='step'>";
        echo "<h3>🔧 Step 4: Fixing Data Integrity</h3>";
        
        // Remove duplicate TMDB IDs in movies
        if ($this->tableExists('movies')) {
            try {
                $stmt = $this->pdo->query("
                    DELETE m1 FROM movies m1
                    INNER JOIN movies m2 
                    WHERE m1.id > m2.id AND m1.tmdb_id = m2.tmdb_id
                ");
                $affected = $stmt->rowCount();
                if ($affected > 0) {
                    echo "<div class='success'>✅ Removed $affected duplicate movie records</div>";
                    $this->repairLog[] = "Removed $affected duplicate movie records";
                } else {
                    echo "<div class='info'>ℹ️ No duplicate movie records found</div>";
                }
            } catch (Exception $e) {
                echo "<div class='error'>❌ Error fixing movie duplicates: " . $e->getMessage() . "</div>";
            }
        }
        
        // Remove duplicate TMDB IDs in tv_shows
        if ($this->tableExists('tv_shows')) {
            try {
                $stmt = $this->pdo->query("
                    DELETE t1 FROM tv_shows t1
                    INNER JOIN tv_shows t2 
                    WHERE t1.id > t2.id AND t1.tmdb_id = t2.tmdb_id
                ");
                $affected = $stmt->rowCount();
                if ($affected > 0) {
                    echo "<div class='success'>✅ Removed $affected duplicate TV show records</div>";
                    $this->repairLog[] = "Removed $affected duplicate TV show records";
                } else {
                    echo "<div class='info'>ℹ️ No duplicate TV show records found</div>";
                }
            } catch (Exception $e) {
                echo "<div class='error'>❌ Error fixing TV show duplicates: " . $e->getMessage() . "</div>";
            }
        }
        
        echo "</div>";
    }

    private function optimizeTables() {
        echo "<div class='step'>";
        echo "<h3>⚡ Step 5: Optimizing Tables</h3>";

        $tables = ['users', 'movies', 'tv_shows', 'genres', 'embed_servers', 'site_settings'];

        foreach ($tables as $table) {
            if ($this->tableExists($table)) {
                try {
                    $this->pdo->exec("OPTIMIZE TABLE $table");
                    echo "<div class='success'>✅ Optimized table: $table</div>";
                    $this->repairLog[] = "Optimized table: $table";
                } catch (Exception $e) {
                    echo "<div class='warning'>⚠️ Could not optimize table $table: " . $e->getMessage() . "</div>";
                }
            }
        }
        echo "</div>";
    }

    private function createDefaultAdmin() {
        echo "<div class='step'>";
        echo "<h3>👤 Step 6: Creating Default Admin User</h3>";

        if ($this->tableExists('users')) {
            try {
                // Check if admin user exists
                $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
                $adminCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

                if ($adminCount == 0) {
                    $defaultPassword = password_hash('admin123', PASSWORD_DEFAULT);
                    $stmt = $this->pdo->prepare("
                        INSERT INTO users (username, email, password, role, is_active)
                        VALUES (?, ?, ?, 'admin', 1)
                    ");
                    $stmt->execute(['admin', '<EMAIL>', $defaultPassword]);

                    echo "<div class='success'>✅ Created default admin user</div>";
                    echo "<div class='warning'>⚠️ <strong>Default credentials:</strong> Username: admin, Password: admin123</div>";
                    echo "<div class='warning'>⚠️ <strong>Please change the password immediately!</strong></div>";
                    $this->repairLog[] = "Created default admin user";
                } else {
                    echo "<div class='info'>ℹ️ Admin user already exists</div>";
                }
            } catch (Exception $e) {
                echo "<div class='error'>❌ Error creating admin user: " . $e->getMessage() . "</div>";
            }
        }
        echo "</div>";
    }

    private function insertDefaultSettings() {
        echo "<div class='step'>";
        echo "<h3>⚙️ Step 7: Inserting Default Settings</h3>";

        if ($this->tableExists('site_settings')) {
            $defaultSettings = [
                ['site_name', 'StreamFlix', 'string'],
                ['site_description', 'Premium Movie & TV Show Streaming Platform', 'string'],
                ['maintenance_mode', '0', 'boolean'],
                ['registration_enabled', '1', 'boolean'],
                ['featured_content_limit', '10', 'number'],
                ['trending_content_limit', '20', 'number'],
                ['player_autoplay', '1', 'boolean'],
                ['show_ratings', '1', 'boolean'],
                ['content_per_page', '24', 'number']
            ];

            foreach ($defaultSettings as $setting) {
                try {
                    $stmt = $this->pdo->prepare("
                        INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type)
                        VALUES (?, ?, ?)
                    ");
                    $stmt->execute($setting);

                    if ($stmt->rowCount() > 0) {
                        echo "<div class='success'>✅ Added setting: {$setting[0]}</div>";
                        $this->repairLog[] = "Added default setting: {$setting[0]}";
                    }
                } catch (Exception $e) {
                    echo "<div class='error'>❌ Error adding setting {$setting[0]}: " . $e->getMessage() . "</div>";
                }
            }
        }

        // Insert default embed servers
        if ($this->tableExists('embed_servers')) {
            $defaultServers = [
                ['LetsEmbed', 'https://letsembed.cc/embed/movie/?id={id}', 'https://letsembed.cc/embed/tv/?id={id}/{season}/{episode}', 1],
                ['AutoEmbed', 'https://player.autoembed.cc/embed/movie/{id}', 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}', 2],
                ['VidJoy', 'https://vidjoy.pro/embed/movie/{id}', 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}', 3]
            ];

            foreach ($defaultServers as $server) {
                try {
                    $stmt = $this->pdo->prepare("
                        INSERT IGNORE INTO embed_servers (name, movie_url, tv_url, priority)
                        VALUES (?, ?, ?, ?)
                    ");
                    $stmt->execute($server);

                    if ($stmt->rowCount() > 0) {
                        echo "<div class='success'>✅ Added embed server: {$server[0]}</div>";
                        $this->repairLog[] = "Added default embed server: {$server[0]}";
                    }
                } catch (Exception $e) {
                    echo "<div class='error'>❌ Error adding server {$server[0]}: " . $e->getMessage() . "</div>";
                }
            }
        }

        echo "</div>";
    }

    private function tableExists($tableName) {
        try {
            $stmt = $this->pdo->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$tableName]);
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    private function columnExists($tableName, $columnName) {
        try {
            $stmt = $this->pdo->prepare("SHOW COLUMNS FROM `$tableName` LIKE ?");
            $stmt->execute([$columnName]);
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }
}

// Handle repair action
if (isset($_POST['action']) && $_POST['action'] === 'repair') {
    $repair = new DatabaseRepair();
    $repair->runRepair();

    echo "<div class='success'>";
    echo "<h2>🎉 Repair Process Completed!</h2>";
    echo "<p>Your database has been repaired and optimized.</p>";
    echo "<p><a href='quick-db-test.php?test=db' class='btn btn-success'>🔍 Run Quick Test</a></p>";
    echo "<p><a href='database-health-checker.php?admin_key=streamflix_admin_2024' class='btn'>🔧 Full Health Check</a></p>";
    echo "</div>";

} else {
    // Show repair form
    echo "<div class='warning'>";
    echo "<h2>⚠️ Warning</h2>";
    echo "<p>This tool will make changes to your database structure and data.</p>";
    echo "<p><strong>It is highly recommended to backup your database before proceeding.</strong></p>";
    echo "</div>";

    echo "<form method='post'>";
    echo "<input type='hidden' name='action' value='repair'>";
    echo "<button type='submit' class='btn btn-danger' onclick='return confirm(\"Are you sure you want to repair the database? Make sure you have a backup!\")'>🔧 Start Database Repair</button>";
    echo "</form>";

    echo "<div class='info'>";
    echo "<h3>What this tool will do:</h3>";
    echo "<ul>";
    echo "<li>✅ Create missing tables</li>";
    echo "<li>✅ Add missing columns</li>";
    echo "<li>✅ Create performance indexes</li>";
    echo "<li>✅ Fix data integrity issues</li>";
    echo "<li>✅ Optimize table performance</li>";
    echo "<li>✅ Create default admin user (if missing)</li>";
    echo "<li>✅ Insert default settings</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div></body></html>";
?>
