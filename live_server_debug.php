<?php
require_once 'config/database.php';

echo "<h2>🔍 Live Server Debug - BDFLiX</h2>";
echo "<p>Debugging server synchronization issue on live site</p>";

try {
    $db = new Database();
    $conn = $db->connect();
    
    echo "<h3>1️⃣ Database Connection</h3>";
    echo "<p style='color: green;'>✅ Database connected successfully</p>";
    
    echo "<h3>2️⃣ embed_servers Table Structure</h3>";
    
    // Check table structure
    try {
        $stmt = $conn->query("DESCRIBE embed_servers");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        
        $has_movie_url = false;
        $has_tv_url = false;
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td><strong>{$column['Field']}</strong></td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
            
            if ($column['Field'] == 'movie_url') $has_movie_url = true;
            if ($column['Field'] == 'tv_url') $has_tv_url = true;
        }
        echo "</table>";
        
        if (!$has_movie_url || !$has_tv_url) {
            echo "<p style='color: red;'>❌ Missing required columns: movie_url or tv_url</p>";
        } else {
            echo "<p style='color: green;'>✅ Required columns exist</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Table structure error: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3>3️⃣ Current Database Servers</h3>";
    
    try {
        $stmt = $conn->query("SELECT * FROM embed_servers ORDER BY priority ASC");
        $db_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p><strong>Database servers count: " . count($db_servers) . "</strong></p>";
        
        if (!empty($db_servers)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Name</th><th>Priority</th><th>Active</th><th>Movie URL</th><th>TV URL</th></tr>";
            
            foreach ($db_servers as $server) {
                $status = $server['is_active'] ? '✅ Active' : '❌ Inactive';
                $movie_url = isset($server['movie_url']) ? substr($server['movie_url'], 0, 40) . '...' : 'N/A';
                $tv_url = isset($server['tv_url']) ? substr($server['tv_url'], 0, 40) . '...' : 'N/A';
                
                $row_style = $server['is_active'] ? '' : 'style="background: #ffeeee;"';
                echo "<tr {$row_style}>";
                echo "<td>{$server['id']}</td>";
                echo "<td><strong>{$server['name']}</strong></td>";
                echo "<td>{$server['priority']}</td>";
                echo "<td>{$status}</td>";
                echo "<td><small>{$movie_url}</small></td>";
                echo "<td><small>{$tv_url}</small></td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: red;'>❌ No servers found in database!</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Database query error: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3>4️⃣ Config File Check</h3>";
    
    // Check config file for hardcoded servers
    $config_file = 'config/database.php';
    if (file_exists($config_file)) {
        $config_content = file_get_contents($config_file);
        
        if (strpos($config_content, '$embed_servers') !== false) {
            echo "<p style='color: red;'>❌ Config file still contains \$embed_servers array!</p>";
            
            // Extract the embed_servers array from config
            if (preg_match('/\$embed_servers\s*=\s*\[(.*?)\];/s', $config_content, $matches)) {
                echo "<p style='color: orange;'>⚠️ Found hardcoded servers in config file</p>";
                echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>";
                echo htmlspecialchars('$embed_servers = [' . substr($matches[1], 0, 200) . '...]');
                echo "</pre>";
            }
        } else {
            echo "<p style='color: green;'>✅ Config file is clean (no hardcoded servers)</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Config file not found!</p>";
    }
    
    echo "<h3>5️⃣ getEmbedUrls Function Test</h3>";
    
    // Test the actual function that player uses
    require_once 'includes/functions.php';
    $streamflix = new StreamFlix();
    
    echo "<h4>Testing Movie (TMDB ID: 950387 - A Minecraft Movie):</h4>";
    $movie_urls = $streamflix->getEmbedUrls('movie', 950387);
    
    echo "<p><strong>Function returns " . count($movie_urls) . " servers:</strong></p>";
    
    if (!empty($movie_urls)) {
        echo "<ol>";
        foreach ($movie_urls as $index => $url) {
            echo "<li><strong>{$url['name']}</strong> (Priority: {$url['priority']})<br>";
            echo "<small>URL: " . htmlspecialchars($url['url']) . "</small></li>";
        }
        echo "</ol>";
    } else {
        echo "<p style='color: red;'>❌ No servers returned by getEmbedUrls function!</p>";
    }
    
    echo "<h3>6️⃣ Admin Panel Server Count</h3>";
    
    // Check what admin panel shows
    try {
        $stmt = $conn->query("SELECT COUNT(*) as total, COUNT(CASE WHEN is_active = 1 THEN 1 END) as active FROM embed_servers");
        $counts = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<p>Total servers in admin: <strong>{$counts['total']}</strong></p>";
        echo "<p>Active servers in admin: <strong>{$counts['active']}</strong></p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Count query error: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3>7️⃣ Problem Analysis</h3>";
    
    $player_count = count($movie_urls);
    $admin_count = isset($counts) ? $counts['active'] : 0;
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-left: 5px solid #007bff; margin: 10px 0;'>";
    echo "<h4>Current Status:</h4>";
    echo "<ul>";
    echo "<li>Player shows: <strong>{$player_count} servers</strong></li>";
    echo "<li>Admin panel has: <strong>{$admin_count} active servers</strong></li>";
    echo "<li>Database total: <strong>" . count($db_servers) . " servers</strong></li>";
    echo "</ul>";
    echo "</div>";
    
    if ($player_count != $admin_count) {
        echo "<div style='background: #fff3cd; padding: 15px; border-left: 5px solid #ffc107; margin: 10px 0;'>";
        echo "<h4 style='color: #856404;'>🚨 MISMATCH DETECTED!</h4>";
        echo "<p>Player and admin panel show different server counts.</p>";
        
        if ($player_count > $admin_count) {
            echo "<p><strong>Likely cause:</strong> Player is loading servers from config file or cache</p>";
            echo "<p><strong>Solution needed:</strong> Remove config fallback and clear cache</p>";
        } else {
            echo "<p><strong>Likely cause:</strong> Some database servers are inactive or malformed</p>";
            echo "<p><strong>Solution needed:</strong> Check server URLs and active status</p>";
        }
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-left: 5px solid #28a745; margin: 10px 0;'>";
        echo "<h4 style='color: #155724;'>✅ COUNTS MATCH</h4>";
        echo "<p>Player and admin show same number of servers. Issue might be elsewhere.</p>";
        echo "</div>";
    }
    
    echo "<h3>8️⃣ Recommended Actions</h3>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0;'>";
    echo "<h4>Next Steps:</h4>";
    echo "<ol>";
    
    if (strpos($config_content, '$embed_servers') !== false) {
        echo "<li><strong>Remove hardcoded servers from config file</strong></li>";
    }
    
    if (count($db_servers) != $player_count) {
        echo "<li><strong>Sync database servers with expected count</strong></li>";
    }
    
    echo "<li><strong>Clear all caches</strong> (browser, server, CDN)</li>";
    echo "<li><strong>Test admin panel changes</strong> immediately</li>";
    echo "<li><strong>Check server URLs</strong> for any malformed entries</li>";
    echo "</ol>";
    
    echo "<p><a href='admin/servers.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 Open Admin Panel</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-left: 5px solid #dc3545; margin: 10px 0;'>";
    echo "<h3 style='color: #721c24;'>❌ Critical Error</h3>";
    echo "<p><strong>Database Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration and connection.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>Debug script for BDFLiX server synchronization issue</small></p>";
?>
