/* Modern Admin Panel Styles - Consistent Design */

.admin-container {
    max-width: 1400px;
    margin: 100px auto 20px;
    padding: 0 20px;
}

.admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 40px;
    border-radius: 20px;
    margin-bottom: 40px;
    text-align: center;
    color: white;
    box-shadow: 0 15px 35px rgba(0,0,0,0.3);
    position: relative;
    overflow: hidden;
}

.admin-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.1) 100%);
    pointer-events: none;
}

.admin-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 700;
    position: relative;
    z-index: 1;
}

.admin-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

.admin-nav {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 40px;
    background: var(--secondary-color);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.1);
}

.admin-nav a {
    padding: 15px 25px;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 10px;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.admin-nav a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.admin-nav a:hover::before {
    left: 100%;
}

.admin-nav a:hover,
.admin-nav a.active {
    background: #e50914;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(229, 9, 20, 0.4);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.stat-card {
    background: var(--secondary-color);
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    border: 1px solid rgba(255,255,255,0.1);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), #ff6b6b);
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.stat-label {
    color: var(--text-color);
    font-size: 1rem;
    font-weight: 500;
    opacity: 0.8;
}

.stat-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.7;
}

.content-section {
    background: var(--secondary-color);
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.1);
}

.content-section h2 {
    color: var(--text-color);
    margin-bottom: 20px;
    font-size: 1.5rem;
    font-weight: 600;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #e50914;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(229, 9, 20, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
    transform: translateY(-2px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-color);
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #333;
    border-radius: 8px;
    background: var(--bg-color);
    color: var(--text-color);
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    outline: none;
}

.success-message {
    background: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #c3e6cb;
    font-weight: 500;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #f5c6cb;
    font-weight: 500;
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--secondary-color);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.table th,
.table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.table th {
    background: rgba(255,255,255,0.1);
    font-weight: 600;
    color: var(--text-color);
}

.table td {
    color: var(--text-color);
}

.table tr:hover {
    background: rgba(255,255,255,0.05);
}

.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 30px;
}

.pagination a,
.pagination span {
    padding: 10px 15px;
    background: var(--secondary-color);
    color: var(--text-color);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.pagination a:hover,
.pagination .current {
    background: var(--primary-color);
    color: white;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .admin-container {
        margin-top: 80px;
        padding: 0 15px;
    }
    
    .admin-nav {
        justify-content: center;
    }
    
    .admin-nav a {
        padding: 12px 18px;
        font-size: 13px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }
    
    .content-section {
        padding: 20px;
    }
    
    .admin-header {
        padding: 30px 20px;
    }
    
    .admin-header h1 {
        font-size: 2rem;
    }
    
    .table {
        font-size: 14px;
    }
    
    .table th,
    .table td {
        padding: 10px;
    }
}

@media (max-width: 480px) {
    .admin-nav a {
        padding: 10px 15px;
        font-size: 12px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .admin-header h1 {
        font-size: 1.8rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
}
