package com.streamflix.app.ui.theme;

import androidx.compose.material3.Typography;
import androidx.compose.ui.text.TextStyle;
import androidx.compose.ui.text.font.FontWeight;
import com.streamflix.app.R;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b+\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006R\u0011\u0010\u0007\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0006R\u0011\u0010\t\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u0006R\u0011\u0010\u000b\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u0006R\u0011\u0010\r\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u0006R\u0011\u0010\u000f\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0006R\u0011\u0010\u0011\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0006R\u0011\u0010\u0013\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0006R\u0011\u0010\u0015\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0006R\u0011\u0010\u0017\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0006R\u0011\u0010\u0019\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0006R\u0011\u0010\u001b\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0006R\u0011\u0010\u001d\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0006R\u0011\u0010\u001f\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0006R\u0011\u0010!\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0006R\u0011\u0010#\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u0006R\u0011\u0010%\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u0006R\u0011\u0010\'\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\u0006R\u0011\u0010)\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010\u0006R\u0011\u0010+\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010\u0006R\u0011\u0010-\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010\u0006\u00a8\u0006/"}, d2 = {"Lcom/streamflix/app/ui/theme/StreamFlixTextStyles;", "", "()V", "ButtonText", "Landroidx/compose/ui/text/TextStyle;", "getButtonText", "()Landroidx/compose/ui/text/TextStyle;", "CardSubtitle", "getCardSubtitle", "CardTitle", "getCardTitle", "CastCharacter", "getCastCharacter", "CastName", "getCastName", "Duration", "getDuration", "EpisodeNumber", "getEpisodeNumber", "EpisodeTitle", "getEpisodeTitle", "ErrorMessage", "getErrorMessage", "Genre", "getGenre", "LoadingText", "getLoadingText", "MovieOverview", "getMovieOverview", "MovieSubtitle", "getMovieSubtitle", "MovieTitle", "getMovieTitle", "PlayerSubtitle", "getPlayerSubtitle", "PlayerTitle", "getPlayerTitle", "Rating", "getRating", "SearchHint", "getSearchHint", "SeasonTitle", "getSeasonTitle", "SectionTitle", "getSectionTitle", "TabText", "getTabText", "app_debug"})
public final class StreamFlixTextStyles {
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle MovieTitle = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle MovieSubtitle = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle MovieOverview = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle Rating = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle Genre = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle Duration = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle ButtonText = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle TabText = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle SearchHint = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle ErrorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle LoadingText = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle SectionTitle = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle CardTitle = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle CardSubtitle = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle PlayerTitle = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle PlayerSubtitle = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle EpisodeTitle = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle EpisodeNumber = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle SeasonTitle = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle CastName = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.TextStyle CastCharacter = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.streamflix.app.ui.theme.StreamFlixTextStyles INSTANCE = null;
    
    private StreamFlixTextStyles() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getMovieTitle() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getMovieSubtitle() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getMovieOverview() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getRating() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getGenre() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getDuration() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getButtonText() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getTabText() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getSearchHint() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getErrorMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getLoadingText() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getSectionTitle() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getCardTitle() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getCardSubtitle() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getPlayerTitle() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getPlayerSubtitle() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getEpisodeTitle() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getEpisodeNumber() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getSeasonTitle() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getCastName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.TextStyle getCastCharacter() {
        return null;
    }
}