<?php
require_once '../includes/functions.php';

header('Content-Type: application/json');

$query = sanitizeInput($_GET['q'] ?? '');

if (strlen($query) < 2) {
    echo json_encode(['success' => false, 'message' => 'Query too short']);
    exit();
}

try {
    $db = new Database();
    $conn = $db->connect();
    
    $results = [];
    
    // Search movies
    $stmt = $conn->prepare("
        SELECT id, tmdb_id, title as name, poster_path, release_date, 'movie' as type
        FROM movies 
        WHERE title LIKE :query 
        ORDER BY popularity DESC 
        LIMIT 10
    ");
    $stmt->execute([':query' => "%{$query}%"]);
    $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Search TV shows
    $stmt = $conn->prepare("
        SELECT id, tmdb_id, name, poster_path, first_air_date as release_date, 'tv_show' as type
        FROM tv_shows 
        WHERE name LIKE :query 
        ORDER BY popularity DESC 
        LIMIT 10
    ");
    $stmt->execute([':query' => "%{$query}%"]);
    $tv_shows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $results = array_merge($movies, $tv_shows);
    
    // Sort by relevance (you can implement more sophisticated sorting)
    usort($results, function($a, $b) {
        return $b['popularity'] ?? 0 <=> $a['popularity'] ?? 0;
    });
    
    $results = array_slice($results, 0, 10);
    
    echo json_encode([
        'success' => true,
        'results' => $results
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
