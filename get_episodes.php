<?php
require_once 'config/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Check if required parameters are provided
if (!isset($_GET['tv_show_id']) || !isset($_GET['season'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing required parameters']);
    exit;
}

$tv_show_id = (int)$_GET['tv_show_id'];
$season_number = (int)$_GET['season'];

try {
    // Get episodes for the specified season
    $stmt = $conn->prepare("
        SELECT 
            e.episode_number,
            e.name,
            e.overview,
            e.air_date,
            e.runtime,
            e.vote_average,
            e.still_path
        FROM episodes e
        JOIN seasons s ON e.season_id = s.id
        WHERE s.tv_show_id = :tv_show_id 
        AND s.season_number = :season_number
        ORDER BY e.episode_number ASC
    ");
    
    $stmt->execute([
        ':tv_show_id' => $tv_show_id,
        ':season_number' => $season_number
    ]);
    
    $episodes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // If no episodes found in database, return empty array
    // The frontend will handle fallback episode generation
    echo json_encode($episodes);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
}
?>
