<?php
require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

try {
    $db = new Database();
    $conn = $db->connect();
    
    echo "<h2>Setting up Hentai Support...</h2>";
    
    // Step 1: Add hentai_url column
    echo "<p>1. Adding hentai_url column...</p>";
    try {
        $stmt = $conn->query("SHOW COLUMNS FROM embed_servers LIKE 'hentai_url'");
        if ($stmt->rowCount() == 0) {
            $conn->exec("ALTER TABLE embed_servers ADD COLUMN hentai_url TEXT NULL AFTER tv_url");
            echo "<p style='color: green;'>✅ Added hentai_url column</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ hentai_url column already exists</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
    
    // Step 2: Update LetsEmbed server
    echo "<p>2. Updating LetsEmbed server...</p>";
    try {
        $stmt = $conn->prepare("UPDATE embed_servers SET hentai_url = ? WHERE name = 'LetsEmbed'");
        $hentai_url = 'https://letsembed.cc/embed/hentai/?id={tmdb_id}/{season_number}/{episode_number}';
        $stmt->execute([$hentai_url]);
        
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✅ Updated LetsEmbed server with hentai URL</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ LetsEmbed server not found or already updated</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
    
    // Step 3: Add Hentai genre
    echo "<p>3. Adding Hentai genre...</p>";
    try {
        $stmt = $conn->prepare("INSERT IGNORE INTO genres (name, tmdb_id) VALUES (?, ?)");
        $stmt->execute(['Hentai', 99999]);
        
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✅ Added Hentai genre</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ Hentai genre already exists</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
    
    // Step 4: Mark existing hentai content
    echo "<p>4. Marking existing hentai content...</p>";
    try {
        // Get hentai genre ID
        $stmt = $conn->prepare("SELECT id FROM genres WHERE name = 'Hentai'");
        $stmt->execute();
        $hentai_genre = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($hentai_genre) {
            $hentai_keywords = ['hentai', 'ecchi', 'adult', '18+', 'xxx', 'erotic'];
            $marked_count = 0;
            
            foreach ($hentai_keywords as $keyword) {
                // Mark TV shows
                $stmt = $conn->prepare("
                    INSERT IGNORE INTO tv_show_genres (tv_show_id, genre_id)
                    SELECT id, ? FROM tv_shows 
                    WHERE LOWER(name) LIKE ? OR LOWER(overview) LIKE ?
                ");
                $like_pattern = '%' . strtolower($keyword) . '%';
                $stmt->execute([$hentai_genre['id'], $like_pattern, $like_pattern]);
                $marked_count += $stmt->rowCount();
            }
            
            echo "<p style='color: green;'>✅ Marked {$marked_count} content items as hentai</p>";
        } else {
            echo "<p style='color: red;'>❌ Hentai genre not found</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3 style='color: green;'>✅ Hentai support setup complete!</h3>";
    echo "<p><strong>Now hentai content will use:</strong> https://letsembed.cc/embed/hentai/?id={tmdb_id}/{season_number}/{episode_number}</p>";
    echo "<p><a href='servers.php'>← Back to Servers</a> | <a href='import.php'>Import Content →</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Hentai Setup - StreamFlix Admin</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #1a1a1a; color: white; }
        h2, h3 { color: #e50914; }
        p { margin: 10px 0; }
        a { color: #e50914; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
</body>
</html>
