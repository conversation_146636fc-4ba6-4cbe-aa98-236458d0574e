package com.streamflix.app.data.api;

import com.streamflix.app.data.model.*;
import retrofit2.Response;
import retrofit2.http.*;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0013\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BA\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00030\b\u0012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00030\b\u0012\u0006\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\fJ\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0006H\u00c6\u0003J\u000f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00030\bH\u00c6\u0003J\u000f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00030\bH\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u000bH\u00c6\u0003JQ\u0010\u001d\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00030\b2\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00030\b2\b\b\u0002\u0010\n\u001a\u00020\u000bH\u00c6\u0001J\u0013\u0010\u001e\u001a\u00020\u001f2\b\u0010 \u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010!\u001a\u00020\"H\u00d6\u0001J\t\u0010#\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u0017\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00030\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00030\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0011\u00a8\u0006$"}, d2 = {"Lcom/streamflix/app/data/api/AppConfig;", "", "app_name", "", "api_version", "features", "Lcom/streamflix/app/data/api/AppFeatures;", "content_types", "", "supported_qualities", "pagination", "Lcom/streamflix/app/data/api/PaginationConfig;", "(Ljava/lang/String;Ljava/lang/String;Lcom/streamflix/app/data/api/AppFeatures;Ljava/util/List;Ljava/util/List;Lcom/streamflix/app/data/api/PaginationConfig;)V", "getApi_version", "()Ljava/lang/String;", "getApp_name", "getContent_types", "()Ljava/util/List;", "getFeatures", "()Lcom/streamflix/app/data/api/AppFeatures;", "getPagination", "()Lcom/streamflix/app/data/api/PaginationConfig;", "getSupported_qualities", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
public final class AppConfig {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String app_name = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String api_version = null;
    @org.jetbrains.annotations.NotNull()
    private final com.streamflix.app.data.api.AppFeatures features = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> content_types = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> supported_qualities = null;
    @org.jetbrains.annotations.NotNull()
    private final com.streamflix.app.data.api.PaginationConfig pagination = null;
    
    public AppConfig(@org.jetbrains.annotations.NotNull()
    java.lang.String app_name, @org.jetbrains.annotations.NotNull()
    java.lang.String api_version, @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.api.AppFeatures features, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> content_types, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> supported_qualities, @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.api.PaginationConfig pagination) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getApp_name() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getApi_version() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.streamflix.app.data.api.AppFeatures getFeatures() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getContent_types() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getSupported_qualities() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.streamflix.app.data.api.PaginationConfig getPagination() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.streamflix.app.data.api.AppFeatures component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.streamflix.app.data.api.PaginationConfig component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.streamflix.app.data.api.AppConfig copy(@org.jetbrains.annotations.NotNull()
    java.lang.String app_name, @org.jetbrains.annotations.NotNull()
    java.lang.String api_version, @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.api.AppFeatures features, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> content_types, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> supported_qualities, @org.jetbrains.annotations.NotNull()
    com.streamflix.app.data.api.PaginationConfig pagination) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}