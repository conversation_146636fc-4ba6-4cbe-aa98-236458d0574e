package com.streamflix.app;

import dagger.hilt.InstallIn;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.components.SingletonComponent;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = StreamFlixApplication.class
)
@GeneratedEntryPoint
@InstallIn(SingletonComponent.class)
public interface StreamFlixApplication_GeneratedInjector {
  void injectStreamFlixApplication(StreamFlixApplication streamFlixApplication);
}
