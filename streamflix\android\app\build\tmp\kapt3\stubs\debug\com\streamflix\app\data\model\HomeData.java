package com.streamflix.app.data.model;

import com.google.gson.annotations.SerializedName;

/**
 * Home page data model
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001Bu\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u0012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u0012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u0012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0003\u0012\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003\u00a2\u0006\u0002\u0010\u000eJ\u000f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u00c6\u0003J\u000f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u00c6\u0003J\u000f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u00c6\u0003J\u000f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\f0\u0003H\u00c6\u0003J\u000f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u00c6\u0003J\u0089\u0001\u0010 \u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u00032\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00060\u00032\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00060\u00032\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u00032\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u00c6\u0001J\u0013\u0010!\u001a\u00020\"2\b\u0010#\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010$\u001a\u00020%H\u00d6\u0001J\t\u0010&\u001a\u00020\'H\u00d6\u0001R\u001c\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u001c\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0010R\u001c\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0010R\u001c\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00040\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0010R\u001c\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00060\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0010R\u001c\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0010R\u001c\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00040\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0010R\u001c\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00060\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0010\u00a8\u0006("}, d2 = {"Lcom/streamflix/app/data/model/HomeData;", "", "featuredMovies", "", "Lcom/streamflix/app/data/model/Movie;", "featuredTvShows", "Lcom/streamflix/app/data/model/TvShow;", "trendingMovies", "trendingTvShows", "latestMovies", "latestTvShows", "continueWatching", "Lcom/streamflix/app/data/model/WatchHistoryItem;", "recommended", "(Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V", "getContinueWatching", "()Ljava/util/List;", "getFeaturedMovies", "getFeaturedTvShows", "getLatestMovies", "getLatestTvShows", "getRecommended", "getTrendingMovies", "getTrendingTvShows", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
public final class HomeData {
    @com.google.gson.annotations.SerializedName(value = "featured_movies")
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.streamflix.app.data.model.Movie> featuredMovies = null;
    @com.google.gson.annotations.SerializedName(value = "featured_tv_shows")
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.streamflix.app.data.model.TvShow> featuredTvShows = null;
    @com.google.gson.annotations.SerializedName(value = "trending_movies")
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.streamflix.app.data.model.Movie> trendingMovies = null;
    @com.google.gson.annotations.SerializedName(value = "trending_tv_shows")
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.streamflix.app.data.model.TvShow> trendingTvShows = null;
    @com.google.gson.annotations.SerializedName(value = "latest_movies")
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.streamflix.app.data.model.Movie> latestMovies = null;
    @com.google.gson.annotations.SerializedName(value = "latest_tv_shows")
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.streamflix.app.data.model.TvShow> latestTvShows = null;
    @com.google.gson.annotations.SerializedName(value = "continue_watching")
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.streamflix.app.data.model.WatchHistoryItem> continueWatching = null;
    @com.google.gson.annotations.SerializedName(value = "recommended")
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.Object> recommended = null;
    
    public HomeData(@org.jetbrains.annotations.NotNull()
    java.util.List<com.streamflix.app.data.model.Movie> featuredMovies, @org.jetbrains.annotations.NotNull()
    java.util.List<com.streamflix.app.data.model.TvShow> featuredTvShows, @org.jetbrains.annotations.NotNull()
    java.util.List<com.streamflix.app.data.model.Movie> trendingMovies, @org.jetbrains.annotations.NotNull()
    java.util.List<com.streamflix.app.data.model.TvShow> trendingTvShows, @org.jetbrains.annotations.NotNull()
    java.util.List<com.streamflix.app.data.model.Movie> latestMovies, @org.jetbrains.annotations.NotNull()
    java.util.List<com.streamflix.app.data.model.TvShow> latestTvShows, @org.jetbrains.annotations.NotNull()
    java.util.List<com.streamflix.app.data.model.WatchHistoryItem> continueWatching, @org.jetbrains.annotations.NotNull()
    java.util.List<? extends java.lang.Object> recommended) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.streamflix.app.data.model.Movie> getFeaturedMovies() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.streamflix.app.data.model.TvShow> getFeaturedTvShows() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.streamflix.app.data.model.Movie> getTrendingMovies() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.streamflix.app.data.model.TvShow> getTrendingTvShows() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.streamflix.app.data.model.Movie> getLatestMovies() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.streamflix.app.data.model.TvShow> getLatestTvShows() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.streamflix.app.data.model.WatchHistoryItem> getContinueWatching() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.Object> getRecommended() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.streamflix.app.data.model.Movie> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.streamflix.app.data.model.TvShow> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.streamflix.app.data.model.Movie> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.streamflix.app.data.model.TvShow> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.streamflix.app.data.model.Movie> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.streamflix.app.data.model.TvShow> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.streamflix.app.data.model.WatchHistoryItem> component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.Object> component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.streamflix.app.data.model.HomeData copy(@org.jetbrains.annotations.NotNull()
    java.util.List<com.streamflix.app.data.model.Movie> featuredMovies, @org.jetbrains.annotations.NotNull()
    java.util.List<com.streamflix.app.data.model.TvShow> featuredTvShows, @org.jetbrains.annotations.NotNull()
    java.util.List<com.streamflix.app.data.model.Movie> trendingMovies, @org.jetbrains.annotations.NotNull()
    java.util.List<com.streamflix.app.data.model.TvShow> trendingTvShows, @org.jetbrains.annotations.NotNull()
    java.util.List<com.streamflix.app.data.model.Movie> latestMovies, @org.jetbrains.annotations.NotNull()
    java.util.List<com.streamflix.app.data.model.TvShow> latestTvShows, @org.jetbrains.annotations.NotNull()
    java.util.List<com.streamflix.app.data.model.WatchHistoryItem> continueWatching, @org.jetbrains.annotations.NotNull()
    java.util.List<? extends java.lang.Object> recommended) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}