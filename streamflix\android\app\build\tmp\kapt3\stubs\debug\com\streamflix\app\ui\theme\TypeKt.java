package com.streamflix.app.ui.theme;

import androidx.compose.material3.Typography;
import androidx.compose.ui.text.TextStyle;
import androidx.compose.ui.text.font.FontWeight;
import com.streamflix.app.R;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0012\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\"\u0011\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0003\"\u0011\u0010\u0004\u001a\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0003\"\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\t\u00a8\u0006\n"}, d2 = {"DefaultFontFamily", "Landroidx/compose/ui/text/font/SystemFontFamily;", "getDefaultFontFamily", "()Landroidx/compose/ui/text/font/SystemFontFamily;", "NetflixSansFamily", "getNetflixSansFamily", "Typography", "Landroidx/compose/material3/Typography;", "getTypography", "()Landroidx/compose/material3/Typography;", "app_debug"})
public final class TypeKt {
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.font.SystemFontFamily NetflixSansFamily = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.ui.text.font.SystemFontFamily DefaultFontFamily = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.material3.Typography Typography = null;
    
    @org.jetbrains.annotations.NotNull()
    public static final androidx.compose.ui.text.font.SystemFontFamily getNetflixSansFamily() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final androidx.compose.ui.text.font.SystemFontFamily getDefaultFontFamily() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final androidx.compose.material3.Typography getTypography() {
        return null;
    }
}