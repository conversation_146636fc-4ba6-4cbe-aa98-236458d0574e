<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$user_id = (int)($_GET['id'] ?? 0);
$message = '';
$error = '';

if ($user_id <= 0) {
    redirectTo('users.php');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $db = new Database();
        $conn = $db->connect();
        
        if ($action === 'update_user') {
            $username = sanitizeInput($_POST['username'] ?? '');
            $email = sanitizeInput($_POST['email'] ?? '');
            $role = sanitizeInput($_POST['role'] ?? 'user');
            $is_active = isset($_POST['is_active']) ? 1 : 0;
            $is_premium = isset($_POST['is_premium']) ? 1 : 0;
            
            if (!empty($username) && !empty($email)) {
                // Check if username/email already exists for other users
                $stmt = $conn->prepare("SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?");
                $stmt->execute([$username, $email, $user_id]);
                
                if ($stmt->rowCount() > 0) {
                    $error = 'Username or email already exists for another user.';
                } else {
                    $stmt = $conn->prepare("
                        UPDATE users 
                        SET username = ?, email = ?, role = ?, is_active = ?, is_premium = ?
                        WHERE id = ?
                    ");
                    $stmt->execute([$username, $email, $role, $is_active, $is_premium, $user_id]);
                    $message = 'User updated successfully!';
                }
            } else {
                $error = 'Username and email are required.';
            }
        }
        
        if ($action === 'change_password') {
            $new_password = $_POST['new_password'] ?? '';
            $confirm_password = $_POST['confirm_password'] ?? '';
            
            if (!empty($new_password) && $new_password === $confirm_password) {
                if (strlen($new_password) >= 6) {
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
                    $stmt->execute([$hashed_password, $user_id]);
                    $message = 'Password updated successfully!';
                } else {
                    $error = 'Password must be at least 6 characters long.';
                }
            } else {
                $error = 'Passwords do not match or are empty.';
            }
        }
        
        if ($action === 'ban_user') {
            $ban_reason = sanitizeInput($_POST['ban_reason'] ?? '');
            
            if ($user_id != $_SESSION['user_id']) {
                // Deactivate user
                $stmt = $conn->prepare("UPDATE users SET is_active = 0 WHERE id = ?");
                $stmt->execute([$user_id]);
                
                // Add ban record
                $stmt = $conn->prepare("
                    INSERT INTO user_bans (user_id, reason, banned_by) 
                    VALUES (?, ?, ?)
                ");
                $stmt->execute([$user_id, $ban_reason, $_SESSION['user_id']]);
                
                $message = 'User banned successfully!';
            } else {
                $error = 'You cannot ban yourself.';
            }
        }
        
        if ($action === 'unban_user') {
            if ($user_id != $_SESSION['user_id']) {
                // Activate user
                $stmt = $conn->prepare("UPDATE users SET is_active = 1 WHERE id = ?");
                $stmt->execute([$user_id]);
                
                // Deactivate ban record
                $stmt = $conn->prepare("UPDATE user_bans SET is_active = 0 WHERE user_id = ? AND is_active = 1");
                $stmt->execute([$user_id]);
                
                $message = 'User unbanned successfully!';
            }
        }
        
    } catch (Exception $e) {
        $error = 'Error: ' . $e->getMessage();
    }
}

// Get user details
try {
    $db = new Database();
    $conn = $db->connect();
    
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        redirectTo('users.php');
    }
    
    // Get user ban status
    $stmt = $conn->prepare("
        SELECT ub.*, u.username as banned_by_username 
        FROM user_bans ub 
        LEFT JOIN users u ON ub.banned_by = u.id 
        WHERE ub.user_id = ? AND ub.is_active = 1 
        ORDER BY ub.banned_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$user_id]);
    $ban_info = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get user statistics
    $stmt = $conn->prepare("SELECT COUNT(*) FROM watchlist WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $watchlist_count = $stmt->fetchColumn();
    
} catch (Exception $e) {
    $error = 'Database error: ' . $e->getMessage();
    redirectTo('users.php');
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit User - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container {
            max-width: 800px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .edit-form {
            background: var(--secondary-color);
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--dark-bg);
            color: var(--text-primary);
            font-size: 14px;
        }
        
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), #ff6b6b);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 2rem;
            margin-bottom: 20px;
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 1px solid #28a745;
        }
        
        .alert-error {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid #dc3545;
        }
        
        .alert-warning {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            border: 1px solid #ffc107;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .ban-section {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid #dc3545;
            border-radius: 8px;
            padding: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: var(--dark-bg);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>✏️ Edit User</h1>
            <p>Edit user details and manage account settings</p>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <?php if ($ban_info): ?>
            <div class="alert alert-warning">
                <strong>⚠️ This user is currently banned</strong><br>
                Reason: <?php echo htmlspecialchars($ban_info['reason']); ?><br>
                Banned by: <?php echo htmlspecialchars($ban_info['banned_by_username']); ?><br>
                Banned on: <?php echo date('M j, Y g:i A', strtotime($ban_info['banned_at'])); ?>
            </div>
        <?php endif; ?>

        <div class="edit-form">
            <div style="display: flex; gap: 30px; margin-bottom: 30px;">
                <div>
                    <div class="user-avatar">
                        <?php echo strtoupper(substr($user['username'], 0, 2)); ?>
                    </div>
                </div>
                
                <div style="flex: 1;">
                    <h3><?php echo htmlspecialchars($user['username']); ?></h3>
                    <p style="color: var(--text-secondary);"><?php echo htmlspecialchars($user['email']); ?></p>
                    <p style="color: var(--text-secondary);">
                        <strong>Role:</strong> <?php echo ucfirst($user['role']); ?> | 
                        <strong>Status:</strong> <?php echo $user['is_active'] ? 'Active' : 'Inactive'; ?> |
                        <strong>Premium:</strong> <?php echo $user['is_premium'] ? 'Yes' : 'No'; ?>
                    </p>
                    <p style="color: var(--text-secondary);">
                        <strong>Joined:</strong> <?php echo date('M j, Y', strtotime($user['created_at'])); ?>
                    </p>
                </div>
            </div>
            
            <!-- User Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $watchlist_count; ?></div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Watchlist Items</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $user['id']; ?></div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">User ID</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo date('M j', strtotime($user['created_at'])); ?></div>
                    <div style="color: var(--text-secondary); font-size: 0.9rem;">Join Date</div>
                </div>
            </div>
        </div>

        <!-- Edit User Form -->
        <div class="edit-form">
            <h3>👤 Edit User Details</h3>
            <form method="POST">
                <input type="hidden" name="action" value="update_user">

                <div class="form-row">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username"
                               value="<?php echo htmlspecialchars($user['username']); ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" name="email"
                               value="<?php echo htmlspecialchars($user['email']); ?>" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="role">Role</label>
                        <select id="role" name="role" <?php echo $user['id'] == $_SESSION['user_id'] ? 'disabled' : ''; ?>>
                            <option value="user" <?php echo $user['role'] == 'user' ? 'selected' : ''; ?>>User</option>
                            <option value="admin" <?php echo $user['role'] == 'admin' ? 'selected' : ''; ?>>Admin</option>
                        </select>
                        <?php if ($user['id'] == $_SESSION['user_id']): ?>
                            <input type="hidden" name="role" value="<?php echo $user['role']; ?>">
                            <small style="color: var(--text-secondary);">You cannot change your own role</small>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="is_active" name="is_active"
                                   <?php echo $user['is_active'] ? 'checked' : ''; ?>
                                   <?php echo $user['id'] == $_SESSION['user_id'] ? 'disabled' : ''; ?>>
                            <label for="is_active">Active User</label>
                        </div>
                        <?php if ($user['id'] == $_SESSION['user_id']): ?>
                            <input type="hidden" name="is_active" value="1">
                        <?php endif; ?>
                    </div>

                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="is_premium" name="is_premium"
                                   <?php echo $user['is_premium'] ? 'checked' : ''; ?>>
                            <label for="is_premium">Premium User</label>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">Update User</button>
            </form>
        </div>

        <!-- Change Password -->
        <div class="edit-form">
            <h3>🔒 Change Password</h3>
            <form method="POST">
                <input type="hidden" name="action" value="change_password">

                <div class="form-row">
                    <div class="form-group">
                        <label for="new_password">New Password</label>
                        <input type="password" id="new_password" name="new_password"
                               placeholder="Enter new password" required>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password">Confirm Password</label>
                        <input type="password" id="confirm_password" name="confirm_password"
                               placeholder="Confirm new password" required>
                    </div>
                </div>

                <button type="submit" class="btn btn-secondary">Change Password</button>
            </form>
        </div>

        <!-- Ban/Unban User -->
        <?php if ($user['id'] != $_SESSION['user_id']): ?>
            <div class="edit-form ban-section">
                <?php if ($ban_info): ?>
                    <h3>🔓 Unban User</h3>
                    <p>This user is currently banned. Click the button below to unban them.</p>
                    <form method="POST" onsubmit="return confirm('Are you sure you want to unban this user?')">
                        <input type="hidden" name="action" value="unban_user">
                        <button type="submit" class="btn btn-success">Unban User</button>
                    </form>
                <?php else: ?>
                    <h3>🔒 Ban User</h3>
                    <p>Banning a user will deactivate their account and prevent them from logging in.</p>
                    <form method="POST" onsubmit="return confirm('Are you sure you want to ban this user?')">
                        <input type="hidden" name="action" value="ban_user">

                        <div class="form-group">
                            <label for="ban_reason">Ban Reason</label>
                            <textarea id="ban_reason" name="ban_reason"
                                      placeholder="Enter reason for banning this user..." required></textarea>
                        </div>

                        <button type="submit" class="btn btn-danger">Ban User</button>
                    </form>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <!-- Danger Zone -->
        <?php if ($user['id'] != $_SESSION['user_id']): ?>
            <div class="edit-form" style="border: 2px solid #dc3545;">
                <h3 style="color: #dc3545;">⚠️ Danger Zone</h3>
                <p style="color: var(--text-secondary);">
                    Deleting a user is permanent and cannot be undone. All their data including watchlist will be removed.
                </p>
                <a href="delete-user.php?id=<?php echo $user['id']; ?>"
                   class="btn btn-danger"
                   onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone!')">
                    Delete User
                </a>
            </div>
        <?php endif; ?>

        <div style="margin-top: 30px; text-align: center;">
            <a href="users.php" class="btn btn-secondary">← Back to Users</a>
            <a href="user-management.php" class="btn btn-secondary">🔧 Advanced Management</a>
        </div>
    </div>
</body>
</html>
