<?php
session_start();
require_once '../config/database.php';

// Simple admin check
$is_admin = false;
if (isset($_SESSION['user_id'])) {
    if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
        $is_admin = true;
    } elseif (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
        $is_admin = true;
    }
}

if (!$is_admin) {
    try {
        $db = new Database();
        $conn = $db->connect();
        
        if (isset($_SESSION['user_id'])) {
            $stmt = $conn->prepare("SELECT role FROM users WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && $user['role'] === 'admin') {
                $is_admin = true;
                $_SESSION['role'] = 'admin';
            }
        }
    } catch (Exception $e) {
        // Database check failed
    }
}

if (!$is_admin) {
    header('Location: ../login.php');
    exit;
}

// Database connection
if (!isset($conn)) {
    $db = new Database();
    $conn = $db->connect();
}

echo "<h1>🗑️ Bulk Delete TV Series</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
    .container { max-width: 1000px; margin: 0 auto; }
    .section { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    .danger { background: #f8d7da; color: #721c24; border: 2px solid #dc3545; }
    .btn { padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 6px; display: inline-block; margin: 5px; border: none; cursor: pointer; font-size: 16px; }
    .btn-danger { background: #dc3545; }
    .btn-warning { background: #ffc107; color: #212529; }
    .btn-success { background: #28a745; }
    .btn-secondary { background: #6c757d; }
    .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
    .stat-card { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border: 2px solid #dee2e6; }
    .stat-number { font-size: 2rem; font-weight: bold; margin-bottom: 5px; }
    .danger-number { color: #dc3545; }
    table { width: 100%; border-collapse: collapse; margin: 15px 0; }
    th, td { padding: 8px; border: 1px solid #ddd; text-align: left; font-size: 0.9rem; }
    th { background: #f8f9fa; }
    .progress { background: #e9ecef; border-radius: 4px; height: 20px; margin: 10px 0; }
    .progress-bar { background: #dc3545; height: 100%; border-radius: 4px; transition: width 0.3s ease; }
    .checkbox-group { margin: 15px 0; }
    .checkbox-group label { display: block; margin: 8px 0; padding: 10px; background: #f8f9fa; border-radius: 5px; cursor: pointer; }
    .checkbox-group input[type='checkbox'] { margin-right: 10px; }
    .confirmation-box { background: #fff3cd; border: 2px solid #ffc107; padding: 20px; border-radius: 8px; margin: 20px 0; }
    .final-warning { background: #f8d7da; border: 3px solid #dc3545; padding: 25px; border-radius: 8px; margin: 20px 0; text-align: center; }
</style>";

echo "<div class='container'>";

try {
    // Get current TV series statistics
    $stats = [];
    
    $stmt = $conn->query("SELECT COUNT(*) FROM tv_shows");
    $stats['total_tv_shows'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM tv_shows WHERE content_type = 'hentai'");
    $stats['hentai_tv_shows'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM tv_shows WHERE content_type = 'anime'");
    $stats['anime_tv_shows'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM tv_shows WHERE content_type = 'tv_show' OR content_type IS NULL");
    $stats['regular_tv_shows'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM tv_show_genres");
    $stats['tv_show_genres'] = $stmt->fetchColumn();
    
    // Show current statistics
    echo "<div class='section'>";
    echo "<h2>📊 Current TV Series Statistics</h2>";
    echo "<div class='stats'>";
    echo "<div class='stat-card'>";
    echo "<div class='stat-number danger-number'>{$stats['total_tv_shows']}</div>";
    echo "<div>Total TV Series</div>";
    echo "</div>";
    echo "<div class='stat-card'>";
    echo "<div class='stat-number'>{$stats['regular_tv_shows']}</div>";
    echo "<div>Regular TV Shows</div>";
    echo "</div>";
    echo "<div class='stat-card'>";
    echo "<div class='stat-number'>{$stats['hentai_tv_shows']}</div>";
    echo "<div>Hentai Series</div>";
    echo "</div>";
    echo "<div class='stat-card'>";
    echo "<div class='stat-number'>{$stats['anime_tv_shows']}</div>";
    echo "<div>Anime Series</div>";
    echo "</div>";
    echo "<div class='stat-card'>";
    echo "<div class='stat-number'>{$stats['tv_show_genres']}</div>";
    echo "<div>Genre Relations</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // Handle deletion
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';
        $confirmed = isset($_POST['confirmed']) && $_POST['confirmed'] === 'yes';
        $delete_genres = isset($_POST['delete_genres']);
        $delete_type = $_POST['delete_type'] ?? 'all';
        
        if ($action === 'delete_tv_series' && $confirmed) {
            echo "<div class='section'>";
            echo "<h3>🗑️ Deleting TV Series...</h3>";
            
            $deleted_count = 0;
            $deleted_genres = 0;
            
            try {
                // Start transaction
                $conn->beginTransaction();
                
                // Delete based on type
                if ($delete_type === 'all') {
                    // Delete all TV series
                    if ($delete_genres) {
                        $stmt = $conn->query("DELETE FROM tv_show_genres");
                        $deleted_genres = $stmt->rowCount();
                    }
                    
                    $stmt = $conn->query("DELETE FROM tv_shows");
                    $deleted_count = $stmt->rowCount();
                    
                } elseif ($delete_type === 'regular') {
                    // Delete only regular TV shows
                    if ($delete_genres) {
                        $stmt = $conn->query("
                            DELETE tsg FROM tv_show_genres tsg 
                            INNER JOIN tv_shows ts ON tsg.tv_show_id = ts.id 
                            WHERE ts.content_type = 'tv_show' OR ts.content_type IS NULL
                        ");
                        $deleted_genres = $stmt->rowCount();
                    }
                    
                    $stmt = $conn->query("DELETE FROM tv_shows WHERE content_type = 'tv_show' OR content_type IS NULL");
                    $deleted_count = $stmt->rowCount();
                    
                } elseif ($delete_type === 'hentai') {
                    // Delete only hentai series
                    if ($delete_genres) {
                        $stmt = $conn->query("
                            DELETE tsg FROM tv_show_genres tsg 
                            INNER JOIN tv_shows ts ON tsg.tv_show_id = ts.id 
                            WHERE ts.content_type = 'hentai'
                        ");
                        $deleted_genres = $stmt->rowCount();
                    }
                    
                    $stmt = $conn->query("DELETE FROM tv_shows WHERE content_type = 'hentai'");
                    $deleted_count = $stmt->rowCount();
                    
                } elseif ($delete_type === 'anime') {
                    // Delete only anime series
                    if ($delete_genres) {
                        $stmt = $conn->query("
                            DELETE tsg FROM tv_show_genres tsg 
                            INNER JOIN tv_shows ts ON tsg.tv_show_id = ts.id 
                            WHERE ts.content_type = 'anime'
                        ");
                        $deleted_genres = $stmt->rowCount();
                    }
                    
                    $stmt = $conn->query("DELETE FROM tv_shows WHERE content_type = 'anime'");
                    $deleted_count = $stmt->rowCount();
                }
                
                // Commit transaction
                $conn->commit();
                
                echo "<div class='success'>";
                echo "<h4>✅ Deletion Complete!</h4>";
                echo "<p><strong>TV Series Deleted:</strong> {$deleted_count}</p>";
                if ($delete_genres) {
                    echo "<p><strong>Genre Relations Deleted:</strong> {$deleted_genres}</p>";
                }
                echo "<p><strong>Deletion Type:</strong> " . ucfirst($delete_type) . "</p>";
                echo "</div>";
                
                // Show updated statistics
                $stmt = $conn->query("SELECT COUNT(*) FROM tv_shows");
                $remaining_tv_shows = $stmt->fetchColumn();
                
                echo "<div class='success'>";
                echo "<h4>📊 Updated Statistics</h4>";
                echo "<p><strong>Remaining TV Series:</strong> {$remaining_tv_shows}</p>";
                echo "</div>";
                
            } catch (Exception $e) {
                $conn->rollback();
                echo "<div class='error'>";
                echo "<h4>❌ Deletion Failed!</h4>";
                echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
                echo "</div>";
            }
            echo "</div>";
        }
    }
    
    // Show deletion form if there are TV series to delete
    if ($stats['total_tv_shows'] > 0) {
        echo "<div class='section danger'>";
        echo "<h2>⚠️ DANGER ZONE - Delete TV Series</h2>";
        echo "<p><strong>WARNING:</strong> This action will permanently delete TV series from your database. This cannot be undone!</p>";
        echo "</div>";
        
        echo "<div class='section'>";
        echo "<h3>🎯 Select Deletion Type</h3>";
        
        echo "<form method='POST' id='deleteForm'>";
        echo "<input type='hidden' name='action' value='delete_tv_series'>";
        
        echo "<div class='checkbox-group'>";
        echo "<h4>What to Delete:</h4>";
        
        echo "<label>";
        echo "<input type='radio' name='delete_type' value='all' checked>";
        echo "<strong>🗑️ Delete ALL TV Series ({$stats['total_tv_shows']} items)</strong>";
        echo "<br><small>This will delete all TV shows, including hentai and anime series</small>";
        echo "</label>";
        
        if ($stats['regular_tv_shows'] > 0) {
            echo "<label>";
            echo "<input type='radio' name='delete_type' value='regular'>";
            echo "<strong>📺 Delete Regular TV Shows Only ({$stats['regular_tv_shows']} items)</strong>";
            echo "<br><small>This will keep hentai and anime series</small>";
            echo "</label>";
        }
        
        if ($stats['hentai_tv_shows'] > 0) {
            echo "<label>";
            echo "<input type='radio' name='delete_type' value='hentai'>";
            echo "<strong>🔞 Delete Hentai Series Only ({$stats['hentai_tv_shows']} items)</strong>";
            echo "<br><small>This will keep regular TV shows and anime</small>";
            echo "</label>";
        }
        
        if ($stats['anime_tv_shows'] > 0) {
            echo "<label>";
            echo "<input type='radio' name='delete_type' value='anime'>";
            echo "<strong>🎌 Delete Anime Series Only ({$stats['anime_tv_shows']} items)</strong>";
            echo "<br><small>This will keep regular TV shows and hentai</small>";
            echo "</label>";
        }
        echo "</div>";
        
        echo "<div class='checkbox-group'>";
        echo "<h4>Additional Options:</h4>";
        echo "<label>";
        echo "<input type='checkbox' name='delete_genres' checked>";
        echo "<strong>🏷️ Also Delete Genre Relations ({$stats['tv_show_genres']} relations)</strong>";
        echo "<br><small>Remove genre associations for deleted TV series</small>";
        echo "</label>";
        echo "</div>";
        
        echo "<div class='confirmation-box'>";
        echo "<h4>⚠️ Confirmation Required</h4>";
        echo "<label>";
        echo "<input type='checkbox' name='confirmed' value='yes' required>";
        echo "<strong>I understand this action cannot be undone</strong>";
        echo "</label>";
        echo "</div>";
        
        echo "<div class='final-warning'>";
        echo "<h3>🚨 FINAL WARNING 🚨</h3>";
        echo "<p><strong>You are about to permanently delete TV series from your database!</strong></p>";
        echo "<p>This action is <strong>IRREVERSIBLE</strong>. Make sure you have a backup if needed.</p>";
        echo "<button type='submit' class='btn btn-danger' onclick='return confirmDeletion()'>🗑️ DELETE TV SERIES</button>";
        echo "</div>";
        
        echo "</form>";
        echo "</div>";
        
        // Show some examples of what will be deleted
        echo "<div class='section'>";
        echo "<h3>📋 Preview: TV Series to be Deleted</h3>";
        
        $stmt = $conn->query("
            SELECT id, tmdb_id, name, content_type, vote_average, first_air_date 
            FROM tv_shows 
            ORDER BY name ASC 
            LIMIT 10
        ");
        $preview_items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($preview_items)) {
            echo "<p><strong>First 10 TV Series (out of {$stats['total_tv_shows']}):</strong></p>";
            echo "<table>";
            echo "<tr><th>ID</th><th>TMDB ID</th><th>Name</th><th>Type</th><th>Rating</th><th>Year</th></tr>";
            foreach ($preview_items as $item) {
                echo "<tr>";
                echo "<td>{$item['id']}</td>";
                echo "<td>{$item['tmdb_id']}</td>";
                echo "<td>" . htmlspecialchars($item['name']) . "</td>";
                echo "<td>" . ($item['content_type'] ?? 'NULL') . "</td>";
                echo "<td>{$item['vote_average']}</td>";
                echo "<td>" . ($item['first_air_date'] ? date('Y', strtotime($item['first_air_date'])) : 'N/A') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            if ($stats['total_tv_shows'] > 10) {
                echo "<p><em>... and " . ($stats['total_tv_shows'] - 10) . " more TV series</em></p>";
            }
        }
        echo "</div>";
        
    } else {
        echo "<div class='section success'>";
        echo "<h3>✅ No TV Series Found</h3>";
        echo "<p>There are no TV series in the database to delete.</p>";
        echo "</div>";
    }
    
    // Quick actions
    echo "<div class='section'>";
    echo "<h3>🔧 Quick Actions</h3>";
    echo "<div style='display: flex; gap: 15px; flex-wrap: wrap;'>";
    echo "<a href='index.php' class='btn btn-secondary'>📊 Admin Dashboard</a>";
    echo "<a href='tv-shows.php' class='btn btn-secondary'>📺 TV Shows Management</a>";
    echo "<a href='import.php' class='btn btn-success'>📥 Import New Content</a>";
    echo "<a href='debug-content-display.php' class='btn'>🔍 Debug Content</a>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section error'>";
    echo "<h3>❌ Error</h3>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "</div>";

echo "<script>
function confirmDeletion() {
    const deleteType = document.querySelector('input[name=\"delete_type\"]:checked').value;
    const deleteGenres = document.querySelector('input[name=\"delete_genres\"]').checked;
    const confirmed = document.querySelector('input[name=\"confirmed\"]').checked;
    
    if (!confirmed) {
        alert('Please confirm that you understand this action cannot be undone.');
        return false;
    }
    
    let message = 'Are you absolutely sure you want to delete ';
    
    if (deleteType === 'all') {
        message += 'ALL TV SERIES';
    } else if (deleteType === 'regular') {
        message += 'all REGULAR TV SHOWS';
    } else if (deleteType === 'hentai') {
        message += 'all HENTAI SERIES';
    } else if (deleteType === 'anime') {
        message += 'all ANIME SERIES';
    }
    
    message += '?\\n\\nThis action is PERMANENT and CANNOT be undone!';
    
    if (deleteGenres) {
        message += '\\n\\nGenre relations will also be deleted.';
    }
    
    message += '\\n\\nType \"DELETE\" to confirm:';
    
    const userInput = prompt(message);
    
    if (userInput === 'DELETE') {
        return confirm('FINAL CONFIRMATION: Delete TV series now?');
    } else {
        alert('Deletion cancelled. You must type \"DELETE\" exactly to confirm.');
        return false;
    }
}
</script>";
?>
