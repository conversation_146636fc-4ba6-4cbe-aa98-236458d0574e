<?php
session_start();
require_once '../config/database.php';

// Simple admin check
$is_admin = false;
if (isset($_SESSION['user_id'])) {
    if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
        $is_admin = true;
    } elseif (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
        $is_admin = true;
    }
}

if (!$is_admin) {
    try {
        $db = new Database();
        $conn = $db->connect();
        
        if (isset($_SESSION['user_id'])) {
            $stmt = $conn->prepare("SELECT role FROM users WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && $user['role'] === 'admin') {
                $is_admin = true;
                $_SESSION['role'] = 'admin';
            }
        }
    } catch (Exception $e) {
        // Database check failed
    }
}

if (!$is_admin) {
    header('Location: ../login.php');
    exit;
}

// Database connection
if (!isset($conn)) {
    $db = new Database();
    $conn = $db->connect();
}

echo "<h1>🚀 Quick Hentai & Anime Converter</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
    .container { max-width: 1200px; margin: 0 auto; }
    .section { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    .btn { padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 6px; display: inline-block; margin: 5px; border: none; cursor: pointer; }
    .btn-danger { background: #dc3545; }
    .btn-warning { background: #ffc107; color: #212529; }
    .btn-success { background: #28a745; }
    .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
    .stat-card { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border: 2px solid #dee2e6; }
    .stat-number { font-size: 2rem; font-weight: bold; margin-bottom: 5px; }
    .hentai { color: #ff1744; border-color: #ff1744; }
    .anime { color: #ff6b35; border-color: #ff6b35; }
    table { width: 100%; border-collapse: collapse; margin: 15px 0; }
    th, td { padding: 8px; border: 1px solid #ddd; text-align: left; font-size: 0.9rem; }
    th { background: #f8f9fa; }
    .progress { background: #e9ecef; border-radius: 4px; height: 20px; margin: 10px 0; }
    .progress-bar { background: #007bff; height: 100%; border-radius: 4px; transition: width 0.3s ease; }
</style>";

echo "<div class='container'>";

try {
    // Get current statistics
    $stats = [];
    
    $stmt = $conn->query("SELECT COUNT(*) FROM tv_shows WHERE content_type = 'hentai'");
    $stats['hentai_tv'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM movies WHERE content_type = 'hentai'");
    $stats['hentai_movies'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM tv_shows WHERE content_type = 'anime'");
    $stats['anime_tv'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM movies WHERE content_type = 'anime'");
    $stats['anime_movies'] = $stmt->fetchColumn();
    
    // Potential content
    $stmt = $conn->query("
        SELECT COUNT(*) FROM tv_shows 
        WHERE (
            LOWER(name) LIKE '%hentai%' OR 
            LOWER(name) LIKE '%ecchi%' OR 
            LOWER(name) LIKE '%adult%' OR 
            LOWER(overview) LIKE '%hentai%' OR 
            LOWER(overview) LIKE '%adult%'
        ) AND (content_type != 'hentai' OR content_type IS NULL)
    ");
    $stats['potential_hentai_tv'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("
        SELECT COUNT(*) FROM tv_shows 
        WHERE (
            LOWER(name) LIKE '%anime%' OR 
            LOWER(name) LIKE '%manga%' OR 
            LOWER(overview) LIKE '%anime%' OR 
            LOWER(overview) LIKE '%manga%'
        ) AND (content_type != 'anime' OR content_type IS NULL)
    ");
    $stats['potential_anime_tv'] = $stmt->fetchColumn();
    
    $stats['total_hentai'] = $stats['hentai_tv'] + $stats['hentai_movies'];
    $stats['total_anime'] = $stats['anime_tv'] + $stats['anime_movies'];
    
    echo "<div class='section'>";
    echo "<h2>📊 Current Statistics</h2>";
    echo "<div class='stats'>";
    echo "<div class='stat-card hentai'>";
    echo "<div class='stat-number hentai'>{$stats['total_hentai']}</div>";
    echo "<div>Total Hentai</div>";
    echo "</div>";
    echo "<div class='stat-card hentai'>";
    echo "<div class='stat-number hentai'>{$stats['hentai_tv']}</div>";
    echo "<div>Hentai TV Shows</div>";
    echo "</div>";
    echo "<div class='stat-card hentai'>";
    echo "<div class='stat-number hentai'>{$stats['hentai_movies']}</div>";
    echo "<div>Hentai Movies</div>";
    echo "</div>";
    echo "<div class='stat-card anime'>";
    echo "<div class='stat-number anime'>{$stats['total_anime']}</div>";
    echo "<div>Total Anime</div>";
    echo "</div>";
    echo "<div class='stat-card anime'>";
    echo "<div class='stat-number anime'>{$stats['anime_tv']}</div>";
    echo "<div>Anime TV Shows</div>";
    echo "</div>";
    echo "<div class='stat-card anime'>";
    echo "<div class='stat-number anime'>{$stats['anime_movies']}</div>";
    echo "<div>Anime Movies</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // Handle conversion actions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'auto_convert_hentai') {
            echo "<div class='section'>";
            echo "<h3>🔞 Auto-Converting Potential Hentai Content</h3>";
            
            // Get potential hentai content
            $stmt = $conn->query("
                SELECT id, name, overview 
                FROM tv_shows 
                WHERE (
                    LOWER(name) LIKE '%hentai%' OR 
                    LOWER(name) LIKE '%ecchi%' OR 
                    LOWER(name) LIKE '%adult%' OR 
                    LOWER(overview) LIKE '%hentai%' OR 
                    LOWER(overview) LIKE '%adult%'
                ) AND (content_type != 'hentai' OR content_type IS NULL)
                LIMIT 50
            ");
            $potential_hentai = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $converted_count = 0;
            foreach ($potential_hentai as $item) {
                try {
                    $stmt = $conn->prepare("UPDATE tv_shows SET content_type = 'hentai' WHERE id = ?");
                    $stmt->execute([$item['id']]);
                    
                    echo "<div style='padding: 5px; background: #d4edda; margin: 2px 0; border-radius: 4px;'>";
                    echo "✅ Converted: " . htmlspecialchars($item['name']);
                    echo "</div>";
                    
                    $converted_count++;
                } catch (Exception $e) {
                    echo "<div style='padding: 5px; background: #f8d7da; margin: 2px 0; border-radius: 4px;'>";
                    echo "❌ Failed: " . htmlspecialchars($item['name']) . " - " . $e->getMessage();
                    echo "</div>";
                }
            }
            
            echo "<div class='success' style='margin-top: 15px;'>";
            echo "<strong>✅ Conversion Complete!</strong><br>";
            echo "Successfully converted {$converted_count} items to hentai content.";
            echo "</div>";
            echo "</div>";
        }
        
        if ($action === 'auto_convert_anime') {
            echo "<div class='section'>";
            echo "<h3>🎌 Auto-Converting Potential Anime Content</h3>";
            
            // Get potential anime content
            $stmt = $conn->query("
                SELECT id, name, overview 
                FROM tv_shows 
                WHERE (
                    LOWER(name) LIKE '%anime%' OR 
                    LOWER(name) LIKE '%manga%' OR 
                    LOWER(overview) LIKE '%anime%' OR 
                    LOWER(overview) LIKE '%manga%'
                ) AND (content_type != 'anime' OR content_type IS NULL)
                LIMIT 50
            ");
            $potential_anime = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $converted_count = 0;
            foreach ($potential_anime as $item) {
                try {
                    $stmt = $conn->prepare("UPDATE tv_shows SET content_type = 'anime' WHERE id = ?");
                    $stmt->execute([$item['id']]);
                    
                    echo "<div style='padding: 5px; background: #d4edda; margin: 2px 0; border-radius: 4px;'>";
                    echo "✅ Converted: " . htmlspecialchars($item['name']);
                    echo "</div>";
                    
                    $converted_count++;
                } catch (Exception $e) {
                    echo "<div style='padding: 5px; background: #f8d7da; margin: 2px 0; border-radius: 4px;'>";
                    echo "❌ Failed: " . htmlspecialchars($item['name']) . " - " . $e->getMessage();
                    echo "</div>";
                }
            }
            
            echo "<div class='success' style='margin-top: 15px;'>";
            echo "<strong>✅ Conversion Complete!</strong><br>";
            echo "Successfully converted {$converted_count} items to anime content.";
            echo "</div>";
            echo "</div>";
        }
    }
    
    // Show potential content
    if ($stats['potential_hentai_tv'] > 0 || $stats['potential_anime_tv'] > 0) {
        echo "<div class='section'>";
        echo "<h2>🔍 Potential Content for Conversion</h2>";
        
        if ($stats['potential_hentai_tv'] > 0) {
            echo "<div class='warning' style='margin: 15px 0;'>";
            echo "<h4>🔞 Potential Hentai Content: {$stats['potential_hentai_tv']} items</h4>";
            echo "<p>Content that contains hentai/ecchi/adult keywords but not yet categorized.</p>";
            
            // Show some examples
            $stmt = $conn->query("
                SELECT name, overview 
                FROM tv_shows 
                WHERE (
                    LOWER(name) LIKE '%hentai%' OR 
                    LOWER(name) LIKE '%ecchi%' OR 
                    LOWER(name) LIKE '%adult%' OR 
                    LOWER(overview) LIKE '%hentai%' OR 
                    LOWER(overview) LIKE '%adult%'
                ) AND (content_type != 'hentai' OR content_type IS NULL)
                LIMIT 5
            ");
            $examples = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<div style='margin: 10px 0;'>";
            echo "<strong>Examples:</strong><br>";
            foreach ($examples as $example) {
                echo "• " . htmlspecialchars($example['name']) . "<br>";
            }
            echo "</div>";
            
            echo "<form method='POST' style='margin-top: 10px;'>";
            echo "<button type='submit' name='action' value='auto_convert_hentai' class='btn btn-danger' onclick='return confirm(\"Convert {$stats['potential_hentai_tv']} items to hentai content?\")'>🔞 Auto-Convert to Hentai</button>";
            echo "</form>";
            echo "</div>";
        }
        
        if ($stats['potential_anime_tv'] > 0) {
            echo "<div class='info' style='margin: 15px 0;'>";
            echo "<h4>🎌 Potential Anime Content: {$stats['potential_anime_tv']} items</h4>";
            echo "<p>Content that contains anime/manga keywords but not yet categorized.</p>";
            
            // Show some examples
            $stmt = $conn->query("
                SELECT name, overview 
                FROM tv_shows 
                WHERE (
                    LOWER(name) LIKE '%anime%' OR 
                    LOWER(name) LIKE '%manga%' OR 
                    LOWER(overview) LIKE '%anime%' OR 
                    LOWER(overview) LIKE '%manga%'
                ) AND (content_type != 'anime' OR content_type IS NULL)
                LIMIT 5
            ");
            $examples = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<div style='margin: 10px 0;'>";
            echo "<strong>Examples:</strong><br>";
            foreach ($examples as $example) {
                echo "• " . htmlspecialchars($example['name']) . "<br>";
            }
            echo "</div>";
            
            echo "<form method='POST' style='margin-top: 10px;'>";
            echo "<button type='submit' name='action' value='auto_convert_anime' class='btn btn-warning' onclick='return confirm(\"Convert {$stats['potential_anime_tv']} items to anime content?\")'>🎌 Auto-Convert to Anime</button>";
            echo "</form>";
            echo "</div>";
        }
        echo "</div>";
    }
    
    // Quick actions
    echo "<div class='section'>";
    echo "<h2>🔧 Quick Actions</h2>";
    echo "<div style='display: flex; gap: 15px; flex-wrap: wrap;'>";
    echo "<a href='hentai-anime-management.php' class='btn btn-success'>🔞🎌 Open Management System</a>";
    echo "<a href='hentai-servers.php' class='btn'>🖥️ Manage Servers</a>";
    echo "<a href='../fix_hentai_servers_final.php' target='_blank' class='btn'>🔧 Fix Servers</a>";
    echo "<a href='index.php' class='btn'>📊 Admin Dashboard</a>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section error'>";
    echo "<h3>❌ Error</h3>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "</div>";
?>
