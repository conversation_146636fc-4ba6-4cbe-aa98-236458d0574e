@echo off
echo Setting up Gradle Wrapper...

REM Create gradle wrapper directory
if not exist "gradle\wrapper" mkdir gradle\wrapper

REM Download gradle wrapper jar
echo Downloading Gradle Wrapper JAR...
powershell -Command "Invoke-WebRequest -Uri 'https://github.com/gradle/gradle/raw/v8.4.0/gradle/wrapper/gradle-wrapper.jar' -OutFile 'gradle\wrapper\gradle-wrapper.jar'"

if exist "gradle\wrapper\gradle-wrapper.jar" (
    echo Gradle Wrapper JAR downloaded successfully!
    echo Now you can run: gradlew.bat clean
) else (
    echo Failed to download Gradle Wrapper JAR
    echo Please download manually from: https://github.com/gradle/gradle/raw/v8.4.0/gradle/wrapper/gradle-wrapper.jar
    echo And place it in: gradle\wrapper\gradle-wrapper.jar
)

pause
