package com.streamflix.app.presentation.main;

import android.os.Bundle;
import androidx.activity.ComponentActivity;
import androidx.compose.animation.*;
import androidx.compose.foundation.layout.*;
import androidx.compose.material.icons.Icons;
import androidx.compose.material.icons.filled.*;
import androidx.compose.material3.*;
import androidx.compose.runtime.*;
import androidx.compose.ui.Modifier;
import androidx.compose.ui.graphics.vector.ImageVector;
import com.streamflix.app.ui.theme.*;
import dagger.hilt.android.AndroidEntryPoint;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000*\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\u001a\b\u0010\u0005\u001a\u00020\u0006H\u0007\u001a\b\u0010\u0007\u001a\u00020\u0006H\u0007\u001a\u001c\u0010\b\u001a\u00020\u00062\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00060\nH\u0007\u001a2\u0010\f\u001a\u00020\u00062\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00060\u000e2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00060\u000e2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00060\u000eH\u0007\u001a\b\u0010\u0011\u001a\u00020\u0006H\u0007\u001a\u001c\u0010\u0012\u001a\u00020\u00062\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00060\nH\u0007\"\u0017\u0010\u0000\u001a\b\u0012\u0004\u0012\u00020\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0003\u0010\u0004\u00a8\u0006\u0014"}, d2 = {"bottomNavItems", "", "Lcom/streamflix/app/presentation/main/BottomNavItem;", "getBottomNavItems", "()Ljava/util/List;", "HomeScreen", "", "MainScreen", "MoviesScreen", "onMovieClick", "Lkotlin/Function1;", "", "ProfileScreen", "onNavigateToSettings", "Lkotlin/Function0;", "onNavigateToWatchlist", "onNavigateToDownloads", "SearchScreen", "TvShowsScreen", "onTvShowClick", "app_debug"})
public final class MainActivityKt {
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<com.streamflix.app.presentation.main.BottomNavItem> bottomNavItems = null;
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void MainScreen() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void HomeScreen() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SearchScreen() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MoviesScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<java.lang.Object, kotlin.Unit> onMovieClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TvShowsScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<java.lang.Object, kotlin.Unit> onTvShowClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ProfileScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToWatchlist, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToDownloads) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<com.streamflix.app.presentation.main.BottomNavItem> getBottomNavItems() {
        return null;
    }
}