<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Internet permission for API calls -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    
    <!-- Storage permissions for downloads -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    
    <!-- Media permissions for Android 13+ -->
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    
    <!-- Wake lock for video playback -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    
    <!-- Notification permission for Android 13+ -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <application
        android:name=".StreamFlixApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@drawable/ic_streamflix_logo"
        android:label="@string/app_name"
        android:roundIcon="@drawable/ic_streamflix_logo"
        android:supportsRtl="true"
        android:theme="@style/Theme.StreamFlix.AppCompat"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">

        <!-- Splash Screen Activity -->
        <activity
            android:name=".presentation.splash.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.StreamFlix.Splash.Simple">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Main Activity -->
        <activity
            android:name=".presentation.main.MainActivity"
            android:exported="false"
            android:theme="@style/Theme.StreamFlix"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <!-- Video Player Activity -->
        <activity
            android:name=".presentation.player.VideoPlayerActivity"
            android:exported="false"
            android:theme="@style/Theme.StreamFlix.Player"
            android:screenOrientation="landscape"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:launchMode="singleTop" />

        <!-- Auth Activity -->
        <activity
            android:name=".presentation.auth.AuthActivity"
            android:exported="false"
            android:theme="@style/Theme.StreamFlix"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <!-- Settings Activity -->
        <activity
            android:name=".presentation.settings.SettingsActivity"
            android:exported="false"
            android:theme="@style/Theme.StreamFlix"
            android:screenOrientation="portrait" />

        <!-- Download Service -->
        <service
            android:name=".data.service.DownloadService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- File Provider for sharing -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- Deep link handling -->
        <activity-alias
            android:name=".DeepLinkActivity"
            android:targetActivity=".presentation.main.MainActivity"
            android:exported="true">
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https"
                    android:host="streamflix.app" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="streamflix" />
            </intent-filter>
        </activity-alias>

    </application>

</manifest>
