<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$message = '';
$error = '';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $db = new Database();
        $conn = $db->connect();
        
        if ($action === 'clear_logs') {
            $log_type = $_POST['log_type'] ?? '';
            
            if ($log_type === 'all') {
                $conn->exec("DELETE FROM system_logs");
                $message = 'All logs cleared successfully!';
            } elseif (!empty($log_type)) {
                $stmt = $conn->prepare("DELETE FROM system_logs WHERE log_type = ?");
                $stmt->execute([$log_type]);
                $message = ucfirst($log_type) . ' logs cleared successfully!';
            }
        }
        
    } catch (Exception $e) {
        $error = 'Error: ' . $e->getMessage();
    }
}

// Create system_logs table if not exists
try {
    $db = new Database();
    $conn = $db->connect();
    
    $conn->exec("
        CREATE TABLE IF NOT EXISTS system_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            log_type VARCHAR(50),
            action VARCHAR(100),
            description TEXT,
            user_id INT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_log_type (log_type),
            INDEX idx_created_at (created_at),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )
    ");
    
} catch (Exception $e) {
    // Table creation failed, continue anyway
}

// Get logs with pagination and filtering
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = 50;
$offset = ($page - 1) * $limit;
$filter_type = sanitizeInput($_GET['filter_type'] ?? '');
$search = sanitizeInput($_GET['search'] ?? '');

try {
    $db = new Database();
    $conn = $db->connect();
    
    // Build query
    $where_conditions = [];
    $params = [];
    
    if (!empty($filter_type)) {
        $where_conditions[] = "sl.log_type = ?";
        $params[] = $filter_type;
    }
    
    if (!empty($search)) {
        $where_conditions[] = "(sl.action LIKE ? OR sl.description LIKE ? OR u.username LIKE ?)";
        $params[] = "%{$search}%";
        $params[] = "%{$search}%";
        $params[] = "%{$search}%";
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Count total logs
    $count_sql = "
        SELECT COUNT(*) 
        FROM system_logs sl 
        LEFT JOIN users u ON sl.user_id = u.id 
        $where_clause
    ";
    $stmt = $conn->prepare($count_sql);
    $stmt->execute($params);
    $total_logs = $stmt->fetchColumn();
    $total_pages = ceil($total_logs / $limit);
    
    // Get logs
    $sql = "
        SELECT sl.*, u.username 
        FROM system_logs sl 
        LEFT JOIN users u ON sl.user_id = u.id 
        $where_clause
        ORDER BY sl.created_at DESC 
        LIMIT ? OFFSET ?
    ";
    
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get log statistics
    $stats = [];
    $stmt = $conn->query("SELECT COUNT(*) FROM system_logs");
    $stats['total'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM system_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $stats['today'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM system_logs WHERE log_type = 'error'");
    $stats['errors'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM system_logs WHERE log_type = 'login'");
    $stats['logins'] = $stmt->fetchColumn();
    
    // Get log types for filter
    $stmt = $conn->query("SELECT DISTINCT log_type FROM system_logs ORDER BY log_type");
    $log_types = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
} catch (Exception $e) {
    $error = 'Database error: ' . $e->getMessage();
    $logs = [];
    $total_logs = 0;
    $total_pages = 0;
    $stats = ['total' => 0, 'today' => 0, 'errors' => 0, 'logins' => 0];
    $log_types = [];
}

// Function to get log icon
function getLogIcon($type) {
    $icons = [
        'login' => '🔐',
        'logout' => '🚪',
        'error' => '❌',
        'warning' => '⚠️',
        'info' => 'ℹ️',
        'success' => '✅',
        'admin' => '👑',
        'user' => '👤',
        'content' => '📺',
        'system' => '⚙️',
        'security' => '🛡️'
    ];
    
    return $icons[$type] ?? '📋';
}

// Function to get log color class
function getLogColorClass($type) {
    $colors = [
        'error' => 'log-error',
        'warning' => 'log-warning',
        'success' => 'log-success',
        'info' => 'log-info',
        'login' => 'log-login',
        'logout' => 'log-logout',
        'admin' => 'log-admin',
        'security' => 'log-security'
    ];
    
    return $colors[$type] ?? 'log-default';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Logs - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="assets/admin-style.css">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            color: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .admin-nav {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 30px;
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .admin-nav a {
            padding: 12px 20px;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background: #e50914;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(229, 9, 20, 0.4);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: var(--text-color);
            font-size: 0.9rem;
        }
        
        .controls-section {
            background: var(--secondary-color);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .controls-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .search-box {
            flex: 1;
            min-width: 250px;
            padding: 10px 15px;
            border: 2px solid #333;
            border-radius: 8px;
            background: var(--bg-color);
            color: var(--text-color);
        }
        
        .filter-select {
            padding: 10px 15px;
            border: 2px solid #333;
            border-radius: 8px;
            background: var(--bg-color);
            color: var(--text-color);
        }
        
        .logs-table {
            background: var(--secondary-color);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .table th {
            background: rgba(255,255,255,0.1);
            font-weight: 600;
            color: var(--text-color);
        }
        
        .table td {
            color: var(--text-color);
        }
        
        .table tr:hover {
            background: rgba(255,255,255,0.05);
        }
        
        .log-type {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .log-error { background: #f8d7da; color: #721c24; }
        .log-warning { background: #fff3cd; color: #856404; }
        .log-success { background: #d4edda; color: #155724; }
        .log-info { background: #d1ecf1; color: #0c5460; }
        .log-login { background: #e2e3e5; color: #383d41; }
        .log-logout { background: #e2e3e5; color: #383d41; }
        .log-admin { background: #f8d7da; color: #721c24; }
        .log-security { background: #fff3cd; color: #856404; }
        .log-default { background: #e9ecef; color: #495057; }
        
        .log-description {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .log-user {
            font-weight: 500;
        }
        
        .log-ip {
            font-family: monospace;
            font-size: 0.9rem;
            color: #888;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }
        
        .pagination a,
        .pagination span {
            padding: 10px 15px;
            background: var(--secondary-color);
            color: var(--text-color);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .pagination a:hover,
        .pagination .current {
            background: var(--primary-color);
            color: white;
        }
        
        .success-message,
        .error-message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .clear-logs-section {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        @media (max-width: 768px) {
            .admin-container {
                margin-top: 80px;
                padding: 0 15px;
            }
            
            .controls-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-box {
                min-width: 100%;
            }
            
            .table {
                font-size: 14px;
            }
            
            .table th,
            .table td {
                padding: 10px;
            }
            
            .log-description {
                max-width: 150px;
            }
            
            .admin-nav {
                justify-content: center;
            }
            
            .admin-nav a {
                padding: 10px 15px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <span>Welcome, <?php echo $_SESSION['username']; ?></span>
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>📋 System Logs</h1>
            <p>Monitor system activities and track user actions</p>
        </div>

        <nav class="admin-nav">
            <a href="index.php">📊 Dashboard</a>
            <a href="movies.php">🎬 Movies</a>
            <a href="tv-shows.php">📺 TV Shows</a>
            <a href="users.php">👥 Users</a>
            <a href="servers.php">🖥️ Servers</a>
            <a href="analytics.php">📈 Analytics</a>
            <a href="import.php">📥 Import</a>
            <a href="app-management.php">📱 App Management</a>
            <a href="user-management.php">👤 User Management</a>
            <a href="system-logs.php" class="active">📋 System Logs</a>
            <a href="maintenance.php">🔧 Maintenance</a>
            <a href="database-updater.php">🗄️ DB Updater</a>
            <a href="quick-setup.php">🚀 Quick Setup</a>
            <a href="settings.php">⚙️ Settings</a>
        </nav>

        <?php if (isset($message)): ?>
            <div class="success-message"><?php echo $message; ?></div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="error-message"><?php echo $error; ?></div>
        <?php endif; ?>

        <!-- Log Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['total']); ?></div>
                <div class="stat-label">Total Logs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['today']); ?></div>
                <div class="stat-label">Today's Logs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['errors']); ?></div>
                <div class="stat-label">Error Logs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['logins']); ?></div>
                <div class="stat-label">Login Logs</div>
            </div>
        </div>

        <!-- Search and Filter Controls -->
        <div class="controls-section">
            <form method="GET" class="controls-row">
                <input type="text" name="search" class="search-box" placeholder="🔍 Search logs..."
                       value="<?php echo htmlspecialchars($search); ?>">

                <select name="filter_type" class="filter-select">
                    <option value="">All Types</option>
                    <?php foreach ($log_types as $type): ?>
                        <option value="<?php echo htmlspecialchars($type); ?>"
                                <?php echo $filter_type === $type ? 'selected' : ''; ?>>
                            <?php echo getLogIcon($type) . ' ' . ucfirst($type); ?>
                        </option>
                    <?php endforeach; ?>
                </select>

                <button type="submit" class="btn btn-primary">Filter</button>

                <?php if (!empty($search) || !empty($filter_type)): ?>
                    <a href="system-logs.php" class="btn btn-secondary">Clear</a>
                <?php endif; ?>
            </form>

            <!-- Clear Logs -->
            <div class="clear-logs-section">
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="clear_logs">
                    <select name="log_type" class="filter-select">
                        <option value="">Select log type to clear</option>
                        <option value="all">All Logs</option>
                        <?php foreach ($log_types as $type): ?>
                            <option value="<?php echo htmlspecialchars($type); ?>">
                                <?php echo ucfirst($type) . ' Logs'; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <button type="submit" class="btn btn-danger"
                            onclick="return confirm('Are you sure you want to clear these logs? This action cannot be undone.')">
                        🗑️ Clear Logs
                    </button>
                </form>
            </div>
        </div>

        <!-- Logs Table -->
        <div class="logs-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>Type</th>
                        <th>Action</th>
                        <th>Description</th>
                        <th>User</th>
                        <th>IP Address</th>
                        <th>Date/Time</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($logs as $log): ?>
                    <tr>
                        <td>
                            <span class="log-type <?php echo getLogColorClass($log['log_type']); ?>">
                                <?php echo getLogIcon($log['log_type']) . ' ' . ucfirst($log['log_type']); ?>
                            </span>
                        </td>
                        <td><?php echo htmlspecialchars($log['action']); ?></td>
                        <td>
                            <div class="log-description" title="<?php echo htmlspecialchars($log['description']); ?>">
                                <?php echo htmlspecialchars($log['description']); ?>
                            </div>
                        </td>
                        <td>
                            <?php if ($log['username']): ?>
                                <span class="log-user"><?php echo htmlspecialchars($log['username']); ?></span>
                            <?php else: ?>
                                <span style="color: #888;">System</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="log-ip"><?php echo htmlspecialchars($log['ip_address'] ?? 'N/A'); ?></span>
                        </td>
                        <td>
                            <?php echo date('M j, Y H:i:s', strtotime($log['created_at'])); ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <?php if (empty($logs)): ?>
            <div style="text-align: center; padding: 50px; color: #888;">
                <h3>📋 No logs found</h3>
                <p>Try adjusting your search or filter criteria.</p>
            </div>
        <?php endif; ?>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <div class="pagination">
            <?php if ($page > 1): ?>
                <a href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($filter_type) ? '&filter_type=' . urlencode($filter_type) : ''; ?>">← Previous</a>
            <?php endif; ?>

            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                <?php if ($i == $page): ?>
                    <span class="current"><?php echo $i; ?></span>
                <?php else: ?>
                    <a href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($filter_type) ? '&filter_type=' . urlencode($filter_type) : ''; ?>"><?php echo $i; ?></a>
                <?php endif; ?>
            <?php endfor; ?>

            <?php if ($page < $total_pages): ?>
                <a href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($filter_type) ? '&filter_type=' . urlencode($filter_type) : ''; ?>">Next →</a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>

    <script>
        // Auto-submit filter form on change
        document.querySelector('select[name="filter_type"]').addEventListener('change', function() {
            this.form.submit();
        });

        // Auto-refresh logs every 30 seconds
        setInterval(function() {
            if (!document.querySelector('input[name="search"]').value &&
                !document.querySelector('select[name="filter_type"]').value) {
                location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
